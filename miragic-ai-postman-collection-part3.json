{"info": {"name": "Miragic-AI API - Part 3", "description": "API collection for Miragic-AI SaaS platform - Credits, Image Generation, and Video Generation", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Credits", "description": "Endpoints for credit management", "item": [{"name": "Get Credit Balance", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/v1/credits/balance", "host": ["{{baseUrl}}"], "path": ["api", "v1", "credits", "balance"]}, "description": "Get user's current credit balance"}}, {"name": "Get Credit History", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/v1/credits/history", "host": ["{{baseUrl}}"], "path": ["api", "v1", "credits", "history"]}, "description": "Get user's credit transaction history"}}, {"name": "Get Credit Packages", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/api/v1/credits/packages", "host": ["{{baseUrl}}"], "path": ["api", "v1", "credits", "packages"]}, "description": "Get available credit packages for purchase"}}, {"name": "Get Service Costs", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/api/v1/credits/costs", "host": ["{{baseUrl}}"], "path": ["api", "v1", "credits", "costs"]}, "description": "Get costs of various services in credits"}}, {"name": "Create Stripe Checkout Session", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/v1/credits/purchase/stripe/create-checkout-session", "host": ["{{baseUrl}}"], "path": ["api", "v1", "credits", "purchase", "stripe", "create-checkout-session"]}, "body": {"mode": "raw", "raw": "{\n  \"packageId\": \"credit-package-id\",\n  \"successUrl\": \"https://yourdomain.com/success\",\n  \"cancelUrl\": \"https://yourdomain.com/cancel\"\n}"}, "description": "Create a Stripe checkout session for credit purchase"}}, {"name": "Create PayPal Order", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/v1/credits/purchase/paypal/create-order", "host": ["{{baseUrl}}"], "path": ["api", "v1", "credits", "purchase", "paypal", "create-order"]}, "body": {"mode": "raw", "raw": "{\n  \"packageId\": \"credit-package-id\"\n}"}, "description": "Create a PayPal order for credit purchase"}}, {"name": "Capture PayPal Order", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/v1/credits/purchase/paypal/capture-order", "host": ["{{baseUrl}}"], "path": ["api", "v1", "credits", "purchase", "paypal", "capture-order"]}, "body": {"mode": "raw", "raw": "{\n  \"orderId\": \"paypal-order-id\"\n}"}, "description": "Capture a PayPal payment for credit purchase"}}, {"name": "Admin: Adjust User Credits", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/v1/credits/admin/adjust", "host": ["{{baseUrl}}"], "path": ["api", "v1", "credits", "admin", "adjust"]}, "body": {"mode": "raw", "raw": "{\n  \"userId\": \"user-id\",\n  \"amount\": 100,\n  \"reason\": \"Promotional credits\"\n}"}, "description": "Admin endpoint to adjust a user's credit balance"}}, {"name": "Admin: Get User Credit History", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/v1/credits/admin/users/:userId/history", "host": ["{{baseUrl}}"], "path": ["api", "v1", "credits", "admin", "users", ":userId", "history"], "variable": [{"key": "userId", "value": "user-id"}]}, "description": "Admin endpoint to view a specific user's credit history"}}]}, {"name": "Image Generation", "description": "Endpoints for AI image generation using Stability AI", "item": [{"name": "Generate Image", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/v1/image/generate", "host": ["{{baseUrl}}"], "path": ["api", "v1", "image", "generate"]}, "body": {"mode": "raw", "raw": "{\n  \"prompt\": \"A beautiful sunset over mountains\",\n  \"negativePrompt\": \"blurry, low quality\",\n  \"width\": 1024,\n  \"height\": 1024,\n  \"styleId\": \"style-id\",\n  \"numberOfImages\": 1\n}"}, "description": "Generate AI images based on text prompts (costs 2 credits)"}}, {"name": "Get User Image Jobs", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/v1/image/jobs", "host": ["{{baseUrl}}"], "path": ["api", "v1", "image", "jobs"]}, "description": "Get all image generation jobs for the current user"}}, {"name": "Get Image Job", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/v1/image/jobs/:jobId", "host": ["{{baseUrl}}"], "path": ["api", "v1", "image", "jobs", ":jobId"], "variable": [{"key": "jobId", "value": "image-job-id"}]}, "description": "Get details of a specific image generation job"}}, {"name": "Get Image Styles", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/v1/image/styles", "host": ["{{baseUrl}}"], "path": ["api", "v1", "image", "styles"]}, "description": "Get available image generation styles"}}]}, {"name": "Video Generation", "description": "Endpoints for AI video generation using Synthesia API", "item": [{"name": "Generate Video", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/v1/video/generate", "host": ["{{baseUrl}}"], "path": ["api", "v1", "video", "generate"]}, "body": {"mode": "raw", "raw": "{\n  \"script\": \"Hello, this is a test video generated by Miragic-AI.\",\n  \"avatarId\": \"avatar-id\",\n  \"voiceId\": \"voice-id\",\n  \"title\": \"Test Video\"\n}"}, "description": "Generate AI videos with virtual avatars (costs 50 credits per minute)"}}, {"name": "Get User Video Jobs", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/v1/video/jobs", "host": ["{{baseUrl}}"], "path": ["api", "v1", "video", "jobs"]}, "description": "Get all video generation jobs for the current user"}}, {"name": "Get Video Job", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/v1/video/jobs/:jobId", "host": ["{{baseUrl}}"], "path": ["api", "v1", "video", "jobs", ":jobId"], "variable": [{"key": "jobId", "value": "video-job-id"}]}, "description": "Get details of a specific video generation job"}}, {"name": "Get Avatars", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/v1/video/avatars", "host": ["{{baseUrl}}"], "path": ["api", "v1", "video", "avatars"]}, "description": "Get available avatars for video generation"}}, {"name": "Get Voices", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/v1/video/voices", "host": ["{{baseUrl}}"], "path": ["api", "v1", "video", "voices"]}, "description": "Get available voices for video generation"}}]}], "variable": [{"key": "baseUrl", "value": "http://localhost:3000", "type": "string"}]}