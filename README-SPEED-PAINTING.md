# Speed Painting Feature Documentation

## Overview

The Speed Painting feature in Miragic-AI allows users to generate time-lapse painting videos from static images. This feature transforms a regular image into an artistic video showing the painting process from blank canvas to completed artwork.

## Features

- Upload images in various formats (JPEG, PNG, WEBP, GIF)
- Generate time-lapse painting videos
- Track job status in real-time
- Download generated videos
- View job history
- Admin analytics dashboard

## Technical Implementation

### Backend

The backend implementation includes:

1. **API Endpoints**:
   - `POST /api/v1/speed-painting/create` - Upload an image and create a job
   - `GET /api/v1/speed-painting/jobs/:jobId` - Get a specific job
   - `GET /api/v1/speed-painting/jobs` - Get all jobs for the current user
   - `GET /api/v1/speed-painting/queue-status` - Get queue status
   - `GET /api/v1/admin/analytics/speed-painting` - Admin analytics (admin only)

2. **Components**:
   - `SpeedPaintingController` - Handles API requests
   - `SpeedPaintingService` - Business logic for processing jobs
   - `CreditUsageService` - Manages credit deduction
   - `Analytics` - Tracks usage metrics

3. **Database Models**:
   - `SpeedPaintingJob` - Stores job information
   - `AnalyticsEvent` - Tracks analytics data

### Frontend

The frontend implementation includes:

1. **Components**:
   - `SpeedPaintingPage` - Main user interface for creating jobs
   - `SpeedPaintingAnalytics` - Admin dashboard for analytics

2. **Services**:
   - `SpeedpaintService` - Handles API calls to the backend

## Credit System Integration

The Speed Painting feature is integrated with the Miragic-AI credit system:

- Each speed painting job costs 5 credits
- Credits are deducted upon successful job completion
- Users need sufficient credits to create a job
- Credit usage is tracked for analytics

## Usage

### For Users

1. Navigate to the Speed Painting page
2. Upload an image by dragging and dropping or using the file selector
3. Click "Generate Speed Painting" to start the process
4. Wait for the job to complete (you can track progress in real-time)
5. Download or share the generated video

### For Administrators

1. Access the Admin Analytics dashboard
2. Navigate to the Speed Painting tab
3. View usage statistics, including:
   - Total jobs created
   - Success rate
   - Average processing time
   - Credit usage
   - User engagement metrics

## Environment Variables

The following environment variables are required:

- `MIRAGIC_AI_API_URL` - Base URL for the Miragic AI API
- `MIRAGIC_AI_API_KEY` - API key for authentication with the Miragic AI API
- `DATABASE_URL` - Connection string for the PostgreSQL database
- `STORE_PROCESSED_IMAGES` - Flag to determine whether to store processed images locally

## Security Considerations

- File uploads are validated for type and size
- Rate limiting is implemented to prevent abuse
- JWT authentication is required for all endpoints
- Admin-only endpoints are protected with additional authorization

## Logging and Monitoring

- Comprehensive logging is implemented using Winston
- All API calls, job processing, and errors are logged
- Analytics events are tracked for monitoring usage patterns

## Future Enhancements

Potential future enhancements for the Speed Painting feature:

1. Additional customization options (style, speed, resolution)
2. Batch processing for multiple images
3. Social sharing integration
4. Gallery of public speed paintings
5. AI-based style recommendations

## Troubleshooting

Common issues and solutions:

1. **Job stuck in PENDING status**:
   - Check server logs for errors
   - Verify Miragic AI API connectivity
   - Ensure sufficient server resources

2. **Credit deduction issues**:
   - Check user credit balance
   - Verify credit transaction logs
   - Ensure credit service is functioning properly

3. **File upload failures**:
   - Verify file format and size
   - Check server disk space
   - Ensure upload directory permissions are correct
