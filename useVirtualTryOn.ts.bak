import { useState, useEffect, useCallback, useRef } from "react";
import { toast } from "sonner";
import VirtualTryOnService, {
  type VirtualTryOnJob,
  type ModelImage,
  type RecentTryOn,
  type ClothingItem,
} from "@/services/virtualTryOn.service";

// Import the credit hook
import useCredits from "@/hooks/useCredits";

type TryOnMode = "SINGLE" | "TOP_BOTTOM";

type TryOnMode = "SINGLE" | "TOP_BOTTOM";
type Gender = "MALE" | "FEMALE" | "UNISEX";

interface UseVirtualTryOnOptions {
  pollingInterval?: number;
  autoLoadModels?: boolean;
  autoLoadRecent?: boolean;
}

export function useVirtualTryOn(options: UseVirtualTryOnOptions = {}) {
  const {
    pollingInterval = 2000,
    autoLoadModels = true,
    autoLoadRecent = true,
  } = options;

  const { refreshCredits } = useCredits();

  // State for file uploads
  const [humanImage, setHumanImage] = useState<File | null>(null);
  const [humanImagePreview, setHumanImagePreview] = useState<string | null>(null);
  const [clothImage, setClothImage] = useState<File | null>(null);
  const [clothImagePreview, setClothImagePreview] = useState<string | null>(null);
  const [bottomClothImage, setBottomClothImage] = useState<File | null>(null);
  const [bottomClothImagePreview, setBottomClothImagePreview] = useState<string | null>(null);

  // State for model selection
  const [selectedModelId, setSelectedModelId] = useState<string | null>(null);
  const [mode, setMode] = useState<TryOnMode>("SINGLE");
  
  // State for processing
  const [isProcessing, setIsProcessing] = useState<boolean>(false);
  const [currentJob, setCurrentJob] = useState<VirtualTryOnJob | null>(null);
  
  // State for models and recent try-ons
  const [adminModels, setAdminModels] = useState<ModelImage[]>([]);
  const [userModels, setUserModels] = useState<ModelImage[]>([]);
  const [recentTryOns, setRecentTryOns] = useState<RecentTryOn[]>([]);
  
  // Loading states
  const [isLoadingAdminModels, setIsLoadingAdminModels] = useState<boolean>(false);
  const [isLoadingUserModels, setIsLoadingUserModels] = useState<boolean>(false);
  const [isLoadingRecent, setIsLoadingRecent] = useState<boolean>(false);
  
  // File input refs
  const humanFileInputRef = useRef<HTMLInputElement>(null);
  const clothFileInputRef = useRef<HTMLInputElement>(null);
  const bottomClothFileInputRef = useRef<HTMLInputElement>(null);

  // Fetch models on mount if autoLoadModels is true
  useEffect(() => {
    if (autoLoadModels) {
      fetchAdminModels();
      fetchUserModels();
    }
  }, [autoLoadModels]);

  // Fetch recent try-ons on mount if autoLoadRecent is true
  useEffect(() => {
    if (autoLoadRecent) {
      fetchRecentTryOns();
    }
  }, [autoLoadRecent]);

  // Fetch admin models
  const fetchAdminModels = useCallback(async () => {
    try {
      setIsLoadingAdminModels(true);
      const response = await VirtualTryOnService.getAdminModelImages();
      if (response.success) {
        setAdminModels(response.data);
      } else {
        toast.error("Failed to load admin models");
      }
    } catch (error) {
      console.error("Error fetching admin models:", error);
      toast.error("An error occurred while loading admin models");
    } finally {
      setIsLoadingAdminModels(false);
    }
  }, []);

  // Fetch user models
  const fetchUserModels = useCallback(async () => {
    try {
      setIsLoadingUserModels(true);
      const response = await VirtualTryOnService.getUserModelImages();
      if (response.success) {
        setUserModels(response.data);
      } else {
        toast.error("Failed to load your models");
      }
    } catch (error) {
      console.error("Error fetching user models:", error);
      toast.error("An error occurred while loading your models");
    } finally {
      setIsLoadingUserModels(false);
    }
  }, []);

  // Fetch all models (both admin and user)
  const fetchModels = useCallback(async () => {
    await Promise.all([fetchAdminModels(), fetchUserModels()]);
  }, [fetchAdminModels, fetchUserModels]);

  // Fetch recent try-ons
  const fetchRecentTryOns = useCallback(async () => {
    try {
      setIsLoadingRecent(true);
      const response = await VirtualTryOnService.getRecentTryOns();
      if (response.success) {
        setRecentTryOns(response.data);
      } else {
        toast.error("Failed to load recent try-ons");
      }
    } catch (error) {
      console.error("Error fetching recent try-ons:", error);
      toast.error("An error occurred while loading recent try-ons");
    } finally {
      setIsLoadingRecent(false);
    }
  }, []);

  // Process human file (model image)
  const processHumanFile = useCallback((file: File | null) => {
    if (file) {
      // Create a preview URL
      const previewUrl = URL.createObjectURL(file);
      setHumanImagePreview(previewUrl);
      setHumanImage(file);
    } else {
      // Clear the preview and file
      if (humanImagePreview) {
        URL.revokeObjectURL(humanImagePreview);
      }
      setHumanImagePreview(null);
      setHumanImage(null);
    }
  }, [humanImagePreview]);

  // Process cloth file (clothing image)
  const processClothFile = useCallback((file: File | null) => {
    if (file) {
      // Create a preview URL
      const previewUrl = URL.createObjectURL(file);
      setClothImagePreview(previewUrl);
      setClothImage(file);
    } else {
      // Clear the preview and file
      if (clothImagePreview) {
        URL.revokeObjectURL(clothImagePreview);
      }
      setClothImagePreview(null);
      setClothImage(null);
    }
  }, [clothImagePreview]);

  // Process bottom cloth file (for TOP_BOTTOM mode)
  const processBottomClothFile = useCallback((file: File | null) => {
    if (file) {
      // Create a preview URL
      const previewUrl = URL.createObjectURL(file);
      setBottomClothImagePreview(previewUrl);
      setBottomClothImage(file);
    } else {
      // Clear the preview and file
      if (bottomClothImagePreview) {
        URL.revokeObjectURL(bottomClothImagePreview);
      }
      setBottomClothImagePreview(null);
      setBottomClothImage(null);
    }
  }, [bottomClothImagePreview]);

  // Handle model selection
  const handleModelSelect = useCallback((modelId: string) => {
    setSelectedModelId(modelId === selectedModelId ? null : modelId);
    // Clear human image if a model is selected
    if (modelId !== selectedModelId && humanImage) {
      processHumanFile(null);
    }
  }, [selectedModelId, humanImage, processHumanFile]);

  // Handle mode selection
  const handleModeSelect = useCallback((newMode: TryOnMode) => {
    setMode(newMode);
    // Clear bottom cloth image if switching to SINGLE mode
    if (newMode === "SINGLE" && bottomClothImage) {
      processBottomClothFile(null);
    }
  }, [bottomClothImage, processBottomClothFile]);

  // Reset all state
  const handleReset = useCallback(() => {
    // Clear all images
    processHumanFile(null);
    processClothFile(null);
    processBottomClothFile(null);
    
    // Clear model selection
    setSelectedModelId(null);
    
    // Reset mode
    setMode("SINGLE");
    
    // Clear current job
    setCurrentJob(null);
    
    // Reset processing state
    setIsProcessing(false);
  }, [processHumanFile, processClothFile, processBottomClothFile]);

  // Handle download of processed image
  const handleDownload = useCallback(() => {
    if (currentJob?.outputImageUrl) {
      VirtualTryOnService.downloadImage(
        currentJob.outputImageUrl,
        `virtual-tryon-${new Date().getTime()}.jpg`
      );
    }
  }, [currentJob]);

  // Validate image file
  const validateImageFile = useCallback((file: File, onSuccess: (file: File) => void) => {
    // Check file type
    if (!file.type.startsWith("image/")) {
      toast.error("Please upload an image file");
      return;
    }
    
    // Check file size (max 10MB)
    if (file.size > 10 * 1024 * 1024) {
      toast.error("Image size should be less than 10MB");
      return;
    }
    
    // Call success callback
    onSuccess(file);
  }, []);

  // Start polling for job status
  const startPollingJobStatus = useCallback((jobId: string) => {
    let intervalId: NodeJS.Timeout;
    
    const checkJobStatus = async () => {
      try {
        const response = await VirtualTryOnService.getJob(jobId);
        if (response.success) {
          const job = response.data;
          setCurrentJob(job);
          
          // If job is completed or failed, stop polling
          if (job.status === "COMPLETED" || job.status === "FAILED") {
            clearInterval(intervalId);
            setIsProcessing(false);
            
            // Refresh credits after job completion
            refreshCredits();
            
            // Show success or error message
            if (job.status === "COMPLETED") {
              toast.success("Virtual try-on completed successfully");
              // Refresh recent try-ons
              fetchRecentTryOns();
            } else {
              toast.error("Virtual try-on failed");
            }
          }
        } else {
          // Stop polling on error
          clearInterval(intervalId);
          setIsProcessing(false);
          toast.error("Failed to get job status");
        }
      } catch (error) {
        // Stop polling on error
        clearInterval(intervalId);
        setIsProcessing(false);
        console.error("Error checking job status:", error);
        toast.error("An error occurred while checking job status");
      }
    };
    
    // Start polling immediately
    checkJobStatus();
    
    // Then poll at regular intervals
    intervalId = setInterval(checkJobStatus, pollingInterval);
    
    // Clean up interval on component unmount
    return () => clearInterval(intervalId);
  }, [pollingInterval, refreshCredits, fetchRecentTryOns]);

  // Process virtual try-on
  const processVirtualTryOn = useCallback(async () => {
    try {
      // Check if we have the required inputs
      if (!canGenerate()) {
        toast.error("Please provide all required inputs");
        return;
      }
      
      setIsProcessing(true);
      
      // Prepare parameters
      const params: any = {
        mode,
        clothImage: clothImage as File,
      };
      
      // Add human image or model ID
      if (humanImage) {
        params.humanImage = humanImage;
      } else if (selectedModelId) {
        params.modelImageId = selectedModelId;
      }
      
      // Add bottom clothing image for TOP_BOTTOM mode
      if (mode === "TOP_BOTTOM" && bottomClothImage) {
        params.bottomClothImage = bottomClothImage;
      }
      
      // Make API request
      const response = await VirtualTryOnService.processTryOn(params);
      
      if (response.success) {
        // Start polling for job status
        startPollingJobStatus(response.data.jobId);
        toast.success("Virtual try-on processing started");
      } else {
        toast.error("Failed to start virtual try-on processing");
        setIsProcessing(false);
      }
    } catch (error) {
      console.error("Error starting virtual try-on:", error);
      toast.error("An error occurred while processing your request");
      setIsProcessing(false);
    }
  }, [
    bottomClothImage,
    clothImage,
    humanImage,
    mode,
    selectedModelId,
    startPollingJobStatus,
    canGenerate
  ]);

  // Handle generate button click
  const handleGenerate = useCallback(async () => {
    await processVirtualTryOn();
  }, [processVirtualTryOn]);

  // Load a recent try-on
  const loadRecentTryOn = useCallback((job: VirtualTryOnJob): void => {
    setCurrentJob(job);
    setIsProcessing(false);
  }, []);

  // Upload a new model image
  const uploadModelImage = useCallback(
    async (file: File): Promise<void> => {
      try {
        setIsLoadingUserModels(true);
        const response = await VirtualTryOnService.uploadModelImage(file);
        if (response.success) {
          toast.success("Model image uploaded successfully");
          fetchModels();
        } else {
          toast.error(response.message || "Failed to upload model image");
        }
      } catch (error) {
        console.error("Error uploading model image:", error);
        toast.error("An error occurred while uploading model image");
      } finally {
        setIsLoadingUserModels(false);
      }
    },
    [fetchModels]
  );

  // Delete a model image
  const deleteModelImage = useCallback(
    async (modelId: string): Promise<void> => {
      try {
        setIsLoadingUserModels(true);
        const response = await VirtualTryOnService.deleteModelImage(modelId);
        if (response.success) {
          toast.success("Model deleted successfully");
          // If the deleted model was selected, clear the selection
          if (selectedModelId === modelId) {
            setSelectedModelId(null);
          }
          // Refresh the models list
          fetchModels();
        } else {
          toast.error(response.message || "Failed to delete model");
        }
      } catch (error) {
        console.error("Error deleting model:", error);
        toast.error("An error occurred while deleting the model");
      } finally {
        setIsLoadingUserModels(false);
      }
    },
    [fetchModels, selectedModelId]
  );

  // Check if we can generate (have required inputs and credits)
  const canGenerate = useCallback((): boolean => {
    // Must have either a human image or a selected model
    const hasHumanSource = humanImage !== null || selectedModelId !== null;
    // Must have a clothing image
    const hasClothImage = clothImage !== null;
    // If in TOP_BOTTOM mode, must have a bottom clothing image
    const hasBottomClothImage = mode === "TOP_BOTTOM" ? bottomClothImage !== null : true;

    return hasHumanSource && hasClothImage && hasBottomClothImage && !isProcessing;
  }, [humanImage, selectedModelId, clothImage, bottomClothImage, mode, isProcessing]);

  return {
    // State
    mode,
    setMode,
    humanImage,
    clothImage,
    bottomClothImage,
    humanImagePreview,
    clothImagePreview,
    bottomClothImagePreview,
    isProcessing,
    currentJob,
    recentTryOns,
    isLoadingRecent,
    adminModels,
    userModels,
    isLoadingAdminModels,
    isLoadingUserModels,
    selectedModelId,
    setSelectedModelId,

    // Refs
    humanFileInputRef,
    clothFileInputRef,
    bottomClothFileInputRef,

    // Actions
    processHumanFile,
    processClothFile,
    processBottomClothFile,
    processVirtualTryOn,
    fetchRecentTryOns,
    loadRecentTryOn,
    uploadModelImage,
    deleteModelImage,
    fetchModels,
    fetchAdminModels,
    fetchUserModels,

    // Handlers
    handleModelSelect,
    handleModeSelect,
    handleReset,
    handleDownload,
    handleGenerate,
    validateImageFile,
    canGenerate,
  };
}

  // State for file uploads
  const [humanImage, setHumanImage] = useState<File | null>(null);
  const [humanImagePreview, setHumanImagePreview] = useState<string | null>(
    null
  );
  const [clothImage, setClothImage] = useState<File | null>(null);
  const [clothImagePreview, setClothImagePreview] = useState<string | null>(
    null
  );
  const [bottomClothImage, setBottomClothImage] = useState<File | null>(null);
  const [bottomClothImagePreview, setBottomClothImagePreview] = useState<
    string | null
  >(null);

  // State for model selection
  const [selectedModelId, setSelectedModelId] = useState<string | null>(null);
  const [saveAsModel, setSaveAsModel] = useState<boolean>(false);
  const [modelName, setModelName] = useState<string>("");
  const [modelGender, setModelGender] = useState<"MALE" | "FEMALE" | "UNISEX">(
    "UNISEX"
  );

  // State for try-on mode
  const [mode, setMode] = useState<"SINGLE" | "TOP_BOTTOM">("SINGLE");

  // State for processing
  const [isProcessing, setIsProcessing] = useState<boolean>(false);
  const [currentJob, setCurrentJob] = useState<VirtualTryOnJob | null>(null);

  // State for data fetching
  const [models, setModels] = useState<ModelImage[]>([]);
  const [adminModels, setAdminModels] = useState<ModelImage[]>([]);
  const [userModels, setUserModels] = useState<ModelImage[]>([]);
  const [recentTryOns, setRecentTryOns] = useState<RecentTryOn[]>([]);
  const [clothingItems, setClothingItems] = useState<ClothingItem[]>([]);
  const [isLoadingModels, setIsLoadingModels] = useState<boolean>(false);
  const [isLoadingAdminModels, setIsLoadingAdminModels] =
    useState<boolean>(false);
  const [isLoadingUserModels, setIsLoadingUserModels] =
    useState<boolean>(false);
  const [isLoadingRecent, setIsLoadingRecent] = useState<boolean>(false);

  // Refs for polling and file inputs
  const statusCheckIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const humanFileInputRef = useRef<HTMLInputElement>(null);
  const clothFileInputRef = useRef<HTMLInputElement>(null);
  const bottomClothFileInputRef = useRef<HTMLInputElement>(null);

  // Fetch model images
  const fetchModels = useCallback(async () => {
    setIsLoadingModels(true);
    try {
      const response = await VirtualTryOnService.getModelImages();
      if (response.success) {
        setModels(response.data);
      }
    } catch (error) {
      console.error("Error fetching models:", error);
      toast.error("Failed to load model images");
    } finally {
      setIsLoadingModels(false);
    }
  }, []);

  // Fetch admin model images (our models)
  const fetchAdminModels = useCallback(async () => {
    setIsLoadingAdminModels(true);
    try {
      const response = await VirtualTryOnService.getAdminModelImages();
      if (response.success) {
        setAdminModels(response.data);
      }
    } catch (error) {
      console.error("Error fetching admin models:", error);
      toast.error("Failed to load admin model images");
    } finally {
      setIsLoadingAdminModels(false);
    }
  }, []);

  // Fetch user model images (your models)
  const fetchUserModels = useCallback(async () => {
    setIsLoadingUserModels(true);
    try {
      const response = await VirtualTryOnService.getUserModelImages();
      if (response.success) {
        setUserModels(response.data);
      }
    } catch (error) {
      console.error("Error fetching user models:", error);
      toast.error("Failed to load your model images");
    } finally {
      setIsLoadingUserModels(false);
    }
  }, []);

  // Fetch recent try-ons
  const fetchRecentTryOns = useCallback(async () => {
    setIsLoadingRecent(true);
    try {
      const response = await VirtualTryOnService.getRecentTryOns();
      if (response.success) {
        setRecentTryOns(response.data);
        
        // Extract clothing items from recent try-ons
        const extractedItems: ClothingItem[] = [];
        
        response.data.forEach((tryOn) => {
          // Add top/single clothing item
          if (tryOn.clothImagePath) {
            extractedItems.push({
              id: `${tryOn.id}-top`,
              userId: "",
              imagePath: tryOn.clothImagePath,
              clothingType: tryOn.mode === "SINGLE" ? "FULL_OUTFIT" : "TOP",
              createdAt: tryOn.createdAt,
              updatedAt: tryOn.accessedAt || tryOn.createdAt
            });
          }
          
          // Add bottom clothing item if it exists
          if (tryOn.bottomClothImagePath) {
            extractedItems.push({
              id: `${tryOn.id}-bottom`,
              userId: "",
              imagePath: tryOn.bottomClothImagePath,
              clothingType: "BOTTOM",
              createdAt: tryOn.createdAt,
              updatedAt: tryOn.accessedAt || tryOn.createdAt
            });
          }
        });
        
        // Update clothing items state
        setClothingItems(extractedItems);
      }
    } catch (error) {
      console.error("Error fetching recent try-ons:", error);
      toast.error("Failed to load recent try-ons");
    } finally {
      setIsLoadingRecent(false);
    }
  }, []);

  // Load initial data
  useEffect(() => {
    if (autoLoadModels) {
      fetchModels();
      fetchAdminModels();
      fetchUserModels();
    }

    if (autoLoadRecent) {
      fetchRecentTryOns();
    }

    // Clean up interval on unmount
    return () => {
      if (statusCheckIntervalRef.current) {
        clearInterval(statusCheckIntervalRef.current);
      }
    };
  }, [
    autoLoadModels,
    autoLoadRecent,
    fetchModels,
    fetchAdminModels,
    fetchUserModels,
    fetchRecentTryOns,
  ]);

  // Validate image file
  const validateImageFile = useCallback(
    (file: File, onSuccess: (file: File) => void): void => {
      const maxSize = 10 * 1024 * 1024; // 10MB
      if (file.size > maxSize) {
        toast.error("File size exceeds 10MB limit.");
        return;
      }
      if (
        !["image/png", "image/jpeg", "image/jpg", "image/webp"].includes(
          file.type
        )
      ) {
        toast.error("Invalid file format. Use PNG, JPG, JPEG, or WEBP.");
        return;
      }
      onSuccess(file);
    },
    []
  );

  // Process human image file
  const processHumanFile = useCallback(
    (file: File | null): void => {
      if (!file) {
        // Reset human image state
        setHumanImage(null);
        setHumanImagePreview(null);
        return;
      }
      
      validateImageFile(file, (validFile) => {
        setHumanImage(validFile);
        const reader = new FileReader();
        reader.onload = (e: ProgressEvent<FileReader>): void => {
          if (e.target?.result) {
            setHumanImagePreview(e.target.result as string);
            // Reset model selection when uploading a new human image
            setSelectedModelId(null);
          }
        };
        reader.readAsDataURL(validFile);
      });
    },
    [validateImageFile]
  );

  // Process clothing image file
  const processClothFile = useCallback(
    (file: File | null): void => {
      if (!file) {
        // Reset clothing image state
        setClothImage(null);
        setClothImagePreview(null);
        return;
      }
      
      validateImageFile(file, (validFile) => {
        setClothImage(validFile);
        const reader = new FileReader();
        reader.onload = (e: ProgressEvent<FileReader>): void => {
          if (e.target?.result) {
            setClothImagePreview(e.target.result as string);
          }
        };
        reader.readAsDataURL(validFile);
      });
    },
    [validateImageFile]
  );

  // Process bottom clothing image file
  const processBottomClothFile = useCallback(
    (file: File | null): void => {
      if (!file) {
        // Reset bottom clothing image state
        setBottomClothImage(null);
        setBottomClothImagePreview(null);
        return;
      }
      
      validateImageFile(file, (validFile) => {
        setBottomClothImage(validFile);
        const reader = new FileReader();
        reader.onload = (e: ProgressEvent<FileReader>): void => {
          if (e.target?.result) {
            setBottomClothImagePreview(e.target.result as string);
          }
        };
        reader.readAsDataURL(validFile);
      });
    },
    [validateImageFile]
  );

  // Handle model selection
  const handleModelSelect = useCallback(
    (modelId: string): void => {
      setSelectedModelId(modelId === selectedModelId ? null : modelId);
      // Clear human image when selecting a model
      if (modelId !== selectedModelId) {
        setHumanImage(null);
        setHumanImagePreview(null);
        if (humanFileInputRef.current) humanFileInputRef.current.value = "";
      }
    },
    [selectedModelId]
  );

  // Handle mode selection
  const handleModeSelect = useCallback(
    (newMode: "SINGLE" | "TOP_BOTTOM"): void => {
      // If we're switching from TOP_BOTTOM to SINGLE and we have a bottom cloth image
      // but no top cloth image, move the bottom cloth to the top position
      if (newMode === "SINGLE" && mode === "TOP_BOTTOM" && !clothImage && bottomClothImage) {
        // Move bottom cloth to top position
        setClothImage(bottomClothImage);
        
        // If we have a preview image for the bottom cloth, use it for the top cloth
        if (bottomClothImagePreview) {
          setClothImagePreview(bottomClothImagePreview);
        }
        
        // Clear bottom cloth data
        setBottomClothImage(null);
        setBottomClothImagePreview(null);
        
        // Reset file input references
        if (bottomClothFileInputRef.current) {
          bottomClothFileInputRef.current.value = "";
        }
      } else if (newMode === "SINGLE") {
        // Just reset bottom cloth when switching to single mode
        setBottomClothImage(null);
        setBottomClothImagePreview(null);
        if (bottomClothFileInputRef.current) {
          bottomClothFileInputRef.current.value = "";
        }
      }
      
      setMode(newMode);
    },
    [mode, clothImage, bottomClothImage, bottomClothImagePreview]
  );

  // Reset all state
  const handleReset = useCallback((): void => {
    setHumanImage(null);
    setHumanImagePreview(null);
    setClothImage(null);
    setClothImagePreview(null);
    setBottomClothImage(null);
    setBottomClothImagePreview(null);
    setSelectedModelId(null);
    setCurrentJob(null);
    setIsProcessing(false);
  }, []);

  // Handle download of result image
  const handleDownload = useCallback((): void => {
    if (currentJob?.resultImagePath) {
      VirtualTryOnService.downloadImage(
        currentJob.resultImagePath,
        `virtual-tryon-${currentJob.id}.jpg`
      );
    }
  }, [currentJob]);

  // Poll for job status
  const startPollingJobStatus = useCallback(
    (jobId: string): void => {
      // Clear any existing interval
      if (statusCheckIntervalRef.current) {
        clearInterval(statusCheckIntervalRef.current);
      }

      // Set up new interval
      statusCheckIntervalRef.current = setInterval(async () => {
        try {
          const response = await VirtualTryOnService.getJob(jobId);
          if (response.success) {
            const job = response.data;
            setCurrentJob(job);

            // If job is completed or failed, stop polling
            if (job.status === "COMPLETED" || job.status === "FAILED") {
              if (statusCheckIntervalRef.current) {
                clearInterval(statusCheckIntervalRef.current);
                statusCheckIntervalRef.current = null;
              }
              setIsProcessing(false);

              // Refresh credit balance on completion
              refreshCredits();

              // Refresh recent try-ons
              fetchRecentTryOns();

              if (job.status === "COMPLETED") {
                toast.success("Virtual try-on completed successfully");
              } else {
                toast.error(
                  `Virtual try-on failed: ${
                    job.errorMessage || "Unknown error"
                  }`
                );
              }
            }
          }
        } catch (error) {
          console.error("Error checking job status:", error);
          // Stop polling on error
          if (statusCheckIntervalRef.current) {
            clearInterval(statusCheckIntervalRef.current);
            statusCheckIntervalRef.current = null;
          }
          setIsProcessing(false);
          toast.error("Failed to check processing status");
        }
      }, pollingInterval);
    },
    [fetchRecentTryOns, pollingInterval, refreshCredits]
  );

  // Start processing the virtual try-on
  const handleGenerate = useCallback(async (): Promise<void> => {
    // Validate inputs
    if (!humanImage && !selectedModelId) {
      toast.error("Please upload a human image or select a model");
      return;
    }

    if (!clothImage) {
      toast.error("Please upload a clothing image");
      return;
    }

    if (mode === "TOP_BOTTOM" && !bottomClothImage) {
      toast.error(
        "Please upload a bottom clothing image for Top & Bottom mode"
      );
      return;
    }

    setIsProcessing(true);

    try {
      const params: Parameters<typeof VirtualTryOnService.processTryOn>[0] = {
        mode,
        clothImage: clothImage,
      };

      // Add human image or model ID
      if (humanImage) {
        params.humanImage = humanImage;
        params.saveAsModel = saveAsModel;
        if (saveAsModel) {
          params.modelName =
            modelName || `Model ${new Date().toLocaleDateString()}`;
          params.gender = modelGender;
        }
      } else if (selectedModelId) {
        params.modelImageId = selectedModelId;
      }

      // Add bottom clothing image for TOP_BOTTOM mode
      if (mode === "TOP_BOTTOM" && bottomClothImage) {
        params.bottomClothImage = bottomClothImage;
      }

      // Make API request
      const response = await VirtualTryOnService.processTryOn(params);

      if (response.success) {
        // Start polling for job status
        startPollingJobStatus(response.data.jobId);
        toast.success("Virtual try-on processing started");
      } else {
        toast.error("Failed to start virtual try-on processing");
        setIsProcessing(false);
      }
    } catch (error) {
      console.error("Error starting virtual try-on:", error);
      toast.error("An error occurred while processing your request");
      setIsProcessing(false);
    }
  }, [
    bottomClothImage,
    clothImage,
    humanImage,
    mode,
    modelGender,
    modelName,
    saveAsModel,
    selectedModelId,
    startPollingJobStatus,
  ]);

  // Load a recent try-on
  const loadRecentTryOn = useCallback((job: VirtualTryOnJob): void => {
    setCurrentJob(job);
    setIsProcessing(false);
  }, []);

  // Upload a new model image
  const uploadModelImage = useCallback(
    async (file: File): Promise<void> => {
      try {
        setIsLoadingUserModels(true);
        const response = await VirtualTryOnService.uploadModelImage(file);
        if (response.success) {
          toast.success("Model image uploaded successfully");
          fetchModels();
        } else {
          toast.error(response.message || "Failed to upload model image");
        }
      } catch (error) {
        console.error("Error uploading model image:", error);
        toast.error("An error occurred while uploading model image");
      } finally {
        setIsLoadingUserModels(false);
      }
    },
    [fetchModels]
  );

  // Delete a model image
  const deleteModelImage = useCallback(
    async (modelId: string): Promise<void> => {
      try {
        setIsLoadingUserModels(true);
        const response = await VirtualTryOnService.deleteModelImage(modelId);
        if (response.success) {
          toast.success("Model deleted successfully");
          // If the deleted model was selected, clear the selection
          if (selectedModelId === modelId) {
            setSelectedModelId(null);
          }
          // Refresh the models list
          fetchModels();
        } else {
          toast.error(response.message || "Failed to delete model");
        }
      } catch (error) {
        console.error("Error deleting model:", error);
        toast.error("An error occurred while deleting the model");
      } finally {
        setIsLoadingUserModels(false);
      }
    },
    [fetchModels, selectedModelId]
  );

  // Initialize clothing items if empty
  // useEffect(() => {
  //   if (clothingItems.length === 0) {
  //     // This would be replaced with an API call in production
  //     setClothingItems([
  //       {
  //         id: "1",
  //         userId: "",
  //         imagePath: "/placeholder/cloth1.jpg",
  //         clothingType: "TOP",
  //         createdAt: new Date().toISOString(),
  //         updatedAt: new Date().toISOString()
  //       },
  //       {
  //         id: "2",
  //         userId: "",
  //         imagePath: "/placeholder/cloth2.jpg",
  //         clothingType: "BOTTOM",
  //         createdAt: new Date().toISOString(),
  //         updatedAt: new Date().toISOString()
  //       },
  //       {
  //         id: "3",
  //         userId: "",
  //         imagePath: "/placeholder/cloth3.jpg",
  //         clothingType: "DRESS",
  //         createdAt: new Date().toISOString(),
  //         updatedAt: new Date().toISOString()
  //       },
  //       {
  //         id: "4",
  //         userId: "",
  //         imagePath: "/placeholder/cloth4.jpg",
  //         clothingType: "FULL_OUTFIT",
  //         createdAt: new Date().toISOString(),
  //         updatedAt: new Date().toISOString()
  };

  // Add human image or model ID
  if (humanImage) {
    params.humanImage = humanImage;
    params.saveAsModel = saveAsModel;
    if (saveAsModel) {
      params.modelName =
        modelName || `Model ${new Date().toLocaleDateString()}`;
      params.gender = modelGender;
    }
  } else if (selectedModelId) {
    params.modelImageId = selectedModelId;
  }

  // Add bottom clothing image for TOP_BOTTOM mode
  if (mode === "TOP_BOTTOM" && bottomClothImage) {
    params.bottomClothImage = bottomClothImage;
  }

  // Make API request
  const response = await VirtualTryOnService.processTryOn(params);

  if (response.success) {
    // Start polling for job status
    startPollingJobStatus(response.data.jobId);
    toast.success("Virtual try-on processing started");
  } else {
    toast.error("Failed to start virtual try-on processing");
    setIsProcessing(false);
  }
} catch (error) {
  console.error("Error starting virtual try-on:", error);
  toast.error("An error occurred while processing your request");
  setIsProcessing(false);
}
}, [
  bottomClothImage,
  clothImage,
  humanImage,
  mode,
  modelGender,
  modelName,
  saveAsModel,
  selectedModelId,
  startPollingJobStatus,
]);

// Load a recent try-on
const loadRecentTryOn = useCallback((job: VirtualTryOnJob): void => {
  setCurrentJob(job);
  setIsProcessing(false);
}, []);

// Upload a new model image
const uploadModelImage = useCallback(
  async (file: File): Promise<void> => {
    try {
      setIsLoadingUserModels(true);
      const response = await VirtualTryOnService.uploadModelImage(file);
      if (response.success) {
        toast.success("Model image uploaded successfully");
        fetchModels();
      } else {
        toast.error(response.message || "Failed to upload model image");
      }
    } catch (error) {
      console.error("Error uploading model image:", error);
      toast.error("An error occurred while uploading model image");
    } finally {
      setIsLoadingUserModels(false);
    }
  },
  [fetchModels]
);

// Delete a model image
const deleteModelImage = useCallback(
  async (modelId: string): Promise<void> => {
    try {
      setIsLoadingUserModels(true);
      const response = await VirtualTryOnService.deleteModelImage(modelId);
      if (response.success) {
        toast.success("Model deleted successfully");
        // If the deleted model was selected, clear the selection
        if (selectedModelId === modelId) {
          setSelectedModelId(null);
        }
        // Refresh the models list
        fetchModels();
      } else {
        toast.error(response.message || "Failed to delete model");
      }
    } catch (error) {
      console.error("Error deleting model:", error);
      toast.error("An error occurred while deleting the model");
    } finally {
      setIsLoadingUserModels(false);
    }
  },
  [fetchModels, selectedModelId]
);

// Check if we can generate (have required inputs and credits)
const canGenerate = useCallback((): boolean => {
  // Must have either a human image or a selected model
  const hasHumanSource = humanImage !== null || selectedModelId !== null;
  // Must have a clothing image
  const hasClothImage = clothImage !== null;
  // If in TOP_BOTTOM mode, must have a bottom clothing image
  const hasBottomClothImage = mode === "TOP_BOTTOM" ? bottomClothImage !== null : true;
  // Must have sufficient credits
  const hasSufficientCredits = true; // This would be replaced with actual credit check

  return hasHumanSource && hasClothImage && hasBottomClothImage && hasSufficientCredits && !isProcessing;
}, [humanImage, selectedModelId, clothImage, bottomClothImage, mode, isProcessing]);

// File input refs
const humanFileInputRef = useRef<HTMLInputElement>(null);
const clothFileInputRef = useRef<HTMLInputElement>(null);
const bottomClothFileInputRef = useRef<HTMLInputElement>(null);

return {
  // State
  mode,
  setMode,
  humanImage,
  clothImage,
  bottomClothImage,
  humanImagePreview,
  clothImagePreview,
  bottomClothImagePreview,
  isProcessing,
  currentJob,
  recentTryOns,
  isLoadingRecent,
  adminModels,
  userModels,
  isLoadingAdminModels,
  isLoadingUserModels,
  selectedModelId,
  setSelectedModelId,

  // Refs
  humanFileInputRef,
  clothFileInputRef,
  bottomClothFileInputRef,

  // Actions
  processHumanFile,
  processClothFile,
  processBottomClothFile,
  processVirtualTryOn,
  fetchRecentTryOns,
  loadRecentTryOn,
  uploadModelImage,
  deleteModelImage,
  fetchModels,
  fetchAdminModels,
  fetchUserModels,
  canGenerate,
  handleGenerate,
};

export default useVirtualTryOn;
