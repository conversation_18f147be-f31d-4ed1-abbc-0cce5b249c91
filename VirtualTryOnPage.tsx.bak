import React, { useState, type ChangeEvent, type DragEvent } from "react";
import { Check, Loader2, Plus, RefreshCw, Download, Upload, Trash2, X } from "lucide-react";
import ShadowButton from "@/components/ui/shadowButton";
import { useVirtualTryOn } from "@/hooks/useVirtualTryOn";
import useCredits from "@/hooks/useCredits";
import { toast } from "sonner";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog";

const VirtualTryOnPage: React.FC = () => {
  const [isDragOver, setIsDragOver] = useState<boolean>(false);
  const [selectedClothingType, setSelectedClothingType] = useState<
    "single" | "top-bottom"
  >("single");
  const [modelTab, setModelTab] = useState<"our" | "your">("our");
  
  // State for model upload modal
  const [showModelUploadModal, setShowModelUploadModal] = useState<boolean>(false);
  const [uploadedModelFile, setUploadedModelFile] = useState<File | null>(null);
  const [uploadedModelPreview, setUploadedModelPreview] = useState<string | null>(null);
  const [modelUploadName, setModelUploadName] = useState<string>("");
  const [modelUploadGender, setModelUploadGender] = useState<"MALE" | "FEMALE" | "UNISEX">("UNISEX");
  const [isUploadingModel, setIsUploadingModel] = useState<boolean>(false);

  // Use our custom virtual try-on hook
  const {
    // State
    humanImage,
    humanImagePreview,
    clothImage,
    clothImagePreview,
    bottomClothImage,
    bottomClothImagePreview,
    selectedModelId,
    mode,
    isProcessing,
    currentJob,
    models,
    adminModels,
    userModels,
    clothingItems,
    isLoadingAdminModels,
    isLoadingUserModels,
    isLoadingRecent,
    deletingModelIds,

    // Refs
    humanFileInputRef,
    clothFileInputRef,
    bottomClothFileInputRef,

    // Actions
    setMode,

    // Methods
    processHumanFile,
    processClothFile,
    processBottomClothFile,
    handleModelSelect,
    handleModeSelect,
    handleReset,
    handleDownload,
    handleGenerate,
    uploadModelImage,
    deleteModel,
    fetchAdminModels,
    fetchUserModels,
    fetchRecentTryOns,
  } = useVirtualTryOn();

  // Use credits hook
  const { getCost, hasEnoughCredits } = useCredits();

  // Drag and drop handlers
  const handleDragOver = (e: DragEvent<HTMLDivElement>): void => {
    e.preventDefault();
    setIsDragOver(true);
  };

  const handleDragLeave = (e: DragEvent<HTMLDivElement>): void => {
    e.preventDefault();
    setIsDragOver(false);
  };

  const handleDrop = (e: DragEvent<HTMLDivElement>): void => {
    e.preventDefault();
    setIsDragOver(false);
    const files = e.dataTransfer.files;
    if (files.length > 0) {
      processClothFile(files[0]);
    }
  };

  const handleFileSelect = (e: ChangeEvent<HTMLInputElement>): void => {
    const files = e.target.files;
    if (files?.length) {
      processClothFile(files[0]);
    }
  };

  const handleAddClick = (): void => {
    if (clothFileInputRef.current) {
      clothFileInputRef.current.click();
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLDivElement>): void => {
    if (e.key === "Enter" || e.key === " ") {
      e.preventDefault();
      handleAddClick();
    }
  };

  // Check if we can generate (have required inputs and credits)
  const canGenerate = (): boolean => {
    // Must have either a human image or a selected model
    const hasHumanSource = humanImage !== null || selectedModelId !== null;
    // Must have a clothing image
    const hasClothing = clothImage !== null;
    // If in TOP_BOTTOM mode, must have bottom clothing
    const hasRequiredBottomClothing =
      mode === "SINGLE" || bottomClothImage !== null;
    // Must have enough credits
    const hasSufficientCredits = hasEnoughCredits("virtualTryOn");

    return (
      hasHumanSource &&
      hasClothing &&
      hasRequiredBottomClothing &&
      hasSufficientCredits
    );
  };

  // Get credit cost display
  const creditCost = getCost("virtualTryOn");

  // Sync UI state with mode changes
  React.useEffect(() => {
    // Keep the UI clothing type selection in sync with the mode
    setSelectedClothingType(mode === "SINGLE" ? "single" : "top-bottom");
  }, [mode]);

  // Function to handle model file selection
  const handleModelFileSelect = (file: File) => {
    setUploadedModelFile(file);
    
    // Generate a preview
    const reader = new FileReader();
    reader.onload = (e: ProgressEvent<FileReader>) => {
      if (e.target?.result) {
        setUploadedModelPreview(e.target.result as string);
      }
    };
    reader.readAsDataURL(file);
    
    // Set a default model name based on the file name
    const fileName = file.name.split('.')[0];
    setModelUploadName(fileName || `Model ${new Date().toLocaleDateString()}`);
  };
  
  // Function to handle model upload submission
  const handleModelUploadSubmit = async () => {
    if (!uploadedModelFile) {
      toast.error("Please select a model image");
      return;
    }
    
    if (!modelUploadName.trim()) {
      toast.error("Please enter a name for your model");
      return;
    }
    
    try {
      setIsUploadingModel(true);
      
      // Upload the model
      await uploadModelImage(uploadedModelFile, {
        modelName: modelUploadName,
        gender: modelUploadGender
      });
      
      // Close the modal and reset state
      setShowModelUploadModal(false);
      setUploadedModelFile(null);
      setUploadedModelPreview(null);
      setModelUploadName("");
      
      // Switch to the "Your Models" tab to show the newly uploaded model
      setModelTab("your");
      
      toast.success("Model uploaded successfully");
    } catch (error) {
      console.error("Error uploading model:", error);
      toast.error("Failed to upload model");
    } finally {
      setIsUploadingModel(false);
    }
  };
  
  return (
    <div className="min-h-screen flex items-center justify-center relative">
      <img
        src="/png/image_gene_shadow_1.png"
        className="absolute bottom-[-24px] right-[-24px]"
        alt=""
        aria-hidden="true"
      />
      {/* Background Grid Pattern with Fade Effect */}
      <div className="absolute inset-0" aria-hidden="true">
        <div
          className="grid grid-cols-12 h-full opacity-10"
          style={{
            maskImage:
              "radial-gradient(ellipse at top left, transparent 20%, black 50%)",
            WebkitMaskImage:
              "radial-gradient(ellipse at top left, transparent 20%, black 50%)",
          }}
        >
          {Array.from({ length: 144 }).map((_, i: number) => (
            <div key={i} className="border border-white/20"></div>
          ))}
        </div>
      </div>

      {/* Floating Dots */}
      <div
        className="absolute top-20 right-20 w-2 h-2 bg-white/30 rounded-full animate-pulse"
        aria-hidden="true"
      ></div>
      <div
        className="absolute top-40 right-40 w-1 h-1 bg-white/20 rounded-full animate-pulse delay-1000"
        aria-hidden="true"
      ></div>
      <div
        className="absolute bottom-32 left-32 w-1.5 h-1.5 bg-white/25 rounded-full animate-pulse delay-500"
        aria-hidden="true"
      ></div>
      <div
        className="absolute bottom-20 right-60 w-1 h-1 bg-white/20 rounded-full animate-pulse delay-700"
        aria-hidden="true"
      ></div>

      <div className="relative z-10 w-full max-w-7xl px-6">
        {/* Header */}
        <div className="text-left mb-8">
          <h1 className="text-4xl font-inter font-semibold text-white mb-4">
            Virtual Try-On
          </h1>
          <div className="flex items-center">
            <span className="text-sm font-medium mr-2 text-gray-300">
              Cost:
            </span>
            <span className="bg-blue-900/50 text-blue-300 text-xs font-medium px-2.5 py-0.5 rounded">
              {creditCost} credits per try-on
            </span>
          </div>
        </div>

        <div className="flex gap-8 items-center">
          {/* Left Panel - Controls */}
          <div className="w-96">
            <div className="bg-white/10 backdrop-blur-sm border border-gray-600 rounded-2xl p-6">
              {/* Clothing Selection */}
              <div className="mb-6">
                <h3 className="text-white text-lg font-medium mb-4">
                  Select Clothes
                </h3>
                {/* Clothing Type Buttons */}
                <div className="flex space-x-2 mb-4">
                  <button
                    className={`px-4 py-2 rounded-lg ${
                      selectedClothingType === "single"
                        ? "bg-purple-600 text-white"
                        : "bg-gray-700 text-gray-300 hover:bg-gray-600"
                    }`}
                    onClick={() => {
                      setSelectedClothingType("single");
                      handleModeSelect("SINGLE");
                    }}
                  >
                    Single Item
                  </button>
                  <button
                    className={`px-4 py-2 rounded-lg ${
                      selectedClothingType === "top-bottom"
                        ? "bg-purple-600 text-white"
                        : "bg-gray-700 text-gray-300 hover:bg-gray-600"
                    }`}
                    onClick={() => {
                      setSelectedClothingType("top-bottom");
                      handleModeSelect("TOP_BOTTOM");
                    }}
                  >
                    Top & Bottom
                  </button>
                </div>

                {/* Add Item Area */}
                {mode === "SINGLE" && (
                  <div
                    className={`border-2 border-dashed rounded-lg p-8 mt-8 text-center transition-colors cursor-pointer relative ${
                      isDragOver
                        ? "border-purple-400 bg-purple-900/30"
                        : "border-gray-600 hover:border-gray-500"
                    }`}
                    onDragOver={handleDragOver}
                    onDragLeave={handleDragLeave}
                    onDrop={handleDrop}
                    onClick={handleAddClick}
                    onKeyDown={handleKeyDown}
                    role="button"
                    tabIndex={0}
                    aria-label="Add an item"
                  >
                    {clothImagePreview ? (
                      <div className="relative">
                        <img
                          src={clothImagePreview}
                          alt="Clothing preview"
                          className="mx-auto max-h-64 rounded"
                        />
                        <button
                          className="absolute top-2 right-2 bg-white rounded-full p-1 shadow-md hover:bg-gray-100"
                          onClick={(e) => {
                            e.stopPropagation();
                            // Reset only the cloth image
                            processClothFile(null);
                          }}
                        >
                          <RefreshCw className="h-4 w-4" />
                        </button>
                      </div>
                    ) : (
                      <>
                        <div className="flex gap-2 justify-center items-center">
                          <Plus className="w-5 h-5 text-gray-400" />
                          <p className="text-gray-300 font-medium">Add Item</p>
                        </div>
                        <p className="text-gray-500 text-sm mb-3">
                          Or drag & drop here
                        </p>
                      </>
                    )}
                    <input
                      ref={clothFileInputRef}
                      type="file"
                      className="hidden"
                      accept=".png,.jpg,.jpeg,.webp"
                      onChange={handleFileSelect}
                    />
                    {isDragOver && (
                      <div className="absolute inset-0 bg-purple-600/20 border-2 border-purple-400 rounded-lg flex items-center justify-center z-10">
                        <div className="text-center">
                          <Upload
                            size={32}
                            className="text-purple-300 mx-auto mb-2"
                          />
                          <p className="text-purple-200 font-medium">
                            Drop your files here
                          </p>
                        </div>
                      </div>
                    )}
                  </div>
                )}

                {/* Bottom Clothing Item (if TOP_BOTTOM mode) */}
                {mode === "TOP_BOTTOM" && (
                  <div className="flex gap-4">
                    <div
                      className={`border-2 border-dashed rounded-lg p-8 mt-4 text-center transition-colors cursor-pointer relative ${
                        isDragOver
                          ? "border-purple-400 bg-purple-900/30"
                          : "border-gray-600 hover:border-gray-500"
                      }`}
                      onDragOver={handleDragOver}
                      onDragLeave={handleDragLeave}
                      onDrop={handleDrop}
                      onClick={handleAddClick}
                      onKeyDown={handleKeyDown}
                      role="button"
                      tabIndex={0}
                      aria-label="Add an item"
                    >
                      {clothImagePreview ? (
                        <div className="relative">
                          <img
                            src={clothImagePreview}
                            alt="Clothing preview"
                            className="mx-auto max-h-64 rounded"
                          />
                          <button
                            className="absolute top-2 right-2 bg-white rounded-full p-1 shadow-md hover:bg-gray-100"
                            onClick={(e) => {
                              e.stopPropagation();
                              // Reset only the cloth image
                              processClothFile(null);
                            }}
                          >
                            <RefreshCw className="h-4 w-4" />
                          </button>
                        </div>
                      ) : (
                        <>
                          <div className="flex gap-2 justify-center items-center">
                            <Plus className="w-5 h-5 text-gray-400" />
                            <p className="text-gray-300 font-medium">
                              Add Item
                            </p>
                          </div>
                          <p className="text-gray-500 text-sm mb-3">
                            Or drag & drop here
                          </p>
                        </>
                      )}
                      <input
                        ref={clothFileInputRef}
                        type="file"
                        className="hidden"
                        accept=".png,.jpg,.jpeg,.webp"
                        onChange={handleFileSelect}
                      />
                      {isDragOver && (
                        <div className="absolute inset-0 bg-purple-600/20 border-2 border-purple-400 rounded-lg flex items-center justify-center z-10">
                          <div className="text-center">
                            <Upload
                              size={32}
                              className="text-purple-300 mx-auto mb-2"
                            />
                            <p className="text-purple-200 font-medium">
                              Drop your files here
                            </p>
                          </div>
                        </div>
                      )}
                    </div>
                    <div
                      className={`border-2 border-dashed rounded-lg p-8 mt-4 text-center transition-colors cursor-pointer relative border-gray-600 hover:border-gray-500`}
                      onClick={() => {
                        if (bottomClothFileInputRef.current) {
                          bottomClothFileInputRef.current.click();
                        }
                      }}
                    >
                      {bottomClothImagePreview ? (
                        <div className="relative">
                          <img
                            src={bottomClothImagePreview}
                            alt="Bottom clothing preview"
                            className="mx-auto max-h-64 rounded"
                          />
                          <button
                            className="absolute top-2 right-2 bg-white rounded-full p-1 shadow-md hover:bg-gray-100"
                            onClick={(e) => {
                              e.stopPropagation();
                              // Reset only the bottom cloth image
                              processBottomClothFile(null);
                            }}
                          >
                            <RefreshCw className="h-4 w-4" />
                          </button>
                        </div>
                      ) : (
                        <>
                          <div className="flex gap-2 justify-center items-center">
                            <Plus className="w-5 h-5 text-gray-400" />
                            <p className="text-gray-300 font-medium">
                              Add Bottom Item
                            </p>
                          </div>
                          <p className="text-gray-500 text-sm mb-3">
                            Or drag & drop here
                          </p>
                        </>
                      )}
                      <input
                        ref={bottomClothFileInputRef}
                        type="file"
                        className="hidden"
                        accept=".png,.jpg,.jpeg,.webp"
                        onChange={(e) => {
                          const files = e.target.files;
                          if (files?.length) {
                            processBottomClothFile(files[0]);
                          }
                        }}
                      />
                    </div>
                  </div>
                )}
              </div>

              {/* Recent Items Section */}
              <div className="mt-8">
                <div className="flex justify-between items-center mb-2">
                  <h3 className="text-lg font-semibold">Recent Items</h3>
                  <button
                    onClick={() => {
                      // Refresh recent items
                      fetchRecentTryOns();
                    }}
                    className="text-purple-400 hover:text-purple-300 flex items-center gap-1"
                  >
                    <RefreshCw className="h-4 w-4" />
                    <span>Refresh</span>
                  </button>
                </div>

                {isLoadingRecent ? (
                  <div className="flex justify-center py-4">
                    <Loader2 className="h-8 w-8 animate-spin text-gray-400" />
                  </div>
                ) : clothingItems.length > 0 ? (
                  <div className="grid grid-cols-6 gap-2">
                    {clothingItems.map((item) => (
                      <div
                        key={item.id}
                        className="aspect-square bg-gray-700 rounded-lg overflow-hidden cursor-pointer relative group"
                        onClick={() => {
                          // If this is a top or full outfit, set it as the cloth image
                          if (
                            item.clothingType === "TOP" ||
                            item.clothingType === "FULL_OUTFIT"
                          ) {
                            // Use the image URL directly
                            const img = new Image();
                            img.crossOrigin = "anonymous";
                            img.onload = () => {
                              const canvas = document.createElement("canvas");
                              canvas.width = img.width;
                              canvas.height = img.height;
                              const ctx = canvas.getContext("2d");
                              ctx?.drawImage(img, 0, 0);

                              // Convert to blob and create a File object
                              canvas.toBlob((blob) => {
                                if (blob) {
                                  const file = new File(
                                    [blob],
                                    `clothing-${item.id}.png`,
                                    { type: "image/png" }
                                  );
                                  processClothFile(file);
                                  toast.success(
                                    `Selected ${item.clothingType.toLowerCase()} item`
                                  );
                                }
                              }, "image/png");
                            };
                            img.onerror = () => {
                              toast.error("Failed to load image");
                            };
                            img.src = item.imagePath;
                          }
                          // If this is a bottom, set it as the bottom cloth image
                          else if (item.clothingType === "BOTTOM") {
                            // Make sure we're in TOP_BOTTOM mode
                            if (mode !== "TOP_BOTTOM") {
                              setMode("TOP_BOTTOM");
                            }

                            // Use the image URL directly
                            const img = new Image();
                            img.crossOrigin = "anonymous";
                            img.onload = () => {
                              const canvas = document.createElement("canvas");
                              canvas.width = img.width;
                              canvas.height = img.height;
                              const ctx = canvas.getContext("2d");
                              ctx?.drawImage(img, 0, 0);

                              // Convert to blob and create a File object
                              canvas.toBlob((blob) => {
                                if (blob) {
                                  const file = new File(
                                    [blob],
                                    `bottom-${item.id}.png`,
                                    { type: "image/png" }
                                  );
                                  processBottomClothFile(file);
                                  toast.success("Selected bottom item");
                                }
                              }, "image/png");
                            };
                            img.onerror = () => {
                              toast.error("Failed to load image");
                            };
                            img.src = item.imagePath;
                          }
                        }}
                      >
                        <img
                          src={item.imagePath}
                          alt={`${item.clothingType} item`}
                          className="w-full h-full object-cover"
                        />
                        <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all flex items-center justify-center opacity-0 group-hover:opacity-100">
                          <Upload className="h-6 w-6 text-white" />
                        </div>
                        <div className="absolute bottom-0 left-0 right-0 bg-black bg-opacity-50 text-xs text-white p-1 text-center">
                          {item.clothingType === "TOP"
                            ? "Top"
                            : item.clothingType === "BOTTOM"
                            ? "Bottom"
                            : item.clothingType === "FULL_OUTFIT"
                            ? "Outfit"
                            : "Dress"}
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-4 text-gray-400">
                    No recent items found. Try uploading some clothing items
                    first.
                  </div>
                )}
              </div>

              {/* Model Selection */}
              <div className="mt-8">
                <h3 className="text-lg font-semibold mb-2">Select a model</h3>
                <p className="text-gray-400 text-sm mb-4">
                  Select our model or upload your model to try on
                </p>

                {/* Model Tabs */}
                <div className="flex border-b border-gray-700 mb-4">
                  <button
                    onClick={() => {
                      // Fetch admin models if not already loaded
                      if (adminModels.length === 0 && !isLoadingAdminModels) {
                        fetchAdminModels();
                      }
                      setModelTab("our");
                    }}
                    className={`px-4 py-2 transition-colors relative ${
                      modelTab === "our"
                        ? "text-white font-medium"
                        : "text-gray-400 hover:text-gray-300"
                    }`}
                  >
                    Our models
                    {modelTab === "our" && (
                      <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-purple-500"></div>
                    )}
                  </button>
                  <button
                    onClick={() => {
                      // Fetch user models if not already loaded
                      if (userModels.length === 0 && !isLoadingUserModels) {
                        fetchUserModels();
                      }
                      setModelTab("your");
                    }}
                    className={`px-4 py-2 transition-colors relative ${
                      modelTab === "your"
                        ? "text-white font-medium"
                        : "text-gray-400 hover:text-gray-300"
                    }`}
                  >
                    Your models
                    {modelTab === "your" && (
                      <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-purple-500"></div>
                    )}
                  </button>
                </div>

                <div className="grid grid-cols-4 gap-3 mb-4">
                  {/* Upload model button */}
                  <div
                    className="aspect-square bg-gray-100 bg-opacity-5 rounded-lg border border-dashed border-gray-600 flex flex-col items-center justify-center cursor-pointer hover:bg-opacity-10 transition-all"
                    onClick={() => {
                      // Open the model upload modal instead of directly opening the file input
                      setShowModelUploadModal(true);
                    }}
                  >
                    <Plus className="w-6 h-6 text-gray-400 mb-1" />
                    <span className="text-xs text-gray-400">Upload</span>
                  </div>

                  {/* Show models based on selected tab */}
                  {modelTab === "our" ? (
                    isLoadingAdminModels ? (
                      <div className="col-span-3 flex justify-center items-center">
                        <Loader2 className="h-8 w-8 animate-spin text-gray-400" />
                      </div>
                    ) : adminModels.length > 0 ? (
                      adminModels.map((model) => (
                        <div
                          key={model.id}
                          onClick={() => handleModelSelect(model.id)}
                          className={`aspect-square rounded-lg overflow-hidden cursor-pointer relative group ${
                            selectedModelId === model.id
                              ? "ring-2 ring-purple-500"
                              : "hover:ring-2 hover:ring-gray-500"
                          }`}
                        >
                          <img
                            src={model.imagePath}
                            alt={model.modelName || `Model ${model.id}`}
                            className="w-full h-full object-cover object-top"
                          />
                          {selectedModelId === model.id && (
                            <div className="absolute top-2 right-2 bg-purple-500 rounded-full p-1">
                              <Check className="h-3 w-3 text-white" />
                            </div>
                          )}
                        </div>
                      ))
                    ) : (
                      <div className="col-span-3 text-center py-8">
                        <p className="text-gray-400 text-sm">
                          No admin models available
                        </p>
                      </div>
                    )
                  ) : modelTab === "your" ? (
                    isLoadingUserModels ? (
                      <div className="col-span-3 flex justify-center py-8">
                        <Loader2 className="h-6 w-6 animate-spin text-gray-400" />
                      </div>
                    ) : userModels.length === 0 ? (
                      <div className="col-span-3 text-center py-8">
                        <p className="text-gray-400 text-sm">
                          You haven't uploaded any models yet.
                        </p>
                      </div>
                    ) : (
                      userModels.map((model) => (
                        <div
                          key={model.id}
                          className={`aspect-square rounded-lg overflow-hidden relative cursor-pointer group ${selectedModelId === model.id ? "ring-2 ring-purple-500" : ""}`}
                          onClick={() => handleModelSelect(model.id)}
                        >
                          <img
                            src={model.imagePath}
                            alt={model.modelName || `Model ${model.id}`}
                            className="w-full h-full object-cover object-top"
                          />
                          {selectedModelId === model.id && (
                            <div className="absolute top-2 left-2 bg-purple-500 rounded-full p-1">
                              <Check className="h-3 w-3 text-white" />
                            </div>
                          )}
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              deleteModel(model.id);
                            }}
                            className="absolute top-2 right-2 bg-black bg-opacity-50 hover:bg-opacity-70 rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity"
                            disabled={deletingModelIds.includes(model.id)}
                          >
                            {deletingModelIds.includes(model.id) ? (
                              <Loader2 className="h-3 w-3 text-white animate-spin" />
                            ) : (
                              <Trash2 className="h-3 w-3 text-white" />
                            )}
                          </button>
                          <div className="absolute bottom-0 left-0 right-0 bg-black bg-opacity-50 text-xs text-white p-1 text-center opacity-0 group-hover:opacity-100 transition-opacity">
                            {model.modelName || `Model ${model.id.substring(0, 6)}`}
                          </div>
                        </div>
                      ))
                                clipRule="evenodd"
                              />
                            </svg>
                          )}
                        </button>
                      </div>
                    ))
                  ) : (
                    <div className="col-span-3 flex flex-col justify-center items-center text-gray-400 py-6">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="h-10 w-10 text-gray-500 mb-2"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={1.5}
                          d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
                        />
                      </svg>
                      <p>You haven't added any models yet</p>
                      <p className="text-xs mt-1">Upload a photo to get started</p>
                    </div>
                  )}

                  {/* Show uploaded human image if present */}
                  {humanImagePreview && (
                    <div className="aspect-square rounded-lg overflow-hidden relative group">
                      <img
                        src={humanImagePreview}
                        alt="Your uploaded model"
                        className="w-full h-full object-cover object-top"
                      />
                      <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all flex items-center justify-center">
                        <button
                          onClick={() => {
                            processHumanFile(null);
                            if (humanFileInputRef.current) {
                              humanFileInputRef.current.value = "";
                            }
                          }}
                          className="bg-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity"
                        >
                          <RefreshCw className="h-4 w-4" />
                        </button>
                      </div>
                      <div className="absolute bottom-0 left-0 right-0 bg-black bg-opacity-50 text-xs text-white p-1 text-center">
                        Current Upload
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* Generate Button */}
              <ShadowButton
                onClick={handleGenerate}
                className="!w-full !py-3"
                disabled={!canGenerate() || isProcessing}
              >
                {isProcessing ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Processing...
                  </>
                ) : (
                  <>Generate</>
                )}
              </ShadowButton>
            </div>
          </div>

          {/* Right Panel - Instructions or Result */}
          <div className="flex-1">
            {!currentJob?.resultImagePath &&
            !humanImagePreview &&
            !selectedModelId ? (
              // Instructions Panel
              <div className="opacity-75 rounded-2xl p-8">
                <div className="space-y-6">
                  <div className="flex items-start gap-4">
                    <div className="w-8 h-8 bg-gray-700 rounded-full flex items-center justify-center text-white font-medium text-sm">
                      1
                    </div>
                    <div>
                      <h3 className="text-white font-medium mb-2">
                        Select Clothes
                      </h3>
                      <p className="text-gray-400">
                        Choose which clothes you'd like to try on, please follow
                        the guidelines
                      </p>
                    </div>
                  </div>
                  <div className="flex items-start gap-4">
                    <div className="w-8 h-8 bg-gray-700 rounded-full flex items-center justify-center text-white font-medium text-sm">
                      2
                    </div>
                    <div>
                      <h3 className="text-white font-medium mb-2">
                        Pick a Model
                      </h3>
                      <p className="text-gray-400">
                        Choose a model of your own to try on
                      </p>
                    </div>
                  </div>
                  <div className="flex items-start gap-4">
                    <div className="w-8 h-8 bg-gray-700 rounded-full flex items-center justify-center text-white font-medium text-sm">
                      3
                    </div>
                    <div>
                      <h3 className="text-white font-medium mb-2">
                        Try it On!
                      </h3>
                      <p className="text-gray-400">
                        Click "Generate" to see the outfit come to life on the
                        model
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            ) : isProcessing ? (
              // Processing Panel
              <div
                className="bg-gray-800/50 backdrop-blur-sm border border-gray-600 rounded-2xl overflow-hidden flex items-center justify-center"
                style={{ minHeight: "500px" }}
              >
                <div className="text-center">
                  <Loader2 className="h-12 w-12 animate-spin text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-300">Processing your try-on...</p>
                  <p className="text-sm text-gray-500 mt-2">
                    This may take a minute or two
                  </p>
                </div>
              </div>
            ) : currentJob?.resultImagePath ? (
              // Result Panel with Generated Image
              <div className="bg-gray-800/50 backdrop-blur-sm border border-gray-600 rounded-2xl overflow-hidden">
                <div className="relative">
                  <img
                    src={currentJob.resultImagePath}
                    alt="Virtual try-on result"
                    className="w-full h-auto object-contain"
                    style={{
                      background:
                        "url(\"data:image/svg+xml,%3csvg width='20' height='20' xmlns='http://www.w3.org/2000/svg'%3e%3cdefs%3e%3cpattern id='a' width='20' height='20' patternUnits='userSpaceOnUse'%3e%3crect width='10' height='10' fill='%23f3f4f6'/%3e%3crect x='10' y='10' width='10' height='10' fill='%23f3f4f6'/%3e%3c/pattern%3e%3c/defs%3e%3crect width='100%25' height='100%25' fill='url(%23a)'/%3e%3c/svg%3e\")",
                      minHeight: "500px",
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "center",
                    }}
                  />
                  {/* Control Buttons Overlay */}
                  <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 bg-gray-900/90 backdrop-blur-sm rounded-lg p-3 flex items-center gap-3">
                    <button
                      onClick={handleReset}
                      className="flex items-center justify-center w-10 h-10 bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition-colors"
                      title="Reset"
                    >
                      <RefreshCw size={18} />
                    </button>
                    <button
                      onClick={() => {
                        if (currentJob?.resultImagePath) {
                          handleDownload(currentJob.resultImagePath);
                          toast.success("Image downloaded successfully!");
                        }
                      }}
                      className="flex items-center gap-2 px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors"
                    >
                      <Download size={18} />
                      Download
                    </button>
                  </div>
                </div>
              </div>
            ) : (
              // Preview Panel with Human/Model
              <div className="bg-gray-800/50 backdrop-blur-sm border border-gray-600 rounded-2xl overflow-hidden">
                <div className="relative">
                  <img
                    src={
                      humanImagePreview ||
                      models.find((m) => m.id === selectedModelId)?.imagePath ||
                      ""
                    }
                    alt="Model preview"
                    className="w-full h-auto object-contain"
                    style={{
                      background:
                        "url(\"data:image/svg+xml,%3csvg width='20' height='20' xmlns='http://www.w3.org/2000/svg'%3e%3cdefs%3e%3cpattern id='a' width='20' height='20' patternUnits='userSpaceOnUse'%3e%3crect width='10' height='10' fill='%23f3f4f6'/%3e%3crect x='10' y='10' width='10' height='10' fill='%23f3f4f6'/%3e%3c/pattern%3e%3c/defs%3e%3crect width='100%25' height='100%25' fill='url(%23a)'/%3e%3c/svg%3e\")",
                    }}
                  />
                  {/* Control Buttons Overlay */}
                  <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 bg-gray-900/90 backdrop-blur-sm rounded-lg p-3 flex items-center gap-3">
                    <button
                      onClick={handleReset}
                      className="flex items-center justify-center w-10 h-10 bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition-colors"
                      title="Reset"
                    >
                      <RefreshCw size={18} />
                    </button>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
      
      {/* Model Upload Modal */}
      <Dialog open={showModelUploadModal} onOpenChange={setShowModelUploadModal}>
        <DialogContent className="sm:max-w-md bg-gray-900 text-white border-gray-800">
          <DialogHeader>
            <DialogTitle className="text-white">Upload Your Model</DialogTitle>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="flex flex-col gap-2">
              <Label htmlFor="model-name" className="text-white">Model Name</Label>
              <Input 
                id="model-name" 
                value={modelUploadName} 
                onChange={(e) => setModelUploadName(e.target.value)} 
                placeholder="Enter a name for your model" 
                className="bg-gray-800 border-gray-700 text-white"
              />
            </div>
            
            <div className="flex flex-col gap-2">
              <Label htmlFor="model-gender" className="text-white">Gender</Label>
              <Select value={modelUploadGender} onValueChange={(value: "MALE" | "FEMALE" | "UNISEX") => setModelUploadGender(value)}>
                <SelectTrigger className="bg-gray-800 border-gray-700 text-white">
                  <SelectValue placeholder="Select gender" />
                </SelectTrigger>
                <SelectContent className="bg-gray-800 border-gray-700 text-white">
                  <SelectItem value="MALE">Male</SelectItem>
                  <SelectItem value="FEMALE">Female</SelectItem>
                  <SelectItem value="UNISEX">Unisex</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="flex flex-col gap-2">
              <Label className="text-white">Model Image</Label>
              {uploadedModelPreview ? (
                <div className="relative aspect-square rounded-lg overflow-hidden">
                  <img 
                    src={uploadedModelPreview} 
                    alt="Model preview" 
                    className="w-full h-full object-cover object-top"
                  />
                  <button 
                    onClick={() => {
                      setUploadedModelFile(null);
                      setUploadedModelPreview(null);
                    }}
                    className="absolute top-2 right-2 bg-black bg-opacity-50 rounded-full p-1 hover:bg-opacity-70 transition-all"
                  >
                    <X className="h-4 w-4 text-white" />
                  </button>
                </div>
              ) : (
                <div 
                  className="aspect-square bg-gray-800 border border-dashed border-gray-600 rounded-lg flex flex-col items-center justify-center cursor-pointer hover:bg-gray-700 transition-all"
                  onClick={() => {
                    const input = document.createElement('input');
                    input.type = 'file';
                    input.accept = '.png,.jpg,.jpeg,.webp';
                    input.onchange = (e) => {
                      const files = (e.target as HTMLInputElement).files;
                      if (files?.length) {
                        handleModelFileSelect(files[0]);
                      }
                    };
                    input.click();
                  }}
                >
                  <Upload className="h-8 w-8 text-gray-400 mb-2" />
                  <span className="text-sm text-gray-400">Click to upload</span>
                  <span className="text-xs text-gray-500 mt-1">PNG, JPG, WEBP</span>
                </div>
              )}
            </div>
          </div>
          <DialogFooter>
            <Button 
              variant="outline" 
              onClick={() => setShowModelUploadModal(false)}
              className="bg-transparent border-gray-600 text-white hover:bg-gray-800 hover:text-white"
            >
              Cancel
            </Button>
            <Button 
              onClick={handleModelUploadSubmit}
              disabled={isUploadingModel || !uploadedModelFile}
              className="bg-purple-600 text-white hover:bg-purple-700"
            >
              {isUploadingModel ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Uploading...
                </>
              ) : (
                'Upload Model'
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      
      {/* File input for human model upload */}
      <input
        ref={humanFileInputRef}
        type="file"
        className="hidden"
        accept=".png,.jpg,.jpeg,.webp"
        onChange={(e) => {
          const files = e.target.files;
          if (files?.length) {
            // Process the human file as a model
            processHumanFile(files[0]);
          }
        }}
      />
    </div>
  );
};

export default VirtualTryOnPage;
