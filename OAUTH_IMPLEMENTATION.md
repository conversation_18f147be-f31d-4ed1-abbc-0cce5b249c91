# OAuth Authentication Implementation

This document provides a comprehensive guide for the OAuth authentication implementation in the MiragicAI platform.

## Overview

The OAuth implementation supports Google and Apple Sign-In as optional authentication methods alongside the existing email/password system. Users can:

- Sign up using Google or Apple OAuth
- Link OAuth accounts to existing email/password accounts
- Unlink OAuth accounts (with safeguards)
- Switch between authentication methods seamlessly

## Architecture

### Backend Components

1. **Database Schema Updates** (`backend/prisma/schema.prisma`)
   - Added `googleId`, `appleId`, and `authProvider` fields to User model
   - Created `AuthProvider` enum with EMAIL, GOOGLE, APPLE values
   - Made `password` field optional for OAuth-only users

2. **OAuth Configuration** (`backend/src/config/oauth.config.ts`)
   - Passport.js strategies for Google and Apple OAuth
   - User creation and account linking logic
   - Session serialization/deserialization

3. **OAuth Controllers** (`backend/src/controllers/oauth.controller.ts`)
   - OAuth initiation and callback handlers
   - Account linking/unlinking endpoints
   - Error handling and token generation

4. **Updated Auth Routes** (`backend/src/routes/auth.routes.ts`)
   - `/auth/google` - Initiate Google OAuth
   - `/auth/google/callback` - Handle Google OAuth callback
   - `/auth/apple` - Initiate Apple Sign-In
   - `/auth/apple/callback` - Handle Apple Sign-In callback
   - `/auth/link-oauth` - Link OAuth account (authenticated)
   - `/auth/unlink-oauth` - Unlink OAuth account (authenticated)

### Frontend Components

1. **OAuth Utilities** (`frontend/src/utils/oauth.utils.ts`)
   - OAuth URL generation
   - Redirect handling
   - Error message mapping
   - Configuration validation

2. **OAuth Components**
   - `OAuthButtons` - Reusable OAuth login buttons
   - `OAuthCallback` - Handles OAuth redirect processing
   - `OAuthAccountManager` - Account linking/unlinking interface

3. **Updated Auth Modals**
   - LoginModal and SignUpModal now include OAuth buttons
   - Proper error handling for OAuth-specific scenarios

## Setup Instructions

### Backend Setup

1. **Install Dependencies**
   ```bash
   cd backend
   npm install passport passport-google-oauth20 passport-apple express-session @types/passport @types/passport-google-oauth20 @types/express-session
   ```

2. **Environment Variables**
   Add to your `.env` file:
   ```env
   # Session Secret for OAuth
   SESSION_SECRET=your_session_secret_key

   # Google OAuth
   GOOGLE_CLIENT_ID=your_google_client_id
   GOOGLE_CLIENT_SECRET=your_google_client_secret

   # Apple OAuth
   APPLE_CLIENT_ID=your_apple_client_id
   APPLE_TEAM_ID=your_apple_team_id
   APPLE_KEY_ID=your_apple_key_id
   APPLE_PRIVATE_KEY=your_apple_private_key
   ```

3. **Database Migration**
   ```bash
   npx prisma migrate dev --name add-oauth-support
   npx prisma generate
   ```

### Frontend Setup

1. **Install Dependencies**
   ```bash
   cd frontend
   npm install @google-cloud/local-auth google-auth-library
   ```

2. **Environment Variables**
   Create/update `.env` file:
   ```env
   VITE_API_BASE_URL=http://localhost:5000
   VITE_GOOGLE_CLIENT_ID=your_google_client_id
   VITE_APPLE_CLIENT_ID=your_apple_client_id
   ```

### OAuth Provider Setup

#### Google OAuth Setup

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing one
3. Enable Google+ API
4. Create OAuth 2.0 credentials
5. Add authorized redirect URIs:
   - `http://localhost:5000/api/v1/auth/google/callback` (development)
   - `https://yourdomain.com/api/v1/auth/google/callback` (production)

#### Apple Sign-In Setup

1. Go to [Apple Developer Console](https://developer.apple.com/)
2. Create a new App ID with Sign In with Apple capability
3. Create a Services ID for web authentication
4. Generate a private key for Sign In with Apple
5. Configure redirect URIs:
   - `http://localhost:5000/api/v1/auth/apple/callback` (development)
   - `https://yourdomain.com/api/v1/auth/apple/callback` (production)

## Security Features

### CSRF Protection
- Session-based state parameter validation
- Secure cookie configuration for production

### Account Linking Safeguards
- Prevents linking OAuth accounts already associated with other users
- Ensures users maintain at least one authentication method
- Validates OAuth tokens before account operations

### Token Management
- JWT tokens generated for OAuth users same as email/password users
- Refresh token rotation and secure storage
- Proper token expiration handling

## User Flows

### New User Registration via OAuth
1. User clicks Google/Apple button on signup modal
2. Redirected to OAuth provider
3. After authorization, redirected to callback URL
4. Backend creates new user with OAuth ID
5. User redirected to dashboard with active session

### Existing User OAuth Login
1. User clicks OAuth button on login modal
2. System checks if OAuth ID is linked to existing account
3. If linked, user is logged in
4. If not linked but email exists, account linking flow initiated

### Account Linking
1. Authenticated user goes to profile page
2. Clicks "Link Account" for desired OAuth provider
3. Completes OAuth flow
4. OAuth ID added to existing user account

### Account Unlinking
1. User goes to profile page
2. Clicks "Unlink" for connected OAuth provider
3. System validates user has alternative auth method
4. OAuth ID removed from user account

## Error Handling

### Common Error Scenarios
- OAuth provider errors (access denied, server errors)
- Account linking conflicts
- Missing authentication methods
- Invalid tokens or expired sessions

### Error Messages
- User-friendly error messages for all scenarios
- Proper logging for debugging
- Graceful fallbacks to email/password authentication

## Testing

### Manual Testing Checklist
- [ ] Google OAuth registration
- [ ] Apple OAuth registration
- [ ] OAuth login for existing users
- [ ] Account linking functionality
- [ ] Account unlinking with safeguards
- [ ] Error handling for various scenarios
- [ ] Mobile responsiveness
- [ ] Cross-browser compatibility

### Automated Testing
- Unit tests for OAuth controllers
- Integration tests for authentication flows
- End-to-end tests for complete user journeys

## Deployment Considerations

### Production Environment
- Use HTTPS for all OAuth redirects
- Set secure session configuration
- Configure proper CORS settings
- Use production OAuth credentials

### Monitoring
- Log OAuth authentication attempts
- Monitor error rates and types
- Track user adoption of OAuth methods

## Troubleshooting

### Common Issues
1. **OAuth redirect mismatch**: Ensure redirect URIs match exactly
2. **Session issues**: Check session secret and cookie configuration
3. **Token validation errors**: Verify JWT secrets and expiration settings
4. **Account linking failures**: Check for existing OAuth associations

### Debug Mode
Enable debug logging by setting `NODE_ENV=development` and checking console logs for detailed OAuth flow information.

## Future Enhancements

- Support for additional OAuth providers (GitHub, Microsoft, etc.)
- Two-factor authentication integration
- OAuth token refresh automation
- Advanced account merging capabilities
- Social profile data synchronization
