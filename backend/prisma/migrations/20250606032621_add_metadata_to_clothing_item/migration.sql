/*
  Warnings:

  - The values [DRESS] on the enum `ClothingType` will be removed. If these variants are still used in the database, this will fail.

*/
-- AlterEnum
BEGIN;
CREATE TYPE "ClothingType_new" AS ENUM ('TOP', 'BOTTOM', 'FULL_SET');
ALTER TABLE "ClothingItem" ALTER COLUMN "clothingType" TYPE "ClothingType_new" USING ("clothingType"::text::"ClothingType_new");
ALTER TYPE "ClothingType" RENAME TO "ClothingType_old";
ALTER TYPE "ClothingType_new" RENAME TO "ClothingType";
DROP TYPE "ClothingType_old";
COMMIT;

-- AlterTable
ALTER TABLE "ClothingItem" ADD COLUMN     "metadata" JSONB DEFAULT '{}';
