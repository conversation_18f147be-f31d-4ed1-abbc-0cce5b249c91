import { PrismaClient } from "@prisma/client";

const prisma = new PrismaClient();

async function main() {
  console.log("Seeding credit packages...");

  // Define credit packages
  const creditPackages = [
    {
      name: "Starter Pack",
      description: "Perfect for beginners - try out our AI services",
      creditsAmount: 50,
      price: 10,
      currency: "USD",
      isActive: true,
    },
    {
      name: "Pro Pack",
      description: "Ideal for regular users - save 10% on credits",
      creditsAmount: 250,
      price: 45,
      currency: "USD",
      isActive: true,
    },
    {
      name: "Business Pack",
      description: "Best value for power users - save 20% on credits",
      creditsAmount: 1000,
      price: 160,
      currency: "USD",
      isActive: true,
    },
    {
      name: "Enterprise Pack",
      description: "For high-volume needs - save 25% on credits",
      creditsAmount: 5000,
      price: 750,
      currency: "USD",
      isActive: true,
    },
  ];

  // Define service costs
  const serviceCosts = {
    imageGeneration: 2, // 1 image = 2 credits
    videoGeneration: 50, // 1 minute of video = 50 credits
    backgroundRemoval: 0, // 1 image background removal = 1 credit
    virtualTryOn: 1, // 1 virtual try-on = 3 credits
    speedpaint: 5, // 1 speedpaint = 5 credits
  };

  // Create credit packages
  for (const packageData of creditPackages) {
    const existingPackage = await prisma.creditPackage.findFirst({
      where: { name: packageData.name },
    });

    if (!existingPackage) {
      await prisma.creditPackage.create({
        data: packageData,
      });
      console.log(`Created credit package: ${packageData.name}`);
    } else {
      console.log(`Credit package already exists: ${packageData.name}`);
    }
  }

  console.log("Credit packages seeded successfully!");
  console.log("Service costs (in credits):");
  console.log(JSON.stringify(serviceCosts, null, 2));
}

main()
  .catch((e) => {
    console.error("Error seeding credit packages:", e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
