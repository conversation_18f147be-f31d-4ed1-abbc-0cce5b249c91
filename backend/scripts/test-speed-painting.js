/**
 * Test script for Speed Painting feature
 * This script tests the basic functionality of the speed painting feature
 */

const axios = require('axios');
const fs = require('fs');
const path = require('path');
const FormData = require('form-data');

// Configuration
const API_URL = process.env.API_URL || 'http://localhost:3000/api/v1';
const TEST_IMAGE_PATH = path.join(__dirname, './test-image.jpg');
const AUTH_TOKEN = process.env.AUTH_TOKEN; // JWT token for authentication

// Check if test image exists
if (!fs.existsSync(TEST_IMAGE_PATH)) {
  console.error(`Test image not found at ${TEST_IMAGE_PATH}`);
  console.log('Please provide a test image or update the TEST_IMAGE_PATH variable');
  process.exit(1);
}

// Check if auth token is provided
if (!AUTH_TOKEN) {
  console.error('AUTH_TOKEN environment variable is required');
  console.log('Please set the AUTH_TOKEN environment variable with a valid JWT token');
  process.exit(1);
}

// Test functions
const testSpeedPainting = async () => {
  console.log('Testing Speed Painting feature...');
  
  try {
    // Step 1: Create a speed painting job
    console.log('\n1. Creating speed painting job...');
    const formData = new FormData();
    formData.append('image', fs.createReadStream(TEST_IMAGE_PATH));
    
    const createResponse = await axios.post(`${API_URL}/speed-painting/create`, formData, {
      headers: {
        ...formData.getHeaders(),
        'Authorization': `Bearer ${AUTH_TOKEN}`
      }
    });
    
    console.log('Job created successfully!');
    console.log('Job ID:', createResponse.data.data.jobId);
    console.log('Status:', createResponse.data.data.status);
    
    const jobId = createResponse.data.data.jobId;
    
    // Step 2: Get job status
    console.log('\n2. Checking job status...');
    const jobResponse = await axios.get(`${API_URL}/speed-painting/jobs/${jobId}`, {
      headers: {
        'Authorization': `Bearer ${AUTH_TOKEN}`
      }
    });
    
    console.log('Job status:', jobResponse.data.data.status);
    
    // Step 3: Get queue status
    console.log('\n3. Checking queue status...');
    const queueResponse = await axios.get(`${API_URL}/speed-painting/queue-status`, {
      headers: {
        'Authorization': `Bearer ${AUTH_TOKEN}`
      }
    });
    
    console.log('Queue length:', queueResponse.data.data.queueLength);
    console.log('Pending jobs:', queueResponse.data.data.pendingJobs);
    console.log('Processing jobs:', queueResponse.data.data.processingJobs);
    console.log('Estimated wait time:', queueResponse.data.data.estimatedWaitTimeMinutes, 'minutes');
    
    // Step 4: Get user jobs
    console.log('\n4. Getting user jobs...');
    const userJobsResponse = await axios.get(`${API_URL}/speed-painting/jobs`, {
      headers: {
        'Authorization': `Bearer ${AUTH_TOKEN}`
      }
    });
    
    console.log('Total user jobs:', userJobsResponse.data.data.jobs.length);
    
    console.log('\nTest completed successfully!');
    return true;
  } catch (error) {
    console.error('Test failed:', error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', error.response.data);
    }
    return false;
  }
};

// Run the test
testSpeedPainting()
  .then(success => {
    if (success) {
      console.log('\nSpeed Painting feature is working correctly!');
      process.exit(0);
    } else {
      console.error('\nSpeed Painting feature test failed!');
      process.exit(1);
    }
  })
  .catch(error => {
    console.error('Unexpected error:', error);
    process.exit(1);
  });
