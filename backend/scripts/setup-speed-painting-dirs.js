/**
 * Setup script for Speed Painting feature
 * Creates the necessary directory structure for storing uploaded images and processed videos
 */

const fs = require('fs');
const path = require('path');
const { exec } = require('child_process');

// Base directories
const BASE_DIR = path.join(__dirname, '../uploads');
const SPEED_PAINTING_DIR = path.join(BASE_DIR, 'speedpainting');
const ORIGINAL_DIR = path.join(SPEED_PAINTING_DIR, 'original');
const PROCESSED_DIR = path.join(SPEED_PAINTING_DIR, 'processed');

// Create directories if they don't exist
const createDirIfNotExists = (dir) => {
  if (!fs.existsSync(dir)) {
    console.log(`Creating directory: ${dir}`);
    fs.mkdirSync(dir, { recursive: true });
    
    // Set proper permissions
    exec(`chmod 755 "${dir}"`, (error) => {
      if (error) {
        console.error(`Error setting permissions for ${dir}:`, error);
      }
    });
  } else {
    console.log(`Directory already exists: ${dir}`);
  }
};

// Create the directory structure
const setup = () => {
  console.log('Setting up Speed Painting directories...');
  
  // Create base directories
  createDirIfNotExists(BASE_DIR);
  createDirIfNotExists(SPEED_PAINTING_DIR);
  createDirIfNotExists(ORIGINAL_DIR);
  createDirIfNotExists(PROCESSED_DIR);
  
  console.log('Directory setup complete!');
  console.log(`
Speed Painting directory structure:
- ${BASE_DIR}
  - ${SPEED_PAINTING_DIR}
    - ${ORIGINAL_DIR} (for uploaded images)
    - ${PROCESSED_DIR} (for generated videos)
  `);
  
  console.log('\nTo use this directory structure, ensure the following:');
  console.log('1. The application has write permissions to these directories');
  console.log('2. The environment variable STORE_PROCESSED_IMAGES is set to true');
  console.log('3. The web server is configured to serve static files from the uploads directory');
};

// Run the setup
setup();
