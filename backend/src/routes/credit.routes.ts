import express from "express";
import { authenticate } from "../middleware/auth";
// import { validateRequest } from "../middleware/validation";
import {
  getUserCreditBalance,
  getUserCreditHistory,
  getCreditPackages,
  createStripeCheckoutSession,
  createStripePaymentIntent,
  createPayPalOrder,
  capturePayPalOrder,
  getServiceCosts,
  adjustUserCredits,
  getAdminUserCreditHistory,
} from "../controllers/credit.controller";

const router = express.Router();
// router.use(authenticate as any);
// Public routes
router.get("/packages", getCreditPackages as any);
router.get("/costs", getServiceCosts as any);

// User routes (require authentication)
router.get("/balance", authenticate as any, getUserCreditBalance as any);
router.get("/history", authenticate as any, getUserCreditHistory as any);
router.post(
  "/purchase/stripe/create-checkout-session",
  authenticate as any,
  createStripeCheckoutSession as any
);

router.post(
  "/purchase/stripe/create-payment-intent",
  authenticate as any,
  createStripePaymentIntent as any
);
router.post(
  "/purchase/paypal/create-order",
  authenticate as any,
  createPayPalOrder as any
);
router.post(
  "/purchase/paypal/capture-order",
  authenticate as any,
  capturePayPalOrder as any
);

// Admin routes
router.post("/admin/adjust", authenticate as any, adjustUserCredits as any);
router.get(
  "/admin/users/:userId/history",
  authenticate as any,
  getAdminUserCreditHistory as any
);

export default router;
