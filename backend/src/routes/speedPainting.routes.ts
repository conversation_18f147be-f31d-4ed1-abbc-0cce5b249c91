import { Router } from "express";
import {
  createSpeedPaintingJob,
  getSpeedPaintingJob,
  getUserSpeedPaintingJobs,
  getQueueStatus,
  upload,
  rateLimitMiddleware,
} from "../controllers/speedPainting.controller";
import { authenticate } from "../middleware/auth";

const router = Router();

// Apply authentication middleware to all routes
router.use(authenticate as any);

// Speed painting routes
router.post(
  "/create",
  rateLimitMiddleware,
  upload.single("image"),
  createSpeedPaintingJob as any
);
router.get("/jobs", getUserSpeedPaintingJobs as any);
router.get("/jobs/:jobId", getSpeedPaintingJob as any);
router.get("/queue-status", getQueueStatus as any);

export default router;
