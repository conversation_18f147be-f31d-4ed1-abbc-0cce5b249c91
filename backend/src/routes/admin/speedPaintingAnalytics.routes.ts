import { Router } from 'express';
import { getSpeedPaintingAnalytics } from '../../controllers/admin/speedPaintingAnalytics.controller';
import { authenticate, authorizeAdmin } from '../../middleware/auth';

const router = Router();

// Apply authentication and admin-only middleware to all routes
router.use(authenticate as any);
router.use(authorizeAdmin as any);

// Speed painting analytics routes
router.get('/speed-painting', getSpeedPaintingAnalytics as any);

export default router;