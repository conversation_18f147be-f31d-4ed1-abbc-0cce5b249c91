// import express from "express";
// import { AdminPlansController } from "../../controllers/admin/plans.controller";
// import { authenticate, authorizeAdmin } from "../../middleware/auth";

// const router = express.Router();
// const adminPlansController = new AdminPlansController();

// // Apply admin middleware to all routes
// // Apply authentication and admin-only middleware to all routes
// router.use(authenticate as any);
// router.use(authorizeAdmin as any);

// // Subscription plans routes
// router.get(
//   "/subscription-plans",
//   adminPlansController.getAllSubscriptionPlans as any
// );
// router.get(
//   "/subscription-plans/:id",
//   adminPlansController.getSubscriptionPlan as any
// );
// router.post(
//   "/subscription-plans",
//   adminPlansController.createSubscriptionPlan as any
// );
// router.put(
//   "/subscription-plans/:id",
//   adminPlansController.updateSubscriptionPlan as any
// );
// router.delete(
//   "/subscription-plans/:id",
//   adminPlansController.deleteSubscriptionPlan as any
// );

// // Credit packages routes
// router.get(
//   "/credit-packages",
//   adminPlansController.getAllCreditPackages as any
// );
// router.post(
//   "/credit-packages",
//   adminPlansController.createCreditPackage as any
// );
// router.put(
//   "/credit-packages/:id",
//   adminPlansController.updateCreditPackage as any
// );
// router.delete(
//   "/credit-packages/:id",
//   adminPlansController.deleteCreditPackage as any
// );

// // Service costs routes
// router.get("/service-costs", adminPlansController.getServiceCosts as any);
// router.put("/service-costs", adminPlansController.updateServiceCosts as any);

// export default router;
