import express from "express";
import { AdminPaymentController } from "../controllers/admin-payment.controller";
import { authenticate } from "../middleware/auth";
import { isAdmin } from "../middleware/admin";

const router = express.Router();
const adminPaymentController = new AdminPaymentController();

// Apply authentication and admin middleware to all routes
router.use(authenticate as any);
router.use(isAdmin as any);

// Payment management routes
router.get("/payments", adminPaymentController.getAllPayments);

// Refund management routes
router.get("/refunds", adminPaymentController.getAllRefundRequests);
router.patch(
  "/refunds/:refundId/process",
  adminPaymentController.processRefundRequest
);

// Canceled subscription management routes
router.get(
  "/canceled-subscriptions",
  adminPaymentController.getCanceledSubscriptions
);

// Subscription plan management routes
router.get("/analytics", adminPaymentController.getSubscriptionAnalytics);
router.post("/plans", adminPaymentController.createSubscriptionPlan);
router.patch("/plans/:planId", adminPaymentController.updateSubscriptionPlan);
router.delete("/plans/:planId", adminPaymentController.deleteSubscriptionPlan);

export default router;
