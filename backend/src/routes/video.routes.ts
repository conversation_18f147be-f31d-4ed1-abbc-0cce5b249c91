import { Router } from 'express';
import {
  generateVideo,
  getVideoJob,
  getUserVideoJobs,
  getAvatars,
  getVoices,
} from '../controllers/video.controller';
import { authenticate } from '../middleware/auth';

const router = Router();

// Apply authentication middleware to all video routes
router.use(authenticate as any);

// Video generation routes
router.post('/generate', generateVideo as any);
router.get('/jobs', getUserVideoJobs as any);
router.get('/jobs/:jobId', getVideoJob as any);
router.get('/avatars', getAvatars as any);
router.get('/voices', getVoices as any);

export default router;
