import express from "express";
import {
  requestSubscriptionRefund,
  getUserRefunds,
  getAllRefunds,
  processRefund,
} from "../controllers/refund.controller";
import { authenticate } from "../middleware/auth";
import { isAdmin } from "../middleware/admin";

const router = express.Router();
router.use(authenticate as any);
// User routes
router.post("/request", requestSubscriptionRefund);
router.get("/user", getUserRefunds);

// Admin routes
router.get("/admin/all", isAdmin, getAllRefunds);
router.post("/admin/process", isAdmin, processRefund);

export default router;
