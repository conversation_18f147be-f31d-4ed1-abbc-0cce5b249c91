import { Router } from "express";
import {
  getSubscriptionPlans,
  getCreditPacks,
  createStripeCheckoutSession,
  createStripeCreditCheckoutSession,
  cancelSubscription,
  getUserInvoices,
  getUserSubscription,
  // createPayPalOrder,
  // capturePayPalOrder,
  // handlePayPalWebhook,
} from "../controllers/payment.controller";
import { authenticate } from "../middleware/auth";

const router = Router();

// Public routes
router.get("/plans", getSubscriptionPlans as any);
router.get("/credit-packs", getCreditPacks as any);

// Protected routes
router.use(authenticate as any);

// Subscription and payment routes
router.post(
  "/stripe/create-checkout-session",
  createStripeCheckoutSession as any
);
router.post(
  "/stripe/create-credit-checkout-session",
  createStripeCreditCheckoutSession as any
);
router.post("/subscriptions/cancel", cancelSubscription as any);
router.get("/subscriptions/me", getUserSubscription as any);
router.get("/invoices", getUserInvoices as any);

// PayPal routes
// router.post('/paypal/create-order', createPayPalOrder as any);
// router.get('/paypal/capture-order/:orderId', capturePayPalOrder as any);
// router.post('/paypal/webhook', express.json(), handlePayPalWebhook as any);

export default router;
