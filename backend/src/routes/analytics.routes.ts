import { Router } from "express";
import {
  getRevenueTimeSeries,
  getUsageTimeSeries,
  getUserGrowthTimeSeries,
  getUserStats
} from "../controllers/analytics.controller";
import { authenticate, authorizeAdmin } from "../middleware/auth";

const router = Router();

// Apply authentication and admin authorization middleware to all analytics routes
router.use(authenticate as any);
router.use(authorizeAdmin as any);

// Analytics endpoints
router.get("/revenue/timeseries", getRevenueTimeSeries as any);
router.get("/usage/timeseries", getUsageTimeSeries as any);
router.get("/users/timeseries", getUserGrowthTimeSeries as any);
router.get("/users", getUserStats as any);

export default router;
