import { Router } from 'express';
import {
  getAllSubscriptionPlans,
  getSubscriptionPlanById,
  createSubscriptionPlan,
  updateSubscriptionPlan,
  deleteSubscriptionPlan,
  getUserSubscription,
  compareSubscriptionPlans,
  upgradeSubscription,
  downgradeSubscription,
  getScheduledPlanChanges,
  cancelScheduledPlanChange,
  processScheduledPlanChanges
} from '../controllers/subscription.controller';
import { authenticate } from '../middleware/auth';

const router = Router();

// Public routes
router.get('/plans', getAllSubscriptionPlans as any);
router.get('/plans/compare', compareSubscriptionPlans as any);
router.get('/plans/:id', getSubscriptionPlanById as any);

// Protected routes (require authentication)
router.use(authenticate as any);
router.get('/user', getUserSubscription as any);

// Subscription management routes
router.post('/upgrade', upgradeSubscription as any);
router.post('/downgrade', downgradeSubscription as any);
router.get('/scheduled-changes', getScheduledPlanChanges as any);
router.post('/cancel-scheduled-change', cancelScheduledPlanChange as any);

// Admin-only routes
router.post('/plans', createSubscriptionPlan as any);
router.put('/plans/:id', updateSubscriptionPlan as any);
router.delete('/plans/:id', deleteSubscriptionPlan as any);
router.post('/process-scheduled-changes', processScheduledPlanChanges as any);

export default router;
