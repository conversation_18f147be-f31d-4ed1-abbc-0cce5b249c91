import express from "express";
import { authenticate } from "../middleware/auth";
import { checkCredits } from "../middleware/checkCredits";

const router = express.Router();

// Import controller
import * as virtualTryOnController from "../controllers/virtualTryOn.controller";

// Apply authentication middleware to all routes
router.use(authenticate as any);

// POST /api/v1/virtual-try-on
// Process virtual try-on request
router.post(
  "/",
  checkCredits("VIRTUAL_TRY_ON") as any,
  virtualTryOnController.uploadFiles,
  virtualTryOnController.processVirtualTryOn as any
);

// GET /api/v1/virtual-try-on/history
// Get user's virtual try-on history
router.get("/history", virtualTryOnController.getVirtualTryOnHistory as any);

// GET /api/v1/virtual-try-on/recent-clothing
// Get user's recent clothing items
router.get(
  "/recent-clothing",
  virtualTryOnController.getRecentClothingItems as any
);

// GET /api/v1/virtual-try-on/recent
// Get user's recent virtual try-ons
router.get("/recent", virtualTryOnController.getRecentVirtualTryOns as any);

// GET /api/v1/virtual-try-on/models
// Get all model images for a user (both admin and user models)
router.get("/models", virtualTryOnController.getModelImages as any);

// GET /api/v1/virtual-try-on/models/admin
// Get admin model images (our models)
router.get("/models/admin", virtualTryOnController.getAdminModelImages as any);

// GET /api/v1/virtual-try-on/models/user
// Get user model images (your models)
router.get("/models/user", virtualTryOnController.getUserModelImages as any);
// GET /api/v1/virtual-try-on/clothing
// Get all clothing items for a user (both admin and user clothing)
router.get("/clothing", virtualTryOnController.getClothingItems as any);

// POST /api/v1/virtual-try-on/models
// Upload a new model image
router.post(
  "/models",
  virtualTryOnController.uploadFiles,
  virtualTryOnController.uploadModelImage as any
);

// DELETE /api/v1/virtual-try-on/models/:id
// Delete a model image
router.delete("/models/:id", virtualTryOnController.deleteModelImage as any);

// POST /api/v1/virtual-try-on/clothing
// Upload a new clothing item
router.post(
  "/clothing",
  virtualTryOnController.uploadFiles,
  virtualTryOnController.uploadClothingItem as any
);

// DELETE /api/v1/virtual-try-on/clothing/:id
// Delete a clothing item
router.delete(
  "/clothing/:id",
  virtualTryOnController.deleteClothingItem as any
);
// DELETE /api/v1/virtual-try-on/history/:id
// Delete a history item
router.delete(
  "/history/:id",
  virtualTryOnController.deleteVirtualTryOnItem as any
);

// GET /api/v1/virtual-try-on/:jobId
// Get status of a virtual try-on job
router.get("/:jobId", virtualTryOnController.getVirtualTryOnJob as any);

export default router;
