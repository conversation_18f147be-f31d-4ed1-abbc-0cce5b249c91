import { Router } from 'express';
import {
  generateImage,
  getImageJob,
  getUserImageJobs,
  getImageStyles,
} from '../controllers/image.controller';
import { authenticate } from '../middleware/auth';

const router = Router();

// Apply authentication middleware to all image routes
router.use(authenticate as any);

// Image generation routes
router.post('/generate', generateImage as any);
router.get('/jobs', getUserImageJobs as any);
router.get('/jobs/:jobId', getImageJob as any);
router.get('/styles', getImageStyles as any);

export default router;
