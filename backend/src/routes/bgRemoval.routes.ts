import { Router } from "express";
import {
  removeBackground,
  getBgRemovalJob,
  getUserBgRemovalJobs,
  upload,
  processImageUrl,
  directRemoveBackground,
  rateLimitMiddleware,
} from "../controllers/bgRemoval.controller";
import { authenticate } from "../middleware/auth";

const router = Router();

// Apply authentication middleware to all background removal routes
router.use(authenticate as any);

// Background removal routes
router.post("/remove", upload.single("image"), removeBackground as any);
router.post("/process-url", processImageUrl as any); // Handler for JSON payload with imageUrl
router.get("/jobs", getUserBgRemovalJobs as any);
router.get("/jobs/:jobId", getBgRemovalJob as any);

// Direct background removal with Miragic AI API
router.post(
  "/direct-remove",
  rateLimitMiddleware,
  upload.single("image"),
  directRemoveBackground as any
);

export default router;
