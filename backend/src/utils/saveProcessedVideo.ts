import fs from 'fs';
import path from 'path';
import axios from 'axios';
import { v4 as uuidv4 } from 'uuid';
import { createWriteStream } from 'fs';
import { promisify } from 'util';
import { pipeline } from 'stream';

const streamPipeline = promisify(pipeline);

/**
 * Downloads and saves a video from a URL to the local file system
 * @param videoUrl URL of the video to download
 * @param userId User ID for organizing files
 * @returns Path to the saved video file (relative to server root)
 */
export async function saveProcessedVideo(videoUrl: string, userId: string = 'anonymous'): Promise<string> {
  try {
    // Create directory structure if it doesn't exist
    const baseDir = path.join(process.cwd(), 'uploads', 'speedpainting', 'processed', userId);
    fs.mkdirSync(baseDir, { recursive: true });

    // Generate unique filename
    const filename = `${uuidv4()}.mp4`;
    const filePath = path.join(baseDir, filename);
    
    // Download the file
    const response = await axios({
      method: 'GET',
      url: videoUrl,
      responseType: 'stream'
    });

    // Save the file
    await streamPipeline(response.data, createWriteStream(filePath));
    
    // Return the relative path for URL construction
    return `uploads/speedpainting/processed/${userId}/${filename}`;
  } catch (error) {
    console.error('Error saving processed video:', error);
    throw new Error(`Failed to save processed video: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Generate a thumbnail from a video file
 * @param videoPath Path to the video file
 * @returns Path to the generated thumbnail
 */
export async function generateVideoThumbnail(videoPath: string): Promise<string> {
  try {
    // This is a placeholder - in a real implementation, you would use
    // ffmpeg or another video processing library to extract a frame
    // For now, we'll just return a placeholder path
    const thumbnailPath = videoPath.replace('.mp4', '-thumbnail.jpg');
    return thumbnailPath;
  } catch (error) {
    console.error('Error generating thumbnail:', error);
    throw new Error(`Failed to generate thumbnail: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}
