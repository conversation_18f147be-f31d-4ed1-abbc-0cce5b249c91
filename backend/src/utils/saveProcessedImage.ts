import fs from "fs";
import path from "path";
import axios from "axios";

export const saveProcessedImage = async (url: string) => {
  try {
    const dir = "./uploads/bgRemoval/processed";
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }

    const filename =
      path.basename(new URL(url).pathname) || `processed-${Date.now()}.png`;
    const filePath = path.join(dir, filename);

    const response = await axios({
      method: "GET",
      url: url,
      responseType: "stream",
    });

    const writer = fs.createWriteStream(filePath);
    response.data.pipe(writer);

    return new Promise((resolve, reject) => {
      writer.on("finish", () => resolve(filePath));
      writer.on("error", reject);
    });
  } catch (error) {
    throw error;
  }
};
