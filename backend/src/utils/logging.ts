import winston from 'winston';
import path from 'path';
import fs from 'fs';

// Create logs directory if it doesn't exist
const logsDir = path.join(__dirname, '../../logs');
if (!fs.existsSync(logsDir)) {
  fs.mkdirSync(logsDir, { recursive: true });
}

// Define log format
const logFormat = winston.format.combine(
  winston.format.timestamp(),
  winston.format.json()
);

// Create separate loggers for different concerns
const speedPaintingLogger = winston.createLogger({
  level: 'info',
  format: logFormat,
  defaultMeta: { service: 'speed-painting-service' },
  transports: [
    new winston.transports.File({ 
      filename: path.join(logsDir, 'speed-painting-error.log'), 
      level: 'error' 
    }),
    new winston.transports.File({ 
      filename: path.join(logsDir, 'speed-painting.log') 
    }),
  ],
});

const creditLogger = winston.createLogger({
  level: 'info',
  format: logFormat,
  defaultMeta: { service: 'credit-service' },
  transports: [
    new winston.transports.File({ 
      filename: path.join(logsDir, 'credit-error.log'), 
      level: 'error' 
    }),
    new winston.transports.File({ 
      filename: path.join(logsDir, 'credit.log') 
    }),
  ],
});

// Add console transport in development environment
if (process.env.NODE_ENV !== 'production') {
  speedPaintingLogger.add(
    new winston.transports.Console({
      format: winston.format.combine(
        winston.format.colorize(),
        winston.format.simple()
      ),
    })
  );
  
  creditLogger.add(
    new winston.transports.Console({
      format: winston.format.combine(
        winston.format.colorize(),
        winston.format.simple()
      ),
    })
  );
}

export { speedPaintingLogger, creditLogger };
