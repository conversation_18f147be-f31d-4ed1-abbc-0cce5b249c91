/**
 * Custom application error class
 * Used for throwing consistent errors throughout the application
 */
export class AppError extends Error {
  statusCode: number;
  isOperational: boolean;
  code?: string;

  constructor(message: string, statusCode: number, code?: string) {
    super(message);
    this.statusCode = statusCode;
    this.isOperational = true;
    this.code = code || this.getDefaultErrorCode(statusCode);

    // Capture stack trace (excluding constructor call from it)
    Error.captureStackTrace(this, this.constructor);
  }

  private getDefaultErrorCode(statusCode: number): string {
    switch (statusCode) {
      case 400:
        return "BAD_REQUEST";
      case 401:
        return "UNAUTHORIZED";
      case 403:
        return "FORBIDDEN";
      case 404:
        return "NOT_FOUND";
      case 409:
        return "CONFLICT";
      case 422:
        return "VALIDATION_ERROR";
      case 500:
        return "SERVER_ERROR";
      default:
        return "ERROR";
    }
  }
}

/**
 * Error handler middleware
 */
export const errorHandler = (err: any, req: any, res: any, next: any) => {
  // Default to 500 internal server error if status code not set
  const statusCode = err.statusCode || 500;
  const message = err.message || "Something went wrong";

  // Log error for server-side debugging
  console.error(`[ERROR] ${statusCode}: ${message}`);
  if (process.env.NODE_ENV === "development") {
    console.error(err.stack);
  }

  // Send error response to client
  res.status(statusCode).json({
    success: false,
    error: {
      message,
      ...(process.env.NODE_ENV === "development" && { stack: err.stack }),
    },
  });
};
