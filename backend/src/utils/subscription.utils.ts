/**
 * Utility functions for subscription management
 */

/**
 * Calculate the yearly price with a discount
 * @param monthlyPrice - The monthly price
 * @param discountPercentage - The discount percentage (default: 20%)
 * @returns The yearly price with discount applied
 */
export const calculateYearlyPrice = (monthlyPrice: number, discountPercentage: number = 20): number => {
  const yearlyPrice = monthlyPrice * 12;
  const discountAmount = (yearlyPrice * discountPercentage) / 100;
  return Math.round(yearlyPrice - discountAmount);
};

/**
 * Generate a yearly plan from a monthly plan
 * @param monthlyPlan - The monthly subscription plan
 * @param discountPercentage - The discount percentage (default: 20%)
 * @returns A yearly subscription plan with discount applied
 */
export const generateYearlyPlan = (monthlyPlan: any, discountPercentage: number = 20): any => {
  const yearlyPrice = calculateYearlyPrice(monthlyPlan.price, discountPercentage);
  
  // Parse featureHighlights safely
  let existingHighlights = [];
  try {
    // Check if featureHighlights exists and is an array
    if (monthlyPlan.featureHighlights && Array.isArray(monthlyPlan.featureHighlights)) {
      existingHighlights = monthlyPlan.featureHighlights;
    } else if (typeof monthlyPlan.featureHighlights === 'string') {
      // Try to parse if it's a JSON string
      try {
        const parsed = JSON.parse(monthlyPlan.featureHighlights);
        if (Array.isArray(parsed)) {
          existingHighlights = parsed;
        }
      } catch (e) {
        // If parsing fails, use empty array
        console.warn('Failed to parse featureHighlights as JSON:', e);
      }
    }
  } catch (error) {
    console.warn('Error processing featureHighlights:', error);
  }
  
  // Parse features safely
  let features = {};
  try {
    if (monthlyPlan.features) {
      if (typeof monthlyPlan.features === 'object') {
        features = monthlyPlan.features;
      } else if (typeof monthlyPlan.features === 'string') {
        // Try to parse if it's a JSON string
        try {
          features = JSON.parse(monthlyPlan.features);
        } catch (e) {
          console.warn('Failed to parse features as JSON:', e);
          features = {};
        }
      }
    }
  } catch (error) {
    console.warn('Error processing features:', error);
    features = {};
  }
  
  // Create the discount highlight
  const discountHighlight = {
    title: `Save ${discountPercentage}% with annual billing`,
    description: `Pay for 12 months and save ${discountPercentage}% compared to monthly billing.`,
    icon: 'CreditCard'
  };
  
  return {
    ...monthlyPlan,
    id: `${monthlyPlan.id}-yearly`,
    name: `${monthlyPlan.name}-yearly`,
    displayName: `${monthlyPlan.displayName} (Yearly)`,
    description: `${monthlyPlan.description} Save ${discountPercentage}% with annual billing.`,
    price: yearlyPrice,
    interval: 'year',
    stripePriceId: monthlyPlan.stripePriceId ? `${monthlyPlan.stripePriceId}-yearly` : null,
    paypalPlanId: monthlyPlan.paypalPlanId ? `${monthlyPlan.paypalPlanId}-yearly` : null,
    // Use the safely parsed features
    features,
    // Add a note about the discount in the feature highlights
    featureHighlights: [discountHighlight, ...existingHighlights],
  };
};
