import { PrismaClient } from '@prisma/client';
import { speedPaintingLogger } from './logging';

const prisma = new PrismaClient();

/**
 * Analytics utility for tracking and analyzing feature usage
 */
export class Analytics {
  /**
   * Track a speed painting job creation
   */
  static async trackSpeedPaintingCreation(userId: string, jobId: string): Promise<void> {
    try {
      await prisma.analyticsEvent.create({
        data: {
          userId,
          eventType: 'SPEED_PAINTING_CREATED',
          metadata: {
            jobId,
            timestamp: new Date().toISOString()
          }
        }
      });
    } catch (error) {
      speedPaintingLogger.error({
        message: 'Failed to track speed painting creation',
        userId,
        jobId,
        error: error instanceof Error ? error.message : String(error),
        action: 'analytics_error'
      });
    }
  }

  /**
   * Track a speed painting job completion
   */
  static async trackSpeedPaintingCompletion(userId: string, jobId: string, processingTimeMs: number): Promise<void> {
    try {
      await prisma.analyticsEvent.create({
        data: {
          userId,
          eventType: 'SPEED_PAINTING_COMPLETED',
          metadata: {
            jobId,
            processingTimeMs,
            timestamp: new Date().toISOString()
          }
        }
      });
    } catch (error) {
      speedPaintingLogger.error({
        message: 'Failed to track speed painting completion',
        userId,
        jobId,
        error: error instanceof Error ? error.message : String(error),
        action: 'analytics_error'
      });
    }
  }

  /**
   * Track a speed painting job failure
   */
  static async trackSpeedPaintingFailure(userId: string, jobId: string, errorMessage: string): Promise<void> {
    try {
      await prisma.analyticsEvent.create({
        data: {
          userId,
          eventType: 'SPEED_PAINTING_FAILED',
          metadata: {
            jobId,
            errorMessage,
            timestamp: new Date().toISOString()
          }
        }
      });
    } catch (error) {
      speedPaintingLogger.error({
        message: 'Failed to track speed painting failure',
        userId,
        jobId,
        error: error instanceof Error ? error.message : String(error),
        action: 'analytics_error'
      });
    }
  }

  /**
   * Track credit usage for speed painting
   */
  static async trackCreditUsage(userId: string, credits: number, jobId?: string): Promise<void> {
    try {
      await prisma.analyticsEvent.create({
        data: {
          userId,
          eventType: 'CREDIT_USAGE',
          metadata: {
            feature: 'SPEED_PAINTING',
            credits,
            jobId,
            timestamp: new Date().toISOString()
          }
        }
      });
    } catch (error) {
      speedPaintingLogger.error({
        message: 'Failed to track credit usage',
        userId,
        credits,
        jobId,
        error: error instanceof Error ? error.message : String(error),
        action: 'analytics_error'
      });
    }
  }

  /**
   * Get speed painting usage statistics
   */
  static async getSpeedPaintingStats(startDate?: Date, endDate?: Date): Promise<any> {
    try {
      const now = new Date();
      const defaultStartDate = new Date(now.setDate(now.getDate() - 30)); // Last 30 days
      const start = startDate || defaultStartDate;
      const end = endDate || new Date();

      // Total jobs created
      const totalJobs = await prisma.speedPaintingJob.count({
        where: {
          createdAt: {
            gte: start,
            lte: end
          }
        }
      });

      // Completed jobs
      const completedJobs = await prisma.speedPaintingJob.count({
        where: {
          status: 'COMPLETED',
          createdAt: {
            gte: start,
            lte: end
          }
        }
      });

      // Failed jobs
      const failedJobs = await prisma.speedPaintingJob.count({
        where: {
          status: 'FAILED',
          createdAt: {
            gte: start,
            lte: end
          }
        }
      });

      // Average processing time
      const jobs = await prisma.speedPaintingJob.findMany({
        where: {
          status: 'COMPLETED',
          createdAt: {
            gte: start,
            lte: end
          }
        },
        select: {
          metadata: true
        }
      });

      let totalProcessingTime = 0;
      let jobsWithProcessingTime = 0;

      jobs.forEach(job => {
        if (job.metadata && typeof job.metadata === 'object' && 'processingDurationMs' in job.metadata) {
          const processingTime = job.metadata.processingDurationMs;
          if (typeof processingTime === 'number') {
            totalProcessingTime += processingTime;
            jobsWithProcessingTime++;
          }
        }
      });

      const averageProcessingTime = jobsWithProcessingTime > 0 
        ? totalProcessingTime / jobsWithProcessingTime 
        : 0;

      // Total credits used
      const creditUsage = await prisma.creditTransaction.aggregate({
        where: {
          type: 'USAGE',
          description: {
            contains: 'Speed painting'
          },
          createdAt: {
            gte: start,
            lte: end
          }
        },
        _sum: {
          amount: true
        }
      });

      return {
        totalJobs,
        completedJobs,
        failedJobs,
        successRate: totalJobs > 0 ? (completedJobs / totalJobs) * 100 : 0,
        averageProcessingTime,
        totalCreditsUsed: Math.abs(creditUsage._sum.amount || 0),
        period: {
          start: start.toISOString(),
          end: end.toISOString()
        }
      };
    } catch (error) {
      speedPaintingLogger.error({
        message: 'Failed to get speed painting statistics',
        error: error instanceof Error ? error.message : String(error),
        action: 'analytics_error'
      });
      
      throw error;
    }
  }
}
