import fs from "fs";
import path from "path";
import axios from "axios";
import { v4 as uuidv4 } from "uuid";
import { ClothingType, VirtualTryOnMode } from "@prisma/client";
import { APP_URL } from "../config";

// Base directory for virtual try-on files
const BASE_DIR = path.join(process.cwd(), "uploads", "virtualtryon");

// Directory structure
const DIRS = {
  original: {
    human: path.join(BASE_DIR, "original", "human"),
    clothes: {
      tops: path.join(BASE_DIR, "original", "clothes", "tops"),
      bottoms: path.join(BASE_DIR, "original", "clothes", "bottoms"),
      single: path.join(BASE_DIR, "original", "clothes", "single"),
    },
    sessions: path.join(BASE_DIR, "original", "sessions"),
  },
  processed: {
    single: path.join(BASE_DIR, "processed", "single"),
    combined: path.join(BASE_DIR, "processed", "combined"),
  },
  models: {
    default: path.join(BASE_DIR, "models", "default"),
    user: path.join(BASE_DIR, "models", "user"),
  },
};

/**
 * Ensures all required directories exist
 */
export const ensureDirectories = (): void => {
  // Create base directories
  Object.values(DIRS.original.clothes).forEach((dir) => {
    fs.mkdirSync(dir, { recursive: true });
  });

  Object.values(DIRS.processed).forEach((dir) => {
    fs.mkdirSync(dir, { recursive: true });
  });

  Object.values(DIRS.models).forEach((dir) => {
    fs.mkdirSync(dir, { recursive: true });
  });

  fs.mkdirSync(DIRS.original.human, { recursive: true });
  fs.mkdirSync(DIRS.original.sessions, { recursive: true });
};

/**
 * Creates a user-specific directory within a parent directory
 * @param parentDir Parent directory
 * @param userId User ID
 * @returns Path to the user directory
 */
export const createUserDirectory = (
  parentDir: string,
  userId: string
): string => {
  const userDir = path.join(parentDir, userId);
  fs.mkdirSync(userDir, { recursive: true });
  return userDir;
};

/**
 * Creates a session directory for multi-step uploads
 * @param sessionId Session ID
 * @returns Path to the session directory
 */
export const createSessionDirectory = (sessionId: string): string => {
  const sessionDir = path.join(DIRS.original.sessions, sessionId);
  fs.mkdirSync(sessionDir, { recursive: true });
  return sessionDir;
};

/**
 * Saves a human/model image
 * @param filePath Path to the uploaded file
 * @param userId User ID
 * @param isDefaultModel Whether this is a default model image
 * @returns Path to the saved image (relative to server root)
 */
export const saveHumanImage = (
  filePath: string,
  userId: string,
  isDefaultModel: boolean = false
): string => {
  const filename = `${uuidv4()}${path.extname(filePath)}`;
  const targetDir = isDefaultModel
    ? DIRS.models.default
    : createUserDirectory(DIRS.models.user, userId);

  const targetPath = path.join(targetDir, filename);
  fs.copyFileSync(filePath, targetPath);

  // Return relative path from server root
  return path.relative(process.cwd(), targetPath);
};

/**
 * Saves a clothing item image
 * @param filePath Path to the uploaded file
 * @param userId User ID
 * @param clothingType Type of clothing
 * @returns Path to the saved image (relative to server root)
 */
export const saveClothingImage = (
  filePath: string,
  userId: string,
  clothingType: ClothingType
): string => {
  const filename = `${uuidv4()}${path.extname(filePath)}`;
  let targetDir: string;

  switch (clothingType) {
    case "TOP":
      targetDir = createUserDirectory(DIRS.original.clothes.tops, userId);
      break;
    case "BOTTOM":
      targetDir = createUserDirectory(DIRS.original.clothes.bottoms, userId);
      break;
    default:
      targetDir = createUserDirectory(DIRS.original.clothes.single, userId);
  }

  const targetPath = path.join(targetDir, filename);
  fs.copyFileSync(filePath, targetPath);

  // Return relative path from server root
  return path.relative(process.cwd(), targetPath);
};

/**
 * Saves a processed image from a URL
 * @param imageUrl URL of the processed image
 * @param userId User ID
 * @param mode Processing mode
 * @returns Path to the saved image (relative to server root)
 */
export const saveProcessedImage = async (
  imageUrl: string,
  userId: string,
  mode: VirtualTryOnMode
): Promise<string> => {
  try {
    const filename = `${uuidv4()}.jpg`;
    const targetDir =
      mode === "SINGLE"
        ? createUserDirectory(DIRS.processed.single, userId)
        : createUserDirectory(DIRS.processed.combined, userId);

    const targetPath = path.join(targetDir, filename);

    // Download the image
    const response = await axios.get(imageUrl, { responseType: "arraybuffer" });
    fs.writeFileSync(targetPath, Buffer.from(response.data));

    // Return relative path from server root
    return path.relative(process.cwd(), targetPath);
  } catch (error) {
    console.error("Error saving processed image:", error);
    throw new Error(
      `Failed to save processed image: ${
        error instanceof Error ? error.message : "Unknown error"
      }`
    );
  }
};

/**
 * Detects the type of clothing from an image
 * @param imagePath Path to the clothing image
 * @returns Detected clothing type
 */
export const detectClothingType = (imagePath: string): ClothingType => {
  // In a real implementation, this would use AI to detect the clothing type
  // For now, we'll return a default value
  return "TOP";
};

/**
 * Gets the full absolute path for a relative file path
 * @param relativePath Relative path from server root
 * @returns Absolute path
 */
export const getFullPath = (relativePath: string): string => {
  // Check if the path is already absolute
  if (path.isAbsolute(relativePath)) {
    return relativePath;
  }
  return path.join(process.cwd(), relativePath);
};

/**
 * Converts an absolute path to a URL path
 * @param absolutePath Absolute file path
 * @returns URL path
 */
export const toUrlPath = (absolutePath: string): string => {
  // Ensure we're working with an absolute path
  const fullPath = path.isAbsolute(absolutePath)
    ? absolutePath
    : path.join(process.cwd(), absolutePath);
  const relativePath = path.relative(process.cwd(), fullPath);

  // Extract the uploads part of the path
  const uploadsIndex = relativePath.indexOf("uploads");
  const uploadsPath =
    uploadsIndex !== -1 ? relativePath.substring(uploadsIndex) : relativePath;

  // Ensure the URL path doesn't have duplicated segments and starts with /uploads
  const urlPath = `${APP_URL}/${uploadsPath
    .replace(/\\/g, "/")
    .replace(/^\/+/, "")
    .replace(/\/+/g, "/")}`;
  return urlPath;
};

/**
 * Deletes a file if it exists
 * @param filePath Path to the file
 */
export const deleteFileIfExists = (filePath: string): void => {
  const fullPath = getFullPath(filePath);
  if (fs.existsSync(fullPath)) {
    fs.unlinkSync(fullPath);
  }
};
