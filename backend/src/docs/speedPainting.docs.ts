/**
 * @swagger
 * tags:
 *   name: Speed Painting
 *   description: Speed painting generation API
 */

/**
 * @swagger
 * /api/v1/speed-painting/create:
 *   post:
 *     summary: Create a new speed painting job
 *     description: Upload an image and create a speed painting job that will generate a time-lapse painting video
 *     tags: [Speed Painting]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             required:
 *               - image
 *             properties:
 *               image:
 *                 type: string
 *                 format: binary
 *                 description: Image file to process (JPEG, PNG, WEBP, or GIF)
 *     responses:
 *       201:
 *         description: Speed painting job created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: object
 *                   properties:
 *                     jobId:
 *                       type: string
 *                       example: "550e8400-e29b-41d4-a716-446655440000"
 *                     status:
 *                       type: string
 *                       enum: [PENDING, PROCESSING, COMPLETED, FAILED]
 *                       example: "PENDING"
 *                     originalImageUrl:
 *                       type: string
 *                       example: "http://localhost:3000/uploads/speedpainting/original/user-id/image.jpg"
 *                 message:
 *                   type: string
 *                   example: "Speed painting job created successfully"
 *       400:
 *         description: Invalid request
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 error:
 *                   type: object
 *                   properties:
 *                     code:
 *                       type: string
 *                       example: "FILE_REQUIRED"
 *                     message:
 *                       type: string
 *                       example: "Image file is required"
 *       401:
 *         description: Unauthorized
 *       402:
 *         description: Insufficient credits
 *       429:
 *         description: Rate limit exceeded
 *       500:
 *         description: Server error
 */

/**
 * @swagger
 * /api/v1/speed-painting/jobs/{jobId}:
 *   get:
 *     summary: Get a specific speed painting job
 *     description: Retrieve details of a specific speed painting job by its ID
 *     tags: [Speed Painting]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: jobId
 *         required: true
 *         schema:
 *           type: string
 *         description: ID of the speed painting job
 *     responses:
 *       200:
 *         description: Speed painting job retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: string
 *                       example: "550e8400-e29b-41d4-a716-446655440000"
 *                     userId:
 *                       type: string
 *                       example: "550e8400-e29b-41d4-a716-446655440001"
 *                     originalImageUrl:
 *                       type: string
 *                       example: "http://localhost:3000/uploads/speedpainting/original/user-id/image.jpg"
 *                     processedVideoUrl:
 *                       type: string
 *                       example: "http://localhost:3000/uploads/speedpainting/processed/user-id/video.mp4"
 *                     status:
 *                       type: string
 *                       enum: [PENDING, PROCESSING, COMPLETED, FAILED]
 *                       example: "COMPLETED"
 *                     createdAt:
 *                       type: string
 *                       format: date-time
 *                     updatedAt:
 *                       type: string
 *                       format: date-time
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden
 *       404:
 *         description: Job not found
 *       500:
 *         description: Server error
 */

/**
 * @swagger
 * /api/v1/speed-painting/jobs:
 *   get:
 *     summary: Get all speed painting jobs for the current user
 *     description: Retrieve all speed painting jobs created by the authenticated user
 *     tags: [Speed Painting]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Speed painting jobs retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: object
 *                   properties:
 *                     jobs:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           id:
 *                             type: string
 *                             example: "550e8400-e29b-41d4-a716-446655440000"
 *                           originalImageUrl:
 *                             type: string
 *                             example: "http://localhost:3000/uploads/speedpainting/original/user-id/image.jpg"
 *                           processedVideoUrl:
 *                             type: string
 *                             example: "http://localhost:3000/uploads/speedpainting/processed/user-id/video.mp4"
 *                           status:
 *                             type: string
 *                             enum: [PENDING, PROCESSING, COMPLETED, FAILED]
 *                             example: "COMPLETED"
 *                           createdAt:
 *                             type: string
 *                             format: date-time
 *                           updatedAt:
 *                             type: string
 *                             format: date-time
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */

/**
 * @swagger
 * /api/v1/speed-painting/queue-status:
 *   get:
 *     summary: Get the queue status for speed painting jobs
 *     description: Retrieve information about the current queue status for speed painting jobs
 *     tags: [Speed Painting]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Queue status retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: object
 *                   properties:
 *                     queueLength:
 *                       type: number
 *                       example: 5
 *                     pendingJobs:
 *                       type: number
 *                       example: 3
 *                     processingJobs:
 *                       type: number
 *                       example: 2
 *                     estimatedWaitTimeMinutes:
 *                       type: number
 *                       example: 10
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */

/**
 * @swagger
 * /api/v1/admin/analytics/speed-painting:
 *   get:
 *     summary: Get speed painting analytics data
 *     description: Retrieve analytics data for speed painting jobs (admin only)
 *     tags: [Speed Painting, Admin]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: startDate
 *         schema:
 *           type: string
 *           format: date-time
 *         description: Start date for analytics data (ISO format)
 *       - in: query
 *         name: endDate
 *         schema:
 *           type: string
 *           format: date-time
 *         description: End date for analytics data (ISO format)
 *     responses:
 *       200:
 *         description: Analytics data retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: object
 *                   properties:
 *                     totalJobs:
 *                       type: number
 *                       example: 150
 *                     completedJobs:
 *                       type: number
 *                       example: 120
 *                     failedJobs:
 *                       type: number
 *                       example: 30
 *                     successRate:
 *                       type: number
 *                       example: 80
 *                     averageProcessingTime:
 *                       type: number
 *                       example: 45000
 *                     totalCreditsUsed:
 *                       type: number
 *                       example: 750
 *                     period:
 *                       type: object
 *                       properties:
 *                         start:
 *                           type: string
 *                           format: date-time
 *                         end:
 *                           type: string
 *                           format: date-time
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - Admin access required
 *       500:
 *         description: Server error
 */