import cron from "node-cron";
import { prisma } from "../index";

// Schedule the cron job to run every day at a specific time (e.g., midnight)
const subscriptionCron = () => {
  cron.schedule("0 0 * * *", async () => {
    console.log("*******RUNNING CRON TO DESTROY SUBSCRIPTION*********");
    try {
      const currentDate = new Date();

      const expiredSubscriptions = await prisma.userSubscription.findMany({
        where: {
          endDate: { lte: currentDate },
          status: "ACTIVE",
        },
      });
      if (expiredSubscriptions.length > 0) {
        await prisma.userSubscription.updateMany({
          where: {
            endDate: { lte: currentDate },
            status: "ACTIVE",
          },
          data: {
            status: "CANCELED",
          },
        });
      }
    } catch (error) {
      console.error("Error deleting expired subscriptions:", error);
    }
  });
};
export default subscriptionCron;
