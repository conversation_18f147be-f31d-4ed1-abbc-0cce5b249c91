import { CreditService } from "./credit.service";

// Initialize credit service
const creditService = new CreditService();

/**
 * Service to handle credit deduction for AI services
 */
export class CreditUsageService {
  /**
   * Get the credit cost for a specific service
   * @param serviceType Type of service (e.g., 'backgroundRemoval', 'imageGeneration')
   * @returns Number of credits required for the service
   */
  async getServiceCost(serviceType: string): Promise<number> {
    const serviceCosts = await creditService.getServiceCosts();

    switch (serviceType) {
      case "backgroundRemoval":
        return serviceCosts.backgroundRemoval || 0;
      case "imageGeneration":
        return serviceCosts.imageGeneration || 1;
      case "videoGeneration":
        return serviceCosts.videoGeneration || 50;
      case "virtualTryOn":
        return serviceCosts.virtualTryOn || 1;
      case "speedPainting":
        return serviceCosts.speedpaint || 5;
      default:
        return 1; // Default cost if service type is not recognized
    }
  }

  /**
   * Deduct credits for any service
   * @param userId User ID
   * @param credits Number of credits to deduct
   * @param description Description of the credit usage
   * @param jobId Optional job ID
   * @returns Result of credit deduction
   */
  async deductCredits(
    userId: string,
    credits: number,
    description: string,
    jobId?: string
  ) {
    return await creditService.spendCredits(
      userId,
      credits,
      description,
      jobId
    );
  }
  /**
   * Deduct credits for image generation
   * @param userId User ID
   * @param prompt Image generation prompt
   * @param jobId Generated image job ID
   * @returns Result of credit deduction
   */
  async deductImageGenerationCredits(
    userId: string,
    prompt: string,
    jobId: string
  ) {
    const serviceCosts = await creditService.getServiceCosts();
    const creditsToSpend = serviceCosts.imageGeneration;

    return await creditService.spendCredits(
      userId,
      creditsToSpend,
      `Image generation: "${prompt.substring(0, 50)}${
        prompt.length > 50 ? "..." : ""
      }"`,
      jobId
    );
  }

  /**
   * Deduct credits for video generation
   * @param userId User ID
   * @param prompt Video generation prompt
   * @param durationMinutes Video duration in minutes
   * @param jobId Generated video job ID
   * @returns Result of credit deduction
   */
  async deductVideoGenerationCredits(
    userId: string,
    prompt: string,
    durationMinutes: number,
    jobId: string
  ) {
    const serviceCosts = await creditService.getServiceCosts();
    // Calculate credits based on duration (rounded up to the nearest minute)
    const minutes = Math.ceil(durationMinutes);
    const creditsToSpend = serviceCosts.videoGeneration * minutes;

    return await creditService.spendCredits(
      userId,
      creditsToSpend,
      `Video generation (${minutes} min): "${prompt.substring(0, 50)}${
        prompt.length > 50 ? "..." : ""
      }"`,
      jobId
    );
  }

  /**
   * Deduct credits for background removal
   * @param userId User ID
   * @param imageFileName Original image file name
   * @param jobId Background removal job ID
   * @returns Result of credit deduction
   */
  async deductBackgroundRemovalCredits(
    userId: string,
    imageFileName: string,
    jobId: string
  ) {
    const serviceCosts = await creditService.getServiceCosts();
    const creditsToSpend = serviceCosts.backgroundRemoval;

    return await creditService.spendCredits(
      userId,
      creditsToSpend,
      `Background removal: "${imageFileName}"`,
      jobId
    );
  }

  /**
   * Deduct credits for virtual try-on
   * @param userId User ID
   * @param garmentFileName Garment image file name
   * @param humanFileName Human image file name
   * @param jobId Virtual try-on job ID
   * @returns Result of credit deduction
   */
  async deductVirtualTryOnCredits(
    userId: string,
    garmentFileName: string,
    humanFileName: string,
    jobId: string
  ) {
    const serviceCosts = await creditService.getServiceCosts();
    const creditsToSpend = serviceCosts.virtualTryOn;

    return await creditService.spendCredits(
      userId,
      creditsToSpend,
      `Virtual try-on: "${garmentFileName}" on "${humanFileName}"`,
      jobId
    );
  }

  /**
   * Deduct credits for speedpaint generation
   * @param userId User ID
   * @param prompt Speedpaint generation prompt
   * @param jobId Speedpaint job ID
   * @returns Result of credit deduction
   */
  async deductSpeedpaintCredits(userId: string, prompt: string, jobId: string) {
    const serviceCosts = await creditService.getServiceCosts();
    const creditsToSpend = serviceCosts.speedpaint;

    return await creditService.spendCredits(
      userId,
      creditsToSpend,
      `Speedpaint generation: "${prompt.substring(0, 50)}${
        prompt.length > 50 ? "..." : ""
      }"`,
      jobId
    );
  }

  /**
   * Check if user has enough credits for a service
   * @param userId User ID
   * @param serviceType Type of service
   * @param quantity Quantity of service (e.g., minutes for video)
   * @returns Whether user has enough credits
   */
  async hasEnoughCredits(
    userId: string,
    serviceType:
      | "image"
      | "video"
      | "backgroundRemoval"
      | "virtualTryOn"
      | "speedpaint"
      | "speedPainting",
    quantity: number = 1
  ) {
    try {
      // Get user's credit balance
      const credit = await creditService.getUserCreditBalance(userId);

      // Get service costs
      const serviceCosts = await creditService.getServiceCosts();

      // Calculate required credits
      let requiredCredits = 0;

      switch (serviceType) {
        case "image":
          requiredCredits = serviceCosts.imageGeneration * quantity;
          break;
        case "video":
          requiredCredits = serviceCosts.videoGeneration * quantity;
          break;
        case "backgroundRemoval":
          requiredCredits = serviceCosts.backgroundRemoval * quantity;
          break;
        case "virtualTryOn":
          requiredCredits = serviceCosts.virtualTryOn * quantity;
          break;
        case "speedpaint":
          requiredCredits = serviceCosts.speedpaint * quantity;
          break;
      }

      // Check if user has enough credits
      return credit.balance >= requiredCredits;
    } catch (error) {
      console.error("Error checking if user has enough credits:", error);
      return false;
    }
  }
}
