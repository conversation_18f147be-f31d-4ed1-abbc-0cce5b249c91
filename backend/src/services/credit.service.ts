import {
  PrismaClient,
  User,
  CreditTransactionType,
  Prisma,
} from "@prisma/client";
import { AppError } from "../utils/error";

const prisma = new PrismaClient();

// Define the ServiceCost type to match our model
type ServiceCost = {
  id: string;
  serviceName: string;
  displayName: string;
  costInCredits: number;
  description?: string;
  createdAt: Date;
  updatedAt: Date;
};

export class CreditService {
  /**
   * Get user's current credit balance
   */
  async getUserCreditBalance(userId: string) {
    try {
      // Find or create a credit record for the user
      const credit = await prisma.credit.upsert({
        where: { userId },
        update: {}, // No updates if found
        create: {
          userId,
          balance: 0,
          spent: 0,
        },
      });

      return credit;
    } catch (error) {
      console.error("Error getting user credit balance:", error);
      throw new AppError("Failed to get credit balance", 500);
    }
  }

  /**
   * Get user's credit transaction history
   */
  async getUserCreditHistory(userId: string, page = 1, limit = 10) {
    try {
      const skip = (page - 1) * limit;

      // Get total count for pagination
      const totalCount = await prisma.creditTransaction.count({
        where: { userId },
      });

      // Get transactions with pagination
      const transactions = await prisma.creditTransaction.findMany({
        where: { userId },
        orderBy: { createdAt: "desc" },
        skip,
        take: limit,
      });

      return {
        transactions,
        pagination: {
          total: totalCount,
          page,
          limit,
          totalPages: Math.ceil(totalCount / limit),
        },
      };
    } catch (error) {
      console.error("Error getting user credit history:", error);
      throw new AppError("Failed to get credit history", 500);
    }
  }

  /**
   * Get all available credit packages
   */
  async getCreditPackages() {
    try {
      const packages = await prisma.creditPackage.findMany({
        where: { isActive: true },
        orderBy: { price: "asc" },
      });

      return packages;
    } catch (error) {
      console.error("Error getting credit packages:", error);
      throw new AppError("Failed to get credit packages", 500);
    }
  }

  /**
   * Create a credit purchase order
   */
  async createCreditPurchaseOrder(
    userId: string,
    packageId: string,
    paymentProvider: "STRIPE" | "PAYPAL"
  ) {
    try {
      // Get the credit package
      const creditPackage = await prisma.creditPackage.findUnique({
        where: { id: packageId },
      });

      if (!creditPackage) {
        throw new AppError("Credit package not found", 404);
      }

      // Create a purchase order
      const purchaseOrder = await prisma.creditPurchaseOrder.create({
        data: {
          userId,
          creditPackageId: packageId,
          creditsToGrant: creditPackage.creditsAmount,
          currency: creditPackage.currency,
          paymentProvider,
          status: "PENDING",
        },
      });

      return purchaseOrder;
    } catch (error) {
      console.error("Error creating credit purchase order:", error);
      if (error instanceof AppError) {
        throw error;
      }
      throw new AppError("Failed to create purchase order", 500);
    }
  }

  /**
   * Update a credit purchase order status
   */
  async updateCreditPurchaseOrder(
    orderId: string,
    data: {
      status: string;
      paymentIntentId?: string;
      amountPaid?: number;
      providerMetadata?: any;
    }
  ) {
    try {
      const updatedOrder = await prisma.creditPurchaseOrder.update({
        where: { id: orderId },
        data: {
          status: data.status,
          paymentIntentId: data.paymentIntentId,
          amountPaid: data.amountPaid,
          providerMetadata: data.providerMetadata
            ? data.providerMetadata
            : undefined,
          updatedAt: new Date(),
        },
      });

      return updatedOrder;
    } catch (error) {
      console.error("Error updating credit purchase order:", error);
      throw new AppError("Failed to update purchase order", 500);
    }
  }

  /**
   * Complete a credit purchase and add credits to user's account
   * This method should be called within a transaction to ensure atomicity
   */
  /**
   * Determine if credits should expire based on user's subscription status
   * For subscribers, credits don't expire as long as subscription is active
   */
  async shouldCreditsExpire(userId: string, tx?: any) {
    const db = tx || prisma;
    
    // Check if user has an active subscription
    const activeSubscription = await db.userSubscription.findFirst({
      where: {
        userId,
        status: { in: ["ACTIVE", "TRIALING"] }
      }
    });
    
    // If user has an active subscription, credits should not expire
    // Otherwise, they expire after 2 years
    return !activeSubscription;
  }

  /**
   * Add credits to a user's account
   * @param userId The user ID
   * @param amount The amount of credits to add
   * @param description Description of the credit addition
   * @param creditType Type of credit (FREE, PAID, PROMOTIONAL)
   * @param tx Optional transaction object for atomic operations
   * @returns The updated credit record and transaction
   */
  async addCredits(
    userId: string,
    amount: number,
    description: string,
    creditType: 'FREE' | 'PAID' | 'PROMOTIONAL' = 'PAID',
    tx?: Prisma.TransactionClient
  ) {
    try {
      const db = tx || prisma;
      
      // Check if credits should expire based on subscription status
      const shouldExpire = await this.shouldCreditsExpire(userId, db);
      
      // Calculate expiry date if needed
      let expiryDate = null;
      if (shouldExpire) {
        expiryDate = new Date();
        expiryDate.setFullYear(expiryDate.getFullYear() + 2);
      }
      
      // Create or update the user's credit record
      const credit = await db.credit.upsert({
        where: { userId },
        update: {
          balance: {
            increment: amount,
          },
        },
        create: {
          userId,
          balance: amount,
          spent: 0,
        },
      });
      
      // Create a credit transaction record
      const transaction = await db.creditTransaction.create({
        data: {
          userId,
          creditId: credit.id,
          type: CreditTransactionType.ADJUSTMENT_ADD,
          amount,
          description,
          creditType,
          expiresAt: expiryDate,
        },
      });
      
      return { credit, transaction };
    } catch (error) {
      console.error("Error adding credits:", error);
      if (error instanceof AppError) {
        throw error;
      }
      throw new AppError("Failed to add credits to user account", 500);
    }
  }

  async completeCreditPurchase(
    orderId: string,
    paymentIntentId: string,
    amountPaid?: number
  ) {
    try {
      return await prisma.$transaction(async (tx) => {
        // Get the purchase order
        const order = await tx.creditPurchaseOrder.findUnique({
          where: { id: orderId },
          include: { creditPackage: true, user: true },
        });

        if (!order) {
          throw new AppError("Purchase order not found", 404);
        }

        if (order.status === "COMPLETED") {
          // Order already completed, return success to ensure idempotency
          return { success: true, order };
        }

        // Check if user has an active subscription to determine if credits should expire
        const shouldExpire = await this.shouldCreditsExpire(order.userId, tx);
        
        // Calculate expiry date (2 years from now) if credits should expire
        // Otherwise, set to null for subscribers (credits don't expire while subscribed)
        let expiryDate = null;
        if (shouldExpire) {
          expiryDate = new Date();
          expiryDate.setFullYear(expiryDate.getFullYear() + 2);
        }

        // Create a credit transaction with or without expiry date based on subscription status
        const description = expiryDate
          ? `Purchased ${order.creditsToGrant} credits (expires ${expiryDate.toLocaleDateString()})`
          : `Purchased ${order.creditsToGrant} credits (no expiry while subscribed)`;
          
        const transaction = await tx.creditTransaction.create({
          data: {
            userId: order.userId,
            creditId: (
              await tx.credit.findUnique({ where: { userId: order.userId } })
            )?.id,
            type: CreditTransactionType.PURCHASE,
            amount: order.creditsToGrant,
            description,
            creditType: "PAID",
            expiresAt: expiryDate, // Will be null for subscribers
          },
        });

        // Update the purchase order with the transaction ID and status
        const updatedOrder = await tx.creditPurchaseOrder.update({
          where: { id: orderId },
          data: {
            status: "COMPLETED",
            paymentIntentId,
            amountPaid: amountPaid ?? order.creditPackage.price,
            creditTransactionId: transaction.id,
            updatedAt: new Date(),
          },
        });

        // Update user's credit balance
        const credit = await tx.credit.upsert({
          where: { userId: order.userId },
          update: {
            balance: {
              increment: order.creditsToGrant,
            },
          },
          create: {
            userId: order.userId,
            balance: order.creditsToGrant,
            spent: 0,
          },
        });

        // Create payment record
        await tx.payment.create({
          data: {
            userId: order.userId,
            amount: amountPaid ?? order.creditPackage.price,
            currency: order.currency ?? order.creditPackage.currency,
            status: "COMPLETED",
            provider: order.paymentProvider === "STRIPE" ? "STRIPE" : "PAYPAL",
            transactionId: paymentIntentId,
            paymentIntentId,
            description: `Purchased ${order.creditsToGrant} credits`,
            paymentType: "CREDIT_PURCHASE",
          },
        });

        return { success: true, order: updatedOrder, credit, transaction };
      });
    } catch (error) {
      console.error("Error completing credit purchase:", error);
      if (error instanceof AppError) {
        throw error;
      }
      throw new AppError("Failed to complete credit purchase", 500);
    }
  }

  /**
   * Update credit expiry based on subscription status
   * This should be called when subscription status changes
   */
  async updateCreditExpiryForUser(userId: string) {
    try {
      const shouldExpire = await this.shouldCreditsExpire(userId);
      
      if (!shouldExpire) {
        // User has an active subscription, remove expiry dates from all credits
        await prisma.creditTransaction.updateMany({
          where: {
            userId,
            type: CreditTransactionType.PURCHASE,
            amount: { gt: 0 }
          },
          data: {
            expiresAt: null
          }
        });
      } else {
        // User doesn't have an active subscription, set 2-year expiry for all credits that don't have an expiry
        const expiryDate = new Date();
        expiryDate.setFullYear(expiryDate.getFullYear() + 2);
        
        await prisma.creditTransaction.updateMany({
          where: {
            userId,
            type: CreditTransactionType.PURCHASE,
            amount: { gt: 0 },
            expiresAt: null
          },
          data: {
            expiresAt: expiryDate
          }
        });
      }
      
      return { success: true };
    } catch (error) {
      console.error("Error updating credit expiry:", error);
      return { success: false, error: "Failed to update credit expiry" };
    }
  }
  
  /**
   * Spend credits for a service
   * Returns true if successful, false if insufficient credits
   */
  async spendCredits(
    userId: string,
    creditsToSpend: number,
    description: string,
    relatedJobId?: string
  ): Promise<{ success: boolean; newBalance?: number; error?: string }> {
    if (creditsToSpend <= 0) {
      return { success: false, error: "Credits to spend must be positive." };
    }

    try {
      const result = await prisma.$transaction(async (tx) => {
        // Get current user and their balance
        const user = await tx.user.findUnique({
          where: { id: userId },
          include: { credit: true },
        });

        if (!user) {
          throw new Error("User not found.");
        }

        // If user has no credit record yet, create one
        let credit = user.credit;
        if (!credit) {
          credit = await tx.credit.create({
            data: {
              userId,
              balance: 0,
              spent: 0,
            },
          });
        }

        if (credit.balance < creditsToSpend) {
          throw new Error("Insufficient credits.");
        }

        // Update user's credit balance
        const updatedCredit = await tx.credit.update({
          where: { userId },
          data: {
            balance: {
              decrement: creditsToSpend,
            },
            spent: {
              increment: creditsToSpend,
            },
          },
        });

        // Create credit transaction ledger entry
        await tx.creditTransaction.create({
          data: {
            userId,
            creditId: credit.id,
            type: CreditTransactionType.USAGE,
            amount: -creditsToSpend, // Negative amount for deduction
            description,
            relatedJobId,
          },
        });

        return { success: true, newBalance: updatedCredit.balance };
      });
      return result;
    } catch (error: any) {
      console.error("Failed to spend credits:", error);
      return { success: false, error: error.message || "Transaction failed." };
    }
  }

  /**
   * Admin function to adjust a user's credit balance
   */
  async adjustCredits(
    userId: string,
    amount: number,
    description: string,
    adjustmentType: "ADJUSTMENT_ADD" | "ADJUSTMENT_SUBTRACT",
    adminId: string
  ) {
    try {
      return await prisma.$transaction(async (tx) => {
        // Get user's credit record
        const credit = await tx.credit.findUnique({
          where: { userId },
        });

        if (!credit && amount < 0) {
          throw new AppError(
            "Cannot deduct credits from a user with no credits",
            400
          );
        }

        // Create or update credit record
        const updatedCredit = await tx.credit.upsert({
          where: { userId },
          update: {
            balance: {
              increment: adjustmentType === "ADJUSTMENT_ADD" ? amount : -amount,
            },
          },
          create: {
            userId,
            balance: adjustmentType === "ADJUSTMENT_ADD" ? amount : 0,
            spent: 0,
          },
        });

        // Create transaction record
        const transaction = await tx.creditTransaction.create({
          data: {
            userId,
            creditId: updatedCredit.id,
            type: adjustmentType as CreditTransactionType,
            amount: adjustmentType === "ADJUSTMENT_ADD" ? amount : -amount,
            description,
            relatedAdminId: adminId,
          },
        });

        return { credit: updatedCredit, transaction };
      });
    } catch (error) {
      console.error("Error adjusting credits:", error);
      if (error instanceof AppError) {
        throw error;
      }
      throw new AppError("Failed to adjust credits", 500);
    }
  }
  
  /**
   * Adjust a user's credit balance with transaction support
   * For use within other services that need transaction support
   */
  async adjustCreditBalanceWithTransaction(
    userId: string,
    amount: number,
    description: string,
    tx: Prisma.TransactionClient
  ) {
    try {
      // Determine if this is an add or subtract operation
      const adjustmentType = amount >= 0 ? "ADJUSTMENT_ADD" : "ADJUSTMENT_SUBTRACT";
      const absoluteAmount = Math.abs(amount);
      
      // Get user's credit record
      const credit = await tx.credit.findUnique({
        where: { userId },
      });

      if (!credit && amount < 0) {
        throw new AppError(
          "Cannot deduct credits from a user with no credits",
          400
        );
      }

      // Create or update credit record
      const updatedCredit = await tx.credit.upsert({
        where: { userId },
        update: {
          balance: {
            increment: amount,
          },
        },
        create: {
          userId,
          balance: amount > 0 ? amount : 0,
          spent: 0,
        },
      });

      // Create transaction record
      const transaction = await tx.creditTransaction.create({
        data: {
          userId,
          creditId: updatedCredit.id,
          type: adjustmentType as CreditTransactionType,
          amount: amount,
          description,
          relatedEntityType: "REFUND",
        },
      });

      return { credit: updatedCredit, transaction };
    } catch (error) {
      console.error("Error adjusting credits with transaction:", error);
      if (error instanceof AppError) {
        throw error;
      }
      throw new AppError("Failed to adjust credits", 500);
    }
  }

  /**
   * Get service costs in credits
   * @returns Record of service names and their costs in credits
   */
  async getServiceCosts(): Promise<Record<string, number>> {
    try {
      // Try to get service costs from database
      const serviceCosts = await prisma.serviceCost.findMany();

      // If service costs exist in database, return them
      if (serviceCosts.length > 0) {
        return serviceCosts.reduce((acc, cost) => {
          acc[cost.serviceName] = cost.costInCredits;
          return acc;
        }, {} as Record<string, number>);
      }

      // If no service costs in database, seed with defaults and return them
      await this.seedDefaultServiceCosts();
      const defaultCosts = await prisma.serviceCost.findMany();

      return defaultCosts.reduce((acc, cost) => {
        acc[cost.serviceName] = cost.costInCredits;
        return acc;
      }, {} as Record<string, number>);
    } catch (error) {
      console.error("Error getting service costs:", error);
      // Return default costs if there's an error
      return {
        imageGeneration: 2,
        videoGeneration: 50,
        backgroundRemoval: 1,
        virtualTryOn: 3,
        speedPaint: 5,
      };
    }
  }

  /**
   * Update multiple service costs
   */
  async updateServiceCosts(costs: Record<string, number>) {
    try {
      const updates = Object.entries(costs).map(
        ([serviceName, costInCredits]) => {
          return prisma.serviceCost.update({
            where: { serviceName },
            data: { costInCredits },
          });
        }
      );

      const results = await Promise.all(updates);
      return results;
    } catch (error) {
      console.error("Error updating service costs:", error);
      throw new AppError("Failed to update service costs", 500);
    }
  }

  /**
   * Seed default service costs if none exist
   */
  private async seedDefaultServiceCosts() {
    try {
      const defaultCosts = [
        {
          serviceName: "imageGeneration",
          displayName: "Image Generation",
          costInCredits: 2,
          description: "Cost per image generation",
        },
        {
          serviceName: "videoGeneration",
          displayName: "Video Generation",
          costInCredits: 50,
          description: "Cost per minute of video generation",
        },
        {
          serviceName: "backgroundRemoval",
          displayName: "Background Removal",
          costInCredits: 1,
          description: "Cost per image background removal",
        },
        {
          serviceName: "virtualTryOn",
          displayName: "Virtual Try-On",
          costInCredits: 3,
          description: "Cost per virtual try-on",
        },
        {
          serviceName: "speedpaint",
          displayName: "Speedpaint",
          costInCredits: 5,
          description: "Cost per speedpaint generation",
        },
      ];

      await prisma.serviceCost.createMany({
        data: defaultCosts,
        skipDuplicates: true,
      });

      console.log("Seeded default service costs");
    } catch (error) {
      console.error("Error seeding default service costs:", error);
      throw new AppError("Failed to seed default service costs", 500);
    }
  }
}
