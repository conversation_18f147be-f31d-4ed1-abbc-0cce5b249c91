import { PrismaClient, Prisma } from "@prisma/client";
import { AppError } from "../utils/error";
import { SubscriptionService } from "./subscription.service";
import { RefundService } from "./refund.service";
import { PaymentService } from "./payment.service";

const prisma = new PrismaClient();

export class AdminPaymentService {
  private subscriptionService: SubscriptionService;
  private refundService: RefundService;
  private paymentService: PaymentService;

  constructor() {
    this.subscriptionService = new SubscriptionService();
    this.refundService = new RefundService();
    this.paymentService = new PaymentService();
  }

  /**
   * Get all payments with pagination
   */
  async getAllPayments(
    page = 1,
    limit = 10,
    filters: Record<string, any> = {},
    search?: string
  ) {
    const skip = (page - 1) * limit;

    const whereClause: any = {};

    // Apply filters
    if (filters.userId) whereClause.userId = filters.userId;
    if (filters.status) whereClause.status = filters.status;
    if (filters.provider) whereClause.provider = filters.provider;
    if (filters.paymentType) whereClause.paymentType = filters.paymentType;
    if (filters.dateFrom) {
      whereClause.createdAt = {
        ...(whereClause.createdAt || {}),
        gte: new Date(filters.dateFrom),
      };
    }
    if (filters.dateTo) {
      whereClause.createdAt = {
        ...(whereClause.createdAt || {}),
        lte: new Date(filters.dateTo),
      };
    }
    // Search by payment ID or user email
    if (search) {
      whereClause.OR = [
        { id: { contains: search } },
        { transactionId: { contains: search } },
        {
          user: {
            email: { contains: search },
            // profile: {
            //   firstName: { contains: search },
            //   lastName: { contains: search },
            // },
          },
          subscriptionId: { contains: search },
        },
      ];
    }

    // Get total count for pagination
    const total = await prisma.payment.count({ where: whereClause });

    // Get payments with user information
    const payments = await prisma.payment.findMany({
      where: whereClause,
      include: {
        user: {
          select: {
            id: true,
            email: true,
            profile: {
              select: {
                firstName: true,
                lastName: true,
              },
            },
          },
        },
      },
      orderBy: {
        createdAt: "desc",
      },
      skip,
      take: limit,
    });

    // Format the response
    const formattedPayments = payments.map((payment) => ({
      id: payment.id,
      userId: payment.userId,
      userName: payment.user?.profile
        ? `${payment.user.profile.firstName} ${payment.user.profile.lastName}`.trim()
        : "Unknown",
      userEmail: payment.user?.email,
      amount: payment.amount,
      currency: payment.currency,
      status: payment.status,
      provider: payment.provider,
      paymentType: payment.paymentType,
      transactionId: payment.transactionId,
      metadata: payment.metadata,
      createdAt: payment.createdAt,
      subscriptionId: payment.subscriptionId || null,
    }));

    return {
      data: formattedPayments,
      pagination: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
      },
    };
  }

  /**
   * Get all refund requests with pagination
   */
  async getAllRefundRequests(
    page = 1,
    limit = 10,
    filters: Record<string, any> = {}
  ) {
    const skip = (page - 1) * limit;

    const whereClause: any = {};

    // Apply filters
    if (filters.userId) whereClause.userId = filters.userId;
    if (filters.status) whereClause.status = filters.status;
    if (filters.dateFrom) {
      whereClause.createdAt = {
        ...(whereClause.createdAt || {}),
        gte: new Date(filters.dateFrom),
      };
    }
    if (filters.dateTo) {
      whereClause.createdAt = {
        ...(whereClause.createdAt || {}),
        lte: new Date(filters.dateTo),
      };
    }

    // Get total count for pagination
    const total = await prisma.refund.count({ where: whereClause });

    // Get refund requests with user and payment information
    const refunds = await prisma.refund.findMany({
      where: whereClause,
      include: {
        user: {
          select: {
            id: true,
            email: true,
            profile: {
              select: {
                firstName: true,
                lastName: true,
              },
            },
          },
        },
        payment: true,
      },
      orderBy: {
        createdAt: "desc",
      },
      skip,
      take: limit,
    });

    // Get payment details for these refunds
    const paymentIds = refunds.map((refund) => refund.paymentId);
    const payments = await prisma.payment.findMany({
      where: {
        id: {
          in: paymentIds,
        },
      },
    });

    // Format the response
    const formattedRefunds = refunds.map((refund) => {
      // Find the corresponding payment
      const paymentData = payments.find((p) => p.id === refund.paymentId);

      return {
        id: refund.id,
        userId: refund.userId,
        userName: refund.user?.profile
          ? `${refund.user.profile.firstName} ${refund.user.profile.lastName}`.trim()
          : "Unknown",
        userEmail: refund.user?.email,
        paymentId: refund.paymentId,
        subscriptionId: paymentData?.subscriptionId || null,
        amount: refund.amount,
        currency: paymentData?.currency || "USD",
        reason: refund.reason,
        status: refund.status,
        notes: refund.notes, // Using notes instead of adminNotes to match schema
        createdAt: refund.createdAt,
        updatedAt: refund.updatedAt,
        processedAt: refund.processedDate,
        processedBy: refund.processedBy,
      };
    });

    return {
      data: formattedRefunds,
      pagination: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
      },
    };
  }

  /**
   * Get all canceled subscriptions with pagination
   */
  async getCanceledSubscriptions(
    page = 1,
    limit = 10,
    filters: Record<string, any> = {}
  ) {
    const skip = (page - 1) * limit;

    const whereClause: any = {
      status: "CANCELED",
    };

    // Apply filters
    if (filters.userId) whereClause.userId = filters.userId;
    if (filters.dateFrom) {
      whereClause.endDate = {
        ...(whereClause.endDate || {}),
        gte: new Date(filters.dateFrom),
      };
    }
    if (filters.dateTo) {
      whereClause.endDate = {
        ...(whereClause.endDate || {}),
        lte: new Date(filters.dateTo),
      };
    }

    // Get total count for pagination
    const total = await prisma.userSubscription.count({ where: whereClause });

    // Get canceled subscriptions with user and plan information
    const canceledSubscriptions = await prisma.userSubscription.findMany({
      where: whereClause,
      include: {
        user: {
          select: {
            id: true,
            email: true,
            profile: {
              select: {
                firstName: true,
                lastName: true,
              },
            },
          },
        },
        plan: true,
      },
      orderBy: {
        endDate: "desc",
      },
      skip,
      take: limit,
    });

    // Get refund information for these subscriptions if available
    const subscriptionIds = canceledSubscriptions.map((sub) => sub.id);
    const refunds = await prisma.refund.findMany({
      where: {
        payment: {
          metadata: {
            path: ["subscriptionId"],
            not: Prisma.JsonNull,
          },
        },
      },
    });

    // Get payment details for these refunds
    const paymentIds = refunds.map((refund) => refund.paymentId);
    const payments = await prisma.payment.findMany({
      where: {
        id: {
          in: paymentIds,
        },
      },
    });

    // Format the response
    const formattedSubscriptions = canceledSubscriptions.map((subscription) => {
      // Find refund for this subscription if exists
      const refund = refunds.find((r) => {
        // We need to get the payment and check its metadata
        const payment = payments.find((p) => p.id === r.paymentId);
        const metadata = payment?.metadata as any;
        return metadata?.subscriptionId === subscription.id;
      });

      return {
        id: subscription.id,
        userId: subscription.userId,
        userName: subscription.user?.profile
          ? `${subscription.user.profile.firstName} ${subscription.user.profile.lastName}`.trim()
          : "Unknown",
        userEmail: subscription.user?.email,
        subscriptionId: subscription.id,
        planId: subscription.planId,
        planName: subscription.plan?.name || "Unknown Plan",
        cancellationReason: null, // Will need to be fetched separately if needed
        cancellationFeedback: null, // Will need to be fetched separately if needed
        cancellationDate: subscription.updatedAt,
        effectiveEndDate: subscription.endDate,
        wasRefunded: !!refund,
        refundId: refund?.id || null,
      };
    });

    return {
      data: formattedSubscriptions,
      pagination: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
      },
    };
  }

  /**
   * Process a refund request (approve or reject)
   */
  async processRefundRequest(
    refundId: string,
    action: "approve" | "reject",
    adminId: string,
    notes?: string
  ) {
    // Start a transaction to ensure data consistency
    return prisma.$transaction(async (tx) => {
      // Get the refund request
      const refund = await tx.refund.findUnique({
        where: { id: refundId },
        include: { payment: true },
      });

      if (!refund) {
        throw new AppError("Refund request not found", 404, "REFUND_NOT_FOUND");
      }

      if (refund.status !== "PENDING") {
        throw new AppError(
          "Refund request has already been processed",
          400,
          "REFUND_ALREADY_PROCESSED"
        );
      }

      // Update the refund status based on the action
      if (action === "approve") {
        // Process the refund through the payment provider
        await this.refundService.processRefund(refund.id, true, adminId, notes);

        // Update the refund record
        const updatedRefund = await tx.refund.update({
          where: { id: refundId },
          data: {
            status: "APPROVED",
            processedDate: new Date(),
            processedBy: adminId,
            notes: notes,
          },
        });

        return {
          success: true,
          data: updatedRefund,
          message: "Refund request approved and processed successfully",
        };
      } else {
        // Reject the refund
        const updatedRefund = await tx.refund.update({
          where: { id: refundId },
          data: {
            status: "REJECTED",
            processedDate: new Date(),
            processedBy: adminId,
            notes: notes,
          },
        });

        return {
          success: true,
          data: updatedRefund,
          message: "Refund request rejected successfully",
        };
      }
    });
  }

  /**
   * Create a new subscription plan
   */
  async createSubscriptionPlan(planData: any) {
    try {
      // Create the plan in the database
      const plan = await prisma.subscriptionPlan.create({
        data: {
          name: planData.name,
          description: planData.description || null,
          price: planData.price,
          currency: planData.currency,
          interval: planData.interval,
          features: planData.features,
          stripePriceId: planData.stripePriceId || null,
          paypalPlanId: planData.paypalPlanId || null,
          isActive: planData.isActive,
          // Add required fields
          displayName: planData.displayName || planData.name,
          creditsAmount: planData.creditsAmount || 0,
          featureHighlights: planData.featureHighlights || [],
        },
      });

      return {
        success: true,
        data: plan,
        message: "Subscription plan created successfully",
      };
    } catch (error) {
      console.error("Error creating subscription plan:", error);
      throw new AppError(
        "Failed to create subscription plan",
        500,
        "PLAN_CREATION_FAILED"
      );
    }
  }

  /**
   * Update an existing subscription plan
   */
  async updateSubscriptionPlan(planId: string, planData: any) {
    try {
      // Check if the plan exists
      const existingPlan = await prisma.subscriptionPlan.findUnique({
        where: { id: planId },
      });

      if (!existingPlan) {
        throw new AppError(
          "Subscription plan not found",
          404,
          "PLAN_NOT_FOUND"
        );
      }

      // Update the plan in the database
      const updatedPlan = await prisma.subscriptionPlan.update({
        where: { id: planId },
        data: {
          name: planData.name !== undefined ? planData.name : undefined,
          description:
            planData.description !== undefined
              ? planData.description
              : undefined,
          price: planData.price !== undefined ? planData.price : undefined,
          currency:
            planData.currency !== undefined ? planData.currency : undefined,
          interval:
            planData.interval !== undefined ? planData.interval : undefined,
          features:
            planData.features !== undefined ? planData.features : undefined,
          stripePriceId:
            planData.stripePriceId !== undefined
              ? planData.stripePriceId
              : undefined,
          paypalPlanId:
            planData.paypalPlanId !== undefined
              ? planData.paypalPlanId
              : undefined,
          isActive:
            planData.isActive !== undefined ? planData.isActive : undefined,
          // Add required fields
          displayName:
            planData.displayName !== undefined
              ? planData.displayName
              : undefined,
          creditsAmount:
            planData.creditsAmount !== undefined
              ? planData.creditsAmount
              : undefined,
          featureHighlights:
            planData.featureHighlights !== undefined
              ? planData.featureHighlights
              : undefined,
        },
      });

      return {
        success: true,
        data: updatedPlan,
        message: "Subscription plan updated successfully",
      };
    } catch (error) {
      console.error("Error updating subscription plan:", error);
      if (error instanceof AppError) {
        throw error;
      }
      throw new AppError(
        "Failed to update subscription plan",
        500,
        "PLAN_UPDATE_FAILED"
      );
    }
  }

  /**
   * Delete a subscription plan
   */
  async deleteSubscriptionPlan(planId: string) {
    try {
      // Check if the plan exists
      const existingPlan = await prisma.subscriptionPlan.findUnique({
        where: { id: planId },
      });

      if (!existingPlan) {
        throw new AppError(
          "Subscription plan not found",
          404,
          "PLAN_NOT_FOUND"
        );
      }

      // Check if the plan is in use by any active subscriptions
      const activeSubscriptions = await prisma.userSubscription.count({
        where: {
          planId,
          status: {
            in: ["ACTIVE", "TRIALING"],
          },
        },
      });

      if (activeSubscriptions > 0) {
        throw new AppError(
          "Cannot delete a plan that has active subscriptions",
          400,
          "PLAN_IN_USE"
        );
      }

      // Delete the plan
      await prisma.subscriptionPlan.delete({
        where: { id: planId },
      });

      return {
        success: true,
        message: "Subscription plan deleted successfully",
      };
    } catch (error) {
      console.error("Error deleting subscription plan:", error);
      if (error instanceof AppError) {
        throw error;
      }
      throw new AppError(
        "Failed to delete subscription plan",
        500,
        "PLAN_DELETION_FAILED"
      );
    }
  }

  /**
   * Get subscription analytics data
   */
  async getSubscriptionAnalytics() {
    // Get total active subscriptions
    const activeSubscriptions = await prisma.userSubscription.count({
      where: {
        status: {
          in: ["ACTIVE", "TRIALING"],
        },
      },
    });

    // Get canceled subscriptions in the last 30 days
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const recentCancellations = await prisma.userSubscription.count({
      where: {
        status: "CANCELED",
        updatedAt: {
          gte: thirtyDaysAgo,
        },
      },
    });

    // Get total subscription revenue
    const subscriptionPayments = await prisma.payment.aggregate({
      where: {
        paymentType: "SUBSCRIPTION",
        status: "COMPLETED",
      },
      _sum: {
        amount: true,
      },
    });

    // Get monthly recurring revenue (MRR)
    const activeSubscriptionsWithPlans = await prisma.userSubscription.findMany(
      {
        where: {
          status: {
            in: ["ACTIVE", "TRIALING"],
          },
        },
        include: {
          plan: true,
        },
      }
    );

    // Calculate MRR
    let mrr = 0;
    for (const subscription of activeSubscriptionsWithPlans) {
      if (subscription.plan.interval === "month") {
        mrr += subscription.plan.price;
      } else if (subscription.plan.interval === "year") {
        mrr += subscription.plan.price / 12;
      }
    }

    // Get subscription distribution by plan
    const planDistribution = await prisma.userSubscription.groupBy({
      by: ["planId"],
      where: {
        status: {
          in: ["ACTIVE", "TRIALING"],
        },
      },
      _count: {
        id: true,
      },
    });

    // Get plan details
    const plans = await prisma.subscriptionPlan.findMany();
    const planMap = new Map(plans.map((plan) => [plan.id, plan]));

    const planStats = planDistribution.map((item) => ({
      planId: item.planId,
      planName: planMap.get(item.planId)?.name || "Unknown Plan",
      count: item._count.id,
      percentage: (item._count.id / activeSubscriptions) * 100,
    }));

    return {
      activeSubscriptions,
      recentCancellations,
      totalRevenue: subscriptionPayments._sum.amount || 0,
      monthlyRecurringRevenue: mrr,
      planDistribution: planStats,
    };
  }
}
