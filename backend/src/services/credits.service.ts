import { prisma } from "../index";
import { CreditTransactionType } from "@prisma/client";

/**
 * Deduct credits from a user's account
 * @param userId The user ID
 * @param featureType The feature type (used for logging)
 * @param amount Number of credits to deduct (defaults to 1)
 */
export const deductCredits = async (
  userId: string,
  featureType: string,
  amount: number = 1
): Promise<void> => {
  // Start a transaction
  await prisma.$transaction(async (tx) => {
    // Get user's current credit balance
    const userCredit = await tx.credit.findUnique({
      where: { userId },
    });

    if (!userCredit) {
      throw new Error("User has no credit account");
    }

    if (userCredit.balance < amount) {
      throw new Error("Insufficient credits");
    }

    // Update credit balance
    await tx.credit.update({
      where: { userId },
      data: {
        balance: {
          decrement: amount,
        },
        spent: {
          increment: amount,
        },
      },
    });

    // Create a transaction record
    await tx.creditTransaction.create({
      data: {
        userId,
        amount: -amount, // Negative amount for usage
        type: CreditTransactionType.USAGE,
        description: `Used ${amount} credit(s) for ${featureType}`,
      },
    });

    // Log API usage
    await tx.apiUsageLog.create({
      data: {
        requestTimestamp: new Date(),
        userId,
        featureUsed: featureType as any, // Cast to any since we're adding a new feature type
        creditsConsumed: amount,
        status: "success",
        responseTimestamp: new Date(),
      },
    });
  });
};
