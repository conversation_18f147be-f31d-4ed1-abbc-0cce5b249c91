import axios from "axios";
import {
  PrismaClient,
  User,
  GeneratedVideoJob,
  JobStatus,
} from "@prisma/client";
import { AppError } from "../utils/error";

const prisma = new PrismaClient();

interface VideoGenerationParams {
  prompt: string;
  style?: string;
  aspectRatio?: string;
  duration?: number;
  [key: string]: any; // Allow for other parameters
}

export class VideoService {
  private apiKey: string;
  private apiBaseUrl: string;

  constructor() {
    this.apiKey = process.env.SYNTHESIA_API_KEY || "";
    this.apiBaseUrl = "https://api.synthesia.io/v2";

    if (!this.apiKey) {
      console.error("Synthesia API key is not set");
    }
  }

  /**
   * Create a video generation job
   */
  async createVideoJob(
    user: User,
    params: VideoGenerationParams
  ): Promise<GeneratedVideoJob> {
    try {
      // Check if user has active subscription or enough credits
      const hasAccess = await this.checkUserAccess(user.id);

      if (!hasAccess) {
        throw new AppError(
          "Insufficient credits or no active subscription",
          403
        );
      }

      // Create job record in database
      const job = await prisma.generatedVideoJob.create({
        data: {
          userId: user.id,
          prompt: params.prompt,
          parameters: params as any, // Store all parameters
          status: JobStatus.PENDING,
        },
      });

      return job;
    } catch (error) {
      if (error instanceof AppError) {
        throw error;
      }
      throw new AppError("Failed to create video generation job", 500);
    }
  }

  /**
   * Process a video generation job
   */
  async processVideoJob(jobId: string): Promise<GeneratedVideoJob> {
    try {
      // Get job from database
      const job = await prisma.generatedVideoJob.findUnique({
        where: { id: jobId },
        include: { user: true },
      });

      if (!job) {
        throw new AppError("Video generation job not found", 404);
      }

      // Update job status to processing
      await prisma.generatedVideoJob.update({
        where: { id: jobId },
        data: { status: JobStatus.PROCESSING },
      });

      // Call Synthesia API to generate video
      const response = await this.callSynthesiaAPI(job.prompt, job.parameters);

      // Update job with video URL and status
      const updatedJob = await prisma.generatedVideoJob.update({
        where: { id: jobId },
        data: {
          status: JobStatus.COMPLETED,
          videoUrl: response.videoUrl,
          thumbnailUrl: response.thumbnailUrl,
          publicId: response.videoId,
          duration: response.duration,
        },
      });

      // Log API usage
      await prisma.apiUsageLog.create({
        data: {
          requestTimestamp: new Date(),
          userId: job.userId,
          featureUsed: "VIDEO_GENERATION",
          creditsConsumed: this.calculateCreditsUsed(job.parameters),
          responseTimestamp: new Date(),
          status: "success",
        },
      });

      // Deduct credits if applicable
      await this.deductCredits(
        job.userId,
        this.calculateCreditsUsed(job.parameters)
      );

      return updatedJob;
    } catch (error) {
      // Update job status to failed
      await prisma.generatedVideoJob.update({
        where: { id: jobId },
        data: {
          status: JobStatus.FAILED,
          errorMessage:
            error instanceof Error ? error.message : "Unknown error",
        },
      });

      if (error instanceof AppError) {
        throw error;
      }
      throw new AppError("Failed to process video generation job", 500);
    }
  }

  /**
   * Get a video generation job
   */
  async getVideoJob(jobId: string, userId: string): Promise<GeneratedVideoJob> {
    try {
      const job = await prisma.generatedVideoJob.findUnique({
        where: { id: jobId },
      });

      if (!job) {
        throw new AppError("Video generation job not found", 404);
      }

      // Check if job belongs to user
      if (job.userId !== userId) {
        throw new AppError("Unauthorized access to video generation job", 403);
      }

      return job;
    } catch (error) {
      if (error instanceof AppError) {
        throw error;
      }
      throw new AppError("Failed to get video generation job", 500);
    }
  }

  /**
   * Get all video generation jobs for a user
   */
  async getUserVideoJobs(userId: string): Promise<GeneratedVideoJob[]> {
    try {
      const jobs = await prisma.generatedVideoJob.findMany({
        where: { userId },
        orderBy: { createdAt: "desc" },
      });

      return jobs;
    } catch (error) {
      throw new AppError("Failed to get user video generation jobs", 500);
    }
  }

  /**
   * Call Synthesia API to generate video
   */
  private async callSynthesiaAPI(
    prompt: string,
    parameters: any
  ): Promise<{
    videoId: string;
    videoUrl: string;
    thumbnailUrl: string;
    duration: number;
  }> {
    try {
      // Check if we're in development mode and should return mock data
      if (
        process.env.NODE_ENV === "development" &&
        process.env.USE_MOCK_API === "true"
      ) {
        console.log("Using mock Synthesia API response in development mode");
        return this.getMockVideoResponse();
      }

      // Prepare the video creation request
      const createVideoRequest = {
        test: process.env.NODE_ENV !== "production", // Use test mode unless in production
        title:
          parameters.title || `Generated Video - ${new Date().toISOString()}`,
        description: `AI generated video from prompt: ${prompt.substring(
          0,
          100
        )}...`,
        visibility: "public",
        avatar: parameters.avatar || "anna", // Default avatar
        background: parameters.background || "office_1", // Default background
        backgroundType: parameters.backgroundType || "synthesia",
        script: prompt,
        voice: parameters.voice || "en-US-JennyNeural", // Default voice
        scriptType: "text",
        callbackId: `video_${Date.now()}_${Math.random()
          .toString(36)
          .substring(2, 9)}`,
      };

      // Create the video
      console.log("Calling Synthesia API to create video...");
      const createResponse = await axios.post(
        `${this.apiBaseUrl}/videos`,
        createVideoRequest,
        {
          headers: {
            Authorization: `api-key ${this.apiKey}`,
            "Content-Type": "application/json",
          },
        }
      );

      // Get the video ID from the response
      const videoId = createResponse.data.id;
      console.log(`Video creation initiated, video ID: ${videoId}`);

      // Poll for video completion
      let videoStatus = "in_progress";
      let videoUrl = "";
      let thumbnailUrl = "";
      let duration = 0;
      let retryCount = 0;
      const maxRetries = 30; // Maximum number of retries (30 * 10 seconds = 5 minutes)

      while (videoStatus !== "complete" && retryCount < maxRetries) {
        await new Promise((resolve) => setTimeout(resolve, 10000)); // Wait 10 seconds between polls
        retryCount++;

        console.log(
          `Checking video status (attempt ${retryCount}/${maxRetries})...`
        );
        const statusResponse = await axios.get(
          `${this.apiBaseUrl}/videos/${videoId}`,
          {
            headers: {
              Authorization: `api-key ${this.apiKey}`,
            },
          }
        );

        videoStatus = statusResponse.data.status;
        console.log(`Current video status: ${videoStatus}`);

        if (videoStatus === "complete") {
          videoUrl = statusResponse.data.download;
          thumbnailUrl =
            statusResponse.data.thumbnailUrl || statusResponse.data.previewUrl;
          duration = statusResponse.data.duration || 60; // Default to 60 seconds if not provided
          console.log("Video generation completed successfully");
        } else if (videoStatus === "failed") {
          console.error("Video generation failed on Synthesia side");
          throw new AppError("Video generation failed on Synthesia side", 500);
        }
      }

      if (videoStatus !== "complete") {
        console.error("Video generation timed out");
        throw new AppError("Video generation timed out", 504);
      }

      return {
        videoId,
        videoUrl,
        thumbnailUrl,
        duration,
      };
    } catch (error) {
      console.error("Error calling Synthesia API:", error);
      if (error instanceof AppError) {
        throw error;
      }
      throw new AppError("Failed to generate video with Synthesia API", 500);
    }
  }

  /**
   * Get mock video response for development
   */
  private getMockVideoResponse(): {
    videoId: string;
    videoUrl: string;
    thumbnailUrl: string;
    duration: number;
  } {
    return {
      videoId: `video_${Math.random().toString(36).substring(2, 15)}`,
      videoUrl: `https://example.com/videos/sample_${Math.floor(
        Math.random() * 1000
      )}.mp4`,
      thumbnailUrl: `https://example.com/thumbnails/sample_${Math.floor(
        Math.random() * 1000
      )}.jpg`,
      duration: Math.floor(Math.random() * 60) + 30, // Random duration between 30-90 seconds
    };
  }

  /**
   * Check if user has access to video generation
   * Either through an active subscription or enough credits
   */
  private async checkUserAccess(userId: string): Promise<boolean> {
    try {
      // Check if user has an active subscription
      const activeSubscription = await prisma.userSubscription.findFirst({
        where: {
          userId,
          status: "ACTIVE",
          endDate: {
            gte: new Date(),
          },
        },
        include: {
          plan: true,
        },
      });

      if (activeSubscription) {
        // Check if subscription plan includes video generation
        const features = activeSubscription.plan.features as any;
        if (features.video_generation_quota > 0) {
          // Check if user has used up their quota
          const usageCount = await prisma.apiUsageLog.count({
            where: {
              userId,
              featureUsed: "VIDEO_GENERATION",
              requestTimestamp: {
                gte: new Date(new Date().setDate(new Date().getDate() - 30)), // Last 30 days
              },
            },
          });

          if (usageCount < features.video_generation_quota) {
            return true;
          }
        }
      }

      // Check if user has enough credits
      const userCredit = await prisma.credit.findUnique({
        where: { userId },
      });

      if (userCredit && userCredit.balance >= 30) {
        // Assuming 30 credits per video
        return true;
      }

      return false;
    } catch (error) {
      console.error("Error checking user access:", error);
      return false;
    }
  }

  /**
   * Calculate credits used for video generation
   */
  private calculateCreditsUsed(parameters: any): number {
    // Base cost is 30 credits per minute
    const baseCost = 30;

    // Additional cost based on parameters
    let additionalCost = 0;

    // Calculate based on duration (if specified)
    if (parameters.duration) {
      // Convert duration to minutes and round up
      const durationInMinutes = Math.ceil(parameters.duration / 60);
      return baseCost * durationInMinutes;
    }

    // Default to base cost
    return baseCost + additionalCost;
  }

  /**
   * Deduct credits from user's account
   */
  private async deductCredits(userId: string, credits: number): Promise<void> {
    try {
      // Check if user has an active subscription
      const activeSubscription = await prisma.userSubscription.findFirst({
        where: {
          userId,
          status: "ACTIVE",
          endDate: {
            gte: new Date(),
          },
        },
        include: {
          plan: true,
        },
      });

      if (activeSubscription) {
        // If user has an active subscription, don't deduct credits
        // as it's included in their plan
        return;
      }

      // Deduct credits from user's account
      await prisma.credit.update({
        where: { userId },
        data: {
          balance: {
            decrement: credits,
          },
        },
      });

      // Log credit transaction
      await prisma.creditTransaction.create({
        data: {
          userId,
          amount: -credits,
          type: "USAGE",
          description: "Video generation",
        },
      });
    } catch (error) {
      console.error("Error deducting credits:", error);
    }
  }
}
