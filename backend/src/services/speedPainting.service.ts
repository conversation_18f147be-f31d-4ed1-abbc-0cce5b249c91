import axios from "axios";
import { PrismaClient, SpeedPaintingJob, JobStatus } from "@prisma/client";
import { AppError } from "../utils/error";
import fs from "fs";
import path from "path";
import { v4 as uuidv4 } from "uuid";
import { RateLimiterMemory } from "rate-limiter-flexible";
import { saveProcessedVideo } from "../utils/saveProcessedVideo";
import { speedPaintingLogger } from "../utils/logging";
import { CreditUsageService } from "./creditUsage.service";
import { Analytics } from "../utils/analytics";
import { APP_URL } from "../config";

const prisma = new PrismaClient();
const creditUsageService = new CreditUsageService();

/**
 * Service for handling speed painting operations
 */
export class SpeedPaintingService {
  private apiKey: string;
  private apiBaseUrl: string;
  private requestQueue: Map<string, Promise<string>>;
  private requestsPerMinute: Map<string, number>;
  private lastResetTime: Map<string, number>;
  private RATE_LIMIT = 10; // 10 requests per hour per user

  constructor() {
    this.apiKey = process.env.MIRAGIC_AI_API_KEY || "";
    this.apiBaseUrl =
      process.env.MIRAGIC_AI_API_URL || "https://api.miragic.ai";
    this.requestQueue = new Map();
    this.requestsPerMinute = new Map();
    this.lastResetTime = new Map();

    if (!this.apiKey) {
      speedPaintingLogger.warn(
        "Miragic AI API key is not set, some features may be limited"
      );
    }

    // Clean up rate limiting data every hour
    setInterval(() => this.cleanupRateLimitData(), 60 * 60 * 1000);
  }

  /**
   * Clean up rate limiting data for users who haven't made requests in the last hour
   */
  private cleanupRateLimitData(): void {
    const now = Date.now();
    const CLEANUP_THRESHOLD = 60 * 60 * 1000; // 1 hour

    for (const [userId, lastReset] of this.lastResetTime.entries()) {
      if (now - lastReset > CLEANUP_THRESHOLD) {
        this.requestsPerMinute.delete(userId);
        this.lastResetTime.delete(userId);
      }
    }
  }

  /**
   * Create a speed painting job
   */
  async createSpeedPaintingJob(
    userId: string,
    originalImagePath: string,
    originalImageUrl: string
  ): Promise<SpeedPaintingJob> {
    try {
      // Check if user has enough credits for speed painting
      const hasEnoughCredits = await creditUsageService.hasEnoughCredits(
        userId,
        "speedPainting"
      );
      if (!hasEnoughCredits) {
        speedPaintingLogger.warn({
          message: "Insufficient credits for speed painting",
          userId,
          action: "access_check",
        });
        throw new AppError(
          "Insufficient credits or no active subscription",
          403
        );
      }

      // Create job record in database
      const job = await prisma.speedPaintingJob.create({
        data: {
          userId,
          originalImagePath,
          originalImageUrl,
          status: JobStatus.PENDING,
          metadata: {
            queuedAt: new Date().toISOString(),
          },
        },
      });

      // Track job creation in analytics
      await Analytics.trackSpeedPaintingCreation(userId, job.id);

      // Process the job asynchronously
      this.processSpeedPaintingJob(job.id).catch((error) => {
        speedPaintingLogger.error({
          message: `Error processing speed painting job ${job.id}`,
          jobId: job.id,
          userId: userId,
          error: error instanceof Error ? error.message : String(error),
          stack: error instanceof Error ? error.stack : undefined,
        });
      });

      return job;
    } catch (error) {
      if (error instanceof AppError) {
        throw error;
      }
      throw new AppError("Failed to create speed painting job", 500);
    }
  }

  /**
   * Process a speed painting job
   */
  async processSpeedPaintingJob(jobId: string): Promise<SpeedPaintingJob> {
    const startTime = Date.now();
    let errorMessage = "";
    let processingStatus = "failed";

    try {
      // Get job from database
      const job = await prisma.speedPaintingJob.findUnique({
        where: { id: jobId },
      });

      if (!job) {
        throw new AppError("Speed painting job not found", 404);
      }

      // Update job status to processing
      await prisma.speedPaintingJob.update({
        where: { id: jobId },
        data: {
          status: JobStatus.PROCESSING,
          metadata: {
            // ...job.metadata,
            processingStartTime: new Date().toISOString(),
          },
        },
      });

      // Call Miragic AI API to generate speed painting
      const videoUrl = await this.callSpeedPaintingAPI(
        job.originalImagePath,
        job.userId
      );

      // Save the video locally
      const processedVideoPath = await saveProcessedVideo(videoUrl, job.userId);
      
      // Ensure the URL is properly formatted with the full APP_URL
      // Remove any potential double slashes except after protocol
      const processedVideoUrl = `${APP_URL}/${processedVideoPath}`.replace(/([^:]\/)\/+/g, '$1');
      // Update job with processed video URL and status
      const updatedJob = await prisma.speedPaintingJob.update({
        where: { id: jobId },
        data: {
          processedVideoUrl,
          status: JobStatus.COMPLETED,
          metadata: {
            // ...job.metadata,
            processingEndTime: new Date().toISOString(),
            processingDurationMs: Date.now() - startTime,
            externalVideoUrl: videoUrl,
          },
        },
      });

      // Log API usage
      await prisma.apiUsageLog.create({
        data: {
          userId: job.userId,
          featureUsed: "SPEED_PAINTING",
          creditsConsumed: 5, // 5 credits per speed painting
          requestTimestamp: new Date(startTime),
          responseTimestamp: new Date(),
          status: "success",
          metadata: {
            processingTimeMs: Date.now() - startTime,
            jobId: job.id,
            apiVersion: "1.0",
          },
        },
      });

      const processingTimeMs = Date.now() - startTime;

      speedPaintingLogger.info({
        message: "Speed painting job completed successfully",
        jobId: job.id,
        userId: job.userId,
        processingTimeMs,
        action: "job_completed",
      });

      // Track job completion in analytics
      await Analytics.trackSpeedPaintingCompletion(
        job.userId,
        job.id,
        processingTimeMs
      );

      // Deduct credits
      await creditUsageService.deductCredits(
        job.userId,
        5,
        `Speed painting generation (Job ID: ${job.id})`,
        job.id
      );

      speedPaintingLogger.info({
        message: "Credits deducted for speed painting",
        userId: job.userId,
        jobId: job.id,
        credits: 5,
        action: "credit_deduction",
      });

      // Track credit usage in analytics
      await Analytics.trackCreditUsage(job.userId, 5, job.id);

      processingStatus = "success";
      return updatedJob;
    } catch (error) {
      // Capture error message for logging
      errorMessage = error instanceof Error ? error.message : "Unknown error";

      // Update job status to failed
      await prisma.speedPaintingJob.update({
        where: { id: jobId },
        data: {
          status: JobStatus.FAILED,
          errorMessage,
          metadata: {
            processingEndTime: new Date().toISOString(),
            processingDurationMs: Date.now() - startTime,
          },
        },
      });

      // Log API usage failure
      try {
        await prisma.apiUsageLog.create({
          data: {
            userId:
              (
                await prisma.speedPaintingJob.findUnique({
                  where: { id: jobId },
                })
              )?.userId || "unknown",
            featureUsed: "SPEED_PAINTING",
            creditsConsumed: 0, // No credits consumed on failure
            requestTimestamp: new Date(startTime),
            responseTimestamp: new Date(),
            metadata: {
              processingTimeMs: Date.now() - startTime,
            },
            status: "failed",
            errorDetails: errorMessage,
          },
        });
      } catch (logError) {
        console.error("Failed to log API usage:", logError);
      }

      if (error instanceof AppError) {
        throw error;
      }
      throw new AppError("Failed to process speed painting job", 500);
    } finally {
      // Record metrics for monitoring
      speedPaintingLogger.info({
        message: `Speed painting job ${jobId} completed with status ${processingStatus}`,
        jobId,
        processingTimeMs: Date.now() - startTime,
        status: processingStatus,
        action: "job_status",
      });

      if (errorMessage) {
        speedPaintingLogger.error({
          message: `Speed painting job ${jobId} failed`,
          jobId,
          error: errorMessage,
          action: "job_failed",
        });

        // Get the user ID for the job
        const failedJob = await prisma.speedPaintingJob.findUnique({
          where: { id: jobId },
          select: { userId: true },
        });

        if (failedJob) {
          // Track job failure in analytics
          await Analytics.trackSpeedPaintingFailure(
            failedJob.userId,
            jobId,
            errorMessage
          );
        }
      }
    }
  }

  /**
   * Get a speed painting job
   */
  async getSpeedPaintingJob(
    jobId: string,
    userId: string
  ): Promise<SpeedPaintingJob> {
    try {
      const job = await prisma.speedPaintingJob.findUnique({
        where: { id: jobId },
      });

      if (!job) {
        throw new AppError("Speed painting job not found", 404);
      }

      // Check if job belongs to user
      if (job.userId !== userId) {
        throw new AppError("Unauthorized access to speed painting job", 403);
      }

      return job;
    } catch (error) {
      if (error instanceof AppError) {
        throw error;
      }
      throw new AppError("Failed to get speed painting job", 500);
    }
  }

  /**
   * Get all speed painting jobs for a user
   */
  async getUserSpeedPaintingJobs(userId: string): Promise<SpeedPaintingJob[]> {
    try {
      const jobs = await prisma.speedPaintingJob.findMany({
        where: { userId },
        orderBy: { createdAt: "desc" },
      });

      return jobs;
    } catch (error) {
      throw new AppError("Failed to get user speed painting jobs", 500);
    }
  }

  /**
   * Call the Miragic AI API to generate a speed painting
   */
  private async callSpeedPaintingAPI(
    imagePath: string,
    userId: string
  ): Promise<string> {
    try {
      // Check rate limiting for this user
      this.checkRateLimit(userId);

      console.log(
        `Calling Miragic AI API to generate speed painting from image: ${imagePath}`
      );

      // Create form data
      const FormData = require("form-data");
      const formData = new FormData();

      // Add the image file to the form data
      formData.append("file", fs.createReadStream(imagePath));

      // Call the Miragic AI API
      const response = await axios.post(
        `${this.apiBaseUrl}/speed_painting`,
        formData,
        {
          headers: {
            ...formData.getHeaders(),
            Authorization: `Bearer ${this.apiKey}`,
          },
          timeout: 120000, // 2 minute timeout
          maxContentLength: 100 * 1024 * 1024, // 100MB max response size
        }
      );

      // Check if the API call was successful
      if (!response.data || response.data.status !== "success") {
        console.error("Miragic AI API error:", response.data);
        throw new AppError(
          `API error: ${response.data?.message || "Unknown error"}`,
          500
        );
      }

      // Get the URL of the processed video
      const videoUrl = response.data.link;
      speedPaintingLogger.info({
        message: "Speed painting generated successfully",
        userId,
        action: "video_generated",
      });

      return videoUrl;
    } catch (error) {
      if (axios.isAxiosError(error) && error.response) {
        console.error(
          `API error (${error.response.status}):`,
          error.response.data
        );
        throw new AppError(
          `API error: ${error.response.status} - ${
            error.response.data?.message || error.message
          }`,
          error.response.status
        );
      }

      throw error;
    }
  }

  /**
   * Check if a user has access to the speed painting feature
   */
  private async checkUserAccess(userId: string): Promise<boolean> {
    try {
      // Check if user has an active subscription
      const activeSubscription = await prisma.userSubscription.findFirst({
        where: {
          userId,
          status: "ACTIVE",
          endDate: {
            gte: new Date(),
          },
        },
      });

      if (activeSubscription) {
        return true;
      }

      // Check if user has used their free tier quota
      const usageCount = await prisma.apiUsageLog.count({
        where: {
          userId,
          featureUsed: "SPEED_PAINTING",
          requestTimestamp: {
            gte: new Date(new Date().setDate(new Date().getDate() - 30)), // Last 30 days
          },
        },
      });

      // Check if user has enough credits
      const userCredit = await prisma.credit.findUnique({
        where: { userId },
      });

      if (userCredit && userCredit.balance >= 5) {
        // 5 credits per speed painting
        return true;
      }

      return false;
    } catch (error) {
      speedPaintingLogger.error({
        message: "Error checking user access",
        userId,
        error: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined,
        action: "access_check_error",
      });
      return false;
    }
  }

  /**
   * Check rate limit for a user and throw an error if exceeded
   */
  private checkRateLimit(userId: string): void {
    const now = Date.now();
    const ONE_HOUR = 60 * 60 * 1000;

    // Initialize or reset counters if needed
    if (
      !this.requestsPerMinute.has(userId) ||
      !this.lastResetTime.has(userId)
    ) {
      this.requestsPerMinute.set(userId, 0);
      this.lastResetTime.set(userId, now);
    }

    // Reset counter if an hour has passed
    const lastReset = this.lastResetTime.get(userId)!;
    if (now - lastReset > ONE_HOUR) {
      this.requestsPerMinute.set(userId, 0);
      this.lastResetTime.set(userId, now);
    }

    // Check current count
    const currentCount = this.requestsPerMinute.get(userId)!;
    if (currentCount >= this.RATE_LIMIT) {
      const resetTime = new Date(lastReset.valueOf() + ONE_HOUR);
      throw new AppError(
        `Rate limit exceeded. Try again after ${resetTime.toISOString()}`,
        429
      );
    }

    // Increment the counter
    this.requestsPerMinute.set(userId, currentCount + 1);
  }

  /**
   * Deduct credits from a user's account
   */
  private async deductCredits(userId: string, credits: number): Promise<void> {
    try {
      await prisma.credit.update({
        where: { userId },
        data: {
          balance: {
            decrement: credits,
          },
        },
      });

      // Log the credit transaction
      await prisma.creditTransaction.create({
        data: {
          userId,
          amount: -credits,
          type: "USAGE",
          description: "Speed painting generation",
        },
      });
    } catch (error) {
      speedPaintingLogger.error({
        message: "Error deducting credits",
        userId,
        credits,
        error: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined,
        action: "credit_deduction_error",
      });
    }
  }
}

export default new SpeedPaintingService();
