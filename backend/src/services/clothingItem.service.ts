import { PrismaClient, ClothingType, Prisma } from '@prisma/client';
import { v4 as uuidv4 } from 'uuid';
import path from 'path';
import fs from 'fs';
import { prisma } from '..';

const UPLOAD_DIR = path.join(process.cwd(), 'uploads', 'clothing');

// Ensure upload directory exists
if (!fs.existsSync(UPLOAD_DIR)) {
  fs.mkdirSync(UPLOAD_DIR, { recursive: true });
}

export class ClothingItemService {
  /**
   * Upload a new clothing item
   */
  async uploadClothingItem(
    userId: string,
    file: Express.Multer.File,
    metadata: {
      clothingType: ClothingType;
      category?: string;
      color?: string;
      style?: string;
      season?: string;
    }
  ) {
    const fileExt = path.extname(file.originalname);
    const fileName = `${uuidv4()}${fileExt}`;
    const filePath = path.join('clothing', fileName);
    const fullPath = path.join(UPLOAD_DIR, fileName);

    // Move the file to the uploads directory
    await fs.promises.rename(file.path, fullPath);

    // Create database record
    return prisma.clothingItem.create({
      data: {
        userId,
        imagePath: filePath,
        imageUrl: `/uploads/${filePath}`,
        clothingType: metadata.clothingType,
        category: metadata.category,
        color: metadata.color,
        style: metadata.style,
        season: metadata.season,
      },
    });
  }

  /**
   * Get recent clothing items for a user
   */
  async getRecentItems(userId: string, limit: number = 4) {
    return prisma.clothingItem.findMany({
      where: {
        OR: [
          { userId },
          {
            user: {
              role: 'ADMIN',
            },
          },
        ],
      },
      include: {
        user: {
          select: {
            id: true,
            role: true,
          },
        },
      },
      orderBy: [
        { usageCount: 'desc' },
        { createdAt: 'desc' },
      ],
      take: limit,
    });
  }

  /**
   * Get clothing items by type
   */
  async getClothingByType(
    clothingType: ClothingType,
    userId: string,
    includeAdmin: boolean = true
  ) {
    const where: Prisma.ClothingItemWhereInput = {
      clothingType,
    };

    if (includeAdmin) {
      where.OR = [
        { userId },
        {
          user: {
            role: 'ADMIN',
          },
        },
      ];
    } else {
      where.userId = userId;
    }

    return prisma.clothingItem.findMany({
      where,
      include: {
        user: {
          select: {
            id: true,
            role: true,
          },
        },
      },
      orderBy: [
        { usageCount: 'desc' },
        { createdAt: 'desc' },
      ],
    });
  }

  /**
   * Delete a clothing item
   */
  async deleteClothingItem(
    itemId: string,
    userId: string
  ): Promise<{ success: boolean; message: string }> {
    // Check if item exists and belongs to user
    const item = await prisma.clothingItem.findFirst({
      where: {
        id: itemId,
        userId,
      },
    });

    if (!item) {
      throw new Error('Clothing item not found or insufficient permissions');
    }

    // Check if item is in use
    const inUse = await prisma.virtualTryOn.findFirst({
      where: {
        OR: [
          { clothImagePath: item.imagePath },
          { bottomClothImagePath: item.imagePath },
        ],
        status: {
          in: ['PENDING', 'PROCESSING'],
        },
      },
    });

    if (inUse) {
      throw new Error('Cannot delete clothing item currently in use');
    }

    // Delete the file
    const fullPath = path.join(process.cwd(), 'uploads', item.imagePath);
    if (fs.existsSync(fullPath)) {
      await fs.promises.unlink(fullPath);
    }

    // Delete the database record
    await prisma.clothingItem.delete({
      where: { id: itemId },
    });

    return { success: true, message: 'Clothing item deleted successfully' };
  }

  /**
   * Increment usage count for a clothing item
   */
  async incrementUsage(itemId: string): Promise<void> {
    await prisma.clothingItem.update({
      where: { id: itemId },
      data: {
        usageCount: { increment: 1 },
      },
    });
  }
}

export const clothingItemService = new ClothingItemService();
