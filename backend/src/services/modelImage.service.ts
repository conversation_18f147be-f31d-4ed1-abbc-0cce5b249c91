import { PrismaClient, ModelImage, Prisma } from '@prisma/client';
import { v4 as uuidv4 } from 'uuid';
import path from 'path';
import fs from 'fs';
import { prisma } from '..';

const UPLOAD_DIR = path.join(process.cwd(), 'uploads', 'models');

// Ensure upload directory exists
if (!fs.existsSync(UPLOAD_DIR)) {
  fs.mkdirSync(UPLOAD_DIR, { recursive: true });
}

export class ModelImageService {
  /**
   * Upload a new model image
   */
  async uploadModelImage(
    userId: string,
    file: Express.Multer.File,
    metadata: {
      modelName?: string;
      gender?: string;
      bodyType?: string;
      poseType?: string;
      ethnicity?: string;
    } = {}
  ): Promise<ModelImage> {
    const fileExt = path.extname(file.originalname);
    const fileName = `${uuidv4()}${fileExt}`;
    const filePath = path.join('models', fileName);
    const fullPath = path.join(UPLOAD_DIR, fileName);

    // Move the file to the uploads directory
    await fs.promises.rename(file.path, fullPath);

    // Create database record
    return prisma.modelImage.create({
      data: {
        userId,
        imagePath: filePath,
        imageUrl: `/uploads/${filePath}`,
        modelName: metadata.modelName || 'My Model',
        gender: metadata.gender,
        bodyType: metadata.bodyType,
        poseType: metadata.poseType,
        ethnicity: metadata.ethnicity,
      },
    });
  }

  /**
   * Get model images for a user
   */
  async getUserModels(userId: string): Promise<ModelImage[]> {
    return prisma.modelImage.findMany({
      where: {
        userId,
        isDefault: false,
      },
      orderBy: { createdAt: 'desc' },
    });
  }

  /**
   * Get admin/default model images
   */
  async getAdminModels(): Promise<ModelImage[]> {
    return prisma.modelImage.findMany({
      where: {
        OR: [
          { isDefault: true },
          {
            user: {
              role: 'ADMIN',
            },
          },
        ],
      },
      include: {
        user: {
          select: {
            id: true,
            email: true,
            role: true,
          },
        },
      },
      orderBy: [
        { isDefault: 'desc' },
        { usageCount: 'desc' },
        { createdAt: 'desc' },
      ],
    });
  }

  /**
   * Delete a user's model image
   */
  async deleteModelImage(
    modelId: string,
    userId: string
  ): Promise<{ success: boolean; message: string }> {
    // Check if model exists and belongs to user
    const model = await prisma.modelImage.findFirst({
      where: {
        id: modelId,
        userId,
        isDefault: false, // Prevent deleting default models
      },
    });

    if (!model) {
      throw new Error('Model not found or insufficient permissions');
    }

    // Check if model is in use
    const inUse = await prisma.virtualTryOn.findFirst({
      where: {
        modelImageId: modelId,
        status: {
          in: ['PENDING', 'PROCESSING'],
        },
      },
    });

    if (inUse) {
      throw new Error('Cannot delete model currently in use');
    }

    // Delete the file
    const fullPath = path.join(process.cwd(), 'uploads', model.imagePath);
    if (fs.existsSync(fullPath)) {
      await fs.promises.unlink(fullPath);
    }

    // Delete the database record
    await prisma.modelImage.delete({
      where: { id: modelId },
    });

    return { success: true, message: 'Model deleted successfully' };
  }

  /**
   * Increment usage count for a model
   */
  async incrementUsage(modelId: string): Promise<void> {
    await prisma.modelImage.update({
      where: { id: modelId },
      data: {
        usageCount: { increment: 1 },
      },
    });
  }
}

export const modelImageService = new ModelImageService();
