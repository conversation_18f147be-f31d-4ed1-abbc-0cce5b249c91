import { PrismaClient, RefundStatus, Prisma } from "@prisma/client";
import { AppError } from "../utils/error";
import { PaymentService } from "./payment.service";
import { SubscriptionService } from "./subscription.service";
import { CreditService } from "./credit.service";

const prisma = new PrismaClient();

export class RefundService {
  private paymentService: PaymentService;
  private subscriptionService: SubscriptionService;
  private creditService: CreditService;

  constructor() {
    this.paymentService = new PaymentService();
    this.subscriptionService = new SubscriptionService();
    this.creditService = new CreditService();
  }

  /**
   * Logs refund-related events with structured data
   */
  private logRefundEvent(eventType: string, data: any, error?: any) {
    const logData = {
      timestamp: new Date().toISOString(),
      event: eventType,
      ...data,
    };

    if (error) {
      console.error(`REFUND_EVENT: ${eventType}`, { ...logData, error });
    } else {
      console.log(`REFUND_EVENT: ${eventType}`, logData);
    }
  }

  /**
   * Request a refund for a subscription (14-day money-back guarantee)
   */
  async requestSubscriptionRefund(userId: string, subscriptionId: string) {
    // Start a transaction to ensure data consistency
    return prisma.$transaction(
      async (tx) => {
        try {
          this.logRefundEvent("REFUND_REQUEST_INITIATED", {
            userId,
            subscriptionId,
          });

          // Get the user subscription
          const subscription = await tx.userSubscription.findFirst({
            where: {
              id: subscriptionId,
              userId: userId,
            },
            include: {
              plan: true,
            },
          });

          if (!subscription) {
            const error = new AppError(
              "Subscription not found",
              404,
              "SUBSCRIPTION_NOT_FOUND"
            );
            this.logRefundEvent(
              "REFUND_REQUEST_FAILED",
              { userId, subscriptionId },
              error
            );
            throw error;
          }

          // Check if the subscription is within the 14-day money-back guarantee period
          const subscriptionStartDate = new Date(subscription.startDate);
          const now = new Date();
          const daysSinceSubscription = Math.floor(
            (now.getTime() - subscriptionStartDate.getTime()) /
              (1000 * 60 * 60 * 24)
          );

          if (daysSinceSubscription > 14) {
            const error = new AppError(
              "Subscription is outside the 14-day money-back guarantee period",
              400,
              "REFUND_PERIOD_EXPIRED"
            );
            this.logRefundEvent(
              "REFUND_REQUEST_FAILED",
              {
                userId,
                subscriptionId,
                daysSinceSubscription,
              },
              error
            );
            throw error;
          }

          // Find the most recent payment for this subscription
          // First try to find payment with metadata containing subscriptionId
          let payment = await tx.payment.findFirst({
            where: {
              userId: userId,
              paymentType: "SUBSCRIPTION",
              metadata: {
                path: ["subscriptionId"],
                equals: subscriptionId,
              },
            },
            orderBy: {
              createdAt: "desc",
            },
          });

          // If no payment found with metadata, try to find any subscription payment for this user
          // This is a fallback for subscriptions created before we started storing subscriptionId in metadata
          if (!payment) {
            this.logRefundEvent("REFUND_SEARCH_FALLBACK", {
              userId,
              subscriptionId,
              message:
                "No payment found with subscription metadata, trying fallback search",
            });

            // Find the most recent subscription payment for this user
            payment = await tx.payment.findFirst({
              where: {
                userId: userId,
                paymentType: "SUBSCRIPTION",
              },
              orderBy: {
                createdAt: "desc",
              },
            });
          }

          if (!payment) {
            const error = new AppError(
              "No payment found for this subscription",
              404,
              "PAYMENT_NOT_FOUND"
            );
            this.logRefundEvent(
              "REFUND_REQUEST_FAILED",
              { userId, subscriptionId },
              error
            );
            throw error;
          }

          this.logRefundEvent("REFUND_PAYMENT_FOUND", {
            userId,
            subscriptionId,
            paymentId: payment.id,
            paymentAmount: payment.amount,
            paymentDate: payment.createdAt,
          });

          // Check if a refund request already exists
          const existingRefund = await tx.refund.findFirst({
            where: {
              paymentId: payment.id,
            },
          });

          if (existingRefund) {
            const error = new AppError(
              `A refund request already exists with status: ${existingRefund.status}`,
              400,
              "REFUND_EXISTS"
            );
            this.logRefundEvent(
              "REFUND_REQUEST_FAILED",
              {
                userId,
                subscriptionId,
                existingRefundId: existingRefund.id,
                existingRefundStatus: existingRefund.status,
              },
              error
            );
            throw error;
          }

          // Create a refund request
          const refund = await tx.refund.create({
            data: {
              userId: userId,
              paymentId: payment.id,
              amount: payment.amount,
              reason: "14-day money-back guarantee",
              status: RefundStatus.PENDING,
              paymentProvider: payment.provider,
              paymentIntentId: payment.paymentIntentId,
              paypalOrderId: payment.orderId,
            },
          });

          // Cancel the subscription
          await this.subscriptionService.cancelSubscriptionWithTransaction(
            userId,
            subscriptionId,
            tx
          );

          this.logRefundEvent("REFUND_REQUEST_CREATED", {
            userId,
            subscriptionId,
            refundId: refund.id,
            amount: payment.amount,
          });

          return refund;
        } catch (error) {
          if (error instanceof AppError) {
            throw error;
          }

          this.logRefundEvent(
            "REFUND_REQUEST_ERROR",
            { userId, subscriptionId },
            error
          );

          throw new AppError(
            "Failed to request subscription refund",
            500,
            "REFUND_REQUEST_FAILED"
          );
        }
      },
      {
        maxWait: 10000, // 10s max wait time
        timeout: 30000, // 30s timeout
        isolationLevel: Prisma.TransactionIsolationLevel.ReadCommitted,
      }
    );
  }

  /**
   * Process a pending refund (admin only)
   */
  async processRefund(
    refundId: string,
    approve: boolean,
    adminId: string,
    notes?: string,
    reason?: string
  ) {
    return prisma.$transaction(
      async (tx) => {
        try {
          this.logRefundEvent("REFUND_PROCESSING_INITIATED", {
            refundId,
            approve,
            adminId,
          });

          const refund = await tx.refund.findUnique({
            where: {
              id: refundId,
            },
            include: {
              payment: true,
              user: {
                select: {
                  id: true,
                  email: true,
                },
              },
            },
          });

          if (!refund) {
            const error = new AppError(
              "Refund not found",
              404,
              "REFUND_NOT_FOUND"
            );
            this.logRefundEvent(
              "REFUND_PROCESSING_FAILED",
              { refundId },
              error
            );
            throw error;
          }

          if (refund.status !== RefundStatus.PENDING) {
            const error = new AppError(
              `Refund has already been ${refund.status.toLowerCase()}`,
              400,
              "INVALID_REFUND_STATUS"
            );
            this.logRefundEvent(
              "REFUND_PROCESSING_FAILED",
              {
                refundId,
                currentStatus: refund.status,
              },
              error
            );
            throw error;
          }

          if (approve) {
            // Process the refund with Stripe or PayPal
            let refundResult;

            if (
              refund.payment?.provider === "STRIPE" &&
              refund.payment?.transactionId
            ) {
              try {
                refundResult = await this.paymentService.createStripeRefund(
                  refund.payment.transactionId,
                  refund.amount
                );

                this.logRefundEvent("STRIPE_REFUND_PROCESSED", {
                  refundId,
                  paymentId: refund.payment.id,
                  stripeRefundId: refundResult.id,
                });
              } catch (error) {
                this.logRefundEvent(
                  "STRIPE_REFUND_FAILED",
                  {
                    refundId,
                    paymentId: refund.payment.id,
                  },
                  error
                );
                throw error;
              }
            } else if (
              refund.payment?.provider === "PAYPAL" &&
              refund.payment?.orderId
            ) {
              try {
                refundResult = await this.paymentService.createPayPalRefund(
                  refund.payment.orderId,
                  refund.amount
                );

                this.logRefundEvent("PAYPAL_REFUND_PROCESSED", {
                  refundId,
                  paymentId: refund.payment.id,
                  paypalRefundId: refundResult.id,
                });
              } catch (error) {
                this.logRefundEvent(
                  "PAYPAL_REFUND_FAILED",
                  {
                    refundId,
                    paymentId: refund.payment.id,
                  },
                  error
                );
                throw error;
              }
            }

            // If subscription refund, handle credit adjustments
            if (refund.payment?.paymentType === "SUBSCRIPTION") {
              // Remove any credits granted from the subscription
              const subscription = await tx.userSubscription.findFirst({
                where: {
                  id:
                    refund.payment &&
                    typeof refund.payment === "object" &&
                    "subscriptionId" in refund.payment
                      ? String(refund.payment.subscriptionId)
                      : "",
                  userId: refund.userId,
                },
                include: {
                  plan: true,
                },
              });

              // Extract credits from plan features if available
              let creditsToRemove = 0;
              if (
                subscription?.plan?.features &&
                typeof subscription.plan.features === "object" &&
                subscription.plan.features !== null &&
                "credits" in subscription.plan.features
              ) {
                creditsToRemove = Number(subscription.plan.features.credits);

                // Log the credit adjustment
                this.logRefundEvent("SUBSCRIPTION_REFUND_CREDIT_ADJUSTMENT", {
                  refundId,
                  userId: refund.userId,
                  creditsToRemove,
                });

                // Remove the credits
                await this.creditService.adjustCreditBalanceWithTransaction(
                  refund.userId,
                  -creditsToRemove,
                  "Subscription refund - credits removed",
                  tx
                );
              }
            }

            // Update refund status
            await tx.refund.update({
              where: {
                id: refundId,
              },
              data: {
                status: RefundStatus.APPROVED,
                processedDate: new Date(),
                processedBy: adminId,
                notes:
                  notes ||
                  `Refund approved and processed by admin ID: ${adminId}`,
              },
            });
          } else {
            // Reject the refund
            await tx.refund.update({
              where: {
                id: refundId,
              },
              data: {
                status: RefundStatus.REJECTED,
                processedDate: new Date(),
                processedBy: adminId,
                notes:
                  reason || `Refund request rejected by admin ID: ${adminId}`,
              },
            });

            this.logRefundEvent("REFUND_REJECTED", {
              refundId,
              adminId,
            });
          }

          const updatedRefund = await tx.refund.findUnique({
            where: {
              id: refundId,
            },
            include: {
              user: {
                select: {
                  email: true,
                },
              },
            },
          });

          this.logRefundEvent("REFUND_PROCESSING_COMPLETED", {
            refundId,
            status: updatedRefund?.status,
            userEmail: updatedRefund?.user?.email,
          });

          return updatedRefund;
        } catch (error) {
          if (error instanceof AppError) {
            throw error;
          }

          this.logRefundEvent(
            "REFUND_PROCESSING_ERROR",
            { refundId, approve },
            error
          );

          throw new AppError(
            "Failed to process refund",
            500,
            "REFUND_PROCESSING_FAILED"
          );
        }
      },
      {
        maxWait: 10000, // 10s max wait time
        timeout: 30000, // 30s timeout
        isolationLevel: Prisma.TransactionIsolationLevel.ReadCommitted,
      }
    );
  }

  /**
        include: {
          payment: true,
          user: {
            select: {
              id: true,
              email: true,
              profile: {
                select: {
                  firstName: true,
                  lastName: true,
                },
              },
            },
          },
        },
        orderBy: {
          createdAt: "desc",
        },
        skip,
        take: limit,
      }),
      prisma.refund.count({ where }),
    ]);

    return {
      data: refunds,
      meta: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
      },
    };
  } catch (error) {
    console.error("Error getting all refunds:", error);
    throw new AppError("Failed to get refunds", 500);
  }
}

/**
 * Get a specific refund by ID
 */
  async getRefundById(refundId: string) {
    try {
      const refund = await prisma.refund.findUnique({
        where: { id: refundId },
        include: {
          payment: true,
          user: {
            select: {
              id: true,
              email: true,
            },
          },
        },
      });

      if (!refund) {
        throw new AppError("Refund not found", 404);
      }

      return refund;
    } catch (error) {
      console.error("Error getting refund by ID:", error);
      if (error instanceof AppError) {
        throw error;
      }
      throw new AppError("Failed to get refund", 500);
    }
  }

  /**
   * Get user's refund requests
   */
  async getUserRefunds(userId: string) {
    try {
      const refunds = await prisma.refund.findMany({
        where: {
          userId,
        },
        include: {
          payment: {
            select: {
              amount: true,
              provider: true,
              paymentType: true,
              createdAt: true,
            },
          },
        },
        orderBy: {
          createdAt: "desc",
        },
      });

      return refunds;
    } catch (error) {
      console.error("Error getting user refunds:", error);
      throw new AppError(
        "Failed to retrieve your refund requests",
        500,
        "USER_REFUND_RETRIEVAL_FAILED"
      );
    }
  }

  /**
   * Get all refund requests (admin only)
   */
  async getAllRefunds(page = 1, limit = 10, status?: RefundStatus) {
    try {
      const skip = (page - 1) * limit;

      const where = status ? { status } : {};

      const [refunds, total] = await Promise.all([
        prisma.refund.findMany({
          where,
          include: {
            payment: true,
            user: {
              select: {
                id: true,
                email: true,
                profile: {
                  select: {
                    firstName: true,
                    lastName: true,
                  },
                },
              },
            },
          },
          orderBy: {
            createdAt: "desc",
          },
          skip,
          take: limit,
        }),
        prisma.refund.count({ where }),
      ]);

      return {
        data: refunds,
        meta: {
          total,
          page,
          limit,
          totalPages: Math.ceil(total / limit),
        },
      };
    } catch (error) {
      console.error("Error getting all refunds:", error);
      throw new AppError("Failed to get refunds", 500);
    }
  }
}
