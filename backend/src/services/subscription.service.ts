import {
  PrismaClient,
  SubscriptionPlan,
  UserSubscription,
  Prisma,
} from "@prisma/client";
import { AppError } from "../utils/error";
import { CreditService } from "./credit.service";
// import { Stripe } from "stripe";

// const stripe = new Stripe(process.env.STRIPE_SECRET_KEY as string);
const prisma = new PrismaClient();
const creditService = new CreditService();

export class SubscriptionService {
  /**
   * Get all subscription plans
   */
  async getSubscriptionPlans() {
    try {
      const plans = await prisma.subscriptionPlan.findMany({
        orderBy: { sortOrder: "asc" },
      });
      return plans;
    } catch (error) {
      console.error("Error getting subscription plans:", error);
      throw new AppError("Failed to get subscription plans", 500);
    }
  }

  /**
   * Get a specific subscription plan by ID
   */
  async getSubscriptionPlanById(id: string) {
    try {
      const plan = await prisma.subscriptionPlan.findUnique({
        where: { id },
      });
      if (!plan) {
        throw new AppError("Subscription plan not found", 404);
      }
      return plan;
    } catch (error) {
      console.error("Error getting subscription plan:", error);
      if (error instanceof AppError) {
        throw error;
      }
      throw new AppError("Failed to get subscription plan", 500);
    }
  }

  /**
   * Get a user's current subscription
   */
  async getUserSubscription(userId: string) {
    try {
      const subscription = await prisma.userSubscription.findFirst({
        where: {
          userId,
          status: {
            in: ["ACTIVE", "TRIALING"],
          },
        },
        include: {
          plan: true,
        },
        orderBy: {
          createdAt: "desc",
        },
      });
      return subscription;
    } catch (error) {
      console.error("Error getting user subscription:", error);
      throw new AppError("Failed to get user subscription", 500);
    }
  }

  /**
   * Create a new subscription plan (admin only)
   */
  async createSubscriptionPlan(
    data: Omit<SubscriptionPlan, "id" | "createdAt" | "updatedAt">
  ) {
    try {
      const plan = await prisma.subscriptionPlan.create({
        data: {
          ...data,
          isFeatured: data.isFeatured || false,
          isActive: data.isActive || true,
          features: data.features || {},
          featureHighlights: data.featureHighlights || [],
        },
      });
      return plan;
    } catch (error) {
      console.error("Error creating subscription plan:", error);
      throw new AppError("Failed to create subscription plan", 500);
    }
  }

  /**
   * Update an existing subscription plan (admin only)
   */
  async updateSubscriptionPlan(
    id: string,
    data: Partial<Omit<SubscriptionPlan, "id" | "createdAt" | "updatedAt">>
  ) {
    try {
      const plan = await prisma.subscriptionPlan.update({
        where: { id },
        data: {
          ...data,
          features: data.features || {},
          featureHighlights: data.featureHighlights || [],
        },
      });
      return plan;
    } catch (error) {
      console.error("Error updating subscription plan:", error);
      throw new AppError("Failed to update subscription plan", 500);
    }
  }

  /**
   * Delete a subscription plan (admin only)
   * This will only soft-delete by setting isActive to false
   */
  async deleteSubscriptionPlan(id: string) {
    try {
      // Check if plan is in use by any active subscriptions
      const activeSubscriptions = await prisma.userSubscription.findMany({
        where: {
          planId: id,
          status: {
            in: ["ACTIVE", "TRIALING"],
          },
        },
      });

      if (activeSubscriptions.length > 0) {
        throw new AppError(
          "Cannot delete plan with active subscriptions. Deactivate it instead.",
          400
        );
      }

      // Soft delete by setting isActive to false
      const plan = await prisma.subscriptionPlan.update({
        where: { id },
        data: {
          isActive: false,
        },
      });
      return plan;
    } catch (error) {
      console.error("Error deleting subscription plan:", error);
      if (error instanceof AppError) {
        throw error;
      }
      throw new AppError("Failed to delete subscription plan", 500);
    }
  }

  /**
   * Get feature comparison data for all plans
   */
  async getFeatureComparisonData(userId?: string) {
    try {
      // Get all active plans
      const plans = await prisma.subscriptionPlan.findMany({
        where: { isActive: true },
        orderBy: { sortOrder: "asc" },
      });

      // Get user's current plan if userId is provided
      let currentPlanId: string | null = null;
      if (userId) {
        const userSubscription = await this.getUserSubscription(userId);
        if (userSubscription) {
          currentPlanId = userSubscription.planId;
        }
      }

      // Extract all unique feature keys from all plans
      const allFeatureKeys = new Set<string>();
      plans.forEach((plan) => {
        if (plan.features) {
          Object.keys(plan.features as Record<string, any>).forEach((key) => {
            allFeatureKeys.add(key);
          });
        }
      });

      // Group features into categories
      const featureCategories = [
        {
          name: "Content Generation",
          features: [
            "videoGenerationQuota",
            "imageGenerationQuota",
            "backgroundRemovalQuota",
          ],
        },
        {
          name: "Performance",
          features: ["processingPriority", "uploadSpeed", "concurrentJobs"],
        },
        {
          name: "Advanced Features",
          features: ["advancedAI", "customTemplates", "apiAccess"],
        },
        {
          name: "Support & Collaboration",
          features: ["supportLevel", "teamMembers", "sharedWorkspaces"],
        },
        {
          name: "Storage & History",
          features: ["storageLimit", "assetLibrary", "historyRetention"],
        },
      ];

      // Add any remaining features to a "Other Features" category
      const categorizedFeatures = featureCategories.flatMap(
        (cat) => cat.features
      );
      const otherFeatures = Array.from(allFeatureKeys).filter(
        (key) => !categorizedFeatures.includes(key)
      );

      if (otherFeatures.length > 0) {
        featureCategories.push({
          name: "Other Features",
          features: otherFeatures,
        });
      }

      return {
        plans,
        currentPlanId,
        featureCategories,
      };
    } catch (error) {
      console.error("Error getting feature comparison data:", error);
      throw new AppError("Failed to get feature comparison data", 500);
    }
  }

  /**
   * Create a subscription for a user
   * This is called after payment is successful
   */
  async createSubscription(
    userId: string,
    planId: string,
    stripeSubscriptionId?: string,
    paypalSubscriptionId?: string,
    status: UserSubscription["status"] = "ACTIVE",
    currentPeriodEnd?: Date
  ) {
    try {
      // Get the plan
      const plan = await prisma.subscriptionPlan.findUnique({
        where: { id: planId },
      });

      if (!plan) {
        throw new AppError("Subscription plan not found", 404);
      }

      // Cancel any existing active subscriptions
      await prisma.userSubscription.updateMany({
        where: {
          userId,
          status: {
            in: ["ACTIVE", "TRIALING"],
          },
        },
        data: {
          status: "CANCELED",
          endDate: new Date(),
        },
      });

      // Create new subscription
      const subscription = await prisma.userSubscription.create({
        data: {
          userId,
          planId,
          stripeSubscriptionId,
          paypalSubscriptionId,
          status,
          startDate: new Date(),
          currentPeriodEnd:
            currentPeriodEnd || new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // Default to 30 days
        },
      });

      // Update credit expiry for the user (remove expiry for active subscribers)
      if (status === "ACTIVE" || status === "TRIALING") {
        await creditService.updateCreditExpiryForUser(userId);
      }

      return subscription;
    } catch (error) {
      console.error("Error creating subscription:", error);
      if (error instanceof AppError) {
        throw error;
      }
      throw new AppError("Failed to create subscription", 500);
    }
  }

  /**
   * Update a subscription status
   */
  async updateSubscriptionStatus(
    subscriptionId: string,
    status: UserSubscription["status"],
    endDate?: Date
  ) {
    try {
      const subscription = await prisma.userSubscription.update({
        where: { id: subscriptionId },
        data: {
          status,
          endDate: status === "CANCELED" ? endDate || new Date() : undefined,
        },
        include: { user: true },
      });

      // Update credit expiry based on new subscription status
      if (subscription.user) {
        await creditService.updateCreditExpiryForUser(subscription.user.id);
      }

      return subscription;
    } catch (error) {
      console.error("Error updating subscription status:", error);
      throw new AppError("Failed to update subscription status", 500);
    }
  }

  /**
   * Cancel a subscription
   */
  async cancelSubscription(userId: string, subscriptionId: string) {
    try {
      const subscription = await prisma.userSubscription.update({
        where: {
          id: subscriptionId,
          userId: userId,
        },
        data: {
          status: "CANCELED",
          endDate: new Date(),
        },
        include: { user: true },
      });

      // When subscription is canceled, update credit expiry (set expiry date for credits)
      if (subscription.user) {
        // We don't update immediately because subscription is still valid until end of billing period
        // We'll update when the subscription actually ends
        console.log(
          `Subscription canceled for user ${subscription.user.id}, credits will expire when subscription ends`
        );
      }

      return subscription;
    } catch (error) {
      console.error("Error canceling subscription:", error);
      throw new AppError("Failed to cancel subscription", 500);
    }
  }

  /**
   * Cancel a subscription with transaction support
   */
  async cancelSubscriptionWithTransaction(
    userId: string,
    subscriptionId: string,
    tx: Prisma.TransactionClient
  ) {
    try {
      // const stripeCancelSubscription = await stripe.subscriptions.cancel(
      //   subscriptionId
      // );
      // console.log("stripeCancelSubscription", stripeCancelSubscription);
      const subscription = await tx.userSubscription.update({
        where: {
          id: subscriptionId,
          userId: userId,
        },
        data: {
          status: "CANCELED",
          endDate: new Date(),
        },
        include: { user: true },
      });

      // When subscription is canceled, update credit expiry (set expiry date for credits)
      if (subscription.user) {
        // We don't update immediately because subscription is still valid until end of billing period
        // We'll update when the subscription actually ends
        console.log(
          `Subscription canceled for user ${subscription.user.id}, credits will expire when subscription ends`
        );
      }

      return subscription;
    } catch (error) {
      console.error("Error canceling subscription with transaction:", error);
      throw new AppError("Failed to cancel subscription", 500);
    }
  }

  /**
   * Upgrade a subscription to a higher tier plan
   * This method handles the immediate upgrade process with proration
   */
  async upgradeSubscription(
    userId: string,
    currentSubscriptionId: string,
    newPlanId: string
  ) {
    try {
      // Get current subscription with plan details
      const currentSubscription = await prisma.userSubscription.findFirst({
        where: {
          id: currentSubscriptionId,
          userId: userId,
          status: {
            in: ["ACTIVE", "TRIALING"],
          },
        },
        include: {
          plan: true,
        },
      });

      if (!currentSubscription) {
        throw new AppError(
          "Current subscription not found or not active",
          404,
          "SUBSCRIPTION_NOT_FOUND"
        );
      }

      // Get new plan details
      const newPlan = await prisma.subscriptionPlan.findUnique({
        where: { id: newPlanId },
      });

      if (!newPlan) {
        throw new AppError(
          "New subscription plan not found",
          404,
          "PLAN_NOT_FOUND"
        );
      }

      // Verify this is actually an upgrade (higher price)
      if (newPlan.price <= currentSubscription.plan.price) {
        throw new AppError(
          "The selected plan is not an upgrade. Please use downgrade method instead.",
          400,
          "INVALID_UPGRADE"
        );
      }

      // Calculate remaining days in current subscription period
      const now = new Date();
      const currentPeriodEnd = new Date(currentSubscription.currentPeriodEnd);
      const remainingDays = Math.max(
        0,
        Math.ceil(
          (currentPeriodEnd.getTime() - now.getTime()) / (1000 * 60 * 60 * 24)
        )
      );
      const totalDays =
        currentSubscription.plan.interval === "monthly" ? 30 : 365;

      // Calculate prorated amount for remaining days
      const unusedAmount =
        (currentSubscription.plan.price * remainingDays) / totalDays;

      // Calculate amount to charge for upgrade (new plan price - unused amount from current plan)
      const upgradeAmount = Math.max(0, newPlan.price - unusedAmount);

      // Perform upgrade in a transaction
      return prisma.$transaction(async (tx) => {
        // Cancel current subscription
        await tx.userSubscription.update({
          where: { id: currentSubscriptionId },
          data: {
            status: "CANCELED",
            endDate: now,
          },
        });

        // Create new subscription
        const newSubscription = await tx.userSubscription.create({
          data: {
            userId,
            planId: newPlanId,
            stripeSubscriptionId: currentSubscription.stripeSubscriptionId,
            paypalSubscriptionId: currentSubscription.paypalSubscriptionId,
            status: "ACTIVE",
            startDate: now,
            currentPeriodEnd: currentPeriodEnd, // Keep the same billing cycle
          },
          include: {
            plan: true,
          },
        });

        // Handle credit adjustments if needed
        if (
          newPlan.features &&
          typeof newPlan.features === "object" &&
          currentSubscription.plan.features &&
          typeof currentSubscription.plan.features === "object"
        ) {
          // Calculate credit difference between plans if credits are part of the plan features
          if (
            "credits" in newPlan.features &&
            "credits" in currentSubscription.plan.features
          ) {
            const newCredits = Number(newPlan.features.credits);
            const oldCredits = Number(
              currentSubscription.plan.features.credits
            );
            const creditDifference = newCredits - oldCredits;

            if (creditDifference > 0) {
              // Add the additional credits from the upgrade
              await creditService.addCredits(
                userId,
                creditDifference,
                "Subscription upgrade - additional credits",
                "PAID", // Use PAID instead of SUBSCRIPTION
                tx
              );
            }
          }
        }

        return {
          subscription: newSubscription,
          prorationDetails: {
            remainingDays,
            unusedAmount,
            upgradeAmount,
          },
        };
      });
    } catch (error) {
      console.error("Error upgrading subscription:", error);
      if (error instanceof AppError) {
        throw error;
      }
      throw new AppError(
        "Failed to upgrade subscription",
        500,
        "UPGRADE_FAILED"
      );
    }
  }

  /**
   * Downgrade a subscription to a lower tier plan
   * This method schedules the downgrade to take effect at the end of the current billing period
   */
  async downgradeSubscription(
    userId: string,
    currentSubscriptionId: string,
    newPlanId: string
  ) {
    try {
      // Get current subscription with plan details
      const currentSubscription = await prisma.userSubscription.findFirst({
        where: {
          id: currentSubscriptionId,
          userId: userId,
          status: {
            in: ["ACTIVE", "TRIALING"],
          },
        },
        include: {
          plan: true,
        },
      });

      if (!currentSubscription) {
        throw new AppError(
          "Current subscription not found or not active",
          404,
          "SUBSCRIPTION_NOT_FOUND"
        );
      }

      // Get new plan details
      const newPlan = await prisma.subscriptionPlan.findUnique({
        where: { id: newPlanId },
      });

      if (!newPlan) {
        throw new AppError(
          "New subscription plan not found",
          404,
          "PLAN_NOT_FOUND"
        );
      }

      // Verify this is actually a downgrade (lower price)
      if (newPlan.price >= currentSubscription.plan.price) {
        throw new AppError(
          "The selected plan is not a downgrade. Please use upgrade method instead.",
          400,
          "INVALID_DOWNGRADE"
        );
      }

      // Use a transaction to ensure data consistency
      return prisma.$transaction(async (tx) => {
        // Cancel any existing scheduled plan changes for this subscription
        const existingScheduledChanges = await tx.scheduledPlanChange.findMany({
          where: {
            userId,
            currentSubscriptionId,
            status: "SCHEDULED",
          },
        });

        if (existingScheduledChanges.length > 0) {
          // Cancel all existing scheduled changes
          await tx.scheduledPlanChange.updateMany({
            where: {
              userId,
              currentSubscriptionId,
              status: "SCHEDULED",
            },
            data: {
              status: "CANCELLED",
              processedDate: new Date(),
            },
          });
        }

        // Create a new scheduled downgrade record
        const scheduledDowngrade = await tx.scheduledPlanChange.create({
          data: {
            userId,
            currentSubscriptionId,
            newPlanId,
            effectiveDate: new Date(currentSubscription.currentPeriodEnd),
            changeType: "DOWNGRADE",
            status: "SCHEDULED",
          },
          include: {
            currentSubscription: {
              include: {
                plan: true,
              },
            },
            newPlan: true,
          },
        });

        return {
          scheduledDowngrade,
          message: `Your subscription will be downgraded to ${
            newPlan.name
          } at the end of your current billing period on ${new Date(
            currentSubscription.currentPeriodEnd
          ).toLocaleDateString()}.`,
        };
      });
    } catch (error) {
      console.error("Error downgrading subscription:", error);
      if (error instanceof AppError) {
        throw error;
      }
      throw new AppError(
        "Failed to downgrade subscription",
        500,
        "DOWNGRADE_FAILED"
      );
    }
  }

  /**
   * Process scheduled plan changes that are due
   * This should be called by a scheduled job/cron task
   */
  async processScheduledPlanChanges() {
    try {
      const now = new Date();

      // Find all scheduled changes that are due
      const scheduledChanges = await prisma.scheduledPlanChange.findMany({
        where: {
          effectiveDate: {
            lte: now,
          },
          status: "SCHEDULED",
        },
        include: {
          currentSubscription: true,
          newPlan: true,
        },
      });

      const results = [];

      for (const change of scheduledChanges) {
        try {
          // Process the change in a transaction
          const result = await prisma.$transaction(async (tx) => {
            // Cancel current subscription
            await tx.userSubscription.update({
              where: { id: change.currentSubscriptionId },
              data: {
                status: "CANCELED",
                endDate: now,
              },
            });

            // Get the plan interval from the new plan
            const newPlanDetails = await tx.subscriptionPlan.findUnique({
              where: { id: change.newPlanId },
            });

            if (!newPlanDetails) {
              throw new AppError("New plan not found", 404);
            }

            // Calculate the new period end date based on the plan interval
            const intervalDays =
              newPlanDetails.interval === "monthly" ? 30 : 365;
            const newPeriodEnd = new Date(
              now.getTime() + intervalDays * 24 * 60 * 60 * 1000
            );

            // Create new subscription
            const newSubscription = await tx.userSubscription.create({
              data: {
                userId: change.userId,
                planId: change.newPlanId,
                stripeSubscriptionId:
                  change.currentSubscription.stripeSubscriptionId,
                paypalSubscriptionId:
                  change.currentSubscription.paypalSubscriptionId,
                status: "ACTIVE",
                startDate: now,
                currentPeriodEnd: newPeriodEnd,
              },
              include: {
                plan: true,
              },
            });

            // Update the scheduled change status
            await tx.scheduledPlanChange.update({
              where: { id: change.id },
              data: {
                status: "COMPLETED",
                processedDate: now,
              },
            });

            // Get the old plan name
            const oldPlanDetails = await tx.subscriptionPlan.findUnique({
              where: { id: change.currentSubscription.planId },
            });

            return {
              userId: change.userId,
              changeType: change.changeType,
              oldPlan: oldPlanDetails?.name || "Unknown Plan",
              newPlan: change.newPlan.name,
              newSubscriptionId: newSubscription.id,
            };
          });

          results.push({
            success: true,
            ...result,
          });
        } catch (error) {
          console.error(
            `Error processing scheduled change ${change.id}:`,
            error
          );

          // Mark the change as failed
          await prisma.scheduledPlanChange.update({
            where: { id: change.id },
            data: {
              status: "FAILED",
              processedDate: now,
              errorMessage:
                error instanceof Error ? error.message : "Unknown error",
            },
          });

          results.push({
            success: false,
            userId: change.userId,
            changeId: change.id,
            error: error instanceof Error ? error.message : "Unknown error",
          });
        }
      }

      return results;
    } catch (error) {
      console.error("Error processing scheduled plan changes:", error);
      throw new AppError("Failed to process scheduled plan changes", 500);
    }
  }

  /**
   * Cancel a scheduled plan change
   */
  async cancelScheduledPlanChange(userId: string, changeId: string) {
    try {
      // Find the scheduled change
      const scheduledChange = await prisma.scheduledPlanChange.findFirst({
        where: {
          id: changeId,
          userId: userId,
          status: "SCHEDULED",
        },
      });

      if (!scheduledChange) {
        throw new AppError(
          "Scheduled plan change not found or already processed",
          404,
          "CHANGE_NOT_FOUND"
        );
      }

      // Cancel the scheduled change
      await prisma.scheduledPlanChange.update({
        where: { id: changeId },
        data: {
          status: "CANCELLED",
          processedDate: new Date(),
        },
      });

      return {
        message: "Scheduled plan change has been cancelled.",
      };
    } catch (error) {
      console.error("Error cancelling scheduled plan change:", error);
      if (error instanceof AppError) {
        throw error;
      }
      throw new AppError("Failed to cancel scheduled plan change", 500);
    }
  }
}
