// import { PrismaClient, User } from "@prisma/client";
// import { AppError } from "../utils/error";
// import * as paypal from "@paypal/paypal-server-sdk";

// // Define PayPal environment types
// type PayPalEnvironment = {
//   clientId: string;
//   clientSecret: string;
//   baseUrl?: string;
// };

// // Define PayPal order resource type
// type PayPalOrderResource = {
//   id: string;
//   status: string;
//   supplementary_data?: {
//     related_ids?: {
//       order_id?: string;
//     }
//   };
//   related_ids?: {
//     order_id?: string;
//   };
// };

// const prisma = new PrismaClient();

// export class PayPalService {
//   private paypalClient: any;

//   constructor() {
//     // Initialize PayPal client
//     let environment: PayPalEnvironment;
//     if (process.env.NODE_ENV === "production") {
//       environment = {
//         clientId: process.env.PAYPAL_CLIENT_ID || "",
//         clientSecret: process.env.PAYPAL_CLIENT_SECRET || ""
//       };
//     } else {
//       environment = {
//         clientId: process.env.PAYPAL_CLIENT_ID || "",
//         clientSecret: process.env.PAYPAL_CLIENT_SECRET || ""
//       };
//     }

//     // Initialize PayPal client with environment
//     this.paypalClient = new (paypal as any).core.PayPalHttpClient(
//       process.env.NODE_ENV === "production"
//         ? new (paypal as any).core.LiveEnvironment(environment.clientId, environment.clientSecret)
//         : new (paypal as any).core.SandboxEnvironment(environment.clientId, environment.clientSecret)
//     );
//   }

//   /**
//    * Create a PayPal order for credit purchase
//    */
//   async createOrder(user: User, amount: number, credits: number) {
//     try {
//       // Create PayPal order
//       const request = new (paypal as any).orders.OrdersCreateRequest();
//       request.prefer("return=representation");
//       request.requestBody({
//         intent: "CAPTURE",
//         purchase_units: [{
//           amount: {
//             currency_code: "USD",
//             value: amount.toString(),
//             breakdown: {
//               item_total: {
//                 currency_code: "USD",
//                 value: amount.toString()
//               }
//             }
//           },
//           description: `Purchase ${credits} credits for Miragic-AI`,
//           custom_id: user.id,
//           items: [{
//             name: `${credits} Credits`,
//             description: `Purchase ${credits} credits for Miragic-AI`,
//             unit_amount: {
//               currency_code: "USD",
//               value: amount.toString()
//             },
//             quantity: "1",
//             category: "DIGITAL_GOODS"
//           }]
//         }],
//         application_context: {
//           brand_name: "Miragic-AI",
//           user_action: "PAY_NOW",
//           return_url: `${process.env.FRONTEND_URL}/payment/success`,
//           cancel_url: `${process.env.FRONTEND_URL}/payment/cancel`
//         }
//       });

//       // Execute request
//       const response = await this.paypalClient.execute(request);

//       // Store order in database
//       await prisma.paypalOrder.create({
//         data: {
//           orderId: response.result.id,
//           userId: user.id,
//           amount: amount,
//           credits: credits,
//           status: "CREATED"
//         }
//       });

//       return {
//         orderId: response.result.id,
//         status: response.result.status,
//         links: response.result.links
//       };
//     } catch (error: any) {
//       console.error("Error creating PayPal order:", error);
//       throw new AppError("Failed to create PayPal order", 500);
//     }
//   }

//   /**
//    * Capture a PayPal order after approval
//    */
//   async captureOrder(orderId: string) {
//     try {
//       // Find the order in our database
//       const orderRecord = await prisma.paypalOrder.findUnique({
//         where: { orderId }
//       });

//       if (!orderRecord) {
//         throw new AppError("Order not found", 404);
//       }

//       // Capture the PayPal order
//       const request = new (paypal as any).orders.OrdersCaptureRequest(orderId);
//       request.requestBody({});
//       const capture = await this.paypalClient.execute(request);

//       if (capture.result.status === "COMPLETED") {
//         // Update order status
//         await prisma.paypalOrder.update({
//           where: { orderId },
//           data: { status: "COMPLETED" }
//         });

//         // Add credits to user
//         await prisma.user.update({
//           where: { id: orderRecord.userId },
//           data: { credit: orderRecord.credits }
//         });

//         // Record payment
//         await prisma.payment.create({
//           data: {
//             userId: orderRecord.userId,
//             amount: orderRecord.amount,
//             currency: "USD",
//             status: "COMPLETED",
//             provider: "PAYPAL",
//             transactionId: orderId
//           }
//         });

//         return {
//           success: true,
//           message: "Payment completed successfully",
//           credits: orderRecord.credits
//         };
//       } else {
//         throw new AppError("Payment not completed", 400);
//       }
//     } catch (error: any) {
//       console.error("Error capturing PayPal order:", error);
//       if (error instanceof AppError) {
//         throw error;
//       }
//       throw new AppError("Failed to capture PayPal order", 500);
//     }
//   }

//   /**
//    * Handle PayPal webhook events
//    */
//   async handleWebhook(event: any) {
//     try {
//       const eventType = event.event_type;

//       switch (eventType) {
//         case "PAYMENT.CAPTURE.COMPLETED":
//           await this.handlePaymentCompleted(event.resource);
//           break;
//         case "PAYMENT.CAPTURE.DENIED":
//         case "PAYMENT.CAPTURE.REFUNDED":
//           await this.handlePaymentFailed(event.resource);
//           break;
//         default:
//           console.log(`Unhandled PayPal webhook event: ${eventType}`);
//       }

//       return { received: true };
//     } catch (error) {
//       console.error("Error handling PayPal webhook:", error);
//       throw new AppError("Failed to handle PayPal webhook", 500);
//     }
//   }

//   /**
//    * Handle PayPal payment completed event
//    */
//   private async handlePaymentCompleted(resource: PayPalOrderResource) {
//     try {
//       const orderId = resource.supplementary_data?.related_ids?.order_id ||
//                       resource.related_ids?.order_id ||
//                       resource.id;
//       if (!orderId) return;

//       // Find the order in our database
//       const orderRecord = await prisma.paypalOrder.findUnique({
//         where: { orderId }
//       });

//       if (!orderRecord || orderRecord.status === "COMPLETED") return;

//       // Update order status
//       await prisma.paypalOrder.update({
//         where: { orderId },
//         data: { status: "COMPLETED" }
//       });

//       // Add credits to user
//       await prisma.user.update({
//         where: { id: orderRecord.userId },
//         data: { credit: orderRecord.credits }
//       });

//       // Record payment if not already recorded
//       const existingPayment = await prisma.payment.findFirst({
//         where: { transactionId: orderId }
//       });

//       if (!existingPayment) {
//         await prisma.payment.create({
//           data: {
//             userId: orderRecord.userId,
//             amount: orderRecord.amount,
//             currency: "USD",
//             status: "COMPLETED",
//             provider: "PAYPAL",
//             transactionId: orderId
//           }
//         });
//       }
//     } catch (error) {
//       console.error("Error handling PayPal payment completed:", error);
//     }
//   }

//   /**
//    * Handle PayPal payment failed event
//    */
//   private async handlePaymentFailed(resource: PayPalOrderResource) {
//     try {
//       const orderId = resource.supplementary_data?.related_ids?.order_id ||
//                       resource.related_ids?.order_id ||
//                       resource.id;
//       if (!orderId) return;

//       // Find the order in our database
//       const orderRecord = await prisma.paypalOrder.findUnique({
//         where: { orderId }
//       });

//       if (!orderRecord) return;

//       // Update order status
//       await prisma.paypalOrder.update({
//         where: { orderId },
//         data: { status: "FAILED" }
//       });

//       // Record failed payment
//       const existingPayment = await prisma.payment.findFirst({
//         where: { transactionId: orderId }
//       });

//       if (!existingPayment) {
//         await prisma.payment.create({
//           data: {
//             userId: orderRecord.userId,
//             amount: orderRecord.amount,
//             currency: "USD",
//             status: "FAILED",
//             provider: "PAYPAL",
//             transactionId: orderId
//           }
//         });
//       }
//     } catch (error) {
//       console.error("Error handling PayPal payment failed:", error);
//     }
//   }
// }

// export default new PayPalService();
