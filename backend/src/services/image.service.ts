import axios from "axios";
import {
  PrismaClient,
  User,
  GeneratedImageJob,
  JobStatus,
} from "@prisma/client";
import { AppError } from "../utils/error";

const prisma = new PrismaClient();

interface ImageGenerationParams {
  prompt: string;
  negativePrompt?: string;
  style?: string;
  aspectRatio?: string;
  count?: number;
  [key: string]: any; // Allow for other parameters
}

export class ImageService {
  private apiKey: string;
  private apiBaseUrl: string;

  constructor() {
    this.apiKey = process.env.STABILITY_AI_API_KEY || "";
    this.apiBaseUrl = "https://api.stability.ai/v1";

    if (!this.apiKey) {
      console.error("Stability AI API key is not set");
    }
  }

  /**
   * Create an image generation job
   */
  async createImageJob(
    user: User,
    params: ImageGenerationParams
  ): Promise<GeneratedImageJob> {
    try {
      // Check if user has active subscription or enough credits
      const hasAccess = await this.checkUserAccess(user.id);

      if (!hasAccess) {
        throw new AppError(
          "Insufficient credits or no active subscription",
          403
        );
      }

      // Create job record in database
      const job = await prisma.generatedImageJob.create({
        data: {
          userId: user.id,
          prompt: params.prompt,
          parameters: params as any, // Store all parameters
          status: JobStatus.PENDING,
        },
      });

      return job;
    } catch (error) {
      if (error instanceof AppError) {
        throw error;
      }
      throw new AppError("Failed to create image generation job", 500);
    }
  }

  /**
   * Process an image generation job
   */
  async processImageJob(jobId: string): Promise<GeneratedImageJob> {
    try {
      // Get job from database
      const job = await prisma.generatedImageJob.findUnique({
        where: { id: jobId },
        include: { user: true },
      });

      if (!job) {
        throw new AppError("Image generation job not found", 404);
      }

      // Update job status to processing
      await prisma.generatedImageJob.update({
        where: { id: jobId },
        data: { status: JobStatus.PROCESSING },
      });

      // Call Stability AI API to generate image
      const response = await this.callStabilityAIAPI(
        job.prompt,
        job.parameters
      );

      // Update job with image URL and status
      const updatedJob = await prisma.generatedImageJob.update({
        where: { id: jobId },
        data: {
          status: JobStatus.COMPLETED,
          imageUrl: response.imageUrl,
          publicId: response.imageId,
          resolution: response.resolution,
        },
      });

      // Log API usage
      await prisma.apiUsageLog.create({
        data: {
          requestTimestamp: new Date(),
          userId: job.userId,
          featureUsed: "IMAGE_GENERATION",
          creditsConsumed: this.calculateCreditsUsed(job.parameters),
          responseTimestamp: new Date(),
          status: "success",
        },
      });

      // Deduct credits if applicable
      await this.deductCredits(
        job.userId,
        this.calculateCreditsUsed(job.parameters)
      );

      return updatedJob;
    } catch (error) {
      // Update job status to failed
      await prisma.generatedImageJob.update({
        where: { id: jobId },
        data: {
          status: JobStatus.FAILED,
          errorMessage:
            error instanceof Error ? error.message : "Unknown error",
        },
      });

      if (error instanceof AppError) {
        throw error;
      }
      throw new AppError("Failed to process image generation job", 500);
    }
  }

  /**
   * Get an image generation job
   */
  async getImageJob(jobId: string, userId: string): Promise<GeneratedImageJob> {
    try {
      const job = await prisma.generatedImageJob.findUnique({
        where: { id: jobId },
      });

      if (!job) {
        throw new AppError("Image generation job not found", 404);
      }

      // Check if job belongs to user
      if (job.userId !== userId) {
        throw new AppError("Unauthorized access to image generation job", 403);
      }

      return job;
    } catch (error) {
      if (error instanceof AppError) {
        throw error;
      }
      throw new AppError("Failed to get image generation job", 500);
    }
  }

  /**
   * Get all image generation jobs for a user
   */
  async getUserImageJobs(userId: string): Promise<GeneratedImageJob[]> {
    try {
      const jobs = await prisma.generatedImageJob.findMany({
        where: { userId },
        orderBy: { createdAt: "desc" },
      });

      return jobs;
    } catch (error) {
      throw new AppError("Failed to get user image generation jobs", 500);
    }
  }

  /**
   * Call Stability AI API to generate image
   */
  private async callStabilityAIAPI(
    prompt: string,
    parameters: any
  ): Promise<{
    imageId: string;
    imageUrl: string;
    resolution: string;
  }> {
    try {
      // Check if we're in development mode and should return mock data
      if (
        process.env.NODE_ENV === "development" &&
        process.env.USE_MOCK_API === "true"
      ) {
        console.log("Using mock Stability AI API response in development mode");
        return this.getMockImageResponse(parameters);
      }

      // Determine engine ID based on parameters or use default
      const engineId = parameters.engineId || "stable-diffusion-xl-1024-v1-0";

      // Determine dimensions based on aspect ratio
      const width =
        parameters.aspectRatio === "16:9"
          ? 1024
          : parameters.aspectRatio === "9:16"
          ? 576
          : parameters.aspectRatio === "1:1"
          ? 1024
          : 1024;

      const height =
        parameters.aspectRatio === "16:9"
          ? 576
          : parameters.aspectRatio === "9:16"
          ? 1024
          : parameters.aspectRatio === "1:1"
          ? 1024
          : 768;

      // Prepare text prompts
      const textPrompts = [
        {
          text: prompt,
          weight: 1.0,
        },
      ];

      // Add negative prompt if provided
      if (parameters.negativePrompt) {
        textPrompts.push({
          text: parameters.negativePrompt,
          weight: -1.0,
        });
      }

      console.log(
        `Calling Stability AI API with engine ${engineId} to generate image...`
      );
      console.log(`Prompt: ${prompt}`);
      console.log(`Resolution: ${width}x${height}`);

      // Make the API request
      const response = await axios.post(
        `${this.apiBaseUrl}/generation/${engineId}/text-to-image`,
        {
          text_prompts: textPrompts,
          cfg_scale: parameters.cfgScale || 7,
          height: height,
          width: width,
          samples: parameters.count || 1,
          steps: parameters.steps || 50,
          style_preset: parameters.style || "photographic",
        },
        {
          headers: {
            "Content-Type": "application/json",
            Accept: "application/json",
            Authorization: `Bearer ${this.apiKey}`,
          },
        }
      );

      // Check if we got any artifacts back
      if (!response.data.artifacts || response.data.artifacts.length === 0) {
        throw new AppError("No images generated by Stability AI API", 500);
      }

      // In a production environment, you would upload the base64 image to a storage service
      // For this implementation, we'll assume we have a function to handle that
      // and just return the mock URL for now
      const imageId = response.data.artifacts[0].id;

      // In a real implementation, you would save the image to a storage service
      // const base64Image = response.data.artifacts[0].base64;
      // const imageUrl = await this.uploadImageToStorage(base64Image, imageId);

      // For now, we'll use a mock URL
      const imageUrl = `https://storage.miragicai.com/images/${imageId}.png`;

      console.log(`Image generated successfully with ID: ${imageId}`);

      return {
        imageId: imageId,
        imageUrl: imageUrl,
        resolution: `${response.data.artifacts[0].width}x${response.data.artifacts[0].height}`,
      };
    } catch (error) {
      console.error("Error calling Stability AI API:", error);
      if (error instanceof AppError) {
        throw error;
      }
      throw new AppError("Failed to generate image with Stability AI API", 500);
    }
  }

  /**
   * Get mock image response for development
   */
  private getMockImageResponse(parameters: any): {
    imageId: string;
    imageUrl: string;
    resolution: string;
  } {
    const width =
      parameters.aspectRatio === "16:9"
        ? 1024
        : parameters.aspectRatio === "9:16"
        ? 576
        : parameters.aspectRatio === "1:1"
        ? 1024
        : 1024;

    const height =
      parameters.aspectRatio === "16:9"
        ? 576
        : parameters.aspectRatio === "9:16"
        ? 1024
        : parameters.aspectRatio === "1:1"
        ? 1024
        : 768;

    return {
      imageId: `img_${Math.random().toString(36).substring(2, 15)}`,
      imageUrl: `https://example.com/images/sample_${Math.floor(
        Math.random() * 1000
      )}.png`,
      resolution: `${width}x${height}`,
    };
  }

  /**
   * Check if user has access to image generation
   * Either through an active subscription or enough credits
   */
  private async checkUserAccess(userId: string): Promise<boolean> {
    try {
      // Check if user has an active subscription
      const activeSubscription = await prisma.userSubscription.findFirst({
        where: {
          userId,
          status: "ACTIVE",
          endDate: {
            gte: new Date(),
          },
        },
        include: {
          plan: true,
        },
      });

      if (activeSubscription) {
        // Check if subscription plan includes image generation
        const features = activeSubscription.plan.features as any;
        if (features.image_generation_quota > 0) {
          // Check if user has used up their quota
          const usageCount = await prisma.apiUsageLog.count({
            where: {
              userId,
              featureUsed: "IMAGE_GENERATION",
              requestTimestamp: {
                gte: new Date(new Date().setDate(new Date().getDate() - 30)), // Last 30 days
              },
            },
          });

          if (usageCount < features.image_generation_quota) {
            return true;
          }
        }
      }

      // Check if user has enough credits
      const userCredit = await prisma.credit.findUnique({
        where: { userId },
      });

      if (userCredit && userCredit.balance >= 2) {
        // Assuming 2 credits per image
        return true;
      }

      return false;
    } catch (error) {
      console.error("Error checking user access:", error);
      return false;
    }
  }

  /**
   * Calculate credits used for image generation
   */
  private calculateCreditsUsed(parameters: any): number {
    // Base cost is 2 credits per image
    const baseCost = 2;

    // Calculate based on number of images
    const count = parameters.count || 1;

    return baseCost * count;
  }

  /**
   * Deduct credits from user's account
   */
  private async deductCredits(userId: string, credits: number): Promise<void> {
    try {
      // Check if user has an active subscription
      const activeSubscription = await prisma.userSubscription.findFirst({
        where: {
          userId,
          status: "ACTIVE",
          endDate: {
            gte: new Date(),
          },
        },
        include: {
          plan: true,
        },
      });

      if (activeSubscription) {
        // If user has an active subscription, don't deduct credits
        // as it's included in their plan
        return;
      }

      // Deduct credits from user's account
      await prisma.credit.update({
        where: { userId },
        data: {
          balance: {
            decrement: credits,
          },
        },
      });

      // Log credit transaction
      await prisma.creditTransaction.create({
        data: {
          userId,
          amount: -credits,
          type: "USAGE",
          description: "Image generation",
        },
      });
    } catch (error) {
      console.error("Error deducting credits:", error);
    }
  }
}
