import { Request, Response } from 'express';
import { Analytics } from '../../utils/analytics';
import { speedPaintingLogger } from '../../utils/logging';

/**
 * Get speed painting analytics data
 */
export const getSpeedPaintingAnalytics = async (req: Request, res: Response) => {
  try {
    // Authentication and authorization check
    if (!req.user || req.user.role !== 'ADMIN') {
      speedPaintingLogger.warn({
        message: 'Unauthorized attempt to access speed painting analytics',
        userId: req.user?.id,
        ip: req.ip,
        action: 'unauthorized_access'
      });
      
      return res.status(403).json({
        success: false,
        error: {
          code: 'FORBIDDEN',
          message: 'You do not have permission to access this resource'
        }
      });
    }

    // Parse date range from query parameters
    let startDate: Date | undefined;
    let endDate: Date | undefined;

    if (req.query.startDate && typeof req.query.startDate === 'string') {
      startDate = new Date(req.query.startDate);
    }

    if (req.query.endDate && typeof req.query.endDate === 'string') {
      endDate = new Date(req.query.endDate);
    }

    // Get analytics data
    const analyticsData = await Analytics.getSpeedPaintingStats(startDate, endDate);

    speedPaintingLogger.info({
      message: 'Speed painting analytics retrieved',
      userId: req.user.id,
      action: 'analytics_retrieved'
    });

    return res.status(200).json({
      success: true,
      data: analyticsData
    });
  } catch (error) {
    speedPaintingLogger.error({
      message: 'Error retrieving speed painting analytics',
      userId: req.user?.id,
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
      action: 'analytics_error'
    });

    return res.status(500).json({
      success: false,
      error: {
        code: 'SERVER_ERROR',
        message: 'Failed to retrieve speed painting analytics'
      }
    });
  }
};
