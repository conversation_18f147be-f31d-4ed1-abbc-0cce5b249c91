import { Request, Response } from "express";
import { SubscriptionService } from "../../services/subscription.service";
import { CreditService } from "../../services/credit.service";
import { AppError } from "../../utils/error";

const subscriptionService = new SubscriptionService();
const creditService = new CreditService();

/**
 * Admin controller for managing subscription plans, credit packages, and free plans
 */
export class AdminPlansController {
  /**
   * Get all subscription plans
   */
  // async getAllSubscriptionPlans(req: Request, res: Response) {
  //   try {
  //     const plans = await subscriptionService.getSubscriptionPlans();
  //     return res.status(200).json({
  //       success: true,
  //       data: plans,
  //     });
  //   } catch (error) {
  //     console.error("Error getting all subscription plans:", error);
  //     if (error instanceof AppError) {
  //       return res.status(error.statusCode).json({
  //         success: false,
  //         message: error.message,
  //       });
  //     }
  //     return res.status(500).json({
  //       success: false,
  //       message: "Failed to get subscription plans",
  //     });
  //   }
  // }

  // /**
  //  * Get a specific subscription plan
  //  */
  // async getSubscriptionPlan(req: Request, res: Response) {
  //   try {
  //     const { id } = req.params;
  //     const plan = await subscriptionService.getSubscriptionPlanById(id);
  //     return res.status(200).json({
  //       success: true,
  //       data: plan,
  //     });
  //   } catch (error) {
  //     console.error("Error getting subscription plan:", error);
  //     if (error instanceof AppError) {
  //       return res.status(error.statusCode).json({
  //         success: false,
  //         message: error.message,
  //       });
  //     }
  //     return res.status(500).json({
  //       success: false,
  //       message: "Failed to get subscription plan",
  //     });
  //   }
  // }

  // /**
  //  * Create a new subscription plan
  //  */
  // async createSubscriptionPlan(req: Request, res: Response) {
  //   try {
  //     const planData = req.body;
  //     const plan = await subscriptionService.createSubscriptionPlan(planData);
  //     return res.status(201).json({
  //       success: true,
  //       data: plan,
  //     });
  //   } catch (error) {
  //     console.error("Error creating subscription plan:", error);
  //     if (error instanceof AppError) {
  //       return res.status(error.statusCode).json({
  //         success: false,
  //         message: error.message,
  //       });
  //     }
  //     return res.status(500).json({
  //       success: false,
  //       message: "Failed to create subscription plan",
  //     });
  //   }
  // }

  // /**
  //  * Update an existing subscription plan
  //  */
  // async updateSubscriptionPlan(req: Request, res: Response) {
  //   try {
  //     const { id } = req.params;
  //     const planData = req.body;
  //     const plan = await subscriptionService.updateSubscriptionPlan(id, planData);
  //     return res.status(200).json({
  //       success: true,
  //       data: plan,
  //     });
  //   } catch (error) {
  //     console.error("Error updating subscription plan:", error);
  //     if (error instanceof AppError) {
  //       return res.status(error.statusCode).json({
  //         success: false,
  //         message: error.message,
  //       });
  //     }
  //     return res.status(500).json({
  //       success: false,
  //       message: "Failed to update subscription plan",
  //     });
  //   }
  // }

  // /**
  //  * Delete a subscription plan (soft delete)
  //  */
  // async deleteSubscriptionPlan(req: Request, res: Response) {
  //   try {
  //     const { id } = req.params;
  //     const plan = await subscriptionService.deleteSubscriptionPlan(id);
  //     return res.status(200).json({
  //       success: true,
  //       data: plan,
  //     });
  //   } catch (error) {
  //     console.error("Error deleting subscription plan:", error);
  //     if (error instanceof AppError) {
  //       return res.status(error.statusCode).json({
  //         success: false,
  //         message: error.message,
  //       });
  //     }
  //     return res.status(500).json({
  //       success: false,
  //       message: "Failed to delete subscription plan",
  //     });
  //   }
  // }

  /**
   * Get all credit packages
   */
  async getAllCreditPackages(req: Request, res: Response) {
    try {
      const packages = await creditService.getCreditPackages();
      return res.status(200).json({
        success: true,
        data: packages,
      });
    } catch (error) {
      console.error("Error getting all credit packages:", error);
      if (error instanceof AppError) {
        return res.status(error.statusCode).json({
          success: false,
          message: error.message,
        });
      }
      return res.status(500).json({
        success: false,
        message: "Failed to get credit packages",
      });
    }
  }

  /**
   * Create a new credit package
   */
  async createCreditPackage(req: Request, res: Response) {
    try {
      const packageData = req.body;

      // Use Prisma directly since CreditService doesn't have a method for this yet
      const creditPackage = await (req as any).prisma.creditPackage.create({
        data: {
          ...packageData,
          isActive:
            packageData.isActive !== undefined ? packageData.isActive : true,
        },
      });

      return res.status(201).json({
        success: true,
        data: creditPackage,
      });
    } catch (error) {
      console.error("Error creating credit package:", error);
      if (error instanceof AppError) {
        return res.status(error.statusCode).json({
          success: false,
          message: error.message,
        });
      }
      return res.status(500).json({
        success: false,
        message: "Failed to create credit package",
      });
    }
  }

  /**
   * Update an existing credit package
   */
  async updateCreditPackage(req: Request, res: Response) {
    try {
      const { id } = req.params;
      const packageData = req.body;

      // Use Prisma directly since CreditService doesn't have a method for this yet
      const creditPackage = await (req as any).prisma.creditPackage.update({
        where: { id },
        data: packageData,
      });

      return res.status(200).json({
        success: true,
        data: creditPackage,
      });
    } catch (error) {
      console.error("Error updating credit package:", error);
      if (error instanceof AppError) {
        return res.status(error.statusCode).json({
          success: false,
          message: error.message,
        });
      }
      return res.status(500).json({
        success: false,
        message: "Failed to update credit package",
      });
    }
  }

  /**
   * Delete a credit package (soft delete)
   */
  async deleteCreditPackage(req: Request, res: Response) {
    try {
      const { id } = req.params;

      // Check if package is in use by any pending purchase orders
      const pendingOrders = await (
        req as any
      ).prisma.creditPurchaseOrder.findMany({
        where: {
          creditPackageId: id,
          status: "PENDING",
        },
      });

      if (pendingOrders.length > 0) {
        throw new AppError(
          "Cannot delete package with pending orders. Deactivate it instead.",
          400
        );
      }

      // Soft delete by setting isActive to false
      const creditPackage = await (req as any).prisma.creditPackage.update({
        where: { id },
        data: {
          isActive: false,
        },
      });

      return res.status(200).json({
        success: true,
        data: creditPackage,
      });
    } catch (error) {
      console.error("Error deleting credit package:", error);
      if (error instanceof AppError) {
        return res.status(error.statusCode).json({
          success: false,
          message: error.message,
        });
      }
      return res.status(500).json({
        success: false,
        message: "Failed to delete credit package",
      });
    }
  }

  /**
   * Get service costs
   */
  async getServiceCosts(req: Request, res: Response) {
    try {
      const costs = await creditService.getServiceCosts();
      return res.status(200).json({
        success: true,
        data: costs,
      });
    } catch (error) {
      console.error("Error getting service costs:", error);
      if (error instanceof AppError) {
        return res.status(error.statusCode).json({
          success: false,
          message: error.message,
        });
      }
      return res.status(500).json({
        success: false,
        message: "Failed to get service costs",
      });
    }
  }

  /**
   * Update service costs
   */
  async updateServiceCosts(req: Request, res: Response) {
    try {
      const costsData = req.body;
      console.log("Updating service costs with data:", costsData);

      // Use the credit service to update service costs
      const updatedCosts = await creditService.updateServiceCosts(costsData);

      return res.status(200).json({
        success: true,
        data: costsData,
        message: "Service costs updated successfully",
      });
    } catch (error) {
      console.error("Error updating service costs:", error);
      if (error instanceof AppError) {
        return res.status(error.statusCode).json({
          success: false,
          message: error.message,
        });
      }
      return res.status(500).json({
        success: false,
        message: "Failed to update service costs",
      });
    }
  }
}
