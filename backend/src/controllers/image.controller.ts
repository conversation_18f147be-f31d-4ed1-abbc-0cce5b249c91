import { Request, Response } from "express";
import { z } from "zod";
import { ImageService } from "../services/image.service";
import { prisma } from "../index";
import { JobStatus } from "@prisma/client";
import { AppError } from "../utils/error";

// Initialize image service
const imageService = new ImageService();

// Validation schemas
const generateImageSchema = z.object({
  prompt: z.string().min(10).max(1000),
  negativePrompt: z.string().optional(),
  style: z.string().optional(),
  aspectRatio: z.enum(["16:9", "9:16", "1:1", "4:3"]).optional(),
  count: z.number().min(1).max(4).optional(),
});

export const generateImage = async (req: Request, res: Response) => {
  try {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        error: {
          code: "UNAUTHORIZED",
          message: "Authentication required",
        },
      });
    }

    // Validate request body
    const validatedData = generateImageSchema.parse(req.body);

    // Get user
    const user = await prisma.user.findUnique({
      where: { id: req.user.id },
    });

    if (!user) {
      return res.status(404).json({
        success: false,
        error: {
          code: "USER_NOT_FOUND",
          message: "User not found",
        },
      });
    }

    // Create image generation job
    const job = await imageService.createImageJob(user, validatedData);

    // Add job to processing queue
    // In a real implementation, this would be handled by a job queue like Bull
    // For simplicity, we'll process it directly here
    setTimeout(() => {
      imageService.processImageJob(job.id).catch((error) => {
        console.error("Error processing image job:", error);
      });
    }, 1000);

    res.status(201).json({
      success: true,
      data: {
        jobId: job.id,
        status: job.status,
      },
      message: "Image generation job created successfully",
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        error: {
          code: "VALIDATION_ERROR",
          message: "Invalid input data",
          details: error.errors,
        },
      });
    }

    if (error instanceof AppError) {
      return res.status(error.statusCode || 500).json({
        success: false,
        error: {
          code: error.code || "SERVER_ERROR",
          message: error.message,
        },
      });
    }

    res.status(500).json({
      success: false,
      error: {
        code: "SERVER_ERROR",
        message: "Failed to create image generation job",
      },
    });
  }
};

export const getImageJob = async (req: Request, res: Response) => {
  try {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        error: {
          code: "UNAUTHORIZED",
          message: "Authentication required",
        },
      });
    }

    const { jobId } = req.params;

    if (!jobId) {
      return res.status(400).json({
        success: false,
        error: {
          code: "MISSING_JOB_ID",
          message: "Job ID is required",
        },
      });
    }

    // Get image job
    const job = await imageService.getImageJob(jobId, req.user.id);

    res.status(200).json({
      success: true,
      data: job,
    });
  } catch (error) {
    if (error instanceof AppError) {
      return res.status(error.statusCode || 500).json({
        success: false,
        error: {
          code: error.code || "SERVER_ERROR",
          message: error.message,
        },
      });
    }

    res.status(500).json({
      success: false,
      error: {
        code: "SERVER_ERROR",
        message: "Failed to get image generation job",
      },
    });
  }
};

export const getUserImageJobs = async (req: Request, res: Response) => {
  try {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        error: {
          code: "UNAUTHORIZED",
          message: "Authentication required",
        },
      });
    }

    // Get user's image jobs
    const jobs = await imageService.getUserImageJobs(req.user.id);

    res.status(200).json({
      success: true,
      data: jobs,
    });
  } catch (error) {
    if (error instanceof AppError) {
      return res.status(error.statusCode || 500).json({
        success: false,
        error: {
          code: error.code || "SERVER_ERROR",
          message: error.message,
        },
      });
    }

    res.status(500).json({
      success: false,
      error: {
        code: "SERVER_ERROR",
        message: "Failed to get user image generation jobs",
      },
    });
  }
};

/**
 * Get available image styles for image generation
 * Uses Stability AI styles as reference
 */
export const getImageStyles = async (req: Request, res: Response) => {
  try {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        error: {
          code: "UNAUTHORIZED",
          message: "Authentication required",
        },
      });
    }

    // In a real implementation, this would fetch styles from a database or external API
    // For now, we'll return mock data based on Stability AI styles
    const styles = [
      {
        id: "style-1",
        name: "Photographic",
        description: "Realistic photographic style with natural lighting and colors",
        preview: "https://via.placeholder.com/300x200?text=Photographic",
      },
      {
        id: "style-2",
        name: "Digital Art",
        description: "Vibrant digital illustration style with clean lines",
        preview: "https://via.placeholder.com/300x200?text=Digital+Art",
      },
      {
        id: "style-3",
        name: "3D Render",
        description: "3D rendered style with realistic textures and lighting",
        preview: "https://via.placeholder.com/300x200?text=3D+Render",
      },
      {
        id: "style-4",
        name: "Anime",
        description: "Japanese anime style with bold colors and distinctive features",
        preview: "https://via.placeholder.com/300x200?text=Anime",
      },
      {
        id: "style-5",
        name: "Oil Painting",
        description: "Traditional oil painting style with visible brush strokes",
        preview: "https://via.placeholder.com/300x200?text=Oil+Painting",
      },
      {
        id: "style-6",
        name: "Watercolor",
        description: "Soft watercolor style with gentle color blending",
        preview: "https://via.placeholder.com/300x200?text=Watercolor",
      },
    ];

    res.status(200).json({
      success: true,
      data: styles,
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: {
        code: "SERVER_ERROR",
        message: "Failed to get image styles",
      },
    });
  }
};
