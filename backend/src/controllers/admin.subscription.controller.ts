import { Request, Response } from "express";
import { z } from "zod";
import { prisma } from "../index";
import { AppError } from "../utils/error";

// Validation schemas
const createSubscriptionPlanSchema = z.object({
  name: z.string(),
  displayName: z.string(),
  description: z.string(),
  price: z.number().positive(),
  currency: z.string().default("USD"),
  interval: z.enum(["month", "year"]),
  features: z.record(z.any()),
  creditsAmount: z.number().int().min(0),
  featureHighlights: z.array(z.string()),
  colorScheme: z.string().optional(),
  isFeatured: z.boolean().default(false),
  sortOrder: z.number().int().default(0),
  stripePriceId: z.string().optional(),
  paypalPlanId: z.string().optional(),
  isActive: z.boolean().default(true),
});

const updateSubscriptionPlanSchema = createSubscriptionPlanSchema.partial();

/**
 * Get all subscription plans (admin)
 */
export const getAllSubscriptionPlans = async (req: Request, res: Response) => {
  try {
    if (!req.user || req.user.role !== "ADMIN") {
      return res.status(403).json({
        success: false,
        error: {
          code: "FORBIDDEN",
          message: "Admin access required",
        },
      });
    }

    const plans = await prisma.subscriptionPlan.findMany({
      orderBy: [{ sortOrder: "asc" }, { price: "asc" }],
    });

    res.status(200).json({
      success: true,
      data: plans,
    });
  } catch (error) {
    console.error("Error getting subscription plans:", error);
    res.status(500).json({
      success: false,
      error: {
        code: "SERVER_ERROR",
        message: "Failed to get subscription plans",
      },
    });
  }
};

/**
 * Create a new subscription plan (admin)
 */
export const createSubscriptionPlan = async (req: Request, res: Response) => {
  try {
    if (!req.user || req.user.role !== "ADMIN") {
      return res.status(403).json({
        success: false,
        error: {
          code: "FORBIDDEN",
          message: "Admin access required",
        },
      });
    }

    // Validate request body
    const validationResult = createSubscriptionPlanSchema.safeParse(req.body);
    if (!validationResult.success) {
      return res.status(400).json({
        success: false,
        error: {
          code: "VALIDATION_ERROR",
          message: "Invalid subscription plan data",
          details: validationResult.error.format(),
        },
      });
    }

    const planData = validationResult.data;

    // Create the subscription plan
    const plan = await prisma.subscriptionPlan.create({
      data: planData,
    });

    res.status(201).json({
      success: true,
      data: plan,
    });
  } catch (error) {
    console.error("Error creating subscription plan:", error);
    res.status(500).json({
      success: false,
      error: {
        code: "SERVER_ERROR",
        message: "Failed to create subscription plan",
      },
    });
  }
};

/**
 * Update a subscription plan (admin)
 */
export const updateSubscriptionPlan = async (req: Request, res: Response) => {
  try {
    if (!req.user || req.user.role !== "ADMIN") {
      return res.status(403).json({
        success: false,
        error: {
          code: "FORBIDDEN",
          message: "Admin access required",
        },
      });
    }

    const { id } = req.params;

    // Validate request body
    const validationResult = updateSubscriptionPlanSchema.safeParse(req.body);
    if (!validationResult.success) {
      return res.status(400).json({
        success: false,
        error: {
          code: "VALIDATION_ERROR",
          message: "Invalid subscription plan data",
          details: validationResult.error.format(),
        },
      });
    }

    const planData = validationResult.data;

    // Check if plan exists
    const existingPlan = await prisma.subscriptionPlan.findUnique({
      where: { id },
    });

    if (!existingPlan) {
      return res.status(404).json({
        success: false,
        error: {
          code: "NOT_FOUND",
          message: "Subscription plan not found",
        },
      });
    }

    // Update the subscription plan
    const plan = await prisma.subscriptionPlan.update({
      where: { id },
      data: planData,
    });

    res.status(200).json({
      success: true,
      data: plan,
    });
  } catch (error) {
    console.error("Error updating subscription plan:", error);
    res.status(500).json({
      success: false,
      error: {
        code: "SERVER_ERROR",
        message: "Failed to update subscription plan",
      },
    });
  }
};

/**
 * Delete a subscription plan (admin)
 */
export const deleteSubscriptionPlan = async (req: Request, res: Response) => {
  try {
    if (!req.user || req.user.role !== "ADMIN") {
      return res.status(403).json({
        success: false,
        error: {
          code: "FORBIDDEN",
          message: "Admin access required",
        },
      });
    }

    const { id } = req.params;

    // Check if plan exists
    const existingPlan = await prisma.subscriptionPlan.findUnique({
      where: { id },
    });

    if (!existingPlan) {
      return res.status(404).json({
        success: false,
        error: {
          code: "NOT_FOUND",
          message: "Subscription plan not found",
        },
      });
    }

    // Check if plan is in use
    const subscriptionsUsingPlan = await prisma.userSubscription.count({
      where: { planId: id },
    });

    if (subscriptionsUsingPlan > 0) {
      // Instead of deleting, just mark as inactive
      await prisma.subscriptionPlan.update({
        where: { id },
        data: { isActive: false },
      });

      return res.status(200).json({
        success: true,
        data: {
          message: "Plan marked as inactive because it's in use by subscribers",
        },
      });
    }

    // Delete the subscription plan if not in use
    await prisma.subscriptionPlan.delete({
      where: { id },
    });

    res.status(200).json({
      success: true,
      data: {
        message: "Subscription plan deleted successfully",
      },
    });
  } catch (error) {
    console.error("Error deleting subscription plan:", error);
    res.status(500).json({
      success: false,
      error: {
        code: "SERVER_ERROR",
        message: "Failed to delete subscription plan",
      },
    });
  }
};
