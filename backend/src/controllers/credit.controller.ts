import { Request, Response } from "express";
import { z } from "zod";
import { CreditService } from "../services/credit.service";
import { PaymentService } from "../services/payment.service";
import { prisma } from "../index";
import { AppError } from "../utils/error";

// Initialize services
const creditService = new CreditService();
const paymentService = new PaymentService();

// Validation schemas
const creditPackageCheckoutSchema = z.object({
  packageId: z.string().uuid(),
  successUrl: z.string().url(),
  cancelUrl: z.string().url(),
});

const creditPackagePaymentIntentSchema = z.object({
  packageId: z.string().uuid(),
});

const paypalOrderSchema = z.object({
  packageId: z.string(),
});

const paypalCaptureSchema = z.object({
  orderId: z.string(),
});

const creditAdjustmentSchema = z.object({
  userId: z.string(),
  amount: z.number(),
  description: z.string(),
  adjustmentType: z.enum(["ADJUSTMENT_ADD", "ADJUSTMENT_SUBTRACT"]),
});

/**
 * Get user's current credit balance
 */
export const getUserCreditBalance = async (req: Request, res: Response) => {
  try {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        error: {
          code: "UNAUTHORIZED",
          message: "Authentication required",
        },
      });
    }

    const credit = await creditService.getUserCreditBalance(req.user.id);

    res.status(200).json({
      success: true,
      data: credit,
    });
  } catch (error) {
    if (error instanceof AppError) {
      return res.status(error.statusCode || 500).json({
        success: false,
        error: {
          code: error.code || "SERVER_ERROR",
          message: error.message,
        },
      });
    }

    res.status(500).json({
      success: false,
      error: {
        code: "SERVER_ERROR",
        message: "Failed to get credit balance",
      },
    });
  }
};

/**
 * Get user's credit transaction history
 */
export const getUserCreditHistory = async (req: Request, res: Response) => {
  try {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        error: {
          code: "UNAUTHORIZED",
          message: "Authentication required",
        },
      });
    }

    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;

    const history = await creditService.getUserCreditHistory(
      req.user.id,
      page,
      limit
    );

    res.status(200).json({
      success: true,
      data: history,
    });
  } catch (error) {
    if (error instanceof AppError) {
      return res.status(error.statusCode || 500).json({
        success: false,
        error: {
          code: error.code || "SERVER_ERROR",
          message: error.message,
        },
      });
    }

    res.status(500).json({
      success: false,
      error: {
        code: "SERVER_ERROR",
        message: "Failed to get credit history",
      },
    });
  }
};

/**
 * Get all available credit packages
 */
export const getCreditPackages = async (req: Request, res: Response) => {
  try {
    const packages = await creditService.getCreditPackages();

    res.status(200).json({
      success: true,
      data: packages,
    });
  } catch (error) {
    if (error instanceof AppError) {
      return res.status(error.statusCode || 500).json({
        success: false,
        error: {
          code: error.code || "SERVER_ERROR",
          message: error.message,
        },
      });
    }

    res.status(500).json({
      success: false,
      error: {
        code: "SERVER_ERROR",
        message: "Failed to get credit packages",
      },
    });
  }
};

/**
 * Create a Stripe checkout session for credit package purchase
 */
export const createStripeCheckoutSession = async (
  req: Request,
  res: Response
) => {
  try {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        error: {
          code: "UNAUTHORIZED",
          message: "Authentication required",
        },
      });
    }

    // Validate request body
    const validatedData = creditPackageCheckoutSchema.parse(req.body);

    // Get user
    const user = await prisma.user.findUnique({
      where: { id: req.user.id },
    });

    if (!user) {
      return res.status(404).json({
        success: false,
        error: {
          code: "USER_NOT_FOUND",
          message: "User not found",
        },
      });
    }

    // Get credit package
    const creditPackage = await prisma.creditPackage.findUnique({
      where: { id: validatedData.packageId },
    });

    if (!creditPackage) {
      return res.status(404).json({
        success: false,
        error: {
          code: "PACKAGE_NOT_FOUND",
          message: "Credit package not found",
        },
      });
    }

    // Create a purchase order
    const purchaseOrder = await creditService.createCreditPurchaseOrder(
      user.id,
      creditPackage.id,
      "STRIPE"
    );

    // Create checkout session
    const session = await paymentService.createStripeCreditCheckoutSession(
      user,
      creditPackage.price,
      creditPackage.creditsAmount,
      validatedData.successUrl,
      validatedData.cancelUrl,
      purchaseOrder.id
    );

    res.status(200).json({
      success: true,
      data: session,
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        error: {
          code: "VALIDATION_ERROR",
          message: "Invalid input data",
          details: error.errors,
        },
      });
    }

    if (error instanceof AppError) {
      return res.status(error.statusCode || 500).json({
        success: false,
        error: {
          code: error.code || "SERVER_ERROR",
          message: error.message,
        },
      });
    }

    res.status(500).json({
      success: false,
      error: {
        code: "SERVER_ERROR",
        message: "Failed to create checkout session",
      },
    });
  }
};

/**
 * Create a PayPal order for credit package purchase
 */
export const createPayPalOrder = async (req: Request, res: Response) => {
  try {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        error: {
          code: "UNAUTHORIZED",
          message: "Authentication required",
        },
      });
    }

    // Validate request body
    const validatedData = paypalOrderSchema.parse(req.body);

    // Get credit package
    const creditPackage = await prisma.creditPackage.findUnique({
      where: { id: validatedData.packageId },
    });

    if (!creditPackage) {
      return res.status(404).json({
        success: false,
        error: {
          code: "PACKAGE_NOT_FOUND",
          message: "Credit package not found",
        },
      });
    }

    // Create a purchase order
    const purchaseOrder = await creditService.createCreditPurchaseOrder(
      req.user.id,
      creditPackage.id,
      "PAYPAL"
    );

    // Create PayPal order
    const paypalOrder = await paymentService.createPayPalOrder(
      req.user.id,
      creditPackage.price,
      creditPackage.creditsAmount,
      purchaseOrder.id
    );

    res.status(200).json({
      success: true,
      data: paypalOrder,
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        error: {
          code: "VALIDATION_ERROR",
          message: "Invalid input data",
          details: error.errors,
        },
      });
    }

    if (error instanceof AppError) {
      return res.status(error.statusCode || 500).json({
        success: false,
        error: {
          code: error.code || "SERVER_ERROR",
          message: error.message,
        },
      });
    }

    res.status(500).json({
      success: false,
      error: {
        code: "SERVER_ERROR",
        message: "Failed to create PayPal order",
      },
    });
  }
};

/**
 * Capture a PayPal order after approval
 */
export const capturePayPalOrder = async (req: Request, res: Response) => {
  try {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        error: {
          code: "UNAUTHORIZED",
          message: "Authentication required",
        },
      });
    }

    // Validate request body
    const validatedData = paypalCaptureSchema.parse(req.body);

    // Capture the order
    const result = await paymentService.capturePayPalOrder(
      validatedData.orderId
    );

    res.status(200).json({
      success: true,
      data: result,
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        error: {
          code: "VALIDATION_ERROR",
          message: "Invalid input data",
          details: error.errors,
        },
      });
    }

    if (error instanceof AppError) {
      return res.status(error.statusCode || 500).json({
        success: false,
        error: {
          code: error.code || "SERVER_ERROR",
          message: error.message,
        },
      });
    }

    res.status(500).json({
      success: false,
      error: {
        code: "SERVER_ERROR",
        message: "Failed to capture PayPal order",
      },
    });
  }
};

/**
 * Get service costs in credits
 */
export const getServiceCosts = async (req: Request, res: Response) => {
  try {
    const costs = await creditService.getServiceCosts();

    res.status(200).json({
      success: true,
      data: costs,
    });
  } catch (error) {
    if (error instanceof AppError) {
      return res.status(error.statusCode || 500).json({
        success: false,
        error: {
          code: error.code || "SERVER_ERROR",
          message: error.message,
        },
      });
    }

    res.status(500).json({
      success: false,
      error: {
        code: "SERVER_ERROR",
        message: "Failed to get service costs",
      },
    });
  }
};

/**
 * Admin: Adjust a user's credit balance
 */
export const adjustUserCredits = async (req: Request, res: Response) => {
  try {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        error: {
          code: "UNAUTHORIZED",
          message: "Authentication required",
        },
      });
    }

    // Check if user is admin
    if (req.user.role !== "ADMIN") {
      return res.status(403).json({
        success: false,
        error: {
          code: "FORBIDDEN",
          message: "Admin access required",
        },
      });
    }

    // Validate request body
    const validatedData = creditAdjustmentSchema.parse(req.body);

    // Adjust credits
    const result = await creditService.adjustCredits(
      validatedData.userId,
      validatedData.amount,
      validatedData.description,
      validatedData.adjustmentType,
      req.user.id
    );

    res.status(200).json({
      success: true,
      data: result,
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        error: {
          code: "VALIDATION_ERROR",
          message: "Invalid input data",
          details: error.errors,
        },
      });
    }

    if (error instanceof AppError) {
      return res.status(error.statusCode || 500).json({
        success: false,
        error: {
          code: error.code || "SERVER_ERROR",
          message: error.message,
        },
      });
    }

    res.status(500).json({
      success: false,
      error: {
        code: "SERVER_ERROR",
        message: "Failed to adjust credits",
      },
    });
  }
};

/**
 * Admin: Get all credit transactions for a user
 */
export const getAdminUserCreditHistory = async (
  req: Request,
  res: Response
) => {
  try {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        error: {
          code: "UNAUTHORIZED",
          message: "Authentication required",
        },
      });
    }

    // Check if user is admin
    if (req.user.role !== "ADMIN") {
      return res.status(403).json({
        success: false,
        error: {
          code: "FORBIDDEN",
          message: "Admin access required",
        },
      });
    }

    const userId = req.params.userId;
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;

    const history = await creditService.getUserCreditHistory(
      userId,
      page,
      limit
    );

    res.status(200).json({
      success: true,
      data: history,
    });
  } catch (error) {
    if (error instanceof AppError) {
      return res.status(error.statusCode || 500).json({
        success: false,
        error: {
          code: error.code || "SERVER_ERROR",
          message: error.message,
        },
      });
    }

    res.status(500).json({
      success: false,
      error: {
        code: "SERVER_ERROR",
        message: "Failed to get credit history",
      },
    });
  }
};

/**
 * Create a Stripe Payment Intent for credit package purchase
 * This allows using Stripe Elements directly on the site instead of redirecting
 */
export const createStripePaymentIntent = async (
  req: Request,
  res: Response
) => {
  try {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        error: {
          code: "UNAUTHORIZED",
          message: "Authentication required",
        },
      });
    }

    // Validate request body
    const validationResult = creditPackagePaymentIntentSchema.safeParse(
      req.body
    );
    if (!validationResult.success) {
      return res.status(400).json({
        success: false,
        error: {
          code: "VALIDATION_ERROR",
          message: "Invalid request data",
          details: validationResult.error.format(),
        },
      });
    }

    const { packageId } = validationResult.data;

    // Get the credit package
    const creditPackage = await prisma.creditPackage.findUnique({
      where: { id: packageId },
    });

    if (!creditPackage) {
      return res.status(404).json({
        success: false,
        error: {
          code: "NOT_FOUND",
          message: "Credit package not found",
        },
      });
    }

    // Create a credit purchase order
    const order = await creditService.createCreditPurchaseOrder(
      req.user.id,
      packageId,
      "STRIPE"
    );

    // Create a payment intent
    const paymentIntent = await paymentService.createStripePaymentIntent({
      amount: Math.round(creditPackage.price * 100), // Convert to cents
      currency: creditPackage.currency.toLowerCase(),
      customerId: req.user?.stripeCustomerId || null,
      metadata: {
        orderId: order.id,
        packageId: creditPackage.id,
        userId: req.user.id,
        credits: creditPackage.creditsAmount.toString(),
        type: "credit_purchase",
      },
    });

    res.status(200).json({
      success: true,
      data: {
        clientSecret: paymentIntent.client_secret,
        paymentIntentId: paymentIntent.id,
        amount: creditPackage.price,
        currency: creditPackage.currency,
        orderId: order.id,
      },
    });
  } catch (error) {
    console.error("Error creating Stripe payment intent:", error);
    if (error instanceof AppError) {
      return res.status(error.statusCode || 500).json({
        success: false,
        error: {
          code: error.code || "SERVER_ERROR",
          message: error.message,
        },
      });
    }

    res.status(500).json({
      success: false,
      error: {
        code: "SERVER_ERROR",
        message: "Failed to create payment intent",
      },
    });
  }
};
