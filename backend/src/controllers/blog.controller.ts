import { Request, Response, NextFunction } from "express";
import { z } from "zod";
import { prisma } from "../index";
import { AppError } from "../middleware/errorHandler";
import slugify from "slugify";
import multer from "multer";
import path from "path";
import fs from "fs";
import { v4 as uuidv4 } from "uuid";

// Validation schemas
const blogPostSchema = z.object({
  title: z.string().min(5).max(200),
  content: z.string().min(50),
  excerpt: z.string().max(300).optional(),
  featuredImageUrl: z.string().url().optional(),
  status: z.enum(["DRAFT", "PUBLISHED"]).optional(),
  categoryIds: z.array(z.string()).optional(),
  tagIds: z.array(z.string()).optional(),
});


// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadDir = path.join(__dirname, "../../uploads/blogs");

    // Create directory if it doesn't exist
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }

    cb(null, uploadDir);
  },
  filename: (req, file, cb) => {
    const uniqueFilename = `${uuidv4()}${path.extname(file.originalname)}`;
    cb(null, uniqueFilename);
  },
});

const fileFilter = (
  req: any,
  file: Express.Multer.File,
  cb: multer.FileFilterCallback
) => {
  // Accept only image files
  if (file.mimetype.startsWith("image/")) {
    cb(null, true);
  } else {
    cb(new Error("Only image files are allowed") as any);
  }
};

export const upload = multer({
  storage,
  fileFilter,
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB
  },
});

// Get all published blog posts
export const getAllPosts = async (req: Request, res: Response) => {
  try {
    const { page = "1", limit = "10", category, tag, search } = req.query;

    const pageNumber = parseInt(page as string, 10);
    const limitNumber = parseInt(limit as string, 10);
    const skip = (pageNumber - 1) * limitNumber;

    // Build filter conditions
    const where: any = {
      status: "PUBLISHED",
    };

    if (category) {
      where.categories = {
        some: {
          category: {
            slug: category as string,
          },
        },
      };
    }

    if (tag) {
      where.tags = {
        some: {
          tag: {
            slug: tag as string,
          },
        },
      };
    }

    if (search) {
      where.OR = [
        {
          title: {
            contains: search as string,
            mode: "insensitive",
          },
        },
        {
          content: {
            contains: search as string,
            mode: "insensitive",
          },
        },
      ];
    }

    // Get posts
    const posts = await prisma.blogPost.findMany({
      where,
      include: {
        author: {
          select: {
            id: true,
            email: true,
            profile: true,
          },
        },
        categories: {
          include: {
            category: true,
          },
        },
        tags: {
          include: {
            tag: true,
          },
        },
      },
      orderBy: {
        publishedAt: "desc",
      },
      skip,
      take: limitNumber,
    });

    // Get total count for pagination
    const totalPosts = await prisma.blogPost.count({ where });

    // Format response
    const formattedPosts = posts.map((post) => ({
      id: post.id,
      title: post.title,
      slug: post.slug,
      excerpt: post.excerpt,
      featuredImageUrl: post.featuredImageUrl,
      publishedAt: post.publishedAt,
      author: post.author,
      categories: post.categories.map((c) => c.category),
      tags: post.tags.map((t) => t.tag),
    }));

    res.status(200).json({
      success: true,
      data: {
        posts: formattedPosts,
        pagination: {
          total: totalPosts,
          page: pageNumber,
          limit: limitNumber,
          totalPages: Math.ceil(totalPosts / limitNumber),
        },
      },
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: {
        code: "SERVER_ERROR",
        message: "Failed to get blog posts",
      },
    });
  }
};

// Get a single blog post by slug
export const getPostBySlug = async (req: Request, res: Response) => {
  try {
    const { slug } = req.params;

    if (!slug) {
      return res.status(400).json({
        success: false,
        error: {
          code: "MISSING_SLUG",
          message: "Post slug is required",
        },
      });
    }

    // Get post
    const post = await prisma.blogPost.findUnique({
      where: {
        slug,
        status: "PUBLISHED",
      },
      include: {
        author: {
          select: {
            id: true,
            email: true,
            profile: true,
          },
        },
        categories: {
          include: {
            category: true,
          },
        },
        tags: {
          include: {
            tag: true,
          },
        },
      },
    });

    if (!post) {
      return res.status(404).json({
        success: false,
        error: {
          code: "POST_NOT_FOUND",
          message: "Blog post not found",
        },
      });
    }

    // Format response
    const formattedPost = {
      id: post.id,
      title: post.title,
      slug: post.slug,
      content: post.content,
      excerpt: post.excerpt,
      featuredImageUrl: post.featuredImageUrl,
      publishedAt: post.publishedAt,
      author: post.author,
      categories: post.categories.map((c) => c.category),
      tags: post.tags.map((t) => t.tag),
    };

    res.status(200).json({
      success: true,
      data: formattedPost,
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: {
        code: "SERVER_ERROR",
        message: "Failed to get blog post",
      },
    });
  }
};

// Import service function
import {
  createBlogPost as createBlogPostService,
  CreateBlogPostData,
  updateBlogPost as updateBlogPostService,
  UpdateBlogPostData,
  deleteBlogPost as deleteBlogPostService,
  getBlogPostById as getBlogPostByIdService,
  getAllBlogPostsAdmin as getAllBlogPostsAdminService,
  GetAllBlogPostsAdminParams,
  createBlogCategory as createBlogCategoryService,
  CreateBlogCategoryData,
  updateBlogCategory as updateBlogCategoryService,
  UpdateBlogCategoryData,
  deleteBlogCategory as deleteBlogCategoryService,
  getAllBlogCategoriesAdmin as getAllBlogCategoriesAdminService,
  GetAllBlogCategoriesAdminParams,
} from "../services/blog.service";

import { BlogPostStatus } from "@prisma/client"; // Ensure BlogPostStatus is imported
import { APP_URL } from "../config";
import { features } from "process";

// Zod schema for Admin creating a blog post
const adminCreateBlogPostSchema = z.object({
  title: z.string().min(1, "Title is required").max(255),
  content: z.string().min(1, "Content is required"),
  excerpt: z.string().max(500).optional(),
  featuredImageUrl: z.string().url().optional(),
  status: z.enum(["DRAFT", "PUBLISHED", "ARCHIVED", "SCHEDULED"]).optional(),
  publishedAt: z.preprocess((arg) => {
    if (typeof arg == "string" || arg instanceof Date) return new Date(arg);
  }, z.date().optional()),
  scheduledFor: z.preprocess((arg) => {
    if (typeof arg == "string" || arg instanceof Date) return new Date(arg);
  }, z.date().optional()),
  metaTitle: z.string().max(255).optional(),
  metaDescription: z.string().max(500).optional(),
  metaKeywords: z.array(z.string()).optional(),
  categoryIds: z.array(z.string().uuid()).optional(),
  tagIds: z.array(z.string().uuid()).optional(),
});

// Controller function for Admin to create a blog post
export const createBlogPostAdmin = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const authorId = (req as any).user?.id;
    if (!authorId) {
      return next(
        new AppError("User not authenticated or authorId missing", 401)
      );
    }

    const validatedData = adminCreateBlogPostSchema.safeParse(req.body);
    if (!validatedData.success) {
      return res.status(400).json({
        success: false,
        error: {
          code: "VALIDATION_ERROR",
          message: "Invalid input data",
          details: validatedData.error.flatten().fieldErrors,
        },
      });
    }

    // Generate image URL
    const originalImageUrl = req.file && req.file.filename ? `${APP_URL}/uploads/blogs/${req.file.filename}`: undefined;

    const postData: CreateBlogPostData = {
      ...validatedData.data,
      featuredImageUrl: originalImageUrl,
      authorId,
    };

    const newPost = await createBlogPostService(postData);

    res.status(201).json({
      success: true,
      message: "Blog post created successfully",
      data: newPost,
    });
  } catch (error: any) {
    if (error instanceof AppError) {
      return next(error);
    }
    console.error("Error creating blog post:", error);
    if (error.code === "P2002" && error.meta?.target?.includes("slug")) {
      return next(
        new AppError(
          "A post with this title (resulting in the same slug) already exists.",
          409
        )
      );
    }
    return next(
      new AppError("Failed to create blog post. " + error.message, 500)
    );
  }
};

// Zod schema for Admin updating a blog post
const adminUpdateBlogPostSchema = z.object({
  title: z.string().min(1, "Title is required").max(255).optional(),
  content: z.string().min(1, "Content is required").optional(),
  excerpt: z.string().max(500).optional().nullable(),
  featuredImageUrl: z.string().url().optional().nullable(),
  status: z.enum(["DRAFT", "PUBLISHED", "ARCHIVED", "SCHEDULED"]).optional(),
  publishedAt: z.preprocess((arg) => {
    if (typeof arg === "string" || arg instanceof Date) return new Date(arg);
    if (arg === null) return null;
    return undefined;
  }, z.date().optional().nullable()),
  scheduledFor: z.preprocess((arg) => {
    if (typeof arg === "string" || arg instanceof Date) return new Date(arg);
    if (arg === null) return null;
    return undefined;
  }, z.date().optional().nullable()),
  metaTitle: z.string().max(255).optional().nullable(),
  metaDescription: z.string().max(500).optional().nullable(),
  metaKeywords: z.array(z.string()).optional(),
  categoryIds: z.array(z.string().uuid()).optional(),
  tagIds: z.array(z.string().uuid()).optional(),
});

// Controller function for Admin to update a blog post
export const updateBlogPostAdmin = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const { postId } = req.params;
    if (!postId) {
      return next(new AppError("Post ID is required", 400));
    }

    const validatedData = adminUpdateBlogPostSchema.safeParse(req.body);
    if (!validatedData.success) {
      return res.status(400).json({
        success: false,
        error: {
          code: "VALIDATION_ERROR",
          message: "Invalid input data for update",
          details: validatedData.error.flatten().fieldErrors,
        },
      });
    }

    if (Object.keys(validatedData.data).length === 0) {
      return res.status(400).json({
        success: false,
        error: {
          code: "EMPTY_REQUEST",
          message: "Request body cannot be empty for an update operation.",
        },
      });
    }

    const sanitizedData = Object.fromEntries(
      Object.entries(validatedData.data).map(([key, value]) => [
        key,
        value === null ? undefined : value,
      ])
    );
    
    
    const dataToUpdate: UpdateBlogPostData = {
      ...sanitizedData,
    }

    if(req.file && req.file.filename){
      dataToUpdate[`featuredImageUrl`] = `${APP_URL}/uploads/blogs/${req.file.filename}`;
    }

    const updatedPost = await updateBlogPostService(
      postId,
      dataToUpdate as UpdateBlogPostData
    );

    res.status(200).json({
      success: true,
      message: "Blog post updated successfully",
      data: updatedPost,
    });
  } catch (error: any) {
    if (error instanceof AppError) {
      return next(error);
    }
    console.error("Error updating blog post:", error);
    if (error.code === "P2025") {
      return next(
        new AppError("Blog post not found or failed to update.", 404)
      );
    }
    if (error.code === "P2002" && error.meta?.target?.includes("slug")) {
      return next(
        new AppError(
          "A post with this title (resulting in the same slug) already exists.",
          409
        )
      );
    }
    return next(
      new AppError("Failed to update blog post. " + error.message, 500)
    );
  }
};

// Controller function for Admin to delete a blog post
export const deleteBlogPostAdmin = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const { postId } = req.params;
    if (!postId) {
      return next(new AppError("Post ID is required", 400));
    }

    await deleteBlogPostService(postId);

    res.status(200).json({
      success: true,
      message: "Blog post deleted successfully",
    });
    // Alternatively, send a 204 No Content response:
    // res.status(204).send();
  } catch (error: any) {
    if (error instanceof AppError) {
      return next(error);
    }
    console.error("Error deleting blog post:", error);
    if (error.code === "P2025") {
      return next(new AppError("Blog post not found or already deleted.", 404));
    }
    return next(
      new AppError("Failed to delete blog post. " + error.message, 500)
    );
  }
};

// Controller function for Admin to get a single blog post by ID
export const getBlogPostByIdAdmin = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const { postId } = req.params;
    if (!postId) {
      return next(new AppError("Post ID is required", 400));
    }

    const post = await getBlogPostByIdService(postId);

    if (!post) {
      return next(new AppError("Blog post not found", 404));
    }

    res.status(200).json({
      success: true,
      data: post,
    });
  } catch (error: any) {
    if (error instanceof AppError) {
      return next(error);
    }
    console.error("Error fetching blog post by ID:", error);
    return next(
      new AppError("Failed to fetch blog post. " + error.message, 500)
    );
  }
};

// Zod schema for query parameters for getting all blog posts (Admin)
const getAllBlogPostsAdminQuerySchema = z.object({
  page: z.preprocess((val) => Number(val), z.number().int().min(1).optional()),
  limit: z.preprocess((val) => Number(val), z.number().int().min(1).optional()),
  search: z.string().optional(),
  status: z.preprocess(
    (val) =>
      typeof val === "string"
        ? val.split(",")
        : Array.isArray(val)
        ? val
        : undefined,
    z.array(z.nativeEnum(BlogPostStatus)).optional()
  ),
  categoryIds: z.preprocess(
    (val) =>
      typeof val === "string"
        ? val.split(",")
        : Array.isArray(val)
        ? val
        : undefined,
    z.array(z.string().uuid()).optional()
  ),
  tagIds: z.preprocess(
    (val) =>
      typeof val === "string"
        ? val.split(",")
        : Array.isArray(val)
        ? val
        : undefined,
    z.array(z.string().uuid()).optional()
  ),
  authorId: z.string().uuid().optional(),
  sortBy: z
    .enum(["createdAt", "updatedAt", "publishedAt", "title", "status"])
    .optional(),
  sortOrder: z.enum(["asc", "desc"]).optional(),
});

// Controller function for Admin to get all blog posts
export const getAllBlogPostsAdminController = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const validatedQuery = getAllBlogPostsAdminQuerySchema.safeParse(req.query);

    if (!validatedQuery.success) {
      return res.status(400).json({
        success: false,
        error: {
          code: "VALIDATION_ERROR",
          message: "Invalid query parameters for fetching blog posts",
          details: validatedQuery.error.flatten().fieldErrors,
        },
      });
    }

    const params: GetAllBlogPostsAdminParams = validatedQuery.data;
    const result = await getAllBlogPostsAdminService(params);

    res.status(200).json({
      success: true,
      ...result, // Spread data and pagination
    });
  } catch (error: any) {
    if (error instanceof AppError) {
      return next(error);
    }
    console.error("Error fetching all blog posts for admin:", error);
    return next(
      new AppError("Failed to fetch blog posts. " + error.message, 500)
    );
  }
};

// Zod schema for Admin creating a blog category
const adminCreateBlogCategorySchema = z.object({
  name: z
    .string()
    .min(2, "Category name must be at least 2 characters")
    .max(100, "Category name must be at most 100 characters"),
  description: z
    .string()
    .max(500, "Description must be at most 500 characters")
    .optional()
    .nullable(),
});

// Controller function for Admin to create a blog category
export const createBlogCategoryAdmin = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const validatedData = adminCreateBlogCategorySchema.safeParse(req.body);
    if (!validatedData.success) {
      return res.status(400).json({
        success: false,
        error: {
          code: "VALIDATION_ERROR",
          message: "Invalid input data for creating category",
          details: validatedData.error.flatten().fieldErrors,
        },
      });
    }

    const categoryData: CreateBlogCategoryData = validatedData.data;
    const newCategory = await createBlogCategoryService(categoryData);

    res.status(201).json({
      success: true,
      message: "Blog category created successfully",
      data: newCategory,
    });
  } catch (error: any) {
    if (error instanceof AppError) {
      return next(error);
    }
    if (error.code === "P2002" && error.meta?.target?.includes("name")) {
      return next(
        new AppError("A category with this name already exists.", 409)
      );
    }
    if (error.code === "P2002" && error.meta?.target?.includes("slug")) {
      return next(
        new AppError(
          "A category with this name (resulting in the same slug) already exists.",
          409
        )
      );
    }
    console.error("Error creating blog category:", error);
    return next(
      new AppError("Failed to create blog category. " + error.message, 500)
    );
  }
};

// Zod schema for Admin updating a blog category
const adminUpdateBlogCategorySchema = z.object({
  name: z
    .string()
    .min(2, "Category name must be at least 2 characters")
    .max(100, "Category name must be at most 100 characters")
    .optional(),
  description: z
    .string()
    .max(500, "Description must be at most 500 characters")
    .optional()
    .nullable(),
});

// Controller function for Admin to update a blog category
export const updateBlogCategoryAdmin = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const { categoryId } = req.params;
    if (!categoryId) {
      return next(new AppError("Category ID is required", 400));
    }

    const validatedData = adminUpdateBlogCategorySchema.safeParse(req.body);
    if (!validatedData.success) {
      return res.status(400).json({
        success: false,
        error: {
          code: "VALIDATION_ERROR",
          message: "Invalid input data for updating category",
          details: validatedData.error.flatten().fieldErrors,
        },
      });
    }

    if (Object.keys(validatedData.data).length === 0) {
      return res.status(400).json({
        success: false,
        error: {
          code: "EMPTY_REQUEST",
          message: "Request body cannot be empty for an update operation.",
        },
      });
    }

    const categoryData: UpdateBlogCategoryData = validatedData.data;
    const updatedCategory = await updateBlogCategoryService(
      categoryId,
      categoryData
    );

    res.status(200).json({
      success: true,
      message: "Blog category updated successfully",
      data: updatedCategory,
    });
  } catch (error: any) {
    if (error instanceof AppError) {
      return next(error);
    }
    if (error.code === "P2025") {
      return next(
        new AppError("Blog category not found or failed to update.", 404)
      );
    }
    if (error.code === "P2002" && error.meta?.target?.includes("name")) {
      return next(
        new AppError("A category with this name already exists.", 409)
      );
    }
    if (error.code === "P2002" && error.meta?.target?.includes("slug")) {
      return next(
        new AppError(
          "A category with this name (resulting in the same slug) already exists.",
          409
        )
      );
    }
    console.error("Error updating blog category:", error);
    return next(
      new AppError("Failed to update blog category. " + error.message, 500)
    );
  }
};

// Zod schema for query parameters for getting all blog categories (Admin)
const adminGetAllBlogCategoriesQuerySchema = z.object({
  page: z.preprocess(
    (val) => (val ? Number(val) : undefined),
    z.number().int().min(1).optional()
  ),
  limit: z.preprocess(
    (val) => (val ? Number(val) : undefined),
    z.number().int().min(1).optional()
  ),
  search: z.string().optional(),
});

// Controller function for Admin to delete a blog category
export const deleteBlogCategoryAdmin = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const { categoryId } = req.params;
    if (!categoryId) {
      return next(new AppError("Category ID is required", 400));
    }

    await deleteBlogCategoryService(categoryId);

    res.status(200).json({
      success: true,
      message: "Blog category deleted successfully",
    });
  } catch (error: any) {
    if (error instanceof AppError) {
      return next(error);
    }
    console.error("Error deleting blog category:", error);
    return next(
      new AppError("Failed to delete blog category. " + error.message, 500)
    );
  }
};

// Controller function for Admin to get all blog categories
export const getAllBlogCategoriesAdminController = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const validationResult = adminGetAllBlogCategoriesQuerySchema.safeParse(
      req.query
    );
    if (!validationResult.success) {
      return next(
        new AppError(
          "Invalid query parameters",
          400,
          "VALIDATION_ERROR"
          // validationResult.error.errors
        )
      );
    }

    const { page, limit, search } = validationResult.data;

    const params: GetAllBlogCategoriesAdminParams = {};
    if (page) params.page = page;
    if (limit) params.limit = limit;
    if (search) params.search = search;

    const result = await getAllBlogCategoriesAdminService(params);

    res.status(200).json({
      success: true,
      message: "Blog categories fetched successfully for admin",
      data: result.data,
      pagination: result.pagination,
    });
  } catch (error: any) {
    console.error("Error in getAllBlogCategoriesAdminController:", error);
    if (error instanceof AppError) {
      return next(error);
    }
    return next(new AppError("Failed to fetch blog categories for admin", 500));
  }
};

// Get all categories
export const getAllCategories = async (req: Request, res: Response) => {
  try {
    const categories = await prisma.blogCategory.findMany({
      orderBy: {
        name: "asc",
      },
    });

    res.status(200).json({
      success: true,
      data: categories,
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: {
        code: "SERVER_ERROR",
        message: "Failed to get blog categories",
      },
    });
  }
};

// Get all tags
export const getAllTags = async (req: Request, res: Response) => {
  try {
    const tags = await prisma.blogTag.findMany({
      orderBy: {
        name: "asc",
      },
    });

    res.status(200).json({
      success: true,
      data: tags,
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: {
        code: "SERVER_ERROR",
        message: "Failed to get blog tags",
      },
    });
  }
};
