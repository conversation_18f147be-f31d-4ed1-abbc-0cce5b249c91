import { Request, Response } from "express";
import { BgRemovalService } from "../services/bgRemoval.service";
import { CreditUsageService } from "../services/creditUsage.service";
import { AppError } from "../utils/error";
import { prisma } from "../index";
import multer from "multer";
import path from "path";
import fs from "fs";
import { v4 as uuidv4 } from "uuid";
import axios from "axios";
import FormData from "form-data";
import { RateLimiterMemory } from "rate-limiter-flexible";
import { saveProcessedImage } from "../utils/saveProcessedImage";
import { APP_URL } from "../config";

// Rate limiter setup - 10 requests per minute per IP
const rateLimiter = new RateLimiterMemory({
  points: 10, // Number of points
  duration: 60, // Per 60 seconds
});

// Rate limiter middleware
export const rateLimitMiddleware = async (
  req: Request,
  res: Response,
  next: any
) => {
  try {
    // Get client IP
    const clientIp = req.ip || req.headers["x-forwarded-for"] || "unknown";

    // Check rate limit
    await rateLimiter.consume(clientIp.toString());
    next();
  } catch (error) {
    res.status(429).json({
      success: false,
      error: {
        code: "RATE_LIMIT_EXCEEDED",
        message: "Too many requests, please try again later.",
      },
    });
  }
};

// Initialize services
const bgRemovalService = new BgRemovalService();
const creditUsageService = new CreditUsageService();

// Miragic AI API configuration
const MIRAGIC_AI_API_URL =
  process.env.MIRAGIC_AI_API_URL || "https://api.miragic.ai";
const MIRAGIC_AI_API_KEY = process.env.MIRAGIC_AI_API_KEY || "";

// Cache for recently processed images to avoid duplicate processing
const processedImagesCache = new Map<
  string,
  { url: string; timestamp: number }
>();

// Cleanup old cache entries every hour
setInterval(() => {
  const now = Date.now();
  const ONE_HOUR = 60 * 60 * 1000;

  for (const [key, value] of processedImagesCache.entries()) {
    if (now - value.timestamp > ONE_HOUR) {
      processedImagesCache.delete(key);
    }
  }
}, 60 * 60 * 1000); // 1 hour

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadDir = path.join(__dirname, "../../uploads/bgRemoval/original");

    // Create directory if it doesn't exist
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }

    cb(null, uploadDir);
  },
  filename: (req, file, cb) => {
    const uniqueFilename = `${uuidv4()}${path.extname(file.originalname)}`;
    cb(null, uniqueFilename);
  },
});

const fileFilter = (
  req: any,
  file: Express.Multer.File,
  cb: multer.FileFilterCallback
) => {
  // Accept only image files
  if (file.mimetype.startsWith("image/")) {
    cb(null, true);
  } else {
    cb(new Error("Only image files are allowed") as any);
  }
};

export const upload = multer({
  storage,
  fileFilter,
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB
  },
});

export const removeBackground = async (req: Request, res: Response) => {
  try {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        error: {
          code: "UNAUTHORIZED",
          message: "Authentication required",
        },
      });
    }

    if (!req.file) {
      return res.status(400).json({
        success: false,
        error: {
          code: "FILE_REQUIRED",
          message: "Image file is required",
        },
      });
    }

    // Get user
    const user = await prisma.user.findUnique({
      where: { id: req.user.id },
    });

    if (!user) {
      return res.status(404).json({
        success: false,
        error: {
          code: "USER_NOT_FOUND",
          message: "User not found",
        },
      });
    }

    // Check if user has enough credits for background removal
    const hasEnoughCredits = await creditUsageService.hasEnoughCredits(
      user.id,
      "backgroundRemoval"
    );

    if (!hasEnoughCredits) {
      return res.status(402).json({
        success: false,
        error: {
          code: "INSUFFICIENT_CREDITS",
          message:
            "You don't have enough credits for this operation. Please purchase more credits.",
        },
      });
    }

    // Generate image URL
    const originalImageUrl = `${APP_URL}/uploads/bgRemoval/original/${req.file.filename}`;

    // Create background removal job
    const job = await bgRemovalService.createBgRemovalJob(
      user,
      req.file.originalname,
      originalImageUrl,
      req.file.path
    );

    // Deduct credits for background removal
    await creditUsageService.deductBackgroundRemovalCredits(
      user.id,
      req.file.originalname,
      job.id
    );

    // Add job to processing queue
    // In a real implementation, this would be handled by a job queue like Bull
    // For simplicity, we'll process it directly here
    setTimeout(() => {
      bgRemovalService.processBgRemovalJob(job.id).catch((error) => {
        console.error("Error processing background removal job:", error);
      });
    }, 1000);

    res.status(201).json({
      success: true,
      data: {
        jobId: job.id,
        status: job.status,
        originalImageUrl,
      },
      message: "Background removal job created successfully",
    });
  } catch (error) {
    if (error instanceof AppError) {
      return res.status(error.statusCode || 500).json({
        success: false,
        error: {
          code: error.code || "SERVER_ERROR",
          message: error.message,
        },
      });
    }

    res.status(500).json({
      success: false,
      error: {
        code: "SERVER_ERROR",
        message: "Failed to create background removal job",
      },
    });
  }
};

export const getBgRemovalJob = async (req: Request, res: Response) => {
  try {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        error: {
          code: "UNAUTHORIZED",
          message: "Authentication required",
        },
      });
    }

    const { jobId } = req.params;

    if (!jobId) {
      return res.status(400).json({
        success: false,
        error: {
          code: "MISSING_JOB_ID",
          message: "Job ID is required",
        },
      });
    }

    // Get background removal job
    const job = await bgRemovalService.getBgRemovalJob(jobId, req.user.id);

    res.status(200).json({
      success: true,
      data: job,
    });
  } catch (error) {
    if (error instanceof AppError) {
      return res.status(error.statusCode || 500).json({
        success: false,
        error: {
          code: error.code || "SERVER_ERROR",
          message: error.message,
        },
      });
    }

    res.status(500).json({
      success: false,
      error: {
        code: "SERVER_ERROR",
        message: "Failed to get background removal job",
      },
    });
  }
};

export const getUserBgRemovalJobs = async (req: Request, res: Response) => {
  try {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        error: {
          code: "UNAUTHORIZED",
          message: "Authentication required",
        },
      });
    }

    // Get user's background removal jobs
    const jobs = await bgRemovalService.getUserBgRemovalJobs(req.user.id);

    res.status(200).json({
      success: true,
      data: jobs,
    });
  } catch (error) {
    if (error instanceof AppError) {
      return res.status(error.statusCode || 500).json({
        success: false,
        error: {
          code: error.code || "SERVER_ERROR",
          message: error.message,
        },
      });
    }

    res.status(500).json({
      success: false,
      error: {
        code: "SERVER_ERROR",
        message: "Failed to get user background removal jobs",
      },
    });
  }
};

/**
 * Process image from URL for background removal
 * This endpoint accepts a JSON payload with imageUrl
 */
/**
 * Direct background removal using Miragic AI API
 * This endpoint handles file uploads directly to the Miragic AI API
 * with proper error handling, credit management, and file validation
 */
export const directRemoveBackground = async (req: Request, res: Response) => {
  const startTime = Date.now();
  let errorMessage = "";
  let processingStatus = "failed";
  let originalFileName = "";

  try {
    // Authentication check
    if (!req.user) {
      return res.status(401).json({
        success: false,
        error: {
          code: "UNAUTHORIZED",
          message: "Authentication required",
        },
      });
    }

    // File validation
    if (!req.file) {
      return res.status(400).json({
        success: false,
        error: {
          code: "FILE_REQUIRED",
          message: "Image file is required",
        },
      });
    }

    originalFileName = req.file.originalname;

    // Get user
    const user = await prisma.user.findUnique({
      where: { id: req.user.id },
      include: { credit: true },
    });

    if (!user) {
      return res.status(404).json({
        success: false,
        error: {
          code: "USER_NOT_FOUND",
          message: "User not found",
        },
      });
    }

    // Check if user has enough credits
    // const serviceCost = await creditUsageService.getServiceCost(
    //   "backgroundRemoval"
    // );

    // if (!user.credit || user.credit.balance < serviceCost) {
    //   return res.status(402).json({
    //     success: false,
    //     error: {
    //       code: "INSUFFICIENT_CREDITS",
    //       message: `You need ${serviceCost} credits for this operation. Your current balance: ${
    //         user.credit?.balance || 0
    //       }`,
    //     },
    //   });
    // }

    // Generate file hash for caching
    const fileHash = `${req.file.size}-${req.file.originalname}-${req.user.id}`;

    // Check cache first
    if (processedImagesCache.has(fileHash)) {
      const cachedResult = processedImagesCache.get(fileHash)!;

      // Log API usage
      await prisma.apiUsageLog.create({
        data: {
          userId: user.id,
          featureUsed: "BACKGROUND_REMOVAL",
          creditsConsumed: 0, //serviceCost,
          requestTimestamp: new Date(startTime),
          responseTimestamp: new Date(),
          status: "success",
          metadata: {
            source: "cache",
            processingTimeMs: Date.now() - startTime,
            originalFileName,
          },
        },
      });

      // Deduct credits
      // await creditUsageService.deductCredits(
      //   user.id,
      //   serviceCost,
      //   "Background removal"
      // );

      return res.status(200).json({
        success: true,
        data: {
          imageUrl: cachedResult.url,
          cached: true,
        },
        message: "Background removed successfully (cached)",
      });
    }

    // Create a form data object
    const formData = new FormData();
    formData.append("file", fs.createReadStream(req.file.path));

    // Call the Miragic AI API
    const response = await axios.post(
      `${MIRAGIC_AI_API_URL}/remove_background`,
      formData,
      {
        headers: {
          ...formData.getHeaders(),
          Authorization: `Bearer ${MIRAGIC_AI_API_KEY}`,
        },
        timeout: 30000, // 30 second timeout
      }
    );

    // Check if the API call was successful
    if (!response.data || response.data.status !== "success") {
      throw new Error(
        response.data?.message || "Unknown error from Miragic AI API"
      );
    }

    // Get the processed image URL
    const processedImagePath = await saveProcessedImage(response.data.link);
    console.log("processedImagePath", processedImagePath);
    const processedImageUrl = `${APP_URL}/${processedImagePath}`;
    // Cache the result
    processedImagesCache.set(fileHash, {
      url: processedImageUrl,
      timestamp: Date.now(),
    });

    // Create a background removal job record
    const job = await prisma.backgroundImageRemovalJob.create({
      data: {
        userId: user.id,
        originalImageName: req.file.originalname,
        originalImageUrl: `${APP_URL}/uploads/bgRemoval/original/${req.file.filename}`,
        processedImageUrl,
        storagePathOriginal: req.file.path,
        status: "COMPLETED",
        metadata: {
          processingTimeMs: Date.now() - startTime,
          apiVersion: "2.0",
          fileSize: req.file.size,
        },
        creditsUsed: 0, //serviceCost,
      },
    });

    // Log API usage
    await prisma.apiUsageLog.create({
      data: {
        userId: user.id,
        featureUsed: "BACKGROUND_REMOVAL",
        creditsConsumed: 0, //serviceCost,
        requestTimestamp: new Date(startTime),
        responseTimestamp: new Date(),
        status: "success",
        metadata: {
          processingTimeMs: Date.now() - startTime,
          jobId: job.id,
          fileSize: req.file.size,
          originalFileName,
        },
      },
    });

    // Deduct credits
    await creditUsageService.deductCredits(
      user.id,
      0, //serviceCost,
      "Background removal"
    );

    processingStatus = "success";

    // Return the processed image URL
    return res.status(200).json({
      success: true,
      data: {
        imageUrl: processedImageUrl,
        jobId: job.id,
      },
      message: "Background removed successfully",
    });
  } catch (error) {
    // Capture error message for logging
    errorMessage = error instanceof Error ? error.message : "Unknown error";
    console.error("Error in directRemoveBackground:", errorMessage);

    // Log the failure
    if (req.user) {
      try {
        await prisma.apiUsageLog.create({
          data: {
            userId: req.user.id,
            featureUsed: "BACKGROUND_REMOVAL",
            creditsConsumed: 0, // No credits consumed on failure
            requestTimestamp: new Date(startTime),
            responseTimestamp: new Date(),
            status: "failed",
            errorDetails: errorMessage,
            metadata: {
              processingTimeMs: Date.now() - startTime,
              originalFileName,
            },
          },
        });
      } catch (logError) {
        console.error("Failed to log API usage:", logError);
      }
    }

    // Return appropriate error response
    if (error instanceof AppError) {
      return res.status(error.statusCode || 500).json({
        success: false,
        error: {
          code: error.code || "SERVER_ERROR",
          message: error.message,
        },
      });
    }

    // Handle Axios errors
    if (axios.isAxiosError(error) && error.response) {
      return res.status(error.response.status || 500).json({
        success: false,
        error: {
          code: "API_ERROR",
          message: `API error: ${error.response.status} - ${
            error.response.data?.message || error.message
          }`,
        },
      });
    }

    // Generic error
    return res.status(500).json({
      success: false,
      error: {
        code: "SERVER_ERROR",
        message: "Failed to remove background: " + errorMessage,
      },
    });
  } finally {
    // Cleanup temporary file if it exists
    if (req.file && req.file.path && fs.existsSync(req.file.path)) {
      try {
        // Keep files for debugging in development, remove in production
        if (process.env.NODE_ENV === "production") {
          fs.unlinkSync(req.file.path);
        }
      } catch (cleanupError) {
        console.error("Error cleaning up temporary file:", cleanupError);
      }
    }

    // Log processing metrics
    console.log(
      `Background removal completed with status ${processingStatus} in ${
        Date.now() - startTime
      }ms`
    );
  }
};

export const processImageUrl = async (req: Request, res: Response) => {
  try {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        error: {
          code: "UNAUTHORIZED",
          message: "Authentication required",
        },
      });
    }

    const { imageUrl } = req.body;

    if (!imageUrl) {
      return res.status(400).json({
        success: false,
        error: {
          code: "MISSING_IMAGE_URL",
          message: "Image URL is required",
        },
      });
    }

    // Get user
    const user = await prisma.user.findUnique({
      where: { id: req.user.id },
    });

    if (!user) {
      return res.status(404).json({
        success: false,
        error: {
          code: "USER_NOT_FOUND",
          message: "User not found",
        },
      });
    }

    // Check if user has enough credits for background removal
    const hasEnoughCredits = await creditUsageService.hasEnoughCredits(
      user.id,
      "backgroundRemoval"
    );

    if (!hasEnoughCredits) {
      return res.status(402).json({
        success: false,
        error: {
          code: "INSUFFICIENT_CREDITS",
          message:
            "You don't have enough credits for this operation. Please purchase more credits.",
        },
      });
    }

    // For uploaded files through our API, extract the filename from the URL
    let originalFileName = "unknown.jpg";
    let storagePathOriginal = "";

    // Check if it's a local URL from our upload endpoint
    if (imageUrl.includes("/uploads/")) {
      const urlParts = imageUrl.split("/");
      originalFileName = urlParts[urlParts.length - 1];
      storagePathOriginal = path.join(
        __dirname,
        "../../uploads/original",
        originalFileName
      );
    } else {
      // For external URLs, we'd need to download the image first
      // This is simplified for now
      originalFileName = `external-${Date.now()}.jpg`;
      storagePathOriginal = path.join(
        __dirname,
        "../../uploads/original",
        originalFileName
      );
    }

    // Create background removal job
    const job = await bgRemovalService.createBgRemovalJob(
      user,
      originalFileName,
      imageUrl,
      storagePathOriginal
    );

    // Deduct credits for background removal
    await creditUsageService.deductBackgroundRemovalCredits(
      user.id,
      originalFileName,
      job.id
    );

    // Add job to processing queue
    setTimeout(() => {
      bgRemovalService.processBgRemovalJob(job.id).catch((error) => {
        console.error("Error processing background removal job:", error);
      });
    }, 1000);

    res.status(201).json({
      success: true,
      data: {
        jobId: job.id,
        status: job.status,
        originalImageUrl: imageUrl,
      },
      message: "Background removal job created successfully",
    });
  } catch (error) {
    console.error("Error in processImageUrl:", error);
    if (error instanceof AppError) {
      return res.status(error.statusCode || 500).json({
        success: false,
        error: {
          code: error.code || "SERVER_ERROR",
          message: error.message,
        },
      });
    }

    res.status(500).json({
      success: false,
      error: {
        code: "SERVER_ERROR",
        message: "Failed to create background removal job",
      },
    });
  }
};
