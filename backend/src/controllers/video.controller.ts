import { Request, Response } from 'express';
import { z } from 'zod';
import { VideoService } from '../services/video.service';
import { AppError } from '../utils/error';
import { prisma } from '../index';
import { JobStatus } from '@prisma/client';

// Initialize video service
const videoService = new VideoService();

// Validation schemas
const generateVideoSchema = z.object({
  prompt: z.string().min(10).max(1000),
  style: z.string().optional(),
  aspectRatio: z.enum(['16:9', '9:16', '1:1', '4:3']).optional(),
  duration: z.number().optional(),
  avatar: z.string().optional(),
  voice: z.string().optional(),
  background: z.string().optional(),
});

export const generateVideo = async (req: Request, res: Response) => {
  try {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        error: {
          code: 'UNAUTHORIZED',
          message: 'Authentication required',
        },
      });
    }

    // Validate request body
    const validatedData = generateVideoSchema.parse(req.body);

    // Get user
    const user = await prisma.user.findUnique({
      where: { id: req.user.id },
    });

    if (!user) {
      return res.status(404).json({
        success: false,
        error: {
          code: 'USER_NOT_FOUND',
          message: 'User not found',
        },
      });
    }

    // Create video generation job
    const job = await videoService.createVideoJob(user, validatedData);

    // Add job to processing queue
    // In a real implementation, this would be handled by a job queue like Bull
    // For simplicity, we'll process it directly here
    setTimeout(() => {
      videoService.processVideoJob(job.id).catch(error => {
        console.error('Error processing video job:', error);
      });
    }, 1000);

    res.status(201).json({
      success: true,
      data: {
        jobId: job.id,
        status: job.status,
      },
      message: 'Video generation job created successfully',
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Invalid input data',
          details: error.errors,
        },
      });
    }

    if (error instanceof AppError) {
      return res.status(error.statusCode || 500).json({
        success: false,
        error: {
          code: error.code || 'SERVER_ERROR',
          message: error.message,
        },
      });
    }

    res.status(500).json({
      success: false,
      error: {
        code: 'SERVER_ERROR',
        message: 'Failed to create video generation job',
      },
    });
  }
};

export const getVideoJob = async (req: Request, res: Response) => {
  try {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        error: {
          code: 'UNAUTHORIZED',
          message: 'Authentication required',
        },
      });
    }

    const { jobId } = req.params;

    if (!jobId) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'MISSING_JOB_ID',
          message: 'Job ID is required',
        },
      });
    }

    // Get video job
    const job = await videoService.getVideoJob(jobId, req.user.id);

    res.status(200).json({
      success: true,
      data: job,
    });
  } catch (error) {
    if (error instanceof AppError) {
      return res.status(error.statusCode || 500).json({
        success: false,
        error: {
          code: error.code || 'SERVER_ERROR',
          message: error.message,
        },
      });
    }

    res.status(500).json({
      success: false,
      error: {
        code: 'SERVER_ERROR',
        message: 'Failed to get video generation job',
      },
    });
  }
};

export const getUserVideoJobs = async (req: Request, res: Response) => {
  try {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        error: {
          code: 'UNAUTHORIZED',
          message: 'Authentication required',
        },
      });
    }

    // Get user's video jobs
    const jobs = await videoService.getUserVideoJobs(req.user.id);

    res.status(200).json({
      success: true,
      data: jobs,
    });
  } catch (error) {
    if (error instanceof AppError) {
      return res.status(error.statusCode || 500).json({
        success: false,
        error: {
          code: error.code || 'SERVER_ERROR',
          message: error.message,
        },
      });
    }

    res.status(500).json({
      success: false,
      error: {
        code: 'SERVER_ERROR',
        message: 'Failed to get user video generation jobs',
      },
    });
  }
};

/**
 * Get available avatars for video generation
 */
export const getAvatars = async (req: Request, res: Response) => {
  try {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        error: {
          code: 'UNAUTHORIZED',
          message: 'Authentication required',
        },
      });
    }

    // In a real implementation, this would fetch avatars from a database or external API
    // For now, we'll return mock data
    const avatars = [
      {
        id: 'avatar-1',
        name: 'Anna',
        gender: 'female',
        thumbnail: 'https://via.placeholder.com/150?text=Anna',
        category: 'professional',
      },
      {
        id: 'avatar-2',
        name: 'John',
        gender: 'male',
        thumbnail: 'https://via.placeholder.com/150?text=John',
        category: 'professional',
      },
      {
        id: 'avatar-3',
        name: 'Maria',
        gender: 'female',
        thumbnail: 'https://via.placeholder.com/150?text=Maria',
        category: 'casual',
      },
      {
        id: 'avatar-4',
        name: 'David',
        gender: 'male',
        thumbnail: 'https://via.placeholder.com/150?text=David',
        category: 'casual',
      },
    ];

    res.status(200).json({
      success: true,
      data: avatars,
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: {
        code: 'SERVER_ERROR',
        message: 'Failed to get avatars',
      },
    });
  }
};

/**
 * Get available voices for video generation
 */
export const getVoices = async (req: Request, res: Response) => {
  try {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        error: {
          code: 'UNAUTHORIZED',
          message: 'Authentication required',
        },
      });
    }

    // In a real implementation, this would fetch voices from a database or external API
    // For now, we'll return mock data
    const voices = [
      {
        id: 'voice-1',
        name: 'Emma',
        gender: 'female',
        language: 'English (US)',
        preview: 'https://example.com/voice-samples/emma.mp3',
      },
      {
        id: 'voice-2',
        name: 'Michael',
        gender: 'male',
        language: 'English (US)',
        preview: 'https://example.com/voice-samples/michael.mp3',
      },
      {
        id: 'voice-3',
        name: 'Sophie',
        gender: 'female',
        language: 'English (UK)',
        preview: 'https://example.com/voice-samples/sophie.mp3',
      },
      {
        id: 'voice-4',
        name: 'James',
        gender: 'male',
        language: 'English (UK)',
        preview: 'https://example.com/voice-samples/james.mp3',
      },
    ];

    res.status(200).json({
      success: true,
      data: voices,
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: {
        code: 'SERVER_ERROR',
        message: 'Failed to get voices',
      },
    });
  }
};
