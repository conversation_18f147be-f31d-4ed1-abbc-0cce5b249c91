import { Request, Response } from "express";
import { RefundService } from "../services/refund.service";
import { AppError } from "../utils/error";
import { z } from "zod";

const refundService = new RefundService();

export const requestSubscriptionRefund = async (
  req: Request,
  res: Response
) => {
  try {
    const { subscriptionId } = req.body;
    const userId = req.user?.id;

    if (!userId) {
      throw new AppError("User not authenticated", 401, "UNAUTHORIZED");
    }

    if (!subscriptionId) {
      throw new AppError(
        "Subscription ID is required",
        400,
        "MISSING_SUBSCRIPTION_ID"
      );
    }

    const refund = await refundService.requestSubscriptionRefund(
      userId,
      subscriptionId
    );

    res.status(200).json({
      success: true,
      data: refund,
      message:
        "Refund request submitted successfully. Your subscription has been canceled.",
    });
  } catch (error) {
    console.error("Error requesting subscription refund:", error);
    if (error instanceof AppError) {
      res.status(error.statusCode).json({
        success: false,
        message: error.message,
        error: error.code,
      });
    } else {
      res.status(500).json({
        success: false,
        message:
          "An unexpected error occurred while processing your refund request.",
      });
    }
  }
};

export const getUserRefunds = async (req: Request, res: Response) => {
  try {
    const userId = req.user?.id;

    if (!userId) {
      throw new AppError("User not authenticated", 401, "UNAUTHORIZED");
    }

    const refunds = await refundService.getUserRefunds(userId);

    res.status(200).json({
      success: true,
      data: refunds,
    });
  } catch (error) {
    console.error("Error getting user refunds:", error);
    if (error instanceof AppError) {
      res.status(error.statusCode).json({
        success: false,
        message: error.message,
        error: error.code,
      });
    } else {
      res.status(500).json({
        success: false,
        message: "An unexpected error occurred while retrieving your refunds.",
      });
    }
  }
};

// Admin endpoints
export const getAllRefunds = async (req: Request, res: Response) => {
  try {
    const page = req.query.page ? parseInt(req.query.page as string) : 1;
    const limit = req.query.limit ? parseInt(req.query.limit as string) : 10;
    const status = req.query.status as string;

    const result = await refundService.getAllRefunds(
      page,
      limit,
      status as any
    );

    res.status(200).json({
      success: true,
      data: result.data,
      meta: result.meta,
    });
  } catch (error) {
    console.error("Error getting all refunds:", error);
    if (error instanceof AppError) {
      res.status(error.statusCode).json({
        success: false,
        message: error.message,
        error: error.code,
      });
    } else {
      res.status(500).json({
        success: false,
        message: "An unexpected error occurred while retrieving refunds.",
      });
    }
  }
};

export const processRefund = async (req: Request, res: Response) => {
  try {
    const schema = z.object({
      refundId: z.string(),
      approved: z.boolean(),
      notes: z.string().optional(),
    });

    const { refundId, approved, notes } = schema.parse(req.body);

    // Get admin ID from authenticated user
    const adminId = req.user?.id;

    if (!adminId) {
      throw new AppError("Admin ID is required", 401, "UNAUTHORIZED");
    }

    const refund = await refundService.processRefund(
      refundId,
      approved,
      adminId,
      notes
    );

    res.status(200).json({
      success: true,
      data: refund,
      message: `Refund ${approved ? "approved" : "rejected"} successfully.`,
    });
  } catch (error) {
    console.error("Error processing refund:", error);
    if (error instanceof AppError) {
      res.status(error.statusCode).json({
        success: false,
        message: error.message,
        error: error.code,
      });
    } else if (error instanceof z.ZodError) {
      res.status(400).json({
        success: false,
        message: "Invalid request data",
        errors: error.errors,
      });
    } else {
      res.status(500).json({
        success: false,
        message: "An unexpected error occurred while processing the refund.",
      });
    }
  }
};
