import { Request, Response } from "express";
import { prisma } from "../index";
import { format, subDays, subMonths, subWeeks, parseISO } from "date-fns";

// Helper function to generate date ranges based on time range
const getDateRange = (timeRange: string) => {
  const now = new Date();
  let startDate: Date;

  switch (timeRange) {
    case "daily":
      startDate = subDays(now, 30); // Last 30 days
      break;
    case "weekly":
      startDate = subWeeks(now, 12); // Last 12 weeks
      break;
    case "monthly":
    default:
      startDate = subMonths(now, 6); // Last 6 months
      break;
  }

  return {
    startDate,
    endDate: now,
  };
};

// Helper function to format dates based on time range
const formatDateForTimeRange = (date: Date, timeRange: string) => {
  switch (timeRange) {
    case "daily":
      return format(date, "yyyy-MM-dd");
    case "weekly":
      return `Week ${format(date, "w")} - ${format(date, "yyyy")}`;
    case "monthly":
    default:
      return format(date, "MMM yyyy");
  }
};

// Get revenue time series data
export const getRevenueTimeSeries = async (req: Request, res: Response) => {
  try {
    const { timeRange = "monthly" } = req.query;
    const { startDate, endDate } = getDateRange(timeRange as string);

    // Get payments from the database
    const payments = await prisma.payment.findMany({
      where: {
        status: "COMPLETED",
        createdAt: {
          gte: startDate,
          lte: endDate,
        },
      },
      orderBy: {
        createdAt: "asc",
      },
    });

    // Group payments by date
    const revenueByDate = new Map();
    
    payments.forEach((payment) => {
      const dateKey = formatDateForTimeRange(payment.createdAt, timeRange as string);
      const currentAmount = revenueByDate.get(dateKey) || 0;
      revenueByDate.set(dateKey, currentAmount + payment.amount);
    });

    // Convert to time series format
    const labels = Array.from(revenueByDate.keys());
    const data = Array.from(revenueByDate.values());
    
    // Create chart data points
    const chartData = labels.map((date, index) => ({
      date,
      amount: data[index],
    }));

    // Return formatted response
    const responseData: any = {
      labels,
      datasets: [
        {
          label: "Revenue",
          data,
        },
      ],
    };
    
    // Add time range data dynamically
    responseData[timeRange as string] = chartData;
    
    res.status(200).json({
      success: true,
      data: responseData,
    });
  } catch (error) {
    console.error("Error fetching revenue time series:", error);
    res.status(500).json({
      success: false,
      error: {
        code: "SERVER_ERROR",
        message: "Failed to fetch revenue time series data",
      },
    });
  }
};

// Get usage time series data
export const getUsageTimeSeries = async (req: Request, res: Response) => {
  try {
    const { timeRange = "monthly" } = req.query;
    const { startDate, endDate } = getDateRange(timeRange as string);

    // Get API usage logs from the database
    const usageLogs = await prisma.apiUsageLog.findMany({
      where: {
        requestTimestamp: {
          gte: startDate,
          lte: endDate,
        },
      },
      orderBy: {
        requestTimestamp: "asc",
      },
    });

    // Group usage by date and feature type
    const usageByDate = new Map();
    const videoByDate = new Map();
    const imageByDate = new Map();
    const bgRemovalByDate = new Map();
    
    usageLogs.forEach((log) => {
      const dateKey = formatDateForTimeRange(log.requestTimestamp, timeRange as string);
      
      // Total usage
      const currentUsage = usageByDate.get(dateKey) || 0;
      usageByDate.set(dateKey, currentUsage + 1);
      
      // Usage by feature
      if (log.featureUsed === "VIDEO_GENERATION") {
        const current = videoByDate.get(dateKey) || 0;
        videoByDate.set(dateKey, current + 1);
      } else if (log.featureUsed === "IMAGE_GENERATION") {
        const current = imageByDate.get(dateKey) || 0;
        imageByDate.set(dateKey, current + 1);
      } else if (log.featureUsed === "BACKGROUND_REMOVAL") {
        const current = bgRemovalByDate.get(dateKey) || 0;
        bgRemovalByDate.set(dateKey, current + 1);
      }
    });

    // Convert to time series format
    const labels = Array.from(usageByDate.keys());
    const data = Array.from(usageByDate.values());
    
    // Create chart data points
    const chartData = labels.map((date, index) => ({
      date,
      amount: data[index],
    }));

    // Create feature-specific chart data
    const videoData = labels.map(date => ({
      date,
      amount: videoByDate.get(date) || 0,
    }));
    
    const imageData = labels.map(date => ({
      date,
      amount: imageByDate.get(date) || 0,
    }));
    
    const bgRemovalData = labels.map(date => ({
      date,
      amount: bgRemovalByDate.get(date) || 0,
    }));

    // Return formatted response
    const responseData: any = {
      labels,
      datasets: [
        {
          label: "Total Usage",
          data,
        },
      ],
      videoGeneration: videoData,
      imageGeneration: imageData,
      backgroundRemoval: bgRemovalData,
    };
    
    // Add time range data dynamically
    responseData[timeRange as string] = chartData;
    
    res.status(200).json({
      success: true,
      data: responseData,
    });
  } catch (error) {
    console.error("Error fetching usage time series:", error);
    res.status(500).json({
      success: false,
      error: {
        code: "SERVER_ERROR",
        message: "Failed to fetch usage time series data",
      },
    });
  }
};

// Get user growth time series data
export const getUserGrowthTimeSeries = async (req: Request, res: Response) => {
  try {
    const { timeRange = "monthly" } = req.query;
    const { startDate, endDate } = getDateRange(timeRange as string);

    // Get users created in the date range
    const users = await prisma.user.findMany({
      where: {
        createdAt: {
          gte: startDate,
          lte: endDate,
        },
      },
      orderBy: {
        createdAt: "asc",
      },
    });

    // Group users by date
    const usersByDate = new Map();
    
    users.forEach((user) => {
      const dateKey = formatDateForTimeRange(user.createdAt, timeRange as string);
      const currentCount = usersByDate.get(dateKey) || 0;
      usersByDate.set(dateKey, currentCount + 1);
    });

    // Convert to time series format
    const labels = Array.from(usersByDate.keys());
    const data = Array.from(usersByDate.values());
    
    // Create chart data points
    const chartData = labels.map((date, index) => ({
      date,
      amount: data[index],
    }));

    // Return formatted response
    const responseData: any = {
      labels,
      datasets: [
        {
          label: "New Users",
          data,
        },
      ],
    };
    
    // Add time range data dynamically
    responseData[timeRange as string] = chartData;
    
    res.status(200).json({
      success: true,
      data: responseData,
    });
  } catch (error) {
    console.error("Error fetching user growth time series:", error);
    res.status(500).json({
      success: false,
      error: {
        code: "SERVER_ERROR",
        message: "Failed to fetch user growth time series data",
      },
    });
  }
};

// Get user statistics
export const getUserStats = async (req: Request, res: Response) => {
  try {
    // Get total users count
    const totalUsers = await prisma.user.count();
    
    // Get active users (logged in within the last 30 days)
    const thirtyDaysAgo = subDays(new Date(), 30);
    const activeUsers = await prisma.user.count({
      where: {
        lastLogin: {
          gte: thirtyDaysAgo,
        },
      },
    });
    
    // Get new users in the last 30 days
    const newUsers = await prisma.user.count({
      where: {
        createdAt: {
          gte: thirtyDaysAgo,
        },
      },
    });
    
    // Get users by subscription plan
    const subscriptions = await prisma.userSubscription.findMany({
      where: {
        status: "ACTIVE",
      },
      include: {
        plan: true,
      },
    });
    
    // Count users by plan
    const planCounts = new Map();
    subscriptions.forEach((sub) => {
      const planName = sub.plan.name;
      const currentCount = planCounts.get(planName) || 0;
      planCounts.set(planName, currentCount + 1);
    });
    
    // Add free users (users without an active subscription)
    const usersWithSubscription = new Set(subscriptions.map(sub => sub.userId));
    const freeUsersCount = totalUsers - usersWithSubscription.size;
    planCounts.set("Free", freeUsersCount);
    
    // Calculate percentages
    const usersByPlan = Array.from(planCounts.entries()).map(([name, count]) => {
      return {
        name,
        count,
        percentage: Math.round((count as number / totalUsers) * 100),
      };
    });
    
    // Sort by count descending
    usersByPlan.sort((a, b) => b.count - a.count);
    
    // Calculate user growth percentage (compared to previous 30 days)
    const sixtyDaysAgo = subDays(new Date(), 60);
    const previousPeriodUsers = await prisma.user.count({
      where: {
        createdAt: {
          gte: sixtyDaysAgo,
          lt: thirtyDaysAgo,
        },
      },
    });
    
    const userGrowth = previousPeriodUsers === 0 
      ? 100 
      : Math.round(((newUsers - previousPeriodUsers) / previousPeriodUsers) * 100);
    
    // Estimate retention and churn rates (simplified calculation)
    // In a real app, you'd track actual user sessions over time
    const retentionRate = Math.round((activeUsers / totalUsers) * 100);
    const churnRate = 100 - retentionRate;
    
    // Return formatted response
    res.status(200).json({
      success: true,
      data: {
        totalUsers,
        activeUsers,
        newUsers,
        usersByPlan,
        userGrowth,
        retentionRate,
        churnRate,
      },
    });
  } catch (error) {
    console.error("Error fetching user stats:", error);
    res.status(500).json({
      success: false,
      error: {
        code: "SERVER_ERROR",
        message: "Failed to fetch user statistics",
      },
    });
  }
};
