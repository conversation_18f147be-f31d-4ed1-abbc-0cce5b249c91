import { Request, Response } from "express";
import { z } from "zod";
import { PaymentService } from "../services/payment.service";
// import paypalService from "../services/paypal.service";

import { prisma } from "../index";
import Stripe from "stripe";
import { AppError } from "../utils/error";
import { STRIPE_SECRET_KEY, STRIPE_WEBHOOK_SECRET } from "../config";

// Initialize payment service
const paymentService = new PaymentService();

// Validation schemas
const checkoutSessionSchema = z.object({
  planId: z.string(),
  successUrl: z.string().url(),
  cancelUrl: z.string().url(),
});

const creditCheckoutSessionSchema = z.object({
  amount: z.number().positive(),
  credits: z.number().positive(),
  successUrl: z.string().url(),
  cancelUrl: z.string().url(),
});

const subscriptionCancelSchema = z.object({
  subscriptionId: z.string(),
});

const paypalOrderSchema = z.object({
  amount: z.number().positive(),
  credits: z.number().positive(),
});

const paypalCaptureSchema = z.object({
  orderId: z.string(),
});

export const getSubscriptionPlans = async (req: Request, res: Response) => {
  try {
    // Get all active subscription plans
    const plans = await paymentService.getSubscriptionPlans();

    res.status(200).json({
      success: true,
      data: plans,
    });
  } catch (error) {
    if (error instanceof AppError) {
      return res.status(error.statusCode || 500).json({
        success: false,
        error: {
          code: error.code || "SERVER_ERROR",
          message: error.message,
        },
      });
    }

    res.status(500).json({
      success: false,
      error: {
        code: "SERVER_ERROR",
        message: "Failed to get subscription plans",
      },
    });
  }
};

export const getCreditPacks = async (req: Request, res: Response) => {
  try {
    // Get all available credit packs
    const creditPacks = await paymentService.getCreditPacks();

    res.status(200).json({
      success: true,
      data: creditPacks,
    });
  } catch (error) {
    if (error instanceof AppError) {
      return res.status(error.statusCode || 500).json({
        success: false,
        error: {
          code: error.code || "SERVER_ERROR",
          message: error.message,
        },
      });
    }

    res.status(500).json({
      success: false,
      error: {
        code: "SERVER_ERROR",
        message: "Failed to get credit packs",
      },
    });
  }
};

export const createStripeCheckoutSession = async (
  req: Request,
  res: Response
) => {
  try {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        error: {
          code: "UNAUTHORIZED",
          message: "Authentication required",
        },
      });
    }

    // Validate request body
    const validatedData = checkoutSessionSchema.parse(req.body);

    // Get user
    const user = await prisma.user.findUnique({
      where: { id: req.user.id },
    });

    if (!user) {
      return res.status(404).json({
        success: false,
        error: {
          code: "USER_NOT_FOUND",
          message: "User not found",
        },
      });
    }

    // Create checkout session
    const session = await paymentService.createStripeCheckoutSession(
      user,
      validatedData.planId,
      validatedData.successUrl,
      validatedData.cancelUrl
    );

    res.status(200).json({
      success: true,
      data: session,
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        error: {
          code: "VALIDATION_ERROR",
          message: "Invalid input data",
          details: error.errors,
        },
      });
    }

    if (error instanceof AppError) {
      return res.status(error.statusCode || 500).json({
        success: false,
        error: {
          code: error.code || "SERVER_ERROR",
          message: error.message,
        },
      });
    }

    res.status(500).json({
      success: false,
      error: {
        code: "SERVER_ERROR",
        message: "Failed to create checkout session",
      },
    });
  }
};

export const createStripeCreditCheckoutSession = async (
  req: Request,
  res: Response
) => {
  try {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        error: {
          code: "UNAUTHORIZED",
          message: "Authentication required",
        },
      });
    }

    // Validate request body
    const validatedData = creditCheckoutSessionSchema.parse(req.body);

    // Get user
    const user = await prisma.user.findUnique({
      where: { id: req.user.id },
    });

    if (!user) {
      return res.status(404).json({
        success: false,
        error: {
          code: "USER_NOT_FOUND",
          message: "User not found",
        },
      });
    }

    // Create checkout session for credit purchase
    const session = await paymentService.createStripeCreditCheckoutSession(
      user,
      validatedData.amount,
      validatedData.credits,
      validatedData.successUrl,
      validatedData.cancelUrl
    );

    res.status(200).json({
      success: true,
      data: session,
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        error: {
          code: "VALIDATION_ERROR",
          message: "Invalid input data",
          details: error.errors,
        },
      });
    }

    if (error instanceof AppError) {
      return res.status(error.statusCode || 500).json({
        success: false,
        error: {
          code: error.code || "SERVER_ERROR",
          message: error.message,
        },
      });
    }

    res.status(500).json({
      success: false,
      error: {
        code: "SERVER_ERROR",
        message: "Failed to create credit checkout session",
      },
    });
  }
};

export const handleStripeWebhook = async (req: Request, res: Response) => {
  try {
    const signature = req.headers["stripe-signature"] as string;

    if (!signature) {
      console.error("Missing Stripe signature in webhook request");
      return res.status(400).json({
        success: false,
        error: {
          code: "MISSING_SIGNATURE",
          message: "Stripe signature is required",
        },
      });
    }

    const webhookSecret = STRIPE_WEBHOOK_SECRET;
    if (!webhookSecret) {
      console.error(
        "STRIPE_WEBHOOK_SECRET is not configured in environment variables"
      );
      return res.status(500).json({
        success: false,
        error: {
          code: "CONFIGURATION_ERROR",
          message: "Webhook secret is not configured",
        },
      });
    }

    const stripe = new Stripe(STRIPE_SECRET_KEY || "", {
      apiVersion: "2025-05-28.basil",
    });

    // Verify webhook signature
    let event;
    try {
      event = stripe.webhooks.constructEvent(
        req.body,
        signature,
        webhookSecret
      );
    } catch (err) {
      console.error(
        `⚠️ Webhook signature verification failed: ${
          err instanceof Error ? err.message : "Unknown error"
        }`
      );
      return res.status(400).json({
        success: false,
        error: {
          code: "INVALID_SIGNATURE",
          message: "Webhook signature verification failed",
        },
      });
    }

    console.log(`✅ Webhook received: ${event.type}`);

    // Handle webhook event with idempotency
    const idempotencyKey = event.id;
    await paymentService.handleStripeWebhook(event, idempotencyKey);

    res.status(200).json({ received: true, eventType: event.type });
  } catch (error) {
    console.error("Error handling Stripe webhook:", error);

    res.status(400).json({
      success: false,
      error: {
        code: "WEBHOOK_ERROR",
        message:
          error instanceof Error ? error.message : "Failed to handle webhook",
      },
    });
  }
};

export const cancelSubscription = async (req: Request, res: Response) => {
  try {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        error: {
          code: "UNAUTHORIZED",
          message: "Authentication required",
        },
      });
    }

    // Validate request body
    const validatedData = subscriptionCancelSchema.parse(req.body);

    // Get user
    const user = await prisma.user.findUnique({
      where: { id: req.user.id },
    });

    if (!user) {
      return res.status(404).json({
        success: false,
        error: {
          code: "USER_NOT_FOUND",
          message: "User not found",
        },
      });
    }

    // Cancel subscription
    const result = await paymentService.cancelSubscription(
      user.id,
      validatedData.subscriptionId
    );

    res.status(200).json({
      success: true,
      message: result.message,
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        error: {
          code: "VALIDATION_ERROR",
          message: "Invalid input data",
          details: error.errors,
        },
      });
    }

    if (error instanceof AppError) {
      return res.status(error.statusCode || 500).json({
        success: false,
        error: {
          code: error.code || "SERVER_ERROR",
          message: error.message,
        },
      });
    }

    res.status(500).json({
      success: false,
      error: {
        code: "SERVER_ERROR",
        message: "Failed to cancel subscription",
      },
    });
  }
};

export const getUserInvoices = async (req: Request, res: Response) => {
  try {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        error: {
          code: "UNAUTHORIZED",
          message: "Authentication required",
        },
      });
    }

    // Get user
    const user = await prisma.user.findUnique({
      where: { id: req.user.id },
    });

    if (!user) {
      return res.status(404).json({
        success: false,
        error: {
          code: "USER_NOT_FOUND",
          message: "User not found",
        },
      });
    }

    // Get user's invoices
    const invoices = await paymentService.getUserInvoices(user);

    res.status(200).json({
      success: true,
      data: invoices,
    });
  } catch (error) {
    if (error instanceof AppError) {
      return res.status(error.statusCode || 500).json({
        success: false,
        error: {
          code: error.code || "SERVER_ERROR",
          message: error.message,
        },
      });
    }

    res.status(500).json({
      success: false,
      error: {
        code: "SERVER_ERROR",
        message: "Failed to get user invoices",
      },
    });
  }
};

export const getUserSubscription = async (req: Request, res: Response) => {
  try {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        error: {
          code: "UNAUTHORIZED",
          message: "Authentication required",
        },
      });
    }

    // Get user with active subscription
    const user = await prisma.user.findUnique({
      where: { id: req.user.id },
      include: {
        subscriptions: {
          where: {
            status: { in: ["ACTIVE", "TRIALING"] },
          },
          orderBy: {
            createdAt: "desc",
          },
          take: 1,
          include: {
            plan: true,
          },
        },
      },
    });

    if (!user) {
      return res.status(404).json({
        success: false,
        error: {
          code: "USER_NOT_FOUND",
          message: "User not found",
        },
      });
    }

    // Get user's active subscription
    const subscription = user.subscriptions[0] || null;

    res.status(200).json({
      success: true,
      data: subscription,
    });
  } catch (error) {
    if (error instanceof AppError) {
      return res.status(error.statusCode || 500).json({
        success: false,
        error: {
          code: error.code || "SERVER_ERROR",
          message: error.message,
        },
      });
    }

    res.status(500).json({
      success: false,
      error: {
        code: "SERVER_ERROR",
        message: "Failed to get user subscription",
      },
    });
  }
};

// export const createPayPalOrder = async (req: Request, res: Response) => {
//   try {
//     if (!req.user) {
//       return res.status(401).json({
//         success: false,
//         error: {
//           code: "UNAUTHORIZED",
//           message: "Authentication required",
//         },
//       });
//     }

//     // Validate request body
//     const validatedData = paypalOrderSchema.parse(req.body);

//     // Get user
//     const user = await prisma.user.findUnique({
//       where: { id: req.user.id },
//     });

//     if (!user) {
//       return res.status(404).json({
//         success: false,
//         error: {
//           code: "USER_NOT_FOUND",
//           message: "User not found",
//         },
//       });
//     }

//     // Create PayPal order using the dedicated PayPal service
//     const order = await paypalService.createOrder(
//       user,
//       validatedData.amount,
//       validatedData.credits
//     );

//     res.status(200).json({
//       success: true,
//       data: order,
//     });
//   } catch (error) {
//     if (error instanceof z.ZodError) {
//       return res.status(400).json({
//         success: false,
//         error: {
//           code: "VALIDATION_ERROR",
//           message: "Invalid input data",
//           details: error.errors,
//         },
//       });
//     }

//     if (error instanceof AppError) {
//       return res.status(error.statusCode || 500).json({
//         success: false,
//         error: {
//           code: error.code || "SERVER_ERROR",
//           message: error.message,
//         },
//       });
//     }

//     res.status(500).json({
//       success: false,
//       error: {
//         code: "SERVER_ERROR",
//         message: "Failed to create PayPal order",
//       },
//     });
//   }
// };

// export const capturePayPalOrder = async (req: Request, res: Response) => {
//   try {
//     // Validate request body or params
//     const { orderId } = req.params;

//     if (!orderId) {
//       return res.status(400).json({
//         success: false,
//         error: {
//           code: "MISSING_ORDER_ID",
//           message: "Order ID is required",
//         },
//       });
//     }

//     // Capture PayPal order using the dedicated PayPal service
//     const result = await paypalService.captureOrder(orderId);

//     res.status(200).json({
//       success: true,
//       data: result,
//     });
//   } catch (error) {
//     if (error instanceof AppError) {
//       return res.status(error.statusCode || 500).json({
//         success: false,
//         error: {
//           code: error.code || "SERVER_ERROR",
//           message: error.message,
//         },
//       });
//     }

//     res.status(500).json({
//       success: false,
//       error: {
//         code: "SERVER_ERROR",
//         message: "Failed to capture PayPal order",
//       },
//     });
//   }
// };

// export const handlePayPalWebhook = async (req: Request, res: Response) => {
//   try {
//     // Verify webhook signature
//     const webhookId = process.env.PAYPAL_WEBHOOK_ID;
//     const requestBody = req.body;

//     if (!webhookId) {
//       throw new Error("PayPal webhook ID not configured");
//     }

//     // Process the webhook event using the dedicated PayPal service
//     const result = await paypalService.handleWebhook(requestBody);

//     res.status(200).json(result);
//   } catch (error) {
//     console.error("Error handling PayPal webhook:", error);

//     // Always return 200 to PayPal to acknowledge receipt
//     res.status(200).json({
//       received: true,
//       error: error instanceof Error ? error.message : "Failed to handle webhook",
//     });
//   }
// };
