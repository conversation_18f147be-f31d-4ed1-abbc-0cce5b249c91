import { Request, Response, NextFunction } from "express";
import { AdminPaymentService } from "../services/admin-payment.service";
import { AppError } from "../utils/error";

export class AdminPaymentController {
  private adminPaymentService: AdminPaymentService;

  constructor() {
    this.adminPaymentService = new AdminPaymentService();
  }

  /**
   * Get all subscription payments with pagination and filtering
   */
  getAllPayments = async (req: Request, res: Response, next: NextFunction) => {
    try {
      const page = parseInt(req.query.page as string) || 1;
      const limit = parseInt(req.query.limit as string) || 10;
      const search = req.query.search as string;

      // Extract filters from query params
      const filters: Record<string, any> = {};
      if (req.query.userId) filters.userId = req.query.userId as string;
      if (req.query.status) filters.status = req.query.status as string;
      if (req.query.provider) filters.provider = req.query.provider as string;
      if (req.query.dateFrom) filters.dateFrom = req.query.dateFrom as string;
      if (req.query.dateTo) filters.dateTo = req.query.dateTo as string;
      if (req.query.paymentType)
        filters.paymentType = req.query.paymentType as string;

      const result = await this.adminPaymentService.getAllPayments(
        page,
        limit,
        filters,
        search
      );

      res.status(200).json({
        success: true,
        ...result,
      });
    } catch (error) {
      next(error);
    }
  };

  /**
   * Get all refund requests with pagination and filtering
   */
  getAllRefundRequests = async (
    req: Request,
    res: Response,
    next: NextFunction
  ) => {
    try {
      const page = parseInt(req.query.page as string) || 1;
      const limit = parseInt(req.query.limit as string) || 10;

      // Extract filters from query params
      const filters: Record<string, any> = {};
      if (req.query.userId) filters.userId = req.query.userId as string;
      if (req.query.status) filters.status = req.query.status as string;
      if (req.query.dateFrom) filters.dateFrom = req.query.dateFrom as string;
      if (req.query.dateTo) filters.dateTo = req.query.dateTo as string;

      const result = await this.adminPaymentService.getAllRefundRequests(
        page,
        limit,
        filters
      );

      res.status(200).json({
        success: true,
        ...result,
      });
    } catch (error) {
      next(error);
    }
  };

  /**
   * Get all canceled subscriptions with pagination and filtering
   */
  getCanceledSubscriptions = async (
    req: Request,
    res: Response,
    next: NextFunction
  ) => {
    try {
      const page = parseInt(req.query.page as string) || 1;
      const limit = parseInt(req.query.limit as string) || 10;

      // Extract filters from query params
      const filters: Record<string, any> = {};
      if (req.query.userId) filters.userId = req.query.userId as string;
      if (req.query.dateFrom) filters.dateFrom = req.query.dateFrom as string;
      if (req.query.dateTo) filters.dateTo = req.query.dateTo as string;

      const result = await this.adminPaymentService.getCanceledSubscriptions(
        page,
        limit,
        filters
      );

      res.status(200).json({
        success: true,
        ...result,
      });
    } catch (error) {
      next(error);
    }
  };

  /**
   * Process a refund request (approve or reject)
   */
  processRefundRequest = async (
    req: Request,
    res: Response,
    next: NextFunction
  ) => {
    try {
      const { refundId } = req.params;
      const { action, notes } = req.body;

      // Validate the action
      if (action !== "approve" && action !== "reject") {
        throw new AppError(
          "Invalid action. Must be 'approve' or 'reject'",
          400,
          "INVALID_ACTION"
        );
      }

      // Get the admin ID from the authenticated user
      const adminId = req.user?.id;
      if (!adminId) {
        throw new AppError("Admin ID not found", 401, "UNAUTHORIZED");
      }

      const result = await this.adminPaymentService.processRefundRequest(
        refundId,
        action,
        adminId,
        notes
      );

      res.status(200).json(result);
    } catch (error) {
      next(error);
    }
  };

  /**
   * Create a new subscription plan
   */
  createSubscriptionPlan = async (
    req: Request,
    res: Response,
    next: NextFunction
  ) => {
    try {
      const planData = req.body;

      // Validate required fields
      if (
        !planData.name ||
        !planData.price ||
        !planData.currency ||
        !planData.interval
      ) {
        throw new AppError(
          "Missing required fields for subscription plan",
          400,
          "INVALID_PLAN_DATA"
        );
      }

      const result = await this.adminPaymentService.createSubscriptionPlan(
        planData
      );

      res.status(201).json(result);
    } catch (error) {
      next(error);
    }
  };

  /**
   * Update an existing subscription plan
   */
  updateSubscriptionPlan = async (
    req: Request,
    res: Response,
    next: NextFunction
  ) => {
    try {
      const { planId } = req.params;
      const planData = req.body;

      const result = await this.adminPaymentService.updateSubscriptionPlan(
        planId,
        planData
      );

      res.status(200).json(result);
    } catch (error) {
      next(error);
    }
  };

  /**
   * Delete a subscription plan
   */
  deleteSubscriptionPlan = async (
    req: Request,
    res: Response,
    next: NextFunction
  ) => {
    try {
      const { planId } = req.params;

      const result = await this.adminPaymentService.deleteSubscriptionPlan(
        planId
      );

      res.status(200).json(result);
    } catch (error) {
      next(error);
    }
  };

  /**
   * Get subscription analytics data
   */
  getSubscriptionAnalytics = async (
    req: Request,
    res: Response,
    next: NextFunction
  ) => {
    try {
      const analytics =
        await this.adminPaymentService.getSubscriptionAnalytics();

      res.status(200).json({
        success: true,
        data: analytics,
      });
    } catch (error) {
      next(error);
    }
  };
}
