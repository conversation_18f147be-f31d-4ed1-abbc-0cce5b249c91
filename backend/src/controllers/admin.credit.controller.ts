import { Request, Response } from "express";
import { z } from "zod";
import { prisma } from "../index";
import { AppError } from "../utils/error";
import { PaymentService } from "../services/payment.service";

// Initialize services
const paymentService = new PaymentService();

// Validation schemas
const createCreditPackageSchema = z.object({
  name: z.string(),
  description: z.string().optional(),
  creditsAmount: z.number().positive(),
  price: z.number().positive(),
  currency: z.string().default("USD"),
  stripePriceId: z.string().optional(),
  paypalPlanId: z.string().optional(),
  isActive: z.boolean().default(true),
});

const updateCreditPackageSchema = createCreditPackageSchema.partial();

/**
 * Get all credit packages (admin)
 */
export const getAllCreditPackages = async (req: Request, res: Response) => {
  try {
    if (!req.user || req.user.role !== "ADMIN") {
      return res.status(403).json({
        success: false,
        error: {
          code: "FORBIDDEN",
          message: "Admin access required",
        },
      });
    }

    const packages = await prisma.creditPackage.findMany({
      orderBy: [{ price: "asc" }],
    });

    res.status(200).json({
      success: true,
      data: packages,
    });
  } catch (error) {
    console.error("Error getting credit packages:", error);
    res.status(500).json({
      success: false,
      error: {
        code: "SERVER_ERROR",
        message: "Failed to get credit packages",
      },
    });
  }
};

/**
 * Create a new credit package (admin)
 */
export const createCreditPackage = async (req: Request, res: Response) => {
  try {
    if (!req.user || req.user.role !== "ADMIN") {
      return res.status(403).json({
        success: false,
        error: {
          code: "FORBIDDEN",
          message: "Admin access required",
        },
      });
    }

    // Validate request body
    const validationResult = createCreditPackageSchema.safeParse(req.body);
    if (!validationResult.success) {
      return res.status(400).json({
        success: false,
        error: {
          code: "VALIDATION_ERROR",
          message: "Invalid credit package data",
          details: validationResult.error.format(),
        },
      });
    }

    const packageData = validationResult.data;

    // Create the credit package
    const creditPackage = await prisma.creditPackage.create({
      data: packageData,
    });

    res.status(201).json({
      success: true,
      data: creditPackage,
    });
  } catch (error) {
    console.error("Error creating credit package:", error);
    res.status(500).json({
      success: false,
      error: {
        code: "SERVER_ERROR",
        message: "Failed to create credit package",
      },
    });
  }
};

/**
 * Update a credit package (admin)
 */
export const updateCreditPackage = async (req: Request, res: Response) => {
  try {
    if (!req.user || req.user.role !== "ADMIN") {
      return res.status(403).json({
        success: false,
        error: {
          code: "FORBIDDEN",
          message: "Admin access required",
        },
      });
    }

    const { id } = req.params;

    // Validate request body
    const validationResult = updateCreditPackageSchema.safeParse(req.body);
    if (!validationResult.success) {
      return res.status(400).json({
        success: false,
        error: {
          code: "VALIDATION_ERROR",
          message: "Invalid credit package data",
          details: validationResult.error.format(),
        },
      });
    }

    const packageData = validationResult.data;

    // Check if package exists
    const existingPackage = await prisma.creditPackage.findUnique({
      where: { id },
    });

    if (!existingPackage) {
      return res.status(404).json({
        success: false,
        error: {
          code: "NOT_FOUND",
          message: "Credit package not found",
        },
      });
    }

    // Update the credit package
    const creditPackage = await prisma.creditPackage.update({
      where: { id },
      data: packageData,
    });

    res.status(200).json({
      success: true,
      data: creditPackage,
    });
  } catch (error) {
    console.error("Error updating credit package:", error);
    res.status(500).json({
      success: false,
      error: {
        code: "SERVER_ERROR",
        message: "Failed to update credit package",
      },
    });
  }
};

/**
 * Delete a credit package (admin)
 */
export const deleteCreditPackage = async (req: Request, res: Response) => {
  try {
    if (!req.user || req.user.role !== "ADMIN") {
      return res.status(403).json({
        success: false,
        error: {
          code: "FORBIDDEN",
          message: "Admin access required",
        },
      });
    }

    const { id } = req.params;

    // Check if package exists
    const existingPackage = await prisma.creditPackage.findUnique({
      where: { id },
    });

    if (!existingPackage) {
      return res.status(404).json({
        success: false,
        error: {
          code: "NOT_FOUND",
          message: "Credit package not found",
        },
      });
    }

    // Check if package is in use
    const purchaseOrdersUsingPackage = await prisma.creditPurchaseOrder.count({
      where: { creditPackageId: id },
    });

    if (purchaseOrdersUsingPackage > 0) {
      // Instead of deleting, just mark as inactive
      await prisma.creditPackage.update({
        where: { id },
        data: { isActive: false },
      });

      return res.status(200).json({
        success: true,
        data: {
          message: "Package marked as inactive because it has been purchased",
        },
      });
    }

    // Delete the credit package if not in use
    await prisma.creditPackage.delete({
      where: { id },
    });

    res.status(200).json({
      success: true,
      data: {
        message: "Credit package deleted successfully",
      },
    });
  } catch (error) {
    console.error("Error deleting credit package:", error);
    res.status(500).json({
      success: false,
      error: {
        code: "SERVER_ERROR",
        message: "Failed to delete credit package",
      },
    });
  }
};
