import { Request, Response } from "express";
import bcrypt from "bcrypt";
import { prisma } from "../index";
import { z } from "zod";
import path from "path";
import fs from "fs";
import sharp from "sharp";
import {
  getProfileImageUrl,
  getAbsoluteProfileImagePath,
} from "../middleware/upload";

// Validation schemas
const updateProfileSchema = z.object({
  firstName: z.string().optional(),
  lastName: z.string().optional(),
  avatarUrl: z.string().optional(),
});

const updatePasswordSchema = z.object({
  currentPassword: z.string(),
  newPassword: z.string().min(8),
});

export const getCurrentUser = async (req: Request, res: Response) => {
  try {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        error: {
          code: "UNAUTHORIZED",
          message: "Authentication required",
        },
      });
    }

    // Get user with profile, subscription, and credit
    const user = await prisma.user.findUnique({
      where: { id: req.user.id },
      include: {
        profile: true,
        creditPurchaseOrders: {
          include: {
            creditPackage: true,
          },
          where: {
            status: "COMPLETED",
          },
          orderBy: {
            createdAt: "desc",
          },
          take: 1,
        },
        subscriptions: {
          where: {
            status: "ACTIVE",
          },
          include: {
            plan: true,
          },
          orderBy: {
            createdAt: "desc",
          },
          take: 1,
        },
        credit: true,
      },
    });

    if (!user) {
      return res.status(404).json({
        success: false,
        error: {
          code: "USER_NOT_FOUND",
          message: "User not found",
        },
      });
    }

    // Format response data
    const userData = {
      id: user.id,
      email: user.email,
      role: user.role,
      emailVerified: user.emailVerified,
      profile: user.profile,
      lastLogin: user.lastLogin,
      authProvider: user.authProvider,
      googleId: user.googleId,
      appleId: user.appleId,
      subscription: user.subscriptions[0] || null,
      creditPurchase: user.creditPurchaseOrders[0] || null,
      credit: user.credit,
      createdAt: user.createdAt,
    };

    res.status(200).json({
      success: true,
      data: userData,
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: {
        code: "SERVER_ERROR",
        message: "Failed to get user data",
      },
    });
  }
};
export const updateProfile = async (req: Request, res: Response) => {
  try {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        error: {
          code: "UNAUTHORIZED",
          message: "Authentication required",
        },
      });
    }
    console.log("req.body", req.body);
    // Validate request body
    const validatedData = updateProfileSchema.parse(req.body.profile);
    console.log("validatedData", validatedData);
    // Check if user exists
    const existingUser = await prisma.user.findUnique({
      where: { id: req.user.id },
      include: { profile: true },
    });

    if (!existingUser) {
      return res.status(404).json({
        success: false,
        error: {
          code: "USER_NOT_FOUND",
          message: "User not found",
        },
      });
    }

    // Update profile
    if (existingUser.profile) {
      // Update existing profile
      await prisma.userProfile.update({
        where: { userId: req.user.id },
        data: {
          firstName:
            validatedData.firstName !== undefined
              ? validatedData.firstName
              : existingUser.profile.firstName,
          lastName:
            validatedData.lastName !== undefined
              ? validatedData.lastName
              : existingUser.profile.lastName,
          avatarUrl:
            validatedData.avatarUrl !== undefined
              ? validatedData.avatarUrl
              : existingUser.profile.avatarUrl,
        },
      });
    } else {
      // Create new profile
      await prisma.userProfile.create({
        data: {
          userId: req.user.id,
          firstName: validatedData.firstName,
          lastName: validatedData.lastName,
          avatarUrl: validatedData.avatarUrl,
        },
      });
    }

    // Get updated user
    const updatedUser = await prisma.user.findUnique({
      where: { id: req.user.id },
      include: { profile: true },
    });

    res.status(200).json({
      success: true,
      data: {
        id: updatedUser?.id,
        email: updatedUser?.email,
        profile: updatedUser?.profile,
      },
      message: "Profile updated successfully",
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        error: {
          code: "VALIDATION_ERROR",
          message: "Invalid input data",
          details: error.errors,
        },
      });
    }

    res.status(500).json({
      success: false,
      error: {
        code: "SERVER_ERROR",
        message: "Failed to update profile",
      },
    });
  }
};
// export const updateProfileWithPicture = async (req: Request, res: Response) => {
//   try {
//     if (!req.user) {
//       return res.status(401).json({
//         success: false,
//         error: {
//           code: "UNAUTHORIZED",
//           message: "Authentication required",
//         },
//       });
//     }

//     console.log("req.body", req.body);
//     console.log("req.file", req.file);

//     // Check if user exists
//     const existingUser = await prisma.user.findUnique({
//       where: { id: req.user.id },
//       include: { profile: true },
//     });

//     if (!existingUser) {
//       return res.status(404).json({
//         success: false,
//         error: {
//           code: "USER_NOT_FOUND",
//           message: "User not found",
//         },
//       });
//     }

//     let validatedData: any = {};
//     let avatarUrl: string | undefined;

//     // Validate profile data if provided
//     if (req.body.profile) {
//       try {
//         validatedData = updateProfileSchema.parse(req.body.profile);
//         console.log("validatedData", validatedData);
//       } catch (error) {
//         if (error instanceof z.ZodError) {
//           return res.status(400).json({
//             success: false,
//             error: {
//               code: "VALIDATION_ERROR",
//               message: "Invalid profile data",
//               details: error.errors,
//             },
//           });
//         }
//         throw error;
//       }
//     }

//     // Handle image upload if file is provided
//     if (req.file) {
//       try {
//         // Process the uploaded image with sharp
//         const outputFilename = `profile-${req.user.id}-${Date.now()}${path.extname(req.file.filename)}`;
//         const outputPath = getAbsoluteProfileImagePath(outputFilename);

//         await sharp(req.file.path)
//           .resize(256, 256) // Resize to standard profile image size
//           .jpeg({ quality: 90 }) // Convert to JPEG with good quality
//           .toFile(outputPath);

//         // Delete the original uploaded file to save space
//         fs.unlinkSync(req.file.path);

//         // Generate the public URL for the image
//         avatarUrl = getProfileImageUrl(outputFilename);

//         // If the user already has a profile image, delete the old one
//         if (existingUser?.profile?.avatarUrl) {
//           const oldFilename = path.basename(existingUser.profile.avatarUrl);
//           const oldFilePath = getAbsoluteProfileImagePath(oldFilename);
//           if (fs.existsSync(oldFilePath)) {
//             fs.unlinkSync(oldFilePath);
//           }
//         }
//       } catch (imageError) {
//         console.error("Image processing error:", imageError);
//         return res.status(500).json({
//           success: false,
//           error: {
//             code: "IMAGE_PROCESSING_ERROR",
//             message: "Failed to process uploaded image",
//           },
//         });
//       }
//     }

//     // Prepare update data
//     const updateData: any = {};

//     // Only update fields that are provided
//     if (validatedData.firstName !== undefined) {
//       updateData.firstName = validatedData.firstName;
//     }
//     if (validatedData.lastName !== undefined) {
//       updateData.lastName = validatedData.lastName;
//     }
//     if (validatedData.avatarUrl !== undefined) {
//       updateData.avatarUrl = validatedData.avatarUrl;
//     }
//     if (avatarUrl !== undefined) {
//       updateData.avatarUrl = avatarUrl; // Override with uploaded image URL
//     }

//     // Update or create profile
//     if (existingUser.profile) {
//       // Only update if there's data to update
//       if (Object.keys(updateData).length > 0) {
//         await prisma.userProfile.update({
//           where: { userId: req.user.id },
//           data: updateData,
//         });
//       }
//     } else {
//       // Create new profile with provided data or defaults
//       await prisma.userProfile.create({
//         data: {
//           userId: req.user.id,
//           firstName: updateData.firstName || "",
//           lastName: updateData.lastName || "",
//           avatarUrl: updateData.avatarUrl || null,
//         },
//       });
//     }

//     // Get updated user
//     const updatedUser = await prisma.user.findUnique({
//       where: { id: req.user.id },
//       include: { profile: true },
//     });

//     res.status(200).json({
//       success: true,
//       data: {
//         id: updatedUser?.id,
//         email: updatedUser?.email,
//         profile: updatedUser?.profile,
//       },
//       message: "Profile updated successfully",
//     });
//   } catch (error) {
//     console.error("Profile update error:", error);

//     // Clean up uploaded file if it exists and there's an error
//     if (req.file && fs.existsSync(req.file.path)) {
//       try {
//         fs.unlinkSync(req.file.path);
//       } catch (cleanupError) {
//         console.error("Failed to cleanup uploaded file:", cleanupError);
//       }
//     }

//     res.status(500).json({
//       success: false,
//       error: {
//         code: "SERVER_ERROR",
//         message: "Failed to update profile",
//       },
//     });
//   }
// };
export const updateProfileWithPicture = async (req: Request, res: Response) => {
  try {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        error: {
          code: "UNAUTHORIZED",
          message: "Authentication required",
        },
      });
    }

    console.log("req.body", req.body);
    console.log("req.file", req.file);

    // Check if user exists
    const existingUser = await prisma.user.findUnique({
      where: { id: req.user.id },
      include: { profile: true },
    });

    if (!existingUser) {
      return res.status(404).json({
        success: false,
        error: {
          code: "USER_NOT_FOUND",
          message: "User not found",
        },
      });
    }

    let validatedData: any = {};
    let avatarUrl: string | undefined;

    // Validate profile data if provided
    if (req.body.profile) {
      try {
        // Parse JSON string if it comes from FormData
        const profileData =
          typeof req.body.profile === "string"
            ? JSON.parse(req.body.profile)
            : req.body.profile;

        validatedData = updateProfileSchema.parse(profileData);
        console.log("validatedData", validatedData);
      } catch (error) {
        if (error instanceof z.ZodError) {
          return res.status(400).json({
            success: false,
            error: {
              code: "VALIDATION_ERROR",
              message: "Invalid profile data",
              details: error.errors,
            },
          });
        }
        if (error instanceof SyntaxError) {
          return res.status(400).json({
            success: false,
            error: {
              code: "INVALID_JSON",
              message: "Invalid JSON format in profile data",
            },
          });
        }
        throw error;
      }
    }

    // Handle image upload if file is provided
    if (req.file) {
      try {
        // Process the uploaded image with sharp
        const outputFilename = `profile-${req.user.id}-${Date.now()}${path.extname(req.file.filename)}`;
        const outputPath = getAbsoluteProfileImagePath(outputFilename);

        await sharp(req.file.path)
          .resize(256, 256) // Resize to standard profile image size
          .jpeg({ quality: 90 }) // Convert to JPEG with good quality
          .toFile(outputPath);

        // Delete the original uploaded file to save space
        fs.unlinkSync(req.file.path);

        // Generate the public URL for the image
        avatarUrl = getProfileImageUrl(outputFilename);

        // If the user already has a profile image, delete the old one
        if (existingUser?.profile?.avatarUrl) {
          const oldFilename = path.basename(existingUser.profile.avatarUrl);
          const oldFilePath = getAbsoluteProfileImagePath(oldFilename);
          if (fs.existsSync(oldFilePath)) {
            fs.unlinkSync(oldFilePath);
          }
        }
      } catch (imageError) {
        console.error("Image processing error:", imageError);
        return res.status(500).json({
          success: false,
          error: {
            code: "IMAGE_PROCESSING_ERROR",
            message: "Failed to process uploaded image",
          },
        });
      }
    }

    // Prepare update data
    const updateData: any = {};

    // Only update fields that are provided
    if (validatedData.firstName !== undefined) {
      updateData.firstName = validatedData.firstName;
    }
    if (validatedData.lastName !== undefined) {
      updateData.lastName = validatedData.lastName;
    }
    if (validatedData.avatarUrl !== undefined) {
      updateData.avatarUrl = validatedData.avatarUrl;
    }
    if (avatarUrl !== undefined) {
      updateData.avatarUrl = avatarUrl; // Override with uploaded image URL
    }

    // Update or create profile
    if (existingUser.profile) {
      // Only update if there's data to update
      if (Object.keys(updateData).length > 0) {
        await prisma.userProfile.update({
          where: { userId: req.user.id },
          data: updateData,
        });
      }
    } else {
      // Create new profile with provided data or defaults
      await prisma.userProfile.create({
        data: {
          userId: req.user.id,
          firstName: updateData.firstName || "",
          lastName: updateData.lastName || "",
          avatarUrl: updateData.avatarUrl || null,
        },
      });
    }

    // Get updated user
    const updatedUser = await prisma.user.findUnique({
      where: { id: req.user.id },
      include: { profile: true },
    });

    res.status(200).json({
      success: true,
      data: {
        id: updatedUser?.id,
        email: updatedUser?.email,
        profile: updatedUser?.profile,
      },
      message: "Profile updated successfully",
    });
  } catch (error) {
    console.error("Profile update error:", error);

    // Clean up uploaded file if it exists and there's an error
    if (req.file && fs.existsSync(req.file.path)) {
      try {
        fs.unlinkSync(req.file.path);
      } catch (cleanupError) {
        console.error("Failed to cleanup uploaded file:", cleanupError);
      }
    }

    res.status(500).json({
      success: false,
      error: {
        code: "SERVER_ERROR",
        message: "Failed to update profile",
      },
    });
  }
};
export const uploadProfileImage = async (req: Request, res: Response) => {
  try {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        error: {
          code: "UNAUTHORIZED",
          message: "Authentication required",
        },
      });
    }

    if (!req.file) {
      return res.status(400).json({
        success: false,
        error: {
          code: "FILE_REQUIRED",
          message: "No image file provided",
        },
      });
    }

    // Process the uploaded image with sharp
    const outputFilename = `profile-${req.user.id}-${Date.now()}${path.extname(req.file.filename)}`;
    const outputPath = getAbsoluteProfileImagePath(outputFilename);

    await sharp(req.file.path)
      .resize(256, 256) // Resize to standard profile image size
      .jpeg({ quality: 90 }) // Convert to JPEG with good quality
      .toFile(outputPath);

    // Delete the original uploaded file to save space
    fs.unlinkSync(req.file.path);

    // Generate the public URL for the image
    const avatarUrl = getProfileImageUrl(outputFilename);

    // Update the user's profile with the new avatar URL
    const existingUser = await prisma.user.findUnique({
      where: { id: req.user.id },
      include: { profile: true },
    });

    if (existingUser?.profile) {
      // If the user already has a profile image, delete the old one
      if (existingUser.profile.avatarUrl) {
        const oldFilename = path.basename(existingUser.profile.avatarUrl);
        const oldFilePath = getAbsoluteProfileImagePath(oldFilename);
        if (fs.existsSync(oldFilePath)) {
          fs.unlinkSync(oldFilePath);
        }
      }

      // Update existing profile
      await prisma.userProfile.update({
        where: { userId: req.user.id },
        data: {
          avatarUrl,
        },
      });
    } else {
      // Create new profile
      await prisma.userProfile.create({
        data: {
          userId: req.user.id,
          firstName: "",
          lastName: "",
          avatarUrl,
        },
      });
    }

    // Return the updated profile
    const updatedUser = await prisma.user.findUnique({
      where: { id: req.user.id },
      include: { profile: true },
    });

    res.status(200).json({
      success: true,
      data: {
        avatarUrl,
        profile: updatedUser?.profile,
      },
      message: "Profile image uploaded successfully",
    });
  } catch (error) {
    console.error("Profile image upload error:", error);
    res.status(500).json({
      success: false,
      error: {
        code: "SERVER_ERROR",
        message: "Failed to upload profile image",
      },
    });
  }
};

export const updatePassword = async (req: Request, res: Response) => {
  try {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        error: {
          code: "UNAUTHORIZED",
          message: "Authentication required",
        },
      });
    }

    // Validate request body
    const validatedData = updatePasswordSchema.parse(req.body);

    // Find user
    const user = await prisma.user.findUnique({
      where: { id: req.user.id },
    });

    if (!user) {
      return res.status(404).json({
        success: false,
        error: {
          code: "USER_NOT_FOUND",
          message: "User not found",
        },
      });
    }

    // Check if user has a password (not OAuth-only user)
    if (!user.password) {
      return res.status(400).json({
        success: false,
        error: {
          code: "OAUTH_USER",
          message:
            "This account uses social login. Password cannot be updated.",
        },
      });
    }

    // Verify current password
    const isPasswordValid = await bcrypt.compare(
      validatedData.currentPassword,
      user.password
    );

    if (!isPasswordValid) {
      return res.status(400).json({
        success: false,
        error: {
          code: "INVALID_PASSWORD",
          message: "Current password is incorrect",
        },
      });
    }

    // Hash new password
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(validatedData.newPassword, salt);

    // Update password
    await prisma.user.update({
      where: { id: req.user.id },
      data: { password: hashedPassword },
    });

    res.status(200).json({
      success: true,
      message: "Password updated successfully",
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        error: {
          code: "VALIDATION_ERROR",
          message: "Invalid input data",
          details: error.errors,
        },
      });
    }

    res.status(500).json({
      success: false,
      error: {
        code: "SERVER_ERROR",
        message: "Failed to update password",
      },
    });
  }
};

export const getCreditTransactions = async (req: Request, res: Response) => {
  try {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        error: {
          code: "UNAUTHORIZED",
          message: "Authentication required",
        },
      });
    }

    // Get user's credit transactions
    const transactions = await prisma.creditTransaction.findMany({
      where: { userId: req.user.id },
      orderBy: { createdAt: "desc" },
    });

    res.status(200).json({
      success: true,
      data: transactions,
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: {
        code: "SERVER_ERROR",
        message: "Failed to get credit transactions",
      },
    });
  }
};
