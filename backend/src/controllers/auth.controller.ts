import { Request, Response } from "express";
import bcrypt from "bcrypt";
import { prisma } from "../index";
import { generateTokens, verifyRefreshToken } from "../utils/jwt";
import { z } from "zod";
import crypto from "crypto";
import { Resend } from "resend";
import { RESEND_API_KEY, EMAIL_FROM } from "../config";
const resend = new Resend(RESEND_API_KEY);

// Rate limiting store (in production, use Redis)
const otpRateLimit = new Map<string, { count: number; resetTime: number }>();
const OTP_RATE_LIMIT = 3; // Max 3 OTP requests per hour
const OTP_RATE_WINDOW = 60 * 60 * 1000; // 1 hour in milliseconds

// Utility functions
const generateOTP = (): string => {
  return Math.floor(1000 + Math.random() * 9000).toString();
};

const checkRateLimit = (email: string): boolean => {
  const now = Date.now();
  const userLimit = otpRateLimit.get(email);

  if (!userLimit || now > userLimit.resetTime) {
    otpRateLimit.set(email, { count: 1, resetTime: now + OTP_RATE_WINDOW });
    return true;
  }

  if (userLimit.count >= OTP_RATE_LIMIT) {
    return false;
  }

  userLimit.count++;
  return true;
};

const sendVerificationEmail = async (
  email: string,
  otp: string,
  firstName?: string
) => {
  const { data, error } = await resend.emails.send({
    from: `Miragic AI <${EMAIL_FROM}>`,
    to: [email],
    subject: "Verify your email address",
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
        <h2 style="color: #333; text-align: center;">Welcome to Miragic AI!</h2>
        <p style="color: #666; font-size: 16px;">Hi ${firstName || "there"},</p>
        <p style="color: #666; font-size: 16px;">Thank you for signing up! Please verify your email address by entering the following 4-digit code:</p>
        <div style="background-color: #f8f9fa; border: 2px solid #e9ecef; border-radius: 8px; padding: 20px; text-align: center; margin: 20px 0;">
          <h1 style="color: #495057; font-size: 32px; margin: 0; letter-spacing: 8px;">${otp}</h1>
        </div>
        <p style="color: #666; font-size: 14px;">This code will expire in 1 hour. If you didn't create an account, please ignore this email.</p>
        <hr style="border: none; border-top: 1px solid #e9ecef; margin: 30px 0;">
        <p style="color: #999; font-size: 12px; text-align: center;">© 2024 Miragic AI. All rights reserved.</p>
      </div>
    `,
  });

  if (error) {
    console.error("Failed to send verification email:", error);
    throw new Error("Failed to send verification email");
  }

  return data;
};

const sendPasswordResetEmail = async (
  email: string,
  otp: string,
  firstName?: string
) => {
  const { data, error } = await resend.emails.send({
    from: `Miragic AI <${EMAIL_FROM}>`,
    to: [email],
    subject: "Reset your password",
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
        <h2 style="color: #333; text-align: center;">Password Reset Request</h2>
        <p style="color: #666; font-size: 16px;">Hi ${firstName || "there"},</p>
        <p style="color: #666; font-size: 16px;">We received a request to reset your password. Please use the following 4-digit code to reset your password:</p>
        <div style="background-color: #f8f9fa; border: 2px solid #e9ecef; border-radius: 8px; padding: 20px; text-align: center; margin: 20px 0;">
          <h1 style="color: #495057; font-size: 32px; margin: 0; letter-spacing: 8px;">${otp}</h1>
        </div>
        <p style="color: #666; font-size: 14px;">This code will expire in 1 hour. If you didn't request a password reset, please ignore this email.</p>
        <hr style="border: none; border-top: 1px solid #e9ecef; margin: 30px 0;">
        <p style="color: #999; font-size: 12px; text-align: center;">© 2024 Miragic AI. All rights reserved.</p>
      </div>
    `,
  });

  if (error) {
    console.error("Failed to send password reset email:", error);
    throw new Error("Failed to send password reset email");
  }

  return data;
};

// Validation schemas
const registerSchema = z.object({
  email: z.string().email(),
  password: z.string().min(8),
  firstName: z.string().optional(),
  lastName: z.string().optional(),
});

const loginSchema = z.object({
  email: z.string().email(),
  password: z.string(),
});

export const register = async (req: Request, res: Response) => {
  try {
    // Validate request body
    const validatedData = registerSchema.parse(req.body);

    // Check if user already exists
    const existingUser = await prisma.user.findUnique({
      where: { email: validatedData.email },
    });

    if (existingUser) {
      // Check if it's an OAuth user without password
      if (!existingUser.password) {
        return res.status(409).json({
          success: false,
          error: {
            code: "OAUTH_USER_EXISTS",
            message:
              "An account with this email already exists using social login. Please sign in with Google or Apple, or use account linking.",
          },
        });
      }

      return res.status(409).json({
        success: false,
        error: {
          code: "USER_EXISTS",
          message: "User with this email already exists",
        },
      });
    }

    // Hash password
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(validatedData.password, salt);

    // Generate email verification OTP
    const emailVerificationOTP = generateOTP();
    const emailVerificationExpiresAt = new Date(Date.now() + 3600000); // 1 hour

    // Create user
    const user = await prisma.user.create({
      data: {
        email: validatedData.email,
        password: hashedPassword,
        emailVerificationOTP,
        emailVerificationExpiresAt,
        profile: {
          create: {
            firstName: validatedData.firstName,
            lastName: validatedData.lastName,
          },
        },
        credit: {
          create: {
            balance: 10,
          },
        },
      },
      include: {
        profile: true,
        credit: true,
      },
    });

    // Send verification email
    try {
      await sendVerificationEmail(
        user.email,
        emailVerificationOTP,
        validatedData.firstName
      );
    } catch (emailError) {
      console.error("Failed to send verification email:", emailError);
      // Don't fail registration if email fails, but log the error
    }

    // Generate tokens
    const { accessToken, refreshToken } = generateTokens(user);

    // Store refresh token
    const refreshTokenHash = crypto
      .createHash("sha256")
      .update(refreshToken)
      .digest("hex");

    await prisma.authToken.create({
      data: {
        userId: user.id,
        tokenHash: refreshTokenHash,
        expiresAt: new Date(
          Date.now() +
            parseInt(process.env.JWT_REFRESH_EXPIRES_IN_MS || "*********") // Default 7 days
        ),
      },
    });

    // Send response
    res.status(201).json({
      success: true,
      data: {
        user: {
          id: user.id,
          email: user.email,
          role: user.role,
          emailVerified: user.emailVerified,
          lastLogin: user.lastLogin,
          profile: {
            firstName: user.profile?.firstName,
            lastName: user.profile?.lastName,
          },
          credit: {
            balance: user.credit?.balance,
          },
        },
        tokens: {
          accessToken,
          refreshToken,
        },
      },
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        error: {
          code: "VALIDATION_ERROR",
          message: "Invalid input data",
          details: error.errors,
        },
      });
    }

    res.status(500).json({
      success: false,
      error: {
        code: "SERVER_ERROR",
        message: "Failed to register user",
      },
    });
  }
};

export const login = async (req: Request, res: Response) => {
  try {
    // Validate request body
    const validatedData = loginSchema.parse(req.body);

    // Find user
    const user = await prisma.user.findUnique({
      where: { email: validatedData.email },
      include: {
        profile: true,
        credit: true,
        subscriptions: {
          where: {
            status: "ACTIVE",
          },
          include: {
            plan: true,
          },
          orderBy: {
            createdAt: "desc",
          },
          take: 1,
        },
        creditPurchaseOrders: {
          include: {
            creditPackage: true,
          },
          where: {
            status: "COMPLETED",
          },
          orderBy: {
            createdAt: "desc",
          },
          take: 1,
        },
      },
    });

    if (!user) {
      return res.status(401).json({
        success: false,
        error: {
          code: "INVALID_CREDENTIALS",
          message: "Invalid email or password",
        },
      });
    }

    // Check if user has a password (not OAuth-only user)
    if (!user.password) {
      return res.status(401).json({
        success: false,
        error: {
          code: "OAUTH_USER",
          message:
            "This account uses social login. Please sign in with Google or Apple.",
        },
      });
    }

    // Verify password
    const isPasswordValid = await bcrypt.compare(
      validatedData.password,
      user.password
    );

    if (!isPasswordValid) {
      return res.status(401).json({
        success: false,
        error: {
          code: "INVALID_CREDENTIALS",
          message: "Invalid email or password",
        },
      });
    }

    // Generate tokens
    const { accessToken, refreshToken } = generateTokens(user);

    // Store refresh token
    const refreshTokenHash = crypto
      .createHash("sha256")
      .update(refreshToken)
      .digest("hex");

    await prisma.authToken.create({
      data: {
        userId: user.id,
        tokenHash: refreshTokenHash,
        expiresAt: new Date(
          Date.now() +
            parseInt(process.env.JWT_REFRESH_EXPIRES_IN_MS || "*********") // Default 7 days
        ),
      },
    });

    // Update last login
    await prisma.user.update({
      where: { id: user.id },
      data: { lastLogin: new Date() },
    });

    // Send response
    res.status(200).json({
      success: true,
      data: {
        user: {
          id: user.id,
          email: user.email,
          role: user.role,
          emailVerified: user.emailVerified,
          lastLogin: user.lastLogin,
          profile: {
            firstName: user.profile?.firstName,
            lastName: user.profile?.lastName,
            avatarUrl: user.profile?.avatarUrl,
          },
          credit: {
            balance: user.credit?.balance,
          },
          subscription: user.subscriptions[0] || null,
          creditPurchase: user.creditPurchaseOrders[0] || null,
        },
        tokens: {
          accessToken,
          refreshToken,
        },
      },
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        error: {
          code: "VALIDATION_ERROR",
          message: "Invalid input data",
          details: error.errors,
        },
      });
    }

    res.status(500).json({
      success: false,
      error: {
        code: "SERVER_ERROR",
        message: "Failed to login",
      },
    });
  }
};

export const refreshToken = async (req: Request, res: Response) => {
  try {
    const { refreshToken } = req.body;

    if (!refreshToken) {
      return res.status(400).json({
        success: false,
        error: {
          code: "MISSING_REFRESH_TOKEN",
          message: "Refresh token is required",
        },
      });
    }

    // Verify refresh token
    const decoded = verifyRefreshToken(refreshToken);

    // Find user
    const user = await prisma.user.findUnique({
      where: { id: decoded.id },
    });

    if (!user) {
      return res.status(401).json({
        success: false,
        error: {
          code: "INVALID_REFRESH_TOKEN",
          message: "Invalid refresh token",
        },
      });
    }

    // Check if refresh token exists in database
    const refreshTokenHash = crypto
      .createHash("sha256")
      .update(refreshToken)
      .digest("hex");

    const storedToken = await prisma.authToken.findFirst({
      where: {
        userId: user.id,
        tokenHash: refreshTokenHash,
        expiresAt: {
          gt: new Date(),
        },
      },
    });

    if (!storedToken) {
      return res.status(401).json({
        success: false,
        error: {
          code: "INVALID_REFRESH_TOKEN",
          message: "Invalid or expired refresh token",
        },
      });
    }

    // Generate new tokens
    const tokens = generateTokens(user);

    // Delete old refresh token
    await prisma.authToken.delete({
      where: { id: storedToken.id },
    });

    // Store new refresh token
    const newRefreshTokenHash = crypto
      .createHash("sha256")
      .update(tokens.refreshToken)
      .digest("hex");

    await prisma.authToken.create({
      data: {
        userId: user.id,
        tokenHash: newRefreshTokenHash,
        expiresAt: new Date(
          Date.now() +
            parseInt(process.env.JWT_REFRESH_EXPIRES_IN_MS || "*********") // Default 7 days
        ),
      },
    });

    // Send response
    res.status(200).json({
      success: true,
      data: {
        tokens: {
          accessToken: tokens.accessToken,
          refreshToken: tokens.refreshToken,
        },
      },
    });
  } catch (error) {
    res.status(401).json({
      success: false,
      error: {
        code: "INVALID_REFRESH_TOKEN",
        message: "Invalid refresh token",
      },
    });
  }
};

export const logout = async (req: Request, res: Response) => {
  try {
    const { refreshToken } = req.body;
    if (!refreshToken) {
      return res.status(400).json({
        success: false,
        error: {
          code: "MISSING_REFRESH_TOKEN",
          message: "Refresh token is required",
        },
      });
    }

    // Hash refresh token
    const refreshTokenHash = crypto
      .createHash("sha256")
      .update(refreshToken)
      .digest("hex");

    // Delete refresh token from database
    await prisma.authToken.deleteMany({
      where: {
        tokenHash: refreshTokenHash,
      },
    });

    res.status(200).json({
      success: true,
      message: "Logged out successfully",
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: {
        code: "SERVER_ERROR",
        message: "Failed to logout",
      },
    });
  }
};

export const requestPasswordReset = async (req: Request, res: Response) => {
  try {
    const { email } = req.body;

    if (!email) {
      return res.status(400).json({
        success: false,
        error: {
          code: "MISSING_EMAIL",
          message: "Email is required",
        },
      });
    }

    // Check rate limit
    if (!checkRateLimit(email)) {
      return res.status(429).json({
        success: false,
        error: {
          code: "RATE_LIMIT_EXCEEDED",
          message: "Too many password reset requests. Please try again later.",
        },
      });
    }

    // Find user
    const user = await prisma.user.findUnique({
      where: { email },
      include: { profile: true },
    });

    if (!user) {
      // For security reasons, don't reveal that the user doesn't exist
      return res.status(200).json({
        success: true,
        message:
          "If your email is registered, you will receive a password reset code",
      });
    }

    // Generate password reset OTP
    const passwordResetOTP = generateOTP();
    const passwordResetExpiresAt = new Date(Date.now() + 3600000); // 1 hour

    // Store reset OTP in user record
    await prisma.user.update({
      where: { id: user.id },
      data: {
        passwordResetToken: passwordResetOTP,
        passwordResetTokenExpiresAt: passwordResetExpiresAt,
      },
    });

    // Send password reset email
    try {
      await sendPasswordResetEmail(
        user.email,
        passwordResetOTP,
        user.profile?.firstName || undefined
      );
    } catch (emailError) {
      console.error("Failed to send password reset email:", emailError);
      return res.status(500).json({
        success: false,
        error: {
          code: "EMAIL_SEND_FAILED",
          message: "Failed to send password reset email",
        },
      });
    }

    res.status(200).json({
      success: true,
      message:
        "If your email is registered, you will receive a password reset code",
    });
  } catch (error) {
    console.error("Password reset request error:", error);
    res.status(500).json({
      success: false,
      error: {
        code: "SERVER_ERROR",
        message: "Failed to request password reset",
      },
    });
  }
};

// Verify password reset OTP
export const verifyPasswordResetOTP = async (req: Request, res: Response) => {
  try {
    const { email, otp } = req.body;

    if (!email || !otp) {
      return res.status(400).json({
        success: false,
        error: {
          code: "MISSING_FIELDS",
          message: "Email and OTP are required",
        },
      });
    }

    // Find user with matching email and OTP
    const user = await prisma.user.findFirst({
      where: {
        email,
        passwordResetToken: otp,
        passwordResetTokenExpiresAt: {
          gt: new Date(),
        },
      },
    });

    if (!user) {
      return res.status(400).json({
        success: false,
        error: {
          code: "INVALID_OTP",
          message: "Invalid or expired OTP",
        },
      });
    }

    res.status(200).json({
      success: true,
      message: "OTP verified successfully",
    });
  } catch (error) {
    console.error("Password reset OTP verification error:", error);
    res.status(500).json({
      success: false,
      error: {
        code: "SERVER_ERROR",
        message: "Failed to verify OTP",
      },
    });
  }
};

// Reset password with OTP
export const resetPasswordWithOTP = async (req: Request, res: Response) => {
  try {
    const { email, otp, password } = req.body;

    if (!email || !otp || !password) {
      return res.status(400).json({
        success: false,
        error: {
          code: "MISSING_FIELDS",
          message: "Email, OTP, and password are required",
        },
      });
    }

    // Validate password
    if (password.length < 8) {
      return res.status(400).json({
        success: false,
        error: {
          code: "INVALID_PASSWORD",
          message: "Password must be at least 8 characters long",
        },
      });
    }

    // Find user with matching email and OTP
    const user = await prisma.user.findFirst({
      where: {
        email,
        passwordResetToken: otp,
        passwordResetTokenExpiresAt: {
          gt: new Date(),
        },
      },
    });

    if (!user) {
      return res.status(400).json({
        success: false,
        error: {
          code: "INVALID_OTP",
          message: "Invalid or expired OTP",
        },
      });
    }

    // Hash new password
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(password, salt);

    // Update user password and clear reset token
    await prisma.user.update({
      where: { id: user.id },
      data: {
        password: hashedPassword,
        passwordResetToken: null,
        passwordResetTokenExpiresAt: null,
      },
    });

    res.status(200).json({
      success: true,
      message: "Password reset successful",
    });
  } catch (error) {
    console.error("Password reset error:", error);
    res.status(500).json({
      success: false,
      error: {
        code: "SERVER_ERROR",
        message: "Failed to reset password",
      },
    });
  }
};

export const resetPassword = async (req: Request, res: Response) => {
  try {
    const { token, password } = req.body;

    if (!token || !password) {
      return res.status(400).json({
        success: false,
        error: {
          code: "MISSING_FIELDS",
          message: "Token and password are required",
        },
      });
    }

    // Validate password
    if (password.length < 8) {
      return res.status(400).json({
        success: false,
        error: {
          code: "INVALID_PASSWORD",
          message: "Password must be at least 8 characters long",
        },
      });
    }

    // Hash token
    const tokenHash = crypto.createHash("sha256").update(token).digest("hex");

    // Find token in database
    const storedToken = await prisma.authToken.findFirst({
      where: {
        tokenHash,
        expiresAt: {
          gt: new Date(),
        },
      },
    });

    if (!storedToken) {
      return res.status(400).json({
        success: false,
        error: {
          code: "INVALID_TOKEN",
          message: "Invalid or expired token",
        },
      });
    }

    // Hash new password
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(password, salt);

    // Update user password
    await prisma.user.update({
      where: { id: storedToken.userId },
      data: { password: hashedPassword },
    });

    // Delete token
    await prisma.authToken.delete({
      where: { id: storedToken.id },
    });

    res.status(200).json({
      success: true,
      message: "Password reset successful",
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: {
        code: "SERVER_ERROR",
        message: "Failed to reset password",
      },
    });
  }
};

// Email verification with OTP
export const verifyEmailOTP = async (req: Request, res: Response) => {
  try {
    const { email, otp } = req.body;

    if (!email || !otp) {
      return res.status(400).json({
        success: false,
        error: {
          code: "MISSING_FIELDS",
          message: "Email and OTP are required",
        },
      });
    }

    // Find user with matching email and OTP
    const user = await prisma.user.findFirst({
      where: {
        email,
        emailVerificationOTP: otp,
        emailVerificationExpiresAt: {
          gt: new Date(),
        },
      },
    });

    if (!user) {
      return res.status(400).json({
        success: false,
        error: {
          code: "INVALID_OTP",
          message: "Invalid or expired OTP",
        },
      });
    }

    // Update user email verification status
    await prisma.user.update({
      where: { id: user.id },
      data: {
        emailVerified: true,
        emailVerificationOTP: null,
        emailVerificationExpiresAt: null,
      },
    });

    res.status(200).json({
      success: true,
      message: "Email verified successfully",
    });
  } catch (error) {
    console.error("Email verification error:", error);
    res.status(500).json({
      success: false,
      error: {
        code: "SERVER_ERROR",
        message: "Failed to verify email",
      },
    });
  }
};

// Resend email verification OTP
export const resendEmailVerificationOTP = async (
  req: Request,
  res: Response
) => {
  try {
    const { email } = req.body;

    if (!email) {
      return res.status(400).json({
        success: false,
        error: {
          code: "MISSING_EMAIL",
          message: "Email is required",
        },
      });
    }

    // Check rate limit
    if (!checkRateLimit(email)) {
      return res.status(429).json({
        success: false,
        error: {
          code: "RATE_LIMIT_EXCEEDED",
          message: "Too many OTP requests. Please try again later.",
        },
      });
    }

    // Find user
    const user = await prisma.user.findUnique({
      where: { email },
      include: { profile: true },
    });

    if (!user) {
      return res.status(404).json({
        success: false,
        error: {
          code: "USER_NOT_FOUND",
          message: "User not found",
        },
      });
    }

    if (user.emailVerified) {
      return res.status(400).json({
        success: false,
        error: {
          code: "EMAIL_ALREADY_VERIFIED",
          message: "Email is already verified",
        },
      });
    }

    // Generate new OTP
    const emailVerificationOTP = generateOTP();
    const emailVerificationExpiresAt = new Date(Date.now() + 3600000); // 1 hour

    // Update user with new OTP
    await prisma.user.update({
      where: { id: user.id },
      data: {
        emailVerificationOTP,
        emailVerificationExpiresAt,
      },
    });

    // Send verification email
    try {
      await sendVerificationEmail(
        user.email,
        emailVerificationOTP,
        user.profile?.firstName || undefined
      );
    } catch (emailError) {
      console.error("Failed to send verification email:", emailError);
      return res.status(500).json({
        success: false,
        error: {
          code: "EMAIL_SEND_FAILED",
          message: "Failed to send verification email",
        },
      });
    }

    res.status(200).json({
      success: true,
      message: "Verification email sent successfully",
    });
  } catch (error) {
    console.error("Resend verification error:", error);
    res.status(500).json({
      success: false,
      error: {
        code: "SERVER_ERROR",
        message: "Failed to resend verification email",
      },
    });
  }
};

export const verifyEmail = async (req: Request, res: Response) => {
  try {
    const { token } = req.params;

    if (!token) {
      return res.status(400).json({
        success: false,
        error: {
          code: "MISSING_TOKEN",
          message: "Token is required",
        },
      });
    }

    // Hash token
    const tokenHash = crypto.createHash("sha256").update(token).digest("hex");

    // Find token in database
    const storedToken = await prisma.authToken.findFirst({
      where: {
        tokenHash,
        expiresAt: {
          gt: new Date(),
        },
      },
    });

    if (!storedToken) {
      return res.status(400).json({
        success: false,
        error: {
          code: "INVALID_TOKEN",
          message: "Invalid or expired token",
        },
      });
    }

    // Update user email verification status
    await prisma.user.update({
      where: { id: storedToken.userId },
      data: { emailVerified: true },
    });

    // Delete token
    await prisma.authToken.delete({
      where: { id: storedToken.id },
    });

    res.status(200).json({
      success: true,
      message: "Email verified successfully",
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: {
        code: "SERVER_ERROR",
        message: "Failed to verify email",
      },
    });
  }
};
