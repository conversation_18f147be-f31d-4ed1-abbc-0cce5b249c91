import { Request, Response, NextFunction } from "express";
import passport from "passport";
import { generateTokens } from "../utils/jwt";
import { prisma } from "../index";
import crypto from "crypto";

// Google OAuth initiation
export const googleAuth = passport.authenticate("google", {
  scope: ["profile", "email"],
});

// Google OAuth callback
export const googleCallback = (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  passport.authenticate(
    "google",
    { session: false },
    async (err: any, user: any) => {
      try {
        if (err) {
          console.error("Google OAuth error:", err);
          return res.redirect(
            `${process.env.FRONTEND_URL}/auth/error?message=oauth_error`
          );
        }

        if (!user) {
          return res.redirect(
            `${process.env.FRONTEND_URL}/auth/error?message=oauth_failed`
          );
        }

        // Generate JWT tokens
        const { accessToken, refreshToken } = generateTokens(user);

        // Store refresh token
        const refreshTokenHash = crypto
          .createHash("sha256")
          .update(refreshToken)
          .digest("hex");

        await prisma.authToken.create({
          data: {
            userId: user.id,
            tokenHash: refreshTokenHash,
            expiresAt: new Date(
              Date.now() +
                parseInt(process.env.JWT_REFRESH_EXPIRES_IN_MS || "*********")
            ),
          },
        });

        // Redirect to frontend with tokens
        const redirectUrl =
          `${process.env.FRONTEND_URL}/auth/callback?` +
          `access_token=${accessToken}&refresh_token=${refreshToken}&provider=google`;

        res.redirect(redirectUrl);
      } catch (error) {
        console.error("Google callback error:", error);
        res.redirect(
          `${process.env.FRONTEND_URL}/auth/error?message=callback_error`
        );
      }
    }
  )(req, res, next);
};

// Apple OAuth initiation
export const appleAuth = passport.authenticate("apple", {
  scope: ["name", "email"],
});

// Apple OAuth callback
export const appleCallback = (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  passport.authenticate(
    "apple",
    { session: false },
    async (err: any, user: any) => {
      try {
        if (err) {
          console.error("Apple OAuth error:", err);
          return res.redirect(
            `${process.env.FRONTEND_URL}/auth/error?message=oauth_error`
          );
        }

        if (!user) {
          return res.redirect(
            `${process.env.FRONTEND_URL}/auth/error?message=oauth_failed`
          );
        }

        // Generate JWT tokens
        const { accessToken, refreshToken } = generateTokens(user);

        // Store refresh token
        const refreshTokenHash = crypto
          .createHash("sha256")
          .update(refreshToken)
          .digest("hex");

        await prisma.authToken.create({
          data: {
            userId: user.id,
            tokenHash: refreshTokenHash,
            expiresAt: new Date(
              Date.now() +
                parseInt(process.env.JWT_REFRESH_EXPIRES_IN_MS || "*********")
            ),
          },
        });

        // Redirect to frontend with tokens
        const redirectUrl =
          `${process.env.FRONTEND_URL}/auth/callback?` +
          `access_token=${accessToken}&refresh_token=${refreshToken}&provider=apple`;

        res.redirect(redirectUrl);
      } catch (error) {
        console.error("Apple callback error:", error);
        res.redirect(
          `${process.env.FRONTEND_URL}/auth/error?message=callback_error`
        );
      }
    }
  )(req, res, next);
};

// Link OAuth account to existing user
export const linkOAuthAccount = async (
  req: Request,
  res: Response
): Promise<void> => {
  try {
    const { provider, oauthId } = req.body;
    const userId = (req as any).user?.id;

    if (!userId) {
      res.status(401).json({
        success: false,
        error: {
          code: "UNAUTHORIZED",
          message: "User not authenticated",
        },
      });
      return;
    }

    if (!provider || !oauthId) {
      res.status(400).json({
        success: false,
        error: {
          code: "INVALID_REQUEST",
          message: "Provider and OAuth ID are required",
        },
      });
      return;
    }

    // Check if OAuth account is already linked to another user
    const existingOAuthUser = await prisma.user.findFirst({
      where: {
        OR: [
          { googleId: provider === "google" ? oauthId : undefined },
          { appleId: provider === "apple" ? oauthId : undefined },
        ],
      },
    });

    if (existingOAuthUser && existingOAuthUser.id !== userId) {
      res.status(409).json({
        success: false,
        error: {
          code: "OAUTH_ACCOUNT_LINKED",
          message: "This OAuth account is already linked to another user",
        },
      });
      return;
    }

    // Link OAuth account to current user
    const updateData: any = {};
    if (provider === "google") {
      updateData.googleId = oauthId;
    } else if (provider === "apple") {
      updateData.appleId = oauthId;
    }

    const updatedUser = await prisma.user.update({
      where: { id: userId },
      data: updateData,
      include: {
        profile: true,
        credit: true,
      },
    });

    res.status(200).json({
      success: true,
      data: {
        message: `${provider} account linked successfully`,
        user: {
          id: updatedUser.id,
          email: updatedUser.email,
          googleId: updatedUser.googleId,
          appleId: updatedUser.appleId,
        },
      },
    });
  } catch (error) {
    console.error("Link OAuth account error:", error);
    res.status(500).json({
      success: false,
      error: {
        code: "INTERNAL_ERROR",
        message: "Failed to link OAuth account",
      },
    });
  }
};

// Unlink OAuth account from user
export const unlinkOAuthAccount = async (
  req: Request,
  res: Response
): Promise<void> => {
  try {
    const { provider } = req.body;
    const userId = (req as any).user?.id;

    if (!userId) {
      res.status(401).json({
        success: false,
        error: {
          code: "UNAUTHORIZED",
          message: "User not authenticated",
        },
      });
      return;
    }

    if (!provider) {
      res.status(400).json({
        success: false,
        error: {
          code: "INVALID_REQUEST",
          message: "Provider is required",
        },
      });
      return;
    }

    const user = await prisma.user.findUnique({
      where: { id: userId },
    });

    if (!user) {
      res.status(404).json({
        success: false,
        error: {
          code: "USER_NOT_FOUND",
          message: "User not found",
        },
      });
      return;
    }

    // Ensure user has at least one authentication method
    const hasPassword = !!user.password;
    const hasGoogle = !!user.googleId;
    const hasApple = !!user.appleId;

    if (
      !hasPassword &&
      ((provider === "google" && !hasApple) ||
        (provider === "apple" && !hasGoogle))
    ) {
      res.status(400).json({
        success: false,
        error: {
          code: "LAST_AUTH_METHOD",
          message:
            "Cannot unlink the last authentication method. Please set a password first.",
        },
      });
      return;
    }

    // Unlink OAuth account
    const updateData: any = {};
    if (provider === "google") {
      updateData.googleId = null;
    } else if (provider === "apple") {
      updateData.appleId = null;
    }

    const updatedUser = await prisma.user.update({
      where: { id: userId },
      data: updateData,
      include: {
        profile: true,
        credit: true,
      },
    });

    res.status(200).json({
      success: true,
      data: {
        message: `${provider} account unlinked successfully`,
        user: {
          id: updatedUser.id,
          email: updatedUser.email,
          googleId: updatedUser.googleId,
          appleId: updatedUser.appleId,
        },
      },
    });
  } catch (error) {
    console.error("Unlink OAuth account error:", error);
    res.status(500).json({
      success: false,
      error: {
        code: "INTERNAL_ERROR",
        message: "Failed to unlink OAuth account",
      },
    });
  }
};
