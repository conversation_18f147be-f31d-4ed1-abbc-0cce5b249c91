import { Request, Response } from "express";
import { PrismaClient } from "@prisma/client";
import multer from "multer";
import path from "path";
import fs from "fs";
import * as utils from "../utils/virtualTryOn.utils";

const prisma = new PrismaClient();

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadDir = path.join(__dirname, "../../uploads/virtual-try-on");
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + "-" + Math.round(Math.random() * 1e9);
    cb(
      null,
      `${file.fieldname}-${uniqueSuffix}${path.extname(file.originalname)}`
    );
  },
});

const upload = multer({
  storage,
  limits: {
    fileSize: 30 * 1024 * 1024, // 30MB limit
  },
  fileFilter: (req, file, cb) => {
    const allowedTypes = ["image/jpeg", "image/jpg", "image/png", "image/webp"];
    if (allowedTypes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error("Invalid file type. Only JPEG, PNG, and WebP are allowed."));
    }
  },
});

export const uploadFiles = upload.fields([
  { name: "modelImage", maxCount: 1 },
  { name: "clothingImage", maxCount: 1 },
]);

/**
 * Upload admin model image (default model)
 */
export const uploadAdminModelImage = async (
  req: Request,
  res: Response
): Promise<void> => {
  try {
    const userId = req.user?.id;
    if (!userId) {
      res.status(401).json({
        success: false,
        message: "User not authenticated",
      });
      return;
    }

    const files = req.files as {
      modelImage?: Express.Multer.File[];
    };

    if (!files.modelImage) {
      res.status(400).json({
        success: false,
        message: "Model image is required",
      });
      return;
    }

    const modelImagePath = files.modelImage[0].path;
    const { modelName, gender, bodyType, poseType, ethnicity } = req.body;

    // Create admin model image in database
    const modelImage = await prisma.modelImage.create({
      data: {
        userId, // Admin user ID
        imagePath: modelImagePath,
        modelName: modelName || "Admin Model",
        gender: gender || "UNISEX",
        bodyType: bodyType || "AVERAGE",
        poseType: poseType || "STANDING",
        ethnicity: ethnicity || "MIXED",
        isDefault: true, // Admin uploaded items are default
      },
    });

    res.status(201).json({
      success: true,
      message: "Admin model image uploaded successfully",
      data: {
        id: modelImage.id,
        imagePath: utils.toUrlPath(modelImage.imagePath),
        modelName: modelImage.modelName,
        gender: modelImage.gender,
        isDefault: modelImage.isDefault,
        createdAt: modelImage.createdAt,
      },
    });
  } catch (error) {
    console.error("Error uploading admin model image:", error);
    res.status(500).json({
      success: false,
      message: "Failed to upload admin model image",
    });
  }
};

/**
 * Upload admin clothing item (default clothing)
 */
export const uploadAdminClothingItem = async (
  req: Request,
  res: Response
): Promise<void> => {
  try {
    const userId = req.user?.id;
    if (!userId) {
      res.status(401).json({
        success: false,
        message: "User not authenticated",
      });
      return;
    }

    const files = req.files as {
      clothingImage?: Express.Multer.File[];
    };

    if (!files.clothingImage) {
      res.status(400).json({
        success: false,
        message: "Clothing image is required",
      });
      return;
    }

    const clothingImagePath = files.clothingImage[0].path;
    const { name, category, style, color, season, description } = req.body;
    console.log("category", category);
    // Create admin clothing item in database
    const clothingItem = await prisma.clothingItem.create({
      data: {
        userId, // Admin user ID
        name: name || "Admin Clothing",
        imagePath: clothingImagePath,
        clothingType: category || "TOP",
        category: category || "shirt",
        style: style || "CASUAL",
        color: color || null,
        season: season || null,
        description: description || "",
        isDefault: true, // Admin uploaded items are default
      },
    });

    res.status(201).json({
      success: true,
      message: "Admin clothing item uploaded successfully",
      data: {
        id: clothingItem.id,
        imagePath: utils.toUrlPath(clothingItem.imagePath),
        name: clothingItem.name,
        clothingType: clothingItem.clothingType,
        category: clothingItem.category,
        isDefault: clothingItem.isDefault,
        createdAt: clothingItem.createdAt,
      },
    });
  } catch (error) {
    console.error("Error uploading admin clothing item:", error);
    res.status(500).json({
      success: false,
      message: "Failed to upload admin clothing item",
    });
  }
};

/**
 * Get all model images (admin view)
 */
export const getAllModelImages = async (
  req: Request,
  res: Response
): Promise<void> => {
  try {
    const { page = 1, limit = 20, isDefault } = req.query;
    const skip = (Number(page) - 1) * Number(limit);

    const where: any = { role: "ADMIN" };

    if (isDefault !== undefined) {
      where.isDefault = isDefault === "true";
    }

    const [modelImages, total] = await Promise.all([
      prisma.modelImage.findMany({
        where,
        include: {
          user: {
            select: {
              id: true,
              email: true,
              profile: {
                select: {
                  firstName: true,
                  lastName: true,
                },
              },
            },
          },
        },
        orderBy: [{ isDefault: "desc" }, { createdAt: "desc" }],
        skip,
        take: Number(limit),
      }),
      prisma.modelImage.count({ where }),
    ]);

    const formattedModelImages = modelImages.map((model) => ({
      id: model.id,
      imagePath: utils.toUrlPath(model.imagePath),
      modelName: model.modelName,
      gender: model.gender,
      bodyType: model.bodyType,
      poseType: model.poseType,
      ethnicity: model.ethnicity,
      isDefault: model.isDefault,
      usageCount: model.usageCount,
      createdAt: model.createdAt,
      user: {
        id: model.user.id,
        email: model.user.email,
        firstName: model.user.profile?.firstName || "",
        lastName: model.user.profile?.lastName || "",
      },
    }));

    res.json({
      success: true,
      data: {
        modelImages: formattedModelImages,
        pagination: {
          page: Number(page),
          limit: Number(limit),
          total,
          totalPages: Math.ceil(total / Number(limit)),
        },
      },
    });
  } catch (error) {
    console.error("Error fetching all model images:", error);
    res.status(500).json({
      success: false,
      message: "Failed to fetch model images",
    });
  }
};

/**
 * Get all clothing items (admin view)
 */
export const getAllClothingItems = async (
  req: Request,
  res: Response
): Promise<void> => {
  try {
    const { page = 1, limit = 20, isDefault, clothingType } = req.query;
    const skip = (Number(page) - 1) * Number(limit);

    const where: any = { role: "ADMIN" };
    if (isDefault !== undefined) {
      where.isDefault = isDefault === "true";
    }
    if (clothingType) {
      where.clothingType = clothingType;
    }

    const [clothingItems, total] = await Promise.all([
      prisma.clothingItem.findMany({
        where,
        include: {
          user: {
            select: {
              id: true,
              email: true,
              profile: {
                select: {
                  firstName: true,
                  lastName: true,
                },
              },
            },
          },
        },
        orderBy: [{ isDefault: "desc" }, { createdAt: "desc" }],
        skip,
        take: Number(limit),
      }),
      prisma.clothingItem.count({ where }),
    ]);

    const formattedClothingItems = clothingItems.map((item) => ({
      id: item.id,
      imagePath: utils.toUrlPath(item.imagePath),
      name: item.name,
      clothingType: item.clothingType,
      category: item.category,
      style: item.style,
      color: item.color,
      season: item.season,
      description: item.description,
      isDefault: item.isDefault,
      usageCount: item.usageCount,
      createdAt: item.createdAt,
      user: {
        id: item.user.id,
        email: item.user.email,
        firstName: item.user.profile?.firstName || "",
        lastName: item.user.profile?.lastName || "",
      },
    }));

    res.json({
      success: true,
      data: {
        clothingItems: formattedClothingItems,
        pagination: {
          page: Number(page),
          limit: Number(limit),
          total,
          totalPages: Math.ceil(total / Number(limit)),
        },
      },
    });
  } catch (error) {
    console.error("Error fetching all clothing items:", error);
    res.status(500).json({
      success: false,
      message: "Failed to fetch clothing items",
    });
  }
};

/**
 * Delete any model image (admin only)
 */
export const deleteAnyModelImage = async (
  req: Request,
  res: Response
): Promise<void> => {
  try {
    const modelId = req.params.id;

    const modelImage = await prisma.modelImage.findUnique({
      where: { id: modelId },
    });

    if (!modelImage) {
      res.status(404).json({
        success: false,
        message: "Model image not found",
      });
      return;
    }

    // Delete the file from storage
    try {
      fs.unlinkSync(modelImage.imagePath);
    } catch (err) {
      console.error("Error deleting model image file:", err);
    }

    // Delete from database
    await prisma.modelImage.delete({
      where: { id: modelId },
    });

    res.json({
      success: true,
      message: "Model image deleted successfully",
    });
  } catch (error) {
    console.error("Error deleting model image:", error);
    res.status(500).json({
      success: false,
      message: "Failed to delete model image",
    });
  }
};

/**
 * Delete any clothing item (admin only)
 */
export const deleteAnyClothingItem = async (
  req: Request,
  res: Response
): Promise<void> => {
  try {
    const clothingId = req.params.id;

    const clothingItem = await prisma.clothingItem.findUnique({
      where: { id: clothingId },
    });

    if (!clothingItem) {
      res.status(404).json({
        success: false,
        message: "Clothing item not found",
      });
      return;
    }

    // Delete the file from storage
    try {
      fs.unlinkSync(clothingItem.imagePath);
    } catch (err) {
      console.error("Error deleting clothing item file:", err);
    }

    // Delete from database
    await prisma.clothingItem.delete({
      where: { id: clothingId },
    });

    res.json({
      success: true,
      message: "Clothing item deleted successfully",
    });
  } catch (error) {
    console.error("Error deleting clothing item:", error);
    res.status(500).json({
      success: false,
      message: "Failed to delete clothing item",
    });
  }
};

/**
 * Update model image properties
 */
export const updateModelImage = async (
  req: Request,
  res: Response
): Promise<void> => {
  try {
    const modelId = req.params.id;
    const { modelName, gender, bodyType, poseType, ethnicity, isDefault } =
      req.body;

    const modelImage = await prisma.modelImage.findUnique({
      where: { id: modelId },
    });

    if (!modelImage) {
      res.status(404).json({
        success: false,
        message: "Model image not found",
      });
      return;
    }

    const updatedModel = await prisma.modelImage.update({
      where: { id: modelId },
      data: {
        ...(modelName && { modelName }),
        ...(gender && { gender }),
        ...(bodyType && { bodyType }),
        ...(poseType && { poseType }),
        ...(ethnicity && { ethnicity }),
        ...(isDefault !== undefined && { isDefault }),
      },
    });

    res.json({
      success: true,
      message: "Model image updated successfully",
      data: {
        id: updatedModel.id,
        imagePath: utils.toUrlPath(updatedModel.imagePath),
        modelName: updatedModel.modelName,
        gender: updatedModel.gender,
        bodyType: updatedModel.bodyType,
        poseType: updatedModel.poseType,
        ethnicity: updatedModel.ethnicity,
        isDefault: updatedModel.isDefault,
        createdAt: updatedModel.createdAt,
      },
    });
  } catch (error) {
    console.error("Error updating model image:", error);
    res.status(500).json({
      success: false,
      message: "Failed to update model image",
    });
  }
};

/**
 * Update clothing item properties
 */
export const updateClothingItem = async (
  req: Request,
  res: Response
): Promise<void> => {
  try {
    const clothingId = req.params.id;
    const {
      name,
      clothingType,
      category,
      style,
      color,
      season,
      description,
      isDefault,
    } = req.body;

    const clothingItem = await prisma.clothingItem.findUnique({
      where: { id: clothingId },
    });

    if (!clothingItem) {
      res.status(404).json({
        success: false,
        message: "Clothing item not found",
      });
      return;
    }

    const updatedClothing = await prisma.clothingItem.update({
      where: { id: clothingId },
      data: {
        ...(name && { name }),
        ...(clothingType && { clothingType }),
        ...(category && { category }),
        ...(style && { style }),
        ...(color && { color }),
        ...(season && { season }),
        ...(description && { description }),
        ...(isDefault !== undefined && { isDefault }),
      },
    });

    res.json({
      success: true,
      message: "Clothing item updated successfully",
      data: {
        id: updatedClothing.id,
        imagePath: utils.toUrlPath(updatedClothing.imagePath),
        name: updatedClothing.name,
        clothingType: updatedClothing.clothingType,
        category: updatedClothing.category,
        style: updatedClothing.style,
        color: updatedClothing.color,
        season: updatedClothing.season,
        description: updatedClothing.description,
        isDefault: updatedClothing.isDefault,
        createdAt: updatedClothing.createdAt,
      },
    });
  } catch (error) {
    console.error("Error updating clothing item:", error);
    res.status(500).json({
      success: false,
      message: "Failed to update clothing item",
    });
  }
};

/**
 * Get virtual try-on statistics
 */
export const getVirtualTryOnStats = async (
  req: Request,
  res: Response
): Promise<void> => {
  try {
    const [
      totalJobs,
      completedJobs,
      failedJobs,
      totalModels,
      adminModels,
      userModels,
      totalClothing,
      adminClothing,
      userClothing,
      recentJobs,
    ] = await Promise.all([
      prisma.virtualTryOn.count(),
      prisma.virtualTryOn.count({ where: { status: "COMPLETED" } }),
      prisma.virtualTryOn.count({ where: { status: "FAILED" } }),
      prisma.modelImage.count(),
      prisma.modelImage.count({ where: { isDefault: true } }),
      prisma.modelImage.count({ where: { isDefault: false } }),
      prisma.clothingItem.count(),
      prisma.clothingItem.count({ where: { isDefault: true } }),
      prisma.clothingItem.count({ where: { isDefault: false } }),
      prisma.virtualTryOn.findMany({
        take: 10,
        orderBy: { createdAt: "desc" },
        include: {
          user: {
            select: {
              id: true,
              email: true,
              profile: {
                select: {
                  firstName: true,
                  lastName: true,
                },
              },
            },
          },
        },
      }),
    ]);

    const successRate = totalJobs > 0 ? (completedJobs / totalJobs) * 100 : 0;

    res.json({
      success: true,
      data: {
        jobs: {
          total: totalJobs,
          completed: completedJobs,
          failed: failedJobs,
          pending: totalJobs - completedJobs - failedJobs,
          successRate: Math.round(successRate * 100) / 100,
        },
        models: {
          total: totalModels,
          admin: adminModels,
          user: userModels,
        },
        clothing: {
          total: totalClothing,
          admin: adminClothing,
          user: userClothing,
        },
        recentJobs: recentJobs.map((job) => ({
          id: job.id,
          status: job.status,
          mode: job.mode,
          createdAt: job.createdAt,
          user: {
            id: job.user.id,
            email: job.user.email,
            firstName: job.user.profile?.firstName || "",
            lastName: job.user.profile?.lastName || "",
          },
        })),
      },
    });
  } catch (error) {
    console.error("Error fetching virtual try-on stats:", error);
    res.status(500).json({
      success: false,
      message: "Failed to fetch virtual try-on statistics",
    });
  }
};
