import { prisma } from '../index';

/**
 * Test script to verify OAuth implementation
 * Run with: npx ts-node src/scripts/test-oauth.ts
 */

async function testOAuthImplementation() {
  console.log('🔍 Testing OAuth Implementation...\n');

  try {
    // Test 1: Check if OAuth fields exist in User model
    console.log('1. Testing database schema...');
    
    const userCount = await prisma.user.count();
    console.log(`   ✅ User table accessible (${userCount} users)`);

    // Test 2: Create a test OAuth user
    console.log('\n2. Testing OAuth user creation...');
    
    const testEmail = `oauth-test-${Date.now()}@example.com`;
    const testUser = await prisma.user.create({
      data: {
        email: testEmail,
        googleId: 'test-google-id-123',
        authProvider: 'GOOGLE',
        emailVerified: true,
        profile: {
          create: {
            firstName: 'Test',
            lastName: 'User',
          },
        },
        credit: {
          create: {
            balance: 20,
          },
        },
      },
      include: {
        profile: true,
        credit: true,
      },
    });

    console.log(`   ✅ OAuth user created: ${testUser.email}`);
    console.log(`   ✅ Google ID: ${testUser.googleId}`);
    console.log(`   ✅ Auth Provider: ${testUser.authProvider}`);

    // Test 3: Test account linking scenario
    console.log('\n3. Testing account linking...');
    
    const updatedUser = await prisma.user.update({
      where: { id: testUser.id },
      data: {
        appleId: 'test-apple-id-456',
      },
    });

    console.log(`   ✅ Apple ID linked: ${updatedUser.appleId}`);

    // Test 4: Test OAuth user queries
    console.log('\n4. Testing OAuth queries...');
    
    const googleUser = await prisma.user.findUnique({
      where: { googleId: 'test-google-id-123' },
    });
    
    const appleUser = await prisma.user.findUnique({
      where: { appleId: 'test-apple-id-456' },
    });

    console.log(`   ✅ Google user query: ${googleUser ? 'Found' : 'Not found'}`);
    console.log(`   ✅ Apple user query: ${appleUser ? 'Found' : 'Not found'}`);

    // Test 5: Test OAuth provider filtering
    console.log('\n5. Testing provider filtering...');
    
    const googleUsers = await prisma.user.findMany({
      where: { authProvider: 'GOOGLE' },
    });
    
    const emailUsers = await prisma.user.findMany({
      where: { authProvider: 'EMAIL' },
    });

    console.log(`   ✅ Google users: ${googleUsers.length}`);
    console.log(`   ✅ Email users: ${emailUsers.length}`);

    // Test 6: Test constraint validation
    console.log('\n6. Testing constraints...');
    
    try {
      await prisma.user.create({
        data: {
          email: `constraint-test-${Date.now()}@example.com`,
          authProvider: 'EMAIL',
          // Missing password - should fail constraint
          profile: {
            create: {
              firstName: 'Constraint',
              lastName: 'Test',
            },
          },
          credit: {
            create: {
              balance: 20,
            },
          },
        },
      });
      console.log('   ❌ Constraint validation failed - user created without password');
    } catch (error) {
      console.log('   ✅ Constraint validation working - prevented user creation without password');
    }

    // Cleanup
    console.log('\n7. Cleaning up test data...');
    await prisma.user.delete({
      where: { id: testUser.id },
    });
    console.log('   ✅ Test user deleted');

    console.log('\n🎉 All OAuth tests passed!');
    
  } catch (error) {
    console.error('\n❌ OAuth test failed:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the test
testOAuthImplementation();
