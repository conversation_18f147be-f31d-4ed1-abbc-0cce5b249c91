import { Request, Response, NextFunction } from "express";
import { AppError } from "../utils/error";

/**
 * Middleware to check if user is an admin
 */
export const isAdmin = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    // Check if user exists and is authenticated
    if (!req.user) {
      return next(
        new AppError("Not authenticated", 401, "UNAUTHORIZED")
      );
    }

    // Check if user is an admin
    if (req.user.role !== "ADMIN") {
      return next(
        new AppError("Not authorized as admin", 403, "FORBIDDEN")
      );
    }

    next();
  } catch (error) {
    console.error("Admin middleware error:", error);
    return next(
      new AppError("Admin authorization failed", 403, "FORBIDDEN")
    );
  }
};
