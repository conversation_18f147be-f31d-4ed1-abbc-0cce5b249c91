// import { Request, Response, NextFunction } from "express";
// import jwt from "jsonwebtoken";
// import { prisma } from "../index";
// import { AppError } from "../utils/error";

// // Extend Express Request type to include user property
// declare global {
//   namespace Express {
//     interface Request {
//       user?: {
//         id: string;
//         email: string;
//         role: string;
//         // profile?: {
//         //   firstName: string | null;
//         //   lastName: string | null;
//         // } | null;
//         stripeCustomerId?: string | null;
//       };
//     }
//   }
// }

// /**
//  * Middleware to check if user is authenticated
//  */
// export const isAuthenticated = async (
//   req: Request,
//   res: Response,
//   next: NextFunction
// ) => {
//   try {
//     // Get token from header
//     const authHeader = req.headers.authorization;
//     const token = authHeader && authHeader.split(" ")[1];

//     if (!token) {
//       return next(
//         new AppError("Not authorized, no token", 401, "UNAUTHORIZED")
//       );
//     }

//     // Verify token
//     const decoded = jwt.verify(
//       token,
//       process.env.JWT_SECRET || "default_secret"
//     ) as jwt.JwtPayload;

//     // Check if user exists
//     const user = await prisma.user.findUnique({
//       where: { id: decoded.id },
//       select: {
//         id: true,
//         email: true,
//         role: true,
//         stripeCustomerId: true,
//         },
//       },
//     });

//     if (!user) {
//       return next(
//         new AppError("Not authorized, user not found", 401, "UNAUTHORIZED")
//       );
//     }

//     // Add user to request
//     req.user = user;
//     next();
//   } catch (error) {
//     console.error("Auth middleware error:", error);
//     return next(
//       new AppError("Not authorized, token failed", 401, "UNAUTHORIZED")
//     );
//   }
// };
