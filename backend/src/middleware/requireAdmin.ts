import { Request, Response, NextFunction } from "express";

// interface AuthenticatedRequest extends Request {
//   user?: {
//     id: string;
//     role: string;
//     email: string;
//   };
// }

/**
 * Middleware to require admin role
 */
export const requireAdmin = (
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  try {
    const user = req.user;

    if (!user) {
      res.status(401).json({
        success: false,
        message: "Authentication required",
      });
      return;
    }

    if (user.role !== "ADMIN") {
      res.status(403).json({
        success: false,
        message: "Admin access required",
      });
      return;
    }

    next();
  } catch (error) {
    console.error("Error in requireAdmin middleware:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error",
    });
  }
};
