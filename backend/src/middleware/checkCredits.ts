import { Request, Response, NextFunction } from "express";
import { prisma } from "../index";

/**
 * Middleware to check if user has enough credits for a specific feature
 * @param featureType The feature type to check credits for
 * @param amount The amount of credits required (defaults to 1)
 */
export const checkCredits = (featureType: string, amount: number = 1) => {
  return async (req: Request, res: Response, next: NextFunction) => {
    try {
      const userId = req.user?.id;

      if (!userId) {
        return res.status(401).json({
          success: false,
          error: {
            code: "UNAUTHORIZED",
            message: "Authentication required",
          },
        });
      }

      // Get user's subscription status
      const userSubscription = await prisma.userSubscription.findFirst({
        where: {
          userId,
          status: "ACTIVE",
          endDate: {
            gte: new Date(),
          },
        },
        include: {
          plan: true,
        },
      });

      // Check if user has an active subscription with unlimited usage
      if (userSubscription) {
        const features = userSubscription.plan.features as any;
        
        // If the subscription plan has unlimited usage for this feature, allow
        if (features && features[`${featureType.toLowerCase()}_unlimited`] === true) {
          return next();
        }
      }

      // Check user's credit balance
      const userCredit = await prisma.credit.findUnique({
        where: { userId },
      });

      if (!userCredit) {
        return res.status(402).json({
          success: false,
          error: {
            code: "INSUFFICIENT_CREDITS",
            message: "No credit account found",
          },
        });
      }

      if (userCredit.balance < amount) {
        return res.status(402).json({
          success: false,
          error: {
            code: "INSUFFICIENT_CREDITS",
            message: `Insufficient credits. Required: ${amount}, Available: ${userCredit.balance}`,
          },
        });
      }

      next();
    } catch (error) {
      console.error("Error checking credits:", error);
      return res.status(500).json({
        success: false,
        error: {
          code: "SERVER_ERROR",
          message: "An error occurred while checking credits",
        },
      });
    }
  };
};
