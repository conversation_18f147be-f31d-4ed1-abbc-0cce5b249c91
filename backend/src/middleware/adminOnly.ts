import { Request, Response, NextFunction } from 'express';
import { speedPaintingLogger } from '../utils/logging';

/**
 * Middleware to restrict access to admin users only
 */
export const adminOnly = (req: Request, res: Response, next: NextFunction) => {
  // Check if user exists and is an admin
  if (!req.user || req.user.role !== 'ADMIN') {
    speedPaintingLogger.warn({
      message: 'Unauthorized attempt to access admin resource',
      userId: req.user?.id,
      ip: req.ip,
      path: req.path,
      action: 'admin_access_denied'
    });
    
    return res.status(403).json({
      success: false,
      error: {
        code: 'FORBIDDEN',
        message: 'You do not have permission to access this resource'
      }
    });
  }
  
  // User is an admin, proceed
  next();
};
