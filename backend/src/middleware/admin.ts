import { Request, Response, NextFunction } from "express";
import { prisma } from "../index";
import { AppError } from "../utils/error";

/**
 * Middleware to check if the authenticated user is an admin
 */
export const isAdmin = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const userId = req.user?.id;

    if (!userId) {
      return next(new AppError("Authentication required", 401));
    }

    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: { role: true },
    });

    if (!user) {
      return next(new AppError("User not found", 404));
    }

    if (user.role !== "ADMIN") {
      return next(new AppError("Admin access required", 403));
    }

    next();
  } catch (error) {
    next(new AppError("Error checking admin status", 500));
  }
};
