import dotenv from "dotenv";
import path from "path";

// Load environment variables from .env file
dotenv.config({ path: path.resolve(process.cwd(), ".env") });

// Helper function to check and warn about missing environment variables
const checkEnvVar = (name: string, required = false): string | undefined => {
  const value = process.env[name];
  if (!value) {
    if (required) {
      console.error(`ERROR: Required environment variable ${name} is not set!`);
    } else {
      console.warn(`WARNING: Environment variable ${name} is not set.`);
    }
  }
  return value;
};

// Log environment variables loaded
console.log("Environment variables loaded:");

// Server configuration
export const NODE_ENV = process.env.NODE_ENV || "development";
export const PORT = parseInt(process.env.PORT || "5000", 10);
export const FRONTEND_URL =
  checkEnvVar("FRONTEND_URL") || "http://localhost:3000";
export const APP_URL = checkEnvVar("APP_URL") || "http://localhost:5000";

// Database configuration
export const DATABASE_URL = checkEnvVar("DATABASE_URL", true);

// Payment API keys
export const STRIPE_SECRET_KEY = checkEnvVar("STRIPE_SECRET_KEY");
export const STRIPE_WEBHOOK_SECRET = process.env.STRIPE_WEBHOOK_SECRET || "";
export const PAYPAL_CLIENT_ID = checkEnvVar("PAYPAL_CLIENT_ID");
export const PAYPAL_CLIENT_SECRET = checkEnvVar("PAYPAL_CLIENT_SECRET");
export const PAYPAL_WEBHOOK_ID = process.env.PAYPAL_WEBHOOK_ID || "";

// Media services API keys
export const CLOUDINARY_CLOUD_NAME = checkEnvVar("CLOUDINARY_CLOUD_NAME");
export const CLOUDINARY_API_KEY = checkEnvVar("CLOUDINARY_API_KEY");
export const CLOUDINARY_API_SECRET = checkEnvVar("CLOUDINARY_API_SECRET");

// AI services API keys
export const SYNTHESIA_API_KEY = checkEnvVar("SYNTHESIA_API_KEY");
export const STABILITY_AI_API_KEY = checkEnvVar("STABILITY_AI_API_KEY");
export const REMOVAL_AI_API_KEY = checkEnvVar("REMOVAL_AI_API_KEY");

// Email service API key
export const RESEND_API_KEY = checkEnvVar("RESEND_API_KEY");

// JWT secret for authentication
export const JWT_SECRET =
  checkEnvVar("JWT_SECRET", true) || "dev-secret-do-not-use-in-production";
export const JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || "7d";
export const JWT_REFRESH_EXPIRES_IN =
  process.env.JWT_REFRESH_EXPIRES_IN || "30d";

// Log configuration summary
console.log(`Running in ${NODE_ENV} mode on port ${PORT}`);
console.log(`Database: ${DATABASE_URL ? "Configured" : "Not configured"}`);
console.log(
  `Payment providers: ${STRIPE_SECRET_KEY ? "Stripe" : ""} ${
    PAYPAL_CLIENT_ID ? "PayPal" : ""
  }`
);
export const FROM_EMAIL = process.env.FROM_EMAIL || "<EMAIL>";

// Redis
export const REDIS_URL = process.env.REDIS_URL || "redis://localhost:6379";

// Log environment variables for debugging (only in development)
if (NODE_ENV === "development") {
  console.log("Environment variables loaded:");
  console.log("NODE_ENV:", NODE_ENV);
  console.log("DATABASE_URL:", DATABASE_URL ? "Set" : "Not set");
  console.log("STRIPE_SECRET_KEY:", STRIPE_SECRET_KEY ? "Set" : "Not set");
  console.log("PAYPAL_CLIENT_ID:", PAYPAL_CLIENT_ID ? "Set" : "Not set");
  console.log(
    "PAYPAL_CLIENT_SECRET:",
    PAYPAL_CLIENT_SECRET ? "Set" : "Not set"
  );
}
