// Load environment variables first, before any other imports
import { config } from "dotenv";
import path from "path";

// Configure dotenv with explicit path to ensure it loads correctly
config({ path: path.resolve(__dirname, "../.env") });

// Import other dependencies after environment is loaded
import express from "express";
import cors from "cors";
import helmet from "helmet";
import morgan from "morgan";
import passport from "passport";
import session from "express-session";
import { PrismaClient } from "@prisma/client";
import { errorHandler } from "./middleware/errorHandler";
import authRoutes from "./routes/auth.routes";
import userRoutes from "./routes/user.routes";
import videoRoutes from "./routes/video.routes";
import imageRoutes from "./routes/image.routes";
import bgRemovalRoutes from "./routes/bgRemoval.routes";
import paymentRoutes from "./routes/payment.routes";
import webhookRoutes from "./routes/webhook.routes";
import blogRoutes from "./routes/blog.routes";
import adminRoutes from "./routes/admin.routes";
import uploadRoutes from "./routes/upload.routes";
import analyticsRoutes from "./routes/analytics.routes";
import subscriptionRoutes from "./routes/subscription.routes";
import virtualTryOnRoutes from "./routes/virtualTryOn.routes";
import creditRoutes from "./routes/credit.routes";
import speedPaintingRoutes from "./routes/speedPainting.routes";
import speedPaintingAnalyticsRoutes from "./routes/admin/speedPaintingAnalytics.routes";
import refundRoutes from "./routes/refund.routes";
import adminPaymentRoutes from "./routes/admin-payment.routes";
import { SubscriptionService } from "./services/subscription.service";
import { configureOAuth } from "./config/oauth.config";

// Environment variables are already loaded at the top of the file
// Log some environment info for debugging
console.log(`Environment: ${process.env.NODE_ENV || "development"}`);
console.log(
  `Database connection: ${
    process.env.DATABASE_URL ? "Configured" : "Not configured"
  }`
);
console.log(
  `Stripe API key: ${
    process.env.STRIPE_SECRET_KEY ? "Configured" : "Not configured"
  }`
);

// Initialize Prisma client
export const prisma = new PrismaClient();

// Import config values
import { PORT } from "./config";
import subscriptionCron from "./cron/subscription";
// import plansRoutes from "./routes/admin/plans.routes";

// Create Express app
const app = express();
app.use((req, res, next) => {
  res.header("Access-Control-Allow-Origin", "*"); // or your specific origin
  res.header(
    "Access-Control-Allow-Headers",
    "Origin, X-Requested-With, Content-Type, Accept"
  );
  next();
});
app.use(
  cors({
    origin: "*", // Vite's default port
  })
);

// Register services
const subscriptionService = new SubscriptionService();
app.set("subscriptionService", subscriptionService);

// Stripe webhook
app.use("/api/v1/webhook", webhookRoutes);

// Run subscription cron job
subscriptionCron();

// Middleware
// app.use(cors());
app.use(helmet());
app.use(morgan("dev"));
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Session configuration for OAuth
app.use(
  session({
    secret: process.env.SESSION_SECRET || "your-session-secret-key",
    resave: false,
    saveUninitialized: false,
    cookie: {
      secure: process.env.NODE_ENV === "production",
      maxAge: 24 * 60 * 60 * 1000, // 24 hours
    },
  })
);

// Initialize Passport and OAuth configuration
app.use(passport.initialize());
app.use(passport.session());
configureOAuth();

// Serve static files from uploads directory with proper CORS and content type headers
app.use(
  "/uploads",
  (req, res, next) => {
    // Set CORS headers to allow cross-origin access
    res.set({
      "Access-Control-Allow-Origin": "*",
      "Access-Control-Allow-Methods": "GET, OPTIONS",
      "Access-Control-Allow-Headers":
        "Origin, X-Requested-With, Content-Type, Accept, Range",
      "Cross-Origin-Resource-Policy": "cross-origin",
      "Cross-Origin-Embedder-Policy": "credentialless",
    });

    // Set appropriate content type for video files
    if (req.path.endsWith(".mp4")) {
      res.set("Content-Type", "video/mp4");
    }

    // Enable partial content responses for video streaming
    res.set("Accept-Ranges", "bytes");

    next();
  },
  express.static(path.join(__dirname, "../uploads"), {
    setHeaders: (res, path) => {
      // Set cache control headers for better performance
      if (path.endsWith(".mp4")) {
        res.set("Cache-Control", "public, max-age=86400"); // Cache for 24 hours
      }
    },
  })
);

// API Routes
app.use("/api/v1/auth", authRoutes);
app.use("/api/v1/user", userRoutes);
app.use("/api/v1/video", videoRoutes);
app.use("/api/v1/image", imageRoutes);
app.use("/api/v1/background-removal", bgRemovalRoutes);
app.use("/api/v1/payment", paymentRoutes);
app.use("/api/v1/blog", blogRoutes);
app.use("/api/v1/admin", adminRoutes);
app.use("/api/v1/upload", uploadRoutes);
app.use("/api/v1/analytics", analyticsRoutes);
app.use("/api/v1/subscriptions", subscriptionRoutes);
app.use("/api/v1/virtual-try-on", virtualTryOnRoutes);
app.use("/api/v1/credits", creditRoutes);
app.use("/api/v1/speed-painting", speedPaintingRoutes);
app.use("/api/v1/admin/speed-painting", speedPaintingAnalyticsRoutes);
app.use("/api/v1/admin/payment-management", adminPaymentRoutes);
app.use("/api/v1/refunds", refundRoutes);
// app.use("/api/v1/admin/plans", plansRoutes);

// Health check endpoint
app.get("/health", (req, res) => {
  res.status(200).json({ status: "ok", message: "Server is running" });
});

// Error handling middleware
app.use(errorHandler);

// Start the server
app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
});

// Handle graceful shutdown
process.on("SIGINT", async () => {
  await prisma.$disconnect();
  console.log("Server shutting down");
  process.exit(0);
});

process.on("SIGTERM", async () => {
  await prisma.$disconnect();
  console.log("Server shutting down");
  process.exit(0);
});
