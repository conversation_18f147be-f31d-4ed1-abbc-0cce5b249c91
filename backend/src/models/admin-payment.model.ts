import { z } from "zod";

// Schema for subscription payment data
export const SubscriptionPaymentSchema = z.object({
  id: z.string(),
  userId: z.string(),
  userName: z.string().optional(),
  userEmail: z.string().optional(),
  subscriptionId: z.string(),
  planId: z.string(),
  planName: z.string(),
  amount: z.number(),
  currency: z.string(),
  status: z.enum(["PENDING", "COMPLETED", "FAILED", "REFUNDED", "PARTIALLY_REFUNDED"]),
  provider: z.enum(["STRIPE", "PAYPAL"]),
  transactionId: z.string(),
  createdAt: z.string().or(z.date()),
  metadata: z.record(z.string(), z.any()).optional(),
});

// Schema for refund request data
export const RefundRequestSchema = z.object({
  id: z.string(),
  userId: z.string(),
  userName: z.string().optional(),
  userEmail: z.string().optional(),
  paymentId: z.string(),
  subscriptionId: z.string().optional(),
  amount: z.number(),
  currency: z.string(),
  reason: z.string(),
  status: z.enum(["PENDING", "APPROVED", "REJECTED", "PROCESSED"]),
  adminNotes: z.string().optional(),
  createdAt: z.string().or(z.date()),
  updatedAt: z.string().or(z.date()).optional(),
  processedAt: z.string().or(z.date()).optional(),
  processedBy: z.string().optional(),
});

// Schema for canceled subscription data
export const CanceledSubscriptionSchema = z.object({
  id: z.string(),
  userId: z.string(),
  userName: z.string().optional(),
  userEmail: z.string().optional(),
  subscriptionId: z.string(),
  planId: z.string(),
  planName: z.string(),
  cancellationReason: z.string().optional(),
  cancellationFeedback: z.string().optional(),
  cancellationDate: z.string().or(z.date()),
  effectiveEndDate: z.string().or(z.date()),
  wasRefunded: z.boolean().default(false),
  refundId: z.string().optional(),
});

// Schema for subscription plan management
export const SubscriptionPlanManagementSchema = z.object({
  id: z.string(),
  name: z.string(),
  description: z.string().optional(),
  price: z.number(),
  currency: z.string(),
  interval: z.enum(["month", "year"]),
  features: z.record(z.string(), z.any()),
  stripePriceId: z.string().optional(),
  paypalPlanId: z.string().optional(),
  isActive: z.boolean(),
  createdAt: z.string().or(z.date()),
  updatedAt: z.string().or(z.date()).optional(),
});

// Types based on the schemas
export type SubscriptionPayment = z.infer<typeof SubscriptionPaymentSchema>;
export type RefundRequest = z.infer<typeof RefundRequestSchema>;
export type CanceledSubscription = z.infer<typeof CanceledSubscriptionSchema>;
export type SubscriptionPlanManagement = z.infer<typeof SubscriptionPlanManagementSchema>;

// Response schemas for API endpoints
export const PaymentListResponseSchema = z.object({
  success: z.boolean(),
  data: z.array(SubscriptionPaymentSchema),
  pagination: z.object({
    total: z.number(),
    page: z.number(),
    limit: z.number(),
    totalPages: z.number(),
  }),
});

export const RefundRequestListResponseSchema = z.object({
  success: z.boolean(),
  data: z.array(RefundRequestSchema),
  pagination: z.object({
    total: z.number(),
    page: z.number(),
    limit: z.number(),
    totalPages: z.number(),
  }),
});

export const CanceledSubscriptionListResponseSchema = z.object({
  success: z.boolean(),
  data: z.array(CanceledSubscriptionSchema),
  pagination: z.object({
    total: z.number(),
    page: z.number(),
    limit: z.number(),
    totalPages: z.number(),
  }),
});

export const SubscriptionPlanListResponseSchema = z.object({
  success: z.boolean(),
  data: z.array(SubscriptionPlanManagementSchema),
});

// Types for response schemas
export type PaymentListResponse = z.infer<typeof PaymentListResponseSchema>;
export type RefundRequestListResponse = z.infer<typeof RefundRequestListResponseSchema>;
export type CanceledSubscriptionListResponse = z.infer<typeof CanceledSubscriptionListResponseSchema>;
export type SubscriptionPlanListResponse = z.infer<typeof SubscriptionPlanListResponseSchema>;
