{"info": {"name": "Miragic AI - Virtual Try-On API", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "description": "API endpoints for the Virtual Try-On feature of Miragic AI"}, "item": [{"name": "Virtual Try-On", "item": [{"name": "Process Virtual Try-On", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "humanImage", "type": "file", "src": "/path/to/human/image.jpg", "description": "Image of the person (optional if modelImageId is provided)"}, {"key": "clothImage", "type": "file", "src": "/path/to/clothing/item.jpg", "description": "Image of the clothing item to try on"}, {"key": "bottomClothImage", "type": "file", "src": "/path/to/bottom/clothing/item.jpg", "description": "Image of bottom clothing item (for full outfit try-on)"}, {"key": "modelImageId", "value": "uuid-of-saved-model", "type": "text", "description": "ID of a previously saved model image (alternative to humanImage)"}, {"key": "garmentType", "value": "upper_body", "type": "text", "description": "Type of garment: 'upper_body' or 'lower_body'"}]}, "url": {"raw": "{{base_url}}/api/v1/virtual-try-on", "host": ["{{base_url}}"], "path": ["api", "v1", "virtual-try-on"]}, "description": "Process a virtual try-on request with uploaded images"}}, {"name": "Get Try-On Job Status", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/v1/virtual-try-on/{{job_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "virtual-try-on", "{{job_id}}"]}, "description": "Get the status of a specific virtual try-on job"}}, {"name": "Get Try-On History", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/v1/virtual-try-on/history", "host": ["{{base_url}}"], "path": ["api", "v1", "virtual-try-on", "history"], "query": [{"key": "page", "value": "1", "description": "Page number for pagination"}, {"key": "limit", "value": "10", "description": "Number of items per page"}]}, "description": "Get the user's virtual try-on history"}}]}, {"name": "Model Management", "item": [{"name": "Get All Model Images", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/v1/virtual-try-on/models", "host": ["{{base_url}}"], "path": ["api", "v1", "virtual-try-on", "models"]}, "description": "Get all model images (both admin and user models)"}}, {"name": "Upload Model Image", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "modelImage", "type": "file", "src": "/path/to/model/image.jpg", "description": "Image of the model"}, {"key": "modelName", "value": "<PERSON>", "type": "text", "description": "Name of the model"}, {"key": "gender", "value": "male", "type": "text", "description": "Gender of the model (male/female/other)"}]}, "url": {"raw": "{{base_url}}/api/v1/virtual-try-on/models", "host": ["{{base_url}}"], "path": ["api", "v1", "virtual-try-on", "models"]}, "description": "Upload a new model image"}}, {"name": "Delete Model Image", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/v1/virtual-try-on/models/{{model_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "virtual-try-on", "models", "{{model_id}}"]}, "description": "Delete a model image by ID"}}]}, {"name": "Clothing Management", "item": [{"name": "Upload Clothing Item", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "clothingImage", "type": "file", "src": "/path/to/clothing/item.jpg", "description": "Image of the clothing item"}, {"key": "name", "value": "Summer T-Shirt", "type": "text", "description": "Name of the clothing item"}, {"key": "clothingType", "value": "t-shirt", "type": "text", "description": "Type of clothing (e.g., t-shirt, pants, dress)"}, {"key": "category", "value": "casual", "type": "text", "description": "Category of the clothing"}, {"key": "style", "value": "casual", "type": "text", "description": "Style of the clothing"}, {"key": "color", "value": "blue", "type": "text", "description": "Color of the clothing"}, {"key": "season", "value": "summer", "type": "text", "description": "Season for the clothing"}]}, "url": {"raw": "{{base_url}}/api/v1/virtual-try-on/clothing", "host": ["{{base_url}}"], "path": ["api", "v1", "virtual-try-on", "clothing"]}, "description": "Upload a new clothing item"}}, {"name": "Get Clothing Items", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/v1/virtual-try-on/clothing", "host": ["{{base_url}}"], "path": ["api", "v1", "virtual-try-on", "clothing"], "query": [{"key": "page", "value": "1", "description": "Page number for pagination"}, {"key": "limit", "value": "10", "description": "Number of items per page"}, {"key": "clothingType", "value": "t-shirt", "description": "Filter by clothing type"}, {"key": "category", "value": "casual", "description": "Filter by category"}]}, "description": "Get all clothing items with optional filters"}}, {"name": "Delete Clothing Item", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/v1/virtual-try-on/clothing/{{clothing_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "virtual-try-on", "clothing", "{{clothing_id}}"]}, "description": "Delete a clothing item by ID"}}]}], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "variable": [{"key": "base_url", "value": "http://localhost:5000", "type": "string"}, {"key": "access_token", "value": "your_jwt_token_here", "type": "string"}]}