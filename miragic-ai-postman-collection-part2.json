{"info": {"name": "Miragic-AI API - Part 2", "description": "API collection for Miragic-AI SaaS platform - Admin Analytics, Settings, and Subscription Management", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Admin Analytics", "description": "Admin analytics endpoints for platform insights", "item": [{"name": "Get Analytics Summary", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/v1/admin/analytics/summary", "host": ["{{baseUrl}}"], "path": ["api", "v1", "admin", "analytics", "summary"]}, "description": "Get summary of platform analytics (admin only)"}}, {"name": "Get Revenue Analytics", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/v1/admin/analytics/revenue?period=monthly", "host": ["{{baseUrl}}"], "path": ["api", "v1", "admin", "analytics", "revenue"], "query": [{"key": "period", "value": "monthly"}]}, "description": "Get revenue analytics data (admin only)"}}, {"name": "Get Usage Analytics", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/v1/admin/analytics/usage?period=weekly", "host": ["{{baseUrl}}"], "path": ["api", "v1", "admin", "analytics", "usage"], "query": [{"key": "period", "value": "weekly"}]}, "description": "Get platform usage analytics data (admin only)"}}, {"name": "Get User Analytics", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/v1/admin/analytics/users?period=daily", "host": ["{{baseUrl}}"], "path": ["api", "v1", "admin", "analytics", "users"], "query": [{"key": "period", "value": "daily"}]}, "description": "Get user analytics data (admin only)"}}, {"name": "Get Speed Painting Analytics", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/v1/admin/analytics/speed-painting?period=monthly", "host": ["{{baseUrl}}"], "path": ["api", "v1", "admin", "analytics", "speed-painting"], "query": [{"key": "period", "value": "monthly"}]}, "description": "Get speed painting feature analytics data (admin only)"}}]}, {"name": "Admin Dashboard", "description": "Admin dashboard endpoints for platform management", "item": [{"name": "Get Admin Stats", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/v1/admin/stats", "host": ["{{baseUrl}}"], "path": ["api", "v1", "admin", "stats"]}, "description": "Get admin dashboard statistics (admin only)"}}, {"name": "Get Recent Users", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/v1/admin/recent-users?limit=10", "host": ["{{baseUrl}}"], "path": ["api", "v1", "admin", "recent-users"], "query": [{"key": "limit", "value": "10"}]}, "description": "Get list of recently registered users (admin only)"}}, {"name": "Get Recent Jobs", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/v1/admin/recent-jobs?limit=10", "host": ["{{baseUrl}}"], "path": ["api", "v1", "admin", "recent-jobs"], "query": [{"key": "limit", "value": "10"}]}, "description": "Get list of recent AI generation jobs (admin only)"}}]}, {"name": "<PERSON><PERSON>s", "description": "Admin settings endpoints for platform configuration", "item": [{"name": "Get Settings", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/v1/admin/settings", "host": ["{{baseUrl}}"], "path": ["api", "v1", "admin", "settings"]}, "description": "Get platform settings (admin only)"}}, {"name": "Update Settings", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/v1/admin/settings", "host": ["{{baseUrl}}"], "path": ["api", "v1", "admin", "settings"]}, "body": {"mode": "raw", "raw": "{\n  \"siteName\": \"Miragic-AI\",\n  \"siteDescription\": \"AI-powered content generation platform\",\n  \"contactEmail\": \"<EMAIL>\",\n  \"enableRegistration\": true,\n  \"enableBlog\": true,\n  \"maintenanceMode\": false\n}"}, "description": "Update platform settings (admin only)"}}, {"name": "Get Payment Config", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/v1/admin/payment-config", "host": ["{{baseUrl}}"], "path": ["api", "v1", "admin", "payment-config"]}, "description": "Get payment gateway configuration (admin only)"}}, {"name": "Update Payment Config", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/v1/admin/payment-config", "host": ["{{baseUrl}}"], "path": ["api", "v1", "admin", "payment-config"]}, "body": {"mode": "raw", "raw": "{\n  \"enableStripe\": true,\n  \"enablePayPal\": true,\n  \"stripePublicKey\": \"pk_test_example\",\n  \"paypalClientId\": \"client-id-example\",\n  \"currency\": \"USD\"\n}"}, "description": "Update payment gateway configuration (admin only)"}}]}, {"name": "Admin Subscription Plans", "description": "Admin endpoints for subscription plan management", "item": [{"name": "Get All Subscription Plans", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/v1/admin/subscription-plans", "host": ["{{baseUrl}}"], "path": ["api", "v1", "admin", "subscription-plans"]}, "description": "Get all subscription plans (admin only)"}}, {"name": "Get Subscription Plan by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/v1/admin/subscription-plans/:planId", "host": ["{{baseUrl}}"], "path": ["api", "v1", "admin", "subscription-plans", ":planId"], "variable": [{"key": "planId", "value": "plan-id"}]}, "description": "Get subscription plan details by ID (admin only)"}}, {"name": "Create Subscription Plan", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/v1/admin/subscription-plans", "host": ["{{baseUrl}}"], "path": ["api", "v1", "admin", "subscription-plans"]}, "body": {"mode": "raw", "raw": "{\n  \"name\": \"Pro Plan\",\n  \"description\": \"Professional plan with advanced features\",\n  \"price\": 49.99,\n  \"interval\": \"month\",\n  \"features\": [\"Feature 1\", \"Feature 2\", \"Feature 3\"],\n  \"credits\": 500,\n  \"stripePriceId\": \"price_example\",\n  \"isActive\": true\n}"}, "description": "Create a new subscription plan (admin only)"}}, {"name": "Update Subscription Plan", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/v1/admin/subscription-plans/:planId", "host": ["{{baseUrl}}"], "path": ["api", "v1", "admin", "subscription-plans", ":planId"], "variable": [{"key": "planId", "value": "plan-id"}]}, "body": {"mode": "raw", "raw": "{\n  \"name\": \"Updated Pro Plan\",\n  \"description\": \"Updated professional plan with advanced features\",\n  \"price\": 59.99,\n  \"features\": [\"Feature 1\", \"Feature 2\", \"Feature 3\", \"New Feature\"],\n  \"credits\": 600,\n  \"isActive\": true\n}"}, "description": "Update an existing subscription plan (admin only)"}}, {"name": "Delete Subscription Plan", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/v1/admin/subscription-plans/:planId", "host": ["{{baseUrl}}"], "path": ["api", "v1", "admin", "subscription-plans", ":planId"], "variable": [{"key": "planId", "value": "plan-id"}]}, "description": "Delete a subscription plan (admin only)"}}]}], "variable": [{"key": "baseUrl", "value": "http://localhost:3000", "type": "string"}]}