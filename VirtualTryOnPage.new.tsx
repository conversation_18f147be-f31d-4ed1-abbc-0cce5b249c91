import React, { type ChangeEvent, type DragEvent } from "react";
import {
  Upload,
  RefreshCw,
  Download,
  Loader2,
  AlertCircle,
} from "lucide-react";
import ShadowButton from "@/components/ui/shadowButton";
// Import toast if needed later
// import { toast } from "sonner";
import { useVirtualTryOn } from "@/hooks/useVirtualTryOn";

import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import useCredits from "@/hooks/useCredits";
// Import useApp when needed
// import { useApp } from "@/contexts/useApp";

// Use React.FC without props since this component doesn't need any
const VirtualTryOnPage: React.FC = () => {
  // Use authentication context and credits hooks
  // const { user } = useApp();
  const { getCost, hasEnoughCredits } = useCredits();

  // Use our custom virtual try-on hook
  const {
    // State
    humanImage,
    humanImagePreview,
    clothImage,
    clothImagePreview,
    bottomClothImage,
    bottomClothImagePreview,
    selectedModelId,
    saveAsModel,
    modelName,
    modelGender,
    mode,
    isProcessing,
    currentJob,
    models,
    recentTryOns,
    isLoadingModels,
    isLoadingRecent,

    // Refs
    humanFileInputRef,
    clothFileInputRef,
    bottomClothFileInputRef,

    // Actions
    setSaveAsModel,
    setModelName,
    setModelGender,
    setMode,

    // Methods
    processHumanFile,
    processClothFile,
    processBottomClothFile,
    handleModelSelect,
    handleModeSelect,
    handleReset,
    handleDownload,
    handleGenerate,
    loadRecentTryOn,
    // These methods are available but not used in this component
    // fetchModels,
    // fetchRecentTryOns,
  } = useVirtualTryOn();

  // UI state for drag and drop
  const [isDragOverHuman, setIsDragOverHuman] = React.useState<boolean>(false);
  const [isDragOverCloth, setIsDragOverCloth] = React.useState<boolean>(false);
  const [isDragOverBottom, setIsDragOverBottom] =
    React.useState<boolean>(false);
  console.log("recentTryOns", recentTryOns);
  // File handling functions for human image
  const handleDragOverHuman = (e: DragEvent<HTMLDivElement>): void => {
    e.preventDefault();
    setIsDragOverHuman(true);
  };

  const handleDragLeaveHuman = (e: DragEvent<HTMLDivElement>): void => {
    e.preventDefault();
    setIsDragOverHuman(false);
  };

  const handleDropHuman = (e: DragEvent<HTMLDivElement>): void => {
    e.preventDefault();
    setIsDragOverHuman(false);
    const files = e.dataTransfer.files;
    if (files.length > 0) {
      processHumanFile(files[0]);
    }
  };

  // File handling functions for clothing image
  const handleDragOverCloth = (e: DragEvent<HTMLDivElement>): void => {
    e.preventDefault();
    setIsDragOverCloth(true);
  };

  const handleDragLeaveCloth = (e: DragEvent<HTMLDivElement>): void => {
    e.preventDefault();
    setIsDragOverCloth(false);
  };

  const handleDropCloth = (e: DragEvent<HTMLDivElement>): void => {
    e.preventDefault();
    setIsDragOverCloth(false);
    const files = e.dataTransfer.files;
    if (files.length > 0) {
      processClothFile(files[0]);
    }
  };

  // File handling functions for bottom clothing image
  const handleDragOverBottom = (e: DragEvent<HTMLDivElement>): void => {
    e.preventDefault();
    setIsDragOverBottom(true);
  };

  const handleDragLeaveBottom = (e: DragEvent<HTMLDivElement>): void => {
    e.preventDefault();
    setIsDragOverBottom(false);
  };

  const handleDropBottom = (e: DragEvent<HTMLDivElement>): void => {
    e.preventDefault();
    setIsDragOverBottom(false);
    const files = e.dataTransfer.files;
    if (files.length > 0) {
      processBottomClothFile(files[0]);
    }
  };

  // Handle file selection from input
  const handleHumanFileSelect = (e: ChangeEvent<HTMLInputElement>): void => {
    const files = e.target.files;
    if (files?.length) {
      processHumanFile(files[0]);
    }
  };

  const handleClothFileSelect = (e: ChangeEvent<HTMLInputElement>): void => {
    const files = e.target.files;
    if (files?.length) {
      processClothFile(files[0]);
    }
  };

  const handleBottomClothFileSelect = (
    e: ChangeEvent<HTMLInputElement>
  ): void => {
    const files = e.target.files;
    if (files?.length) {
      processBottomClothFile(files[0]);
    }
  };

  // Trigger file input click
  const handleHumanAddClick = (): void => {
    if (humanFileInputRef.current) {
      humanFileInputRef.current.click();
    }
  };

  const handleClothAddClick = (): void => {
    if (clothFileInputRef.current) {
      clothFileInputRef.current.click();
    }
  };

  const handleBottomClothAddClick = (): void => {
    if (bottomClothFileInputRef.current) {
      bottomClothFileInputRef.current.click();
    }
  };

  // Handle save as model toggle
  const handleSaveAsModelToggle = (): void => {
    setSaveAsModel(!saveAsModel);
  };

  // Check if we can generate (have required inputs and credits)
  const canGenerate = (): boolean => {
    // Must have either a human image or a selected model
    const hasHumanSource = humanImage !== null || selectedModelId !== null;
    // Must have a clothing image
    const hasClothing = clothImage !== null;
    // If in TOP_BOTTOM mode, must have bottom clothing
    const hasRequiredBottomClothing =
      mode === "SINGLE" || bottomClothImage !== null;
    // Must have enough credits
    const hasSufficientCredits = hasEnoughCredits("virtualTryOn");

    return (
      hasHumanSource &&
      hasClothing &&
      hasRequiredBottomClothing &&
      hasSufficientCredits
    );
  };

  // Get credit cost display
  const creditCost = getCost("virtualTryOn");

  return (
    <div className="container mx-auto py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">Virtual Try-On</h1>
        <p className="text-gray-600">
          Try on clothing virtually with our AI-powered tool. Upload your photo
          or select a model, then choose the clothing items you want to try on.
        </p>
        <div className="mt-2 flex items-center">
          <span className="text-sm font-medium mr-2">Cost:</span>
          <span className="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded">
            {creditCost} credits per try-on
          </span>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Left column: Input controls */}
        <div className="lg:col-span-2">
          <Card>
            <CardHeader>
              <CardTitle>Create Virtual Try-On</CardTitle>
            </CardHeader>
            <CardContent>
              <Tabs defaultValue="upload" className="mb-6">
                <TabsList className="mb-4">
                  <TabsTrigger value="upload">Upload Your Photo</TabsTrigger>
                  <TabsTrigger value="model">Select a Model</TabsTrigger>
                </TabsList>

                <TabsContent value="upload">
                  <div className="mb-6">
                    <Label
                      htmlFor="human-image"
                      className="block mb-2 text-sm font-medium"
                    >
                      Your Photo
                    </Label>
                    <div
                      className={`border-2 border-dashed rounded-lg p-4 text-center cursor-pointer transition-colors ${
                        isDragOverHuman
                          ? "border-blue-500 bg-blue-50"
                          : "border-gray-300 hover:border-gray-400"
                      } ${humanImagePreview ? "bg-gray-50" : ""}`}
                      onDragOver={handleDragOverHuman}
                      onDragLeave={handleDragLeaveHuman}
                      onDrop={handleDropHuman}
                      onClick={handleHumanAddClick}
                    >
                      {humanImagePreview ? (
                        <div className="relative">
                          <img
                            src={humanImagePreview}
                            alt="Human preview"
                            className="mx-auto max-h-64 rounded"
                          />
                          <button
                            className="absolute top-2 right-2 bg-white rounded-full p-1 shadow-md hover:bg-gray-100"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleReset();
                            }}
                          >
                            <RefreshCw className="h-4 w-4" />
                          </button>
                        </div>
                      ) : (
                        <div className="py-6">
                          <Upload className="mx-auto h-12 w-12 text-gray-400" />
                          <p className="mt-2 text-sm text-gray-600">
                            Drag and drop your photo here, or click to select
                          </p>
                          <p className="mt-1 text-xs text-gray-500">
                            PNG, JPG, JPEG or WEBP (max 10MB)
                          </p>
                        </div>
                      )}
                      <input
                        id="human-image"
                        type="file"
                        className="hidden"
                        accept="image/png,image/jpeg,image/jpg,image/webp"
                        onChange={handleHumanFileSelect}
                        ref={humanFileInputRef}
                      />
                    </div>

                    {humanImage && (
                      <div className="mt-4">
                        <div className="flex items-center">
                          <Switch
                            id="save-as-model"
                            checked={saveAsModel}
                            onCheckedChange={handleSaveAsModelToggle}
                          />
                          <Label
                            htmlFor="save-as-model"
                            className="ml-2 text-sm font-medium"
                          >
                            Save as model for future use
                          </Label>
                        </div>

                        {saveAsModel && (
                          <div className="mt-4 space-y-4">
                            <div>
                              <Label
                                htmlFor="model-name"
                                className="block mb-2 text-sm font-medium"
                              >
                                Model Name
                              </Label>
                              <Input
                                id="model-name"
                                value={modelName}
                                onChange={(e) => setModelName(e.target.value)}
                                placeholder="Enter a name for this model"
                                className="w-full"
                              />
                            </div>

                            <div>
                              <Label
                                htmlFor="model-gender"
                                className="block mb-2 text-sm font-medium"
                              >
                                Gender
                              </Label>
                              <Select
                                value={modelGender}
                                onValueChange={(value) =>
                                  setModelGender(
                                    value as "MALE" | "FEMALE" | "UNISEX"
                                  )
                                }
                              >
                                <SelectTrigger
                                  id="model-gender"
                                  className="w-full"
                                >
                                  <SelectValue placeholder="Select gender" />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="MALE">Male</SelectItem>
                                  <SelectItem value="FEMALE">Female</SelectItem>
                                  <SelectItem value="UNISEX">Unisex</SelectItem>
                                </SelectContent>
                              </Select>
                            </div>
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                </TabsContent>

                <TabsContent value="model">
                  <div className="mb-6">
                    <Label className="block mb-2 text-sm font-medium">
                      Select a Model
                    </Label>
                    {isLoadingModels ? (
                      <div className="flex justify-center py-8">
                        <Loader2 className="h-8 w-8 animate-spin text-gray-400" />
                      </div>
                    ) : models.length > 0 ? (
                      <div className="grid grid-cols-2 sm:grid-cols-3 gap-4">
                        {models.map((model) => (
                          <div
                            key={model.id}
                            className={`border rounded-lg p-2 cursor-pointer transition-all ${
                              selectedModelId === model.id
                                ? "border-blue-500 ring-2 ring-blue-200"
                                : "border-gray-200 hover:border-gray-300"
                            }`}
                            onClick={() => handleModelSelect(model.id)}
                          >
                            <img
                              src={model.imagePath}
                              alt={model.modelName || "Model"}
                              className="w-full h-40 object-cover rounded"
                            />
                            <div className="mt-2 text-center">
                              <p className="text-sm font-medium truncate">
                                {model.modelName || "Model"}
                              </p>
                              {model.gender && (
                                <p className="text-xs text-gray-500 capitalize">
                                  {model.gender.toLowerCase()}
                                </p>
                              )}
                            </div>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <div className="text-center py-8 border border-dashed rounded-lg">
                        <p className="text-gray-500">No models available</p>
                        <p className="text-sm text-gray-400 mt-1">
                          Upload a photo and save it as a model
                        </p>
                      </div>
                    )}
                  </div>
                </TabsContent>
              </Tabs>

              <div className="mb-6">
                <Label className="block mb-2 text-sm font-medium">
                  Try-On Mode
                </Label>
                <div className="flex space-x-4">
                  <Button
                    variant={mode === "SINGLE" ? "default" : "outline"}
                    onClick={() => handleModeSelect("SINGLE")}
                    className="flex-1"
                  >
                    Single Item
                  </Button>
                  <Button
                    variant={mode === "TOP_BOTTOM" ? "default" : "outline"}
                    onClick={() => handleModeSelect("TOP_BOTTOM")}
                    className="flex-1"
                  >
                    Top & Bottom
                  </Button>
                </div>
              </div>

              <div className="mb-6">
                <Label
                  htmlFor="cloth-image"
                  className="block mb-2 text-sm font-medium"
                >
                  {mode === "SINGLE" ? "Clothing Item" : "Top Clothing Item"}
                </Label>
                <div
                  className={`border-2 border-dashed rounded-lg p-4 text-center cursor-pointer transition-colors ${
                    isDragOverCloth
                      ? "border-blue-500 bg-blue-50"
                      : "border-gray-300 hover:border-gray-400"
                  } ${clothImagePreview ? "bg-gray-50" : ""}`}
                  onDragOver={handleDragOverCloth}
                  onDragLeave={handleDragLeaveCloth}
                  onDrop={handleDropCloth}
                  onClick={handleClothAddClick}
                >
                  {clothImagePreview ? (
                    <div className="relative">
                      <img
                        src={clothImagePreview}
                        alt="Clothing preview"
                        className="mx-auto max-h-64 rounded"
                      />
                      <button
                        className="absolute top-2 right-2 bg-white rounded-full p-1 shadow-md hover:bg-gray-100"
                        onClick={(e) => {
                          e.stopPropagation();
                          setMode("SINGLE");
                        }}
                      >
                        <RefreshCw className="h-4 w-4" />
                      </button>
                    </div>
                  ) : (
                    <div className="py-6">
                      <Upload className="mx-auto h-12 w-12 text-gray-400" />
                      <p className="mt-2 text-sm text-gray-600">
                        Drag and drop your{" "}
                        {mode === "SINGLE"
                          ? "clothing item"
                          : "top clothing item"}{" "}
                        here, or click to select
                      </p>
                      <p className="mt-1 text-xs text-gray-500">
                        PNG, JPG, JPEG or WEBP (max 10MB)
                      </p>
                    </div>
                  )}
                  <input
                    id="cloth-image"
                    type="file"
                    className="hidden"
                    accept="image/png,image/jpeg,image/jpg,image/webp"
                    onChange={handleClothFileSelect}
                    ref={clothFileInputRef}
                  />
                </div>
              </div>

              {mode === "TOP_BOTTOM" && (
                <div className="mb-6">
                  <Label
                    htmlFor="bottom-cloth-image"
                    className="block mb-2 text-sm font-medium"
                  >
                    Bottom Clothing Item
                  </Label>
                  <div
                    className={`border-2 border-dashed rounded-lg p-4 text-center cursor-pointer transition-colors ${
                      isDragOverBottom
                        ? "border-blue-500 bg-blue-50"
                        : "border-gray-300 hover:border-gray-400"
                    } ${bottomClothImagePreview ? "bg-gray-50" : ""}`}
                    onDragOver={handleDragOverBottom}
                    onDragLeave={handleDragLeaveBottom}
                    onDrop={handleDropBottom}
                    onClick={handleBottomClothAddClick}
                  >
                    {bottomClothImagePreview ? (
                      <div className="relative">
                        <img
                          src={bottomClothImagePreview}
                          alt="Bottom clothing preview"
                          className="mx-auto max-h-64 rounded"
                        />
                        <button
                          className="absolute top-2 right-2 bg-white rounded-full p-1 shadow-md hover:bg-gray-100"
                          onClick={(e) => {
                            e.stopPropagation();
                            setMode("SINGLE");
                          }}
                        >
                          <RefreshCw className="h-4 w-4" />
                        </button>
                      </div>
                    ) : (
                      <div className="py-6">
                        <Upload className="mx-auto h-12 w-12 text-gray-400" />
                        <p className="mt-2 text-sm text-gray-600">
                          Drag and drop your bottom clothing item here, or click
                          to select
                        </p>
                        <p className="mt-1 text-xs text-gray-500">
                          PNG, JPG, JPEG or WEBP (max 10MB)
                        </p>
                      </div>
                    )}
                    <input
                      id="bottom-cloth-image"
                      type="file"
                      className="hidden"
                      accept="image/png,image/jpeg,image/jpg,image/webp"
                      onChange={handleBottomClothFileSelect}
                      ref={bottomClothFileInputRef}
                    />
                  </div>
                </div>
              )}
            </CardContent>
            <CardFooter>
              <div className="flex flex-col w-full space-y-4">
                <ShadowButton
                  onClick={handleGenerate}
                  disabled={!canGenerate() || isProcessing}
                  className="w-full"
                >
                  {isProcessing ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Processing...
                    </>
                  ) : (
                    <>Generate Virtual Try-On</>
                  )}
                </ShadowButton>

                {!hasEnoughCredits("virtualTryOn") && (
                  <p className="text-red-500 text-sm text-center">
                    You don't have enough credits for this operation. Please
                    purchase more credits.
                  </p>
                )}
              </div>
            </CardFooter>
          </Card>
        </div>

        {/* Right column: Results */}
        <div>
          <Card>
            <CardHeader>
              <CardTitle>Results</CardTitle>
            </CardHeader>
            <CardContent>
              {currentJob ? (
                <div>
                  {currentJob?.status === "COMPLETED" &&
                  currentJob?.resultImagePath ? (
                    <div className="text-center">
                      <img
                        src={currentJob?.resultImagePath}
                        alt="Virtual Try-On Result"
                        className="mx-auto rounded-lg shadow-md max-h-96"
                      />
                      <div className="mt-4">
                        <Button
                          variant="outline"
                          className="w-full"
                          onClick={handleDownload}
                        >
                          <Download className="mr-2 h-4 w-4" />
                          Download Result
                        </Button>
                      </div>
                    </div>
                  ) : currentJob.status === "FAILED" ? (
                    <div className="text-center py-8">
                      <div className="bg-red-50 p-4 rounded-lg">
                        <div className="flex justify-center mb-4">
                          <div className="bg-red-100 rounded-full p-3">
                            <AlertCircle className="h-6 w-6 text-red-600" />
                          </div>
                        </div>
                        <h3 className="text-lg font-medium text-red-800 mb-2">
                          Processing Failed
                        </h3>
                        <p className="text-sm text-red-600">
                          {currentJob.errorMessage ||
                            "An error occurred during processing."}
                        </p>
                        <Button
                          variant="outline"
                          className="mt-4"
                          onClick={handleReset}
                        >
                          Try Again
                        </Button>
                      </div>
                    </div>
                  ) : (
                    <div className="text-center py-12">
                      <Loader2 className="mx-auto h-12 w-12 animate-spin text-gray-400" />
                      <p className="mt-4 text-gray-600">
                        {currentJob.status === "PENDING"
                          ? "Preparing your virtual try-on..."
                          : "Processing your virtual try-on..."}
                      </p>
                    </div>
                  )}
                </div>
              ) : (
                <div className="text-center py-12 border border-dashed rounded-lg">
                  <p className="text-gray-500">No results yet</p>
                  <p className="text-sm text-gray-400 mt-1">
                    Upload images and generate a virtual try-on
                  </p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Recent Try-Ons */}
          <Card className="mt-6">
            <CardHeader>
              <CardTitle>Recent Try-Ons</CardTitle>
            </CardHeader>
            <CardContent>
              {isLoadingRecent ? (
                <div className="flex justify-center py-4">
                  <Loader2 className="h-6 w-6 animate-spin text-gray-400" />
                </div>
              ) : recentTryOns.length > 0 ? (
                <div className="grid grid-cols-2 gap-4">
                  {recentTryOns.map((item) => (
                    <div
                      key={item.id}
                      className="border rounded-lg overflow-hidden cursor-pointer hover:shadow-md transition-shadow"
                      onClick={() => loadRecentTryOn(item.tryOn)}
                    >
                      {item?.tryOn?.resultImagePath ? (
                        <img
                          src={item?.tryOn?.resultImagePath}
                          alt="Recent try-on"
                          className="w-full h-32 object-cover"
                        />
                      ) : (
                        <div className="w-full h-32 bg-gray-100 flex items-center justify-center">
                          <p className="text-gray-400 text-xs">
                            {item?.tryOn?.status === "FAILED"
                              ? "Failed"
                              : "Processing"}
                          </p>
                        </div>
                      )}
                      <div className="p-2">
                        <p className="text-xs text-gray-500">
                          {new Date(item.accessedAt).toLocaleDateString()}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-6">
                  <p className="text-gray-500">No recent try-ons</p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default VirtualTryOnPage;
