<svg width="93" height="82" viewBox="0 0 93 82" fill="none" xmlns="http://www.w3.org/2000/svg">
<foreignObject x="-13.3174" y="-13.3184" width="157.52" height="173.154"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(7px);clip-path:url(#bgblur_0_326_7055_clip_path);height:100%;width:100%"></div></foreignObject><g filter="url(#filter0_i_326_7055)" data-figma-bg-blur-radius="14">
<rect x="38.1953" y="-2" width="100" height="125" rx="10" transform="rotate(18.7572 38.1953 -2)" fill="url(#paint0_linear_326_7055)"/>
<rect x="38.508" y="-1.36578" width="99" height="124" rx="9.5" transform="rotate(18.7572 38.508 -1.36578)" stroke="white" stroke-opacity="0.15"/>
</g>
<defs>
<filter id="filter0_i_326_7055" x="-13.3174" y="-13.3184" width="157.52" height="173.154" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feMorphology radius="3" operator="erode" in="SourceAlpha" result="effect1_innerShadow_326_7055"/>
<feOffset/>
<feGaussianBlur stdDeviation="3"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_326_7055"/>
</filter>
<clipPath id="bgblur_0_326_7055_clip_path" transform="translate(13.3174 13.3184)"><rect x="38.1953" y="-2" width="100" height="125" rx="10" transform="rotate(18.7572 38.1953 -2)"/>
</clipPath><linearGradient id="paint0_linear_326_7055" x1="44.6056" y1="60.5" x2="130.503" y2="60.5" gradientUnits="userSpaceOnUse">
<stop stop-color="#2C9BF7"/>
<stop offset="1" stop-color="#8054F3"/>
</linearGradient>
</defs>
</svg>
