import ApiService from "./api.service";

// Common interfaces
export interface ApiResponse<T> {
  success: boolean;
  data: T;
  error?: {
    code: string;
    message: string;
  };
}

export interface GenerationJob {
  id: string;
  userId: string;
  status: "pending" | "processing" | "completed" | "failed";
  type: "video" | "image" | "background-removal";
  creditsUsed: number;
  createdAt: string;
  updatedAt: string;
  result?: string;
  error?: string;
}

export interface JobListParams {
  page?: number;
  limit?: number;
  type?: "video" | "image" | "background-removal" | "all";
  status?: "pending" | "processing" | "completed" | "failed" | "all";
  sortBy?: string;
  sortOrder?: "asc" | "desc";
}

export interface JobListResponse {
  jobs: GenerationJob[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

// Video generation interfaces
export interface VideoGenerationJob extends GenerationJob {
  type: "video";
  params: {
    text: string;
    voiceId: string;
    avatarId: string;
    background?: string;
    duration?: number;
    resolution?: string;
  };
}

export interface VideoGenerationParams extends Record<string, unknown> {
  text: string;
  voiceId: string;
  avatarId: string;
  background?: string;
  duration?: number;
  resolution?: string;
}

export interface Voice {
  id: string;
  name: string;
  gender: "male" | "female";
  language: string;
  preview?: string;
}

export interface Avatar {
  id: string;
  name: string;
  gender: "male" | "female";
  thumbnail: string;
  category: string;
}

// Image generation interfaces
export interface ImageGenerationJob extends GenerationJob {
  type: "image";
  params: {
    prompt: string;
    negativePrompt?: string;
    width: number;
    height: number;
    numOutputs: number;
    style?: string;
  };
}

export interface ImageGenerationParams extends Record<string, unknown> {
  prompt: string;
  negativePrompt?: string;
  width: number;
  height: number;
  numOutputs: number;
  style?: string;
}

export interface ImageStyle {
  id: string;
  name: string;
  description: string;
  preview: string;
}

// Background removal interfaces
export interface BackgroundRemovalJob extends GenerationJob {
  type: "background-removal";
  params: {
    imageUrl: string;
    newBackground?: string;
  };
}

export interface BackgroundRemovalParams extends Record<string, unknown> {
  imageUrl: string;
  newBackground?: string;
}

const GenerationService = {
  // Job management
  async getJobs(params: JobListParams = {}): Promise<JobListResponse> {
    // Use a more generic endpoint that combines all job types
    const response = await ApiService.get<ApiResponse<JobListResponse>>("/user/jobs", {
      params,
    });
    return response.data.data;
  },

  async getJobById(jobId: string): Promise<GenerationJob> {
    const response = await ApiService.get<ApiResponse<GenerationJob>>(`/user/jobs/${jobId}`);
    return response.data.data;
  },

  // Video generation
  async generateVideo(
    params: VideoGenerationParams
  ): Promise<VideoGenerationJob> {
    const response = await ApiService.post<ApiResponse<VideoGenerationJob>>(
      "/video/generate",
      params
    );
    return response.data.data;
  },

  async getVoices(): Promise<Voice[]> {
    const response = await ApiService.get<ApiResponse<Voice[]>>("/video/voices");
    return response.data.data;
  },

  async getAvatars(): Promise<Avatar[]> {
    const response = await ApiService.get<ApiResponse<Avatar[]>>("/video/avatars");
    console.log("response.data", response.data);
    return response.data.data;
  },

  // Image generation
  async generateImage(
    params: ImageGenerationParams
  ): Promise<ImageGenerationJob> {
    const response = await ApiService.post<ApiResponse<ImageGenerationJob>>(
      "/image/generate",
      params
    );
    return response.data.data;
  },

  async getImageStyles(): Promise<ImageStyle[]> {
    const response = await ApiService.get<ApiResponse<ImageStyle[]>>("/image/styles");
    return response.data.data;
  },

  // Background removal
  async removeBackground(
    params: BackgroundRemovalParams
  ): Promise<BackgroundRemovalJob> {
    const response = await ApiService.post<ApiResponse<BackgroundRemovalJob>>(
      "/background-removal/process",
      params
    );
    console.log("Background removal response:", response.data);
    return response.data.data;
  },

  // File upload for generation
  async uploadFile(
    file: File,
    type: "image" | "background"
  ): Promise<{ url: string }> {
    const formData = new FormData();
    formData.append("file", file);
    formData.append("type", type);

    // Use FormData type for proper typing
    const response = await ApiService.post<ApiResponse<{ url: string; type: string; filename: string; size: number }>, FormData>(
      "/upload",
      formData,
      {
        headers: {
          "Content-Type": "multipart/form-data",
        },
      }
    );

    console.log("Upload response:", response.data);
    
    // Return just the URL for backward compatibility
    return { url: response.data.data.url };
  },
};

export default GenerationService;
