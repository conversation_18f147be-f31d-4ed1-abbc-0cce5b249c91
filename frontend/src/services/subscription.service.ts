import ApiService from "./api.service";
import type { ApiResponse } from "./auth.service";

// Types for subscription plans
export interface FeatureHighlight {
  title: string;
  description: string;
  icon?: string;
}

export interface SubscriptionPlanFeatures {
  videoGenerationQuota: number | string;
  imageGenerationQuota: number | string;
  backgroundRemovalQuota: number | string;
  processingPriority: string;
  uploadSpeed: string;
  concurrentJobs: number | string;
  advancedAI: boolean;
  customTemplates: number | string;
  apiAccess: boolean;
  supportLevel: string;
  teamMembers: number | string;
  sharedWorkspaces: number | string;
  storageLimit: string;
  assetLibrary: string;
  historyRetention: string;
  [key: string]: number | string | boolean; // Allow for additional features
}

export interface SubscriptionPlan {
  id: string;
  name: string;
  displayName: string;
  description: string;
  price: number;
  currency: string;
  interval: string;
  features: SubscriptionPlanFeatures;
  featureHighlights: FeatureHighlight[];
  colorScheme?: string;
  isFeatured: boolean;
  sortOrder: number;
  stripePriceId?: string;
  paypalPlanId?: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface UserSubscription {
  id: string;
  userId: string;
  planId: string;
  stripeSubscriptionId?: string;
  paypalSubscriptionId?: string;
  status: "ACTIVE" | "CANCELED" | "PAST_DUE" | "TRIALING" | "UNPAID";
  startDate: string;
  endDate?: string;
  currentPeriodEnd: string;
  createdAt: string;
  updatedAt: string;
  plan: SubscriptionPlan;
}

export interface ScheduledPlanChange {
  id: string;
  userId: string;
  currentSubscriptionId: string;
  newPlanId: string;
  effectiveDate: string;
  changeType: "UPGRADE" | "DOWNGRADE";
  status: "SCHEDULED" | "COMPLETED" | "FAILED" | "CANCELLED";
  processedDate?: string;
  errorMessage?: string;
  createdAt: string;
  updatedAt: string;
  currentSubscription: UserSubscription;
  newPlan: SubscriptionPlan;
}

export interface ProrationDetails {
  remainingDays: number;
  unusedAmount: number;
  upgradeAmount: number;
}

export interface UpgradeResponse {
  subscription: UserSubscription;
  prorationDetails: ProrationDetails;
}

export interface DowngradeResponse {
  scheduledDowngrade: ScheduledPlanChange;
  message: string;
}

export interface FeatureCategory {
  name: string;
  features: string[];
}

export interface PlanComparisonData {
  plans: SubscriptionPlan[];
  currentPlanId: string | null;
  featureCategories: FeatureCategory[];
}

export interface CheckoutSessionRequest {
  planId: string;
  successUrl: string;
  cancelUrl: string;
}

export interface CheckoutSessionResponse {
  id: string;
  url: string;
}

const SubscriptionService = {
  /**
   * Get all subscription plans
   */
  async getSubscriptionPlans(): Promise<SubscriptionPlan[]> {
    const response = await ApiService.get<ApiResponse<SubscriptionPlan[]>>(
      "/subscriptions/plans"
    );
    return response.data.data;
  },

  /**
   * Request a refund for a subscription
   */
  async requestRefund(subscriptionId: string): Promise<ApiResponse<any>> {
    const response = await ApiService.post<ApiResponse<any>>(
      "/refunds/request",
      { subscriptionId }
    );
    return response.data;
  },

  /**
   * Get user's refund requests
   */
  async getUserRefunds(): Promise<ApiResponse<any>> {
    const response = await ApiService.get<ApiResponse<any>>("/refunds/user");
    return response.data;
  },

  /**
   * Get a specific subscription plan by ID
   */
  async getSubscriptionPlanById(id: string): Promise<SubscriptionPlan> {
    const response = await ApiService.get<ApiResponse<SubscriptionPlan>>(
      `/subscriptions/plans/${id}`
    );
    return response.data.data;
  },

  /**
   * Get the current user's subscription details
   */
  async getUserSubscription(): Promise<UserSubscription | null> {
    const response = await ApiService.get<ApiResponse<UserSubscription | null>>(
      "/subscriptions/user"
    );
    return response.data.data;
  },

  /**
   * Compare all subscription plans
   */
  async compareSubscriptionPlans(): Promise<PlanComparisonData> {
    const response = await ApiService.get<ApiResponse<PlanComparisonData>>(
      "/subscriptions/plans/compare"
    );
    return response.data.data;
  },

  /**
   * Create a checkout session for subscription
   */
  async createCheckoutSession(
    data: CheckoutSessionRequest
  ): Promise<CheckoutSessionResponse> {
    // Convert to a regular object to satisfy Record<string, unknown> requirement
    const requestData = {
      planId: data.planId,
      successUrl: data.successUrl,
      cancelUrl: data.cancelUrl,
    };

    const response = await ApiService.post<
      ApiResponse<CheckoutSessionResponse>
    >("/payment/stripe/create-checkout-session", requestData);
    return response.data.data;
  },

  /**
   * Cancel a subscription
   */
  async cancelSubscription(
    subscriptionId: string
  ): Promise<{ message: string }> {
    const response = await ApiService.post<ApiResponse<{ message: string }>>(
      "/payment/subscriptions/cancel",
      { subscriptionId }
    );
    return response.data.data;
  },

  /**
   * Format price for display
   */
  formatPrice(price: number, currency: string = "USD"): string {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency,
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(price);
  },

  /**
   * Get feature value display text
   */
  getFeatureDisplayValue(
    _featureKey: string,
    value: number | string | boolean
  ): string {
    if (typeof value === "boolean") {
      return value ? "Yes" : "No";
    }

    if (value === "unlimited" || value === "Unlimited") {
      return "Unlimited";
    }

    return String(value);
  },

  /**
   * Get color for a plan
   */
  getPlanColorClass(plan: SubscriptionPlan): string {
    switch (plan.colorScheme) {
      case "blue":
        return "bg-blue-100 border-blue-200 text-blue-800";
      case "purple":
        return "bg-purple-100 border-purple-200 text-purple-800";
      case "amber":
        return "bg-amber-100 border-amber-200 text-amber-800";
      case "gray":
      default:
        return "bg-gray-100 border-gray-200 text-gray-800";
    }
  },

  /**
   * Get badge color for a plan
   */
  getPlanBadgeClass(plan: SubscriptionPlan): string {
    switch (plan.colorScheme) {
      case "blue":
        return "bg-blue-500 text-white";
      case "purple":
        return "bg-purple-500 text-white";
      case "amber":
        return "bg-amber-500 text-white";
      case "green":
        return "bg-green-500 text-white";
      case "red":
        return "bg-red-500 text-white";
      default:
        return "bg-gray-500 text-white";
    }
  },

  /**
   * Upgrade a subscription to a higher tier plan
   * This applies immediately with proration
   */
  async upgradeSubscription(
    currentSubscriptionId: string,
    newPlanId: string
  ): Promise<UpgradeResponse> {
    const response = await ApiService.post<ApiResponse<UpgradeResponse>>(
      "/subscriptions/upgrade",
      { currentSubscriptionId, newPlanId }
    );
    return response.data.data;
  },

  /**
   * Downgrade a subscription to a lower tier plan
   * This schedules the downgrade to take effect at the end of the current billing period
   */
  async downgradeSubscription(
    currentSubscriptionId: string,
    newPlanId: string
  ): Promise<DowngradeResponse> {
    const response = await ApiService.post<ApiResponse<DowngradeResponse>>(
      "/subscriptions/downgrade",
      { currentSubscriptionId, newPlanId }
    );
    return response.data.data;
  },

  /**
   * Get user's scheduled plan changes
   */
  async getScheduledPlanChanges(): Promise<ScheduledPlanChange[]> {
    const response = await ApiService.get<ApiResponse<ScheduledPlanChange[]>>(
      "/subscriptions/scheduled-changes"
    );
    return response.data.data;
  },

  /**
   * Cancel a scheduled plan change
   */
  async cancelScheduledPlanChange(changeId: string): Promise<{ message: string }> {
    const response = await ApiService.post<ApiResponse<{ message: string }>>(
      "/subscriptions/cancel-scheduled-change",
      { changeId }
    );
    return response.data.data;
  },
};

export default SubscriptionService;
