import ApiService from './api.service';
import type { ApiResponse } from './api.service';

// Import or redefine RequestData type
type RequestData = Record<string, unknown>;

interface PaymentGatewayConfig extends RequestData {
  enabled: boolean;
  isLive: boolean;
}

interface StripeConfig extends PaymentGatewayConfig {
  apiKey: string;
  secretKey: string;
  webhookSecret: string;
  [key: string]: unknown;
}

interface PayPalConfig extends PaymentGatewayConfig {
  clientId: string;
  clientSecret: string;
  webhookId: string;
  [key: string]: unknown;
}

export interface PaymentConfig {
  stripe: StripeConfig;
  paypal: PayPalConfig;
}

const PaymentConfigService = {
  /**
   * Get the current payment configuration
   */
  async getConfig(): Promise<PaymentConfig> {
    try {
      const response = await ApiService.get<ApiResponse<PaymentConfig>>('/admin/payment-config');
      return response.data.data;
    } catch (error) {
      console.error('Error fetching payment configuration:', error);
      throw error;
    }
  },

  /**
   * Update Stripe configuration
   */
  async updateStripeConfig(config: StripeConfig): Promise<StripeConfig> {
    try {
      const response = await ApiService.put<ApiResponse<StripeConfig>>('/admin/payment-config/stripe', config);
      return response.data.data;
    } catch (error) {
      console.error('Error updating Stripe configuration:', error);
      throw error;
    }
  },

  /**
   * Update PayPal configuration
   */
  async updatePayPalConfig(config: PayPalConfig): Promise<PayPalConfig> {
    try {
      const response = await ApiService.put<ApiResponse<PayPalConfig>>('/admin/payment-config/paypal', config);
      return response.data.data;
    } catch (error) {
      console.error('Error updating PayPal configuration:', error);
      throw error;
    }
  },

  /**
   * Toggle payment method (enable/disable)
   */
  async togglePaymentMethod(method: 'stripe' | 'paypal', enabled: boolean): Promise<void> {
    try {
      await ApiService.patch(`/admin/payment-config/${method}/toggle`, { enabled });
    } catch (error) {
      console.error(`Error toggling ${method} payment method:`, error);
      throw error;
    }
  },

  /**
   * Test payment gateway connection
   */
  async testConnection(method: 'stripe' | 'paypal'): Promise<{ success: boolean; message: string }> {
    try {
      const response = await ApiService.post<ApiResponse<{ success: boolean; message: string }>>(`/admin/payment-config/${method}/test`);
      return response.data.data;
    } catch (error) {
      console.error(`Error testing ${method} connection:`, error);
      throw error;
    }
  },
};

export default PaymentConfigService;
