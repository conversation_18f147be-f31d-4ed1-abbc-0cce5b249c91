import ApiService from './api.service';
import type { ApiResponse } from './api.service';

export interface AnalyticsTimeRange {
  start: string;
  end: string;
}

export interface AnalyticsParams {
  timeRange?: 'daily' | 'weekly' | 'monthly' | 'yearly' | 'custom';
  startDate?: string; // ISO date string (YYYY-MM-DD)
  endDate?: string; // ISO date string (YYYY-MM-DD)
}

export interface OverviewStats {
  totalUsers: number;
  activeUsers: number;
  totalRevenue: number;
  mrr: number; // Monthly Recurring Revenue
  userGrowth: number; // Percentage
  revenueGrowth: number; // Percentage
}

export interface UsageStats {
  totalJobs: number;
  videoJobs: number;
  imageJobs: number;
  backgroundRemovalJobs: number;
  averageJobsPerUser: number;
  jobsGrowth: number; // Percentage
}

export interface RevenueStats {
  totalRevenue: number;
  subscriptionRevenue: number;
  creditPackRevenue: number;
  revenueByPlan: {
    name: string;
    revenue: number;
    percentage: number;
  }[];
  revenueGrowth: number; // Percentage
}

export interface UserStats {
  totalUsers: number;
  activeUsers: number;
  newUsers: number;
  usersByPlan: {
    name: string;
    count: number;
    percentage: number;
  }[];
  userGrowth: number; // Percentage
  retentionRate: number; // Percentage
  churnRate: number; // Percentage
}

export interface ChartDataPoint {
  date: string;
  amount: number;
}

export interface TimeSeriesData {
  labels: string[];
  datasets: {
    label: string;
    data: number[];
  }[];
  daily?: ChartDataPoint[];
  weekly?: ChartDataPoint[];
  monthly?: ChartDataPoint[];
  videoGeneration?: ChartDataPoint[];
  imageGeneration?: ChartDataPoint[];
  backgroundRemoval?: ChartDataPoint[];
}

const AnalyticsService = {
  /**
   * Get overview statistics
   */
  async getOverviewStats(params: AnalyticsParams = {}): Promise<OverviewStats> {
    const response = await ApiService.get<ApiResponse<OverviewStats>>('/admin/analytics/overview', { params });
    return response.data.data;
  },

  /**
   * Get usage statistics
   */
  async getUsageStats(params: AnalyticsParams = {}): Promise<UsageStats> {
    const response = await ApiService.get<ApiResponse<UsageStats>>('/admin/analytics/usage', { params });
    return response.data.data;
  },

  /**
   * Get revenue statistics
   */
  async getRevenueStats(params: AnalyticsParams = {}): Promise<RevenueStats> {
    const response = await ApiService.get<ApiResponse<RevenueStats>>('/admin/analytics/revenue', { params });
    return response.data.data;
  },

  /**
   * Get user statistics
   */
  async getUserStats(params: AnalyticsParams = {}): Promise<UserStats> {
    const response = await ApiService.get<ApiResponse<UserStats>>('/admin/analytics/users', { params });
    return response.data.data;
  },

  /**
   * Get revenue time series data
   */
  async getRevenueTimeSeries(params: AnalyticsParams = {}): Promise<TimeSeriesData> {
    const response = await ApiService.get<ApiResponse<TimeSeriesData>>('/admin/analytics/revenue/timeseries', { params });
    return response.data.data;
  },

  /**
   * Get user growth time series data
   */
  async getUserGrowthTimeSeries(params: AnalyticsParams = {}): Promise<TimeSeriesData> {
    const response = await ApiService.get<ApiResponse<TimeSeriesData>>('/admin/analytics/users/timeseries', { params });
    return response.data.data;
  },

  /**
   * Get usage time series data
   */
  async getUsageTimeSeries(params: AnalyticsParams = {}): Promise<TimeSeriesData> {
    const response = await ApiService.get<ApiResponse<TimeSeriesData>>('/admin/analytics/usage/timeseries', { params });
    return response.data.data;
  },

  /**
   * Get recent users
   */
  async getRecentUsers(limit: number = 5): Promise<{ id: string; name: string; email: string; role: string; createdAt: string }[]> {
    const response = await ApiService.get<ApiResponse<{ id: string; name: string; email: string; role: string; createdAt: string }[]>>('/admin/analytics/recent-users', { params: { limit } });
    return response.data.data;
  },

  /**
   * Get recent jobs
   */
  async getRecentJobs(limit: number = 5): Promise<{ id: string; userId: string; type: string; status: string; createdAt: string }[]> {
    const response = await ApiService.get<ApiResponse<{ id: string; userId: string; type: string; status: string; createdAt: string }[]>>('/admin/analytics/recent-jobs', { params: { limit } });
    return response.data.data;
  }
};

export default AnalyticsService;
