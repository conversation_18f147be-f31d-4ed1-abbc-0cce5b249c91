import ApiService from "./api.service";

export interface BlogCategory {
  id: string;
  name: string;
  slug: string;
  description?: string;
  createdAt?: string;
  updatedAt?: string;
  postCount?: number;
}

export interface PaginatedBlogCategories {
  data: BlogCategory[];
  pagination: {
    currentPage: number;
    totalPages: number;
    totalItems: number;
    limit: number;
  };
}

// Blog Tag Types
export interface BlogTag {
  id: string;
  name: string;
  slug: string;
  createdAt: string;
  updatedAt: string;
  postCount?: number;
}

export interface CreateBlogTagPayload {
  name: string;
}

// Blog Post Types
export interface BlogPost {
  id: string;
  title: string;
  slug: string;
  content: string;
  excerpt?: string;
  status: "DRAFT" | "PUBLISHED" | "ARCHIVED" | "SCHEDULED";
  authorId: string;
  categoryIds?: string[];
  tagIds?: string[];
  publishedAt?: string;
  createdAt: string;
  updatedAt: string;
  metaTitle?: string;
  metaDescription?: string;
  featuredImage?: string;
}

export interface PaginatedBlogPosts {
  data: BlogPost[];
  pagination: {
    currentPage: number;
    totalPages: number;
    totalItems: number;
    limit: number;
  };
}

export interface CreateBlogPostData {
  title: string;
  content: string;
  excerpt: string;
  imageUrl: string;
  category: string; // Category ID
  tags: string[]; // Array of tag IDs
  status?: "DRAFT" | "PUBLISHED";
}

export interface CreateBlogPostPayload {
  title: string;
  slug: string;
  content: string;
  status: "DRAFT" | "PUBLISHED" | "ARCHIVED" | "SCHEDULED";
  authorId?: string;
  excerpt?: string;
  categoryIds?: string[];
  tagIds?: string[];
  publishedAt?: string;
  metaTitle?: string;
  metaDescription?: string;
  featuredImage?: string;
}

export interface UpdateBlogPostPayload {
  title?: string;
  slug?: string;
  content?: string;
  status?: "DRAFT" | "PUBLISHED" | "ARCHIVED" | "SCHEDULED";
  excerpt?: string;
  categoryIds?: string[];
  tagIds?: string[];
  publishedAt?: string;
  metaTitle?: string;
  metaDescription?: string;
  featuredImage?: string;
}

const ADMIN_BLOG_API_BASE_URL = "/admin/blog";

// Blog Category Service Functions
export const getAllBlogCategoriesAdmin = async (
  page = 1,
  limit = 10,
  searchTerm = ""
): Promise<PaginatedBlogCategories> => {
  try {
    const response = await ApiService.get<PaginatedBlogCategories>(
      `${ADMIN_BLOG_API_BASE_URL}/categories/all`,
      {
        params: { page, limit, searchTerm },
      }
    );
    return response.data;
  } catch (error) {
    console.error("Error fetching blog categories:", error);
    throw error;
  }
};

export interface CreateBlogCategoryPayload {
  name: string;
  description?: string;
}

export const createBlogCategoryAdmin = async (
  payload: CreateBlogCategoryPayload
): Promise<BlogCategory> => {
  try {
    const response = await ApiService.post<{ data: BlogCategory }>(
      `${ADMIN_BLOG_API_BASE_URL}/categories`,
      payload as unknown as Record<string, unknown>
    );
    return response.data.data;
  } catch (error) {
    console.error("Error creating blog category:", error);
    throw error;
  }
};

export const updateBlogCategoryAdmin = async (
  categoryId: string,
  payload: CreateBlogCategoryPayload
): Promise<BlogCategory> => {
  try {
    const response = await ApiService.put<{ data: BlogCategory }>(
      `${ADMIN_BLOG_API_BASE_URL}/categories/${categoryId}`,
      payload as unknown as Record<string, unknown>
    );
    return response.data.data;
  } catch (error) {
    console.error("Error updating blog category:", error);
    throw error;
  }
};

export const deleteBlogCategoryAdmin = async (
  categoryId: string
): Promise<BlogCategory> => {
  try {
    const response = await ApiService.delete<{ data: BlogCategory }>(
      `${ADMIN_BLOG_API_BASE_URL}/categories/${categoryId}`
    );
    return response.data.data;
  } catch (error) {
    console.error("Error deleting blog category:", error);
    throw error;
  }
};

// Blog Post Service Functions
export const getAllBlogPostsAdmin = async (
  page = 1,
  limit = 10,
  searchTerm = ""
): Promise<PaginatedBlogPosts> => {
  try {
    const response = await ApiService.get<PaginatedBlogPosts>(
      `${ADMIN_BLOG_API_BASE_URL}/posts/all`,
      {
        params: { page, limit, searchTerm },
      }
    );

    console.log("Fetched blog posts:", response.data);

    // Type assertion to ensure response.data is PaginatedBlogPosts
    const data = response.data as PaginatedBlogPosts;

    return data;
  } catch (error) {
    console.error("Error fetching blog posts:", error);
    throw error;
  }
};

export const getBlogPostByIdAdmin = async (
  postId: string
): Promise<BlogPost> => {
  try {
    const response = await ApiService.get<{ data: BlogPost }>(
      `${ADMIN_BLOG_API_BASE_URL}/posts/${postId}`
    );
    return response.data.data;
  } catch (error) {
    console.error(`Error fetching blog post with ID ${postId}:`, error);
    throw error;
  }
};

export const createBlogPostAdmin = async (
  payload: CreateBlogPostPayload
): Promise<BlogPost> => {
  try {
    const response = await ApiService.post<{ data: BlogPost }>(
      `${ADMIN_BLOG_API_BASE_URL}/posts`,
      payload as unknown as Record<string, unknown>
    );
    return response.data.data;
  } catch (error) {
    console.error("Error creating blog post:", error);
    throw error;
  }
};

export const updateBlogPostAdmin = async (
  postId: string,
  payload: UpdateBlogPostPayload
): Promise<BlogPost> => {
  try {
    const response = await ApiService.put<{ data: BlogPost }>(
      `${ADMIN_BLOG_API_BASE_URL}/posts/${postId}`,
      payload as unknown as Record<string, unknown>
    );
    return response.data.data;
  } catch (error) {
    console.error(`Error updating blog post with ID ${postId}:`, error);
    throw error;
  }
};

export const createPost = async (payload: FormData): Promise<BlogPost> => {
  try {
    const response = await ApiService.post<{ data: BlogPost }>(
      `${ADMIN_BLOG_API_BASE_URL}/posts`,
      payload,
      {
        headers: { "Content-Type": "multipart/form-data" },
      }
    );
    return response.data.data;
  } catch (error) {
    console.error("Error creating blog post:", error);
    throw error;
  }
};

export const updatePost = async (
  postId: string,
  payload: FormData
): Promise<BlogPost> => {
  try {
    const response = await ApiService.put<{ data: BlogPost }>(
      `${ADMIN_BLOG_API_BASE_URL}/posts/${postId}`,
      payload,
      {
        headers: { "Content-Type": "multipart/form-data" },
      }
    );
    return response.data.data;
  } catch (error) {
    console.error("Error updating blog post:", error);
    throw error;
  }
};

export const deleteBlogPostAdmin = async (
  postId: string
): Promise<BlogPost> => {
  try {
    const response = await ApiService.delete<{ data: BlogPost }>(
      `${ADMIN_BLOG_API_BASE_URL}/posts/${postId}`
    );
    return response.data.data;
  } catch (error) {
    console.error(`Error deleting blog post with ID ${postId}:`, error);
    throw error;
  }
};

// Blog Tag Service Functions (these are from regular blog service, not admin)
export const createBlogTag = async (
  payload: CreateBlogTagPayload
): Promise<BlogTag> => {
  try {
    const response = await ApiService.post<{ data: BlogTag }>(
      `${ADMIN_BLOG_API_BASE_URL}/tags`,
      payload as unknown as Record<string, unknown>
    );
    return response.data.data;
  } catch (error) {
    console.error("Error creating blog tag:", error);
    throw error;
  }
};

export const getAllBlogTags = async (): Promise<BlogTag[]> => {
  try {
    const response = await ApiService.get<{ data: BlogTag[] }>(
      `${ADMIN_BLOG_API_BASE_URL}/tags`
    );
    return response.data.data;
  } catch (error) {
    console.error("Error fetching blog tags:", error);
    throw error;
  }
};

// Admin Blog Service Object
const AdminBlogService = {
  // Categories
  getCategories: getAllBlogCategoriesAdmin,
  createCategory: createBlogCategoryAdmin,
  updateCategory: updateBlogCategoryAdmin,
  deleteCategory: deleteBlogCategoryAdmin,

  // Tags (from regular blog service)
  createTag: createBlogTag,
  getTags: getAllBlogTags,

  // Posts
  getPosts: getAllBlogPostsAdmin,
  getPost: getBlogPostByIdAdmin,
  createPost, // : createBlogPostAdmin,
  updatePost, // : updateBlogPostAdmin,
  deletePost: deleteBlogPostAdmin,
};

export default AdminBlogService;

// import ApiService from "./api.service";

// export interface BlogCategory {
//   id: string;
//   name: string;
//   slug: string;
//   description?: string;
//   createdAt?: string;
//   updatedAt?: string;
//   postCount?: number;
// }

// export interface PaginatedBlogCategories {
//   data: BlogCategory[];
//   pagination: {
//     currentPage: number;
//     totalPages: number;
//     totalItems: number;
//     limit: number;
//   };
// }

// // Blog Tag Types
// export interface BlogTag {
//   id: string;
//   name: string;
//   slug: string;
//   createdAt: string;
//   updatedAt: string;
//   postCount?: number;
// }

// export interface CreateBlogTagPayload {
//   name: string;
// }

// // Blog Post Types
// export interface BlogPost {
//   id: string;
//   title: string;
//   slug: string;
//   content: string;
//   excerpt?: string;
//   status: "DRAFT" | "PUBLISHED" | "ARCHIVED" | "SCHEDULED";
//   authorId: string;
//   categoryIds?: string[];
//   tagIds?: string[];
//   publishedAt?: string;
//   createdAt: string;
//   updatedAt: string;
//   metaTitle?: string;
//   metaDescription?: string;
//   featuredImage?: string;
// }

// export interface PaginatedBlogPosts {
//   data: BlogPost[];
//   pagination: {
//     currentPage: number;
//     totalPages: number;
//     totalItems: number;
//     limit: number;
//   };
// }

// export interface CreateBlogPostData {
//   title: string;
//   content: string;
//   excerpt: string;
//   imageUrl: string;
//   category: string; // Category ID
//   tags: string[]; // Array of tag IDs
//   status?: "DRAFT" | "PUBLISHED";
// }

// export interface CreateBlogPostPayload {
//   title: string;
//   slug: string;
//   content: string;
//   status: "DRAFT" | "PUBLISHED" | "ARCHIVED" | "SCHEDULED";
//   authorId?: string;
//   excerpt?: string;
//   categoryIds?: string[];
//   tagIds?: string[];
//   publishedAt?: string;
//   metaTitle?: string;
//   metaDescription?: string;
//   featuredImage?: string;
// }

// export interface UpdateBlogPostPayload {
//   title?: string;
//   slug?: string;
//   content?: string;
//   status?: "DRAFT" | "PUBLISHED" | "ARCHIVED" | "SCHEDULED";
//   excerpt?: string;
//   categoryIds?: string[];
//   tagIds?: string[];
//   publishedAt?: string;
//   metaTitle?: string;
//   metaDescription?: string;
//   featuredImage?: string;
// }

// const ADMIN_BLOG_API_BASE_URL = "/admin/blog";

// // Blog Category Service Functions
// export const getAllBlogCategoriesAdmin = async (
//   page = 1,
//   limit = 10,
//   searchTerm = ""
// ): Promise<PaginatedBlogCategories> => {
//   try {
//     const response = await ApiService.get<PaginatedBlogCategories>(
//       `${ADMIN_BLOG_API_BASE_URL}/categories/all`,
//       {
//         params: { page, limit, searchTerm },
//       }
//     );
//     return response.data;
//   } catch (error) {
//     console.error("Error fetching blog categories:", error);
//     throw error;
//   }
// };

// export interface CreateBlogCategoryPayload {
//   name: string;
//   description?: string;
//   slug?: string;
// }

// export const createBlogCategoryAdmin = async (
//   payload: CreateBlogCategoryPayload
// ): Promise<BlogCategory> => {
//   try {
//     const response = await ApiService.post<{ data: BlogCategory }>(
//       `${ADMIN_BLOG_API_BASE_URL}/categories`,
//       payload as unknown as Record<string, unknown>
//     );
//     return response.data.data;
//   } catch (error) {
//     console.error("Error creating blog category:", error);
//     throw error;
//   }
// };

// export const updateBlogCategoryAdmin = async (
//   categoryId: string,
//   payload: CreateBlogCategoryPayload
// ): Promise<BlogCategory> => {
//   try {
//     const response = await ApiService.put<{ data: BlogCategory }>(
//       `${ADMIN_BLOG_API_BASE_URL}/categories/${categoryId}`,
//       payload as unknown as Record<string, unknown>
//     );
//     return response.data.data;
//   } catch (error) {
//     console.error("Error updating blog category:", error);
//     throw error;
//   }
// };

// export const deleteBlogCategoryAdmin = async (
//   categoryId: string
// ): Promise<BlogCategory> => {
//   try {
//     const response = await ApiService.delete<{ data: BlogCategory }>(
//       `${ADMIN_BLOG_API_BASE_URL}/categories/${categoryId}`
//     );
//     return response.data.data;
//   } catch (error) {
//     console.error("Error deleting blog category:", error);
//     throw error;
//   }
// };

// // Blog Post Service Functions
// export const getAllBlogPostsAdmin = async (
//   page = 1,
//   limit = 10,
//   searchTerm = ""
// ): Promise<PaginatedBlogPosts> => {
//   try {
//     const response = await ApiService.get<PaginatedBlogPosts>(
//       `${ADMIN_BLOG_API_BASE_URL}/posts/all`,
//       {
//         params: { page, limit, searchTerm },
//       }
//     );

//     console.log("Fetched blog posts:", response.data);

//     // Type assertion to ensure response.data is PaginatedBlogPosts
//     const data = response.data as PaginatedBlogPosts;

//     return data;
//   } catch (error) {
//     console.error("Error fetching blog posts:", error);
//     throw error;
//   }
// };

// export const getBlogPostByIdAdmin = async (
//   postId: string
// ): Promise<BlogPost> => {
//   try {
//     const response = await ApiService.get<{ data: BlogPost }>(
//       `${ADMIN_BLOG_API_BASE_URL}/posts/${postId}`
//     );
//     return response.data.data;
//   } catch (error) {
//     console.error(`Error fetching blog post with ID ${postId}:`, error);
//     throw error;
//   }
// };

// export const createBlogPostAdmin = async (
//   payload: CreateBlogPostPayload
// ): Promise<BlogPost> => {
//   try {
//     const response = await ApiService.post<{ data: BlogPost }>(
//       `${ADMIN_BLOG_API_BASE_URL}/posts`,
//       payload as unknown as Record<string, unknown>
//     );
//     return response.data.data;
//   } catch (error) {
//     console.error("Error creating blog post:", error);
//     throw error;
//   }
// };

// export const updateBlogPostAdmin = async (
//   postId: string,
//   payload: UpdateBlogPostPayload
// ): Promise<BlogPost> => {
//   try {
//     const response = await ApiService.put<{ data: BlogPost }>(
//       `${ADMIN_BLOG_API_BASE_URL}/posts/${postId}`,
//       payload as unknown as Record<string, unknown>
//     );
//     return response.data.data;
//   } catch (error) {
//     console.error(`Error updating blog post with ID ${postId}:`, error);
//     throw error;
//   }
// };

// export const createPost = async (payload: FormData): Promise<BlogPost> => {
//   try {
//     const response = await ApiService.post<{ data: BlogPost }>(
//       `${ADMIN_BLOG_API_BASE_URL}/posts`,
//       payload,
//       {
//         headers: { "Content-Type": "multipart/form-data" },
//       }
//     );
//     return response.data.data;
//   } catch (error) {
//     console.error("Error creating blog post:", error);
//     throw error;
//   }
// };

// export const updatePost = async (postId: string, payload: FormData): Promise<BlogPost> => {
//   try {
//     const response = await ApiService.put<{ data: BlogPost }>(
//       `${ADMIN_BLOG_API_BASE_URL}/posts/${postId}`,
//       payload,
//       {
//         headers: { "Content-Type": "multipart/form-data" },
//       }
//     );
//     return response.data.data;
//   } catch (error) {
//     console.error("Error updating blog post:", error);
//     throw error;
//   }
// };

// export const deleteBlogPostAdmin = async (
//   postId: string
// ): Promise<BlogPost> => {
//   try {
//     const response = await ApiService.delete<{ data: BlogPost }>(
//       `${ADMIN_BLOG_API_BASE_URL}/posts/${postId}`
//     );
//     return response.data.data;
//   } catch (error) {
//     console.error(`Error deleting blog post with ID ${postId}:`, error);
//     throw error;
//   }
// };

// // Blog Tag Service Functions (these are from regular blog service, not admin)
// export const createBlogTag = async (
//   payload: CreateBlogTagPayload
// ): Promise<BlogTag> => {
//   try {
//     const response = await ApiService.post<{ data: BlogTag }>(
//       `${ADMIN_BLOG_API_BASE_URL}/tags`,
//       payload as unknown as Record<string, unknown>
//     );
//     return response.data.data;
//   } catch (error) {
//     console.error("Error creating blog tag:", error);
//     throw error;
//   }
// };

// export const getAllBlogTags = async (): Promise<BlogTag[]> => {
//   try {
//     const response = await ApiService.get<{ data: BlogTag[] }>(
//       `${ADMIN_BLOG_API_BASE_URL}/tags`
//     );
//     return response.data.data;
//   } catch (error) {
//     console.error("Error fetching blog tags:", error);
//     throw error;
//   }
// };

// // Admin Blog Service Object
// const AdminBlogService = {
//   // Categories
//   getCategories: getAllBlogCategoriesAdmin,
//   createCategory: createBlogCategoryAdmin,
//   updateCategory: updateBlogCategoryAdmin,
//   deleteCategory: deleteBlogCategoryAdmin,

//   // Tags (from regular blog service)
//   createTag: createBlogTag,
//   getTags: getAllBlogTags,

//   // Posts
//   getPosts: getAllBlogPostsAdmin,
//   getPost: getBlogPostByIdAdmin,
//   createPost: createBlogPostAdmin,
//   updatePost: updateBlogPostAdmin,
//   deletePost: deleteBlogPostAdmin,
// };

// export default AdminBlogService;
