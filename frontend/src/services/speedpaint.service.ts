import ApiService from "./api.service";
import type { ApiResponse } from "./api.service";

export interface SpeedpaintJob {
  id: string;
  userId: string;
  originalImageUrl: string;
  processedVideoUrl: string | null;
  status: "PENDING" | "PROCESSING" | "COMPLETED" | "FAILED";
  createdAt: string;
  updatedAt: string;
  error?: string;
  metadata?: {
    processingStartTime?: string;
    processingEndTime?: string;
    processingDurationMs?: number;
    externalVideoUrl?: string;
    queuedAt?: string;
    [key: string]: unknown;
  };
}

export interface SpeedpaintJobResponse {
  jobId: string;
  status: string;
}

export interface SpeedpaintJobsResponse {
  jobs: SpeedpaintJob[];
}

/**
 * Service for handling speedpaint generation API calls
 */
const SpeedpaintService = {
  /**
   * Create a speedpaint generation job by uploading an image
   * @param imageFile Image file to upload
   * @returns Speedpaint job response
   */
  createJob(imageFile: File): Promise<ApiResponse<SpeedpaintJobResponse>> {
    const formData = new FormData();
    formData.append("image", imageFile);

    return ApiService.post<ApiResponse<SpeedpaintJobResponse>>(
      "/speed-painting/create",
      formData,
      {
        headers: {
          "Content-Type": "multipart/form-data",
        },
        timeout: 60000, // 60 second timeout for larger images
      }
    ).then((response) => response.data);
  },

  /**
   * Get the queue status for speed painting jobs
   * @returns Queue status information
   */
  getQueueStatus(): Promise<
    ApiResponse<{
      queueLength: number;
      pendingJobs: number;
      processingJobs: number;
      estimatedWaitTimeMinutes: number;
    }>
  > {
    return ApiService.get<
      ApiResponse<{
        queueLength: number;
        pendingJobs: number;
        processingJobs: number;
        estimatedWaitTimeMinutes: number;
      }>
    >("/speed-painting/queue-status").then((response) => response.data);
  },

  /**
   * Get a speedpaint job by ID
   * @param jobId Job ID
   * @returns Speedpaint job
   */
  getJob(jobId: string): Promise<ApiResponse<SpeedpaintJob>> {
    return ApiService.get<ApiResponse<SpeedpaintJob>>(
      `/speed-painting/jobs/${jobId}`
    ).then((response) => response.data);
  },

  /**
   * Get all speedpaint jobs for the current user
   * @returns List of speedpaint jobs
   */
  getUserJobs(): Promise<ApiResponse<SpeedpaintJobsResponse>> {
    return ApiService.get<ApiResponse<SpeedpaintJobsResponse>>(
      "/speed-painting/jobs"
    ).then((response) => response.data);
  },

  /**
   * Download a processed video
   * @param videoUrl URL of the processed video
   * @param filename Filename to save the video as
   */

  handleDownload(videoUrl: string, filename: string): void {
    if (videoUrl) {
      fetch(videoUrl)
        .then((response) => response.blob())
        .then((blob) => {
          const link = document.createElement("a");
          const url = URL.createObjectURL(blob);
          link.href = url;
          link.download = filename;
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
          URL.revokeObjectURL(url);
        })
        .catch((error) => {
          console.error("Error fetching video:", error);
          alert("Failed to download the video.");
        });
    } else {
      console.log("No generated video to download");
      alert("No generated video available to download.");
    }
  },
};

export default SpeedpaintService;
