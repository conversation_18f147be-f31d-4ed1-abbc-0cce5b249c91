import ApiService from "./api.service";
import type { ApiResponse } from "./api.service";

export interface Plan {
  id: string;
  name: string;
  description: string;
  price: number;
  interval: "month" | "year";
  features: string[];
  credits: {
    video: number;
    image: number;
    backgroundRemoval: number;
  };
  popular?: boolean;
  legacy?: boolean;
}

export interface CreditPack {
  id: string;
  name: string;
  description: string;
  price: number;
  credits: number;
  type: "video" | "image" | "background-removal";
  popular?: boolean;
}

export interface Subscription {
  id: string;
  userId: string;
  planId: string;
  plan: Plan;
  status: "active" | "canceled" | "past_due" | "trialing" | "incomplete";
  currentPeriodStart: string;
  currentPeriodEnd: string;
  cancelAtPeriodEnd: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface PaymentMethod {
  id: string;
  type: "card" | "paypal";
  last4?: string;
  expMonth?: number;
  expYear?: number;
  brand?: string;
  isDefault: boolean;
}

export interface Invoice {
  id: string;
  userId: string;
  amount: number;
  status: "paid" | "open" | "void" | "uncollectible";
  description: string;
  currency: string;
  paidAt?: string;
  createdAt: string;
}

export interface InvoiceListParams {
  page?: number;
  limit?: number;
  status?: "paid" | "open" | "void" | "uncollectible" | "all";
}

export interface InvoiceListResponse {
  invoices: Invoice[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export interface Transaction {
  id: string;
  amount: number;
  currency: string;
  status: string;
  paymentType: string;
  transactionId: string;
  hostedInvoiceUrl?: string;
  invoicePdf?: string;
  createdAt: string;
  updatedAt: string;
  metadata?: {
    planName?: string;
    planInterval?: string;
    creditAmount?: number;
    packageName?: string;
    refundReason?: string;
  };
  user?: {
    name: string;
    email: string;
  };
}

export interface CheckoutSession {
  id: string;
  url: string;
}

const PaymentService = {
  // Plans
  async getPlans(): Promise<Plan[]> {
    const response = await ApiService.get<ApiResponse<Plan[]>>(
      "/payment/plans"
    );
    return response.data.data;
  },

  async getPlanById(planId: string): Promise<Plan> {
    const response = await ApiService.get<ApiResponse<Plan>>(
      `/payment/plans/${planId}`
    );
    return response.data.data;
  },

  // Credit packs
  async getCreditPacks(): Promise<CreditPack[]> {
    const response = await ApiService.get<ApiResponse<CreditPack[]>>(
      "/payment/credit-packs"
    );
    return response.data.data;
  },

  async getCreditPackById(packId: string): Promise<CreditPack> {
    const response = await ApiService.get<ApiResponse<CreditPack>>(
      `/payment/credit-packs/${packId}`
    );
    return response.data.data;
  },

  // Subscription
  async getSubscription(): Promise<Subscription> {
    const response = await ApiService.get<ApiResponse<Subscription>>(
      "/payment/subscriptions/me"
    );
    return response.data.data;
  },

  async cancelSubscription(
    cancelImmediately: boolean = false
  ): Promise<Subscription> {
    const response = await ApiService.post<ApiResponse<Subscription>>(
      "/payment/subscriptions/cancel",
      {
        cancelImmediately,
      }
    );
    return response.data.data;
  },

  async reactivateSubscription(): Promise<Subscription> {
    const response = await ApiService.post<ApiResponse<Subscription>>(
      "/payment/subscription/reactivate"
    );
    return response.data.data;
  },

  // Payment methods
  async getPaymentMethods(): Promise<PaymentMethod[]> {
    const response = await ApiService.get<ApiResponse<PaymentMethod[]>>(
      "/payment/methods"
    );
    return response.data.data;
  },

  async setDefaultPaymentMethod(
    paymentMethodId: string
  ): Promise<PaymentMethod> {
    const response = await ApiService.post<ApiResponse<PaymentMethod>>(
      `/payment/methods/${paymentMethodId}/default`
    );
    return response.data.data;
  },

  async deletePaymentMethod(paymentMethodId: string): Promise<void> {
    await ApiService.delete(`/payment/methods/${paymentMethodId}`);
  },

  // Invoices
  async getInvoices(
    params: InvoiceListParams = {}
  ): Promise<InvoiceListResponse> {
    const queryParams = new URLSearchParams();
    if (params.page) queryParams.append("page", params.page.toString());
    if (params.limit) queryParams.append("limit", params.limit.toString());
    if (params.status && params.status !== "all")
      queryParams.append("status", params.status);

    const response = await ApiService.get<ApiResponse<InvoiceListResponse>>(
      `/payment/invoices?${queryParams.toString()}`
    );
    return response.data.data;
  },

  async getInvoiceById(invoiceId: string): Promise<Invoice> {
    const response = await ApiService.get<ApiResponse<Invoice>>(
      `/payment/invoices/${invoiceId}`
    );
    return response.data.data;
  },

  async downloadInvoice(invoiceId: string): Promise<string> {
    const response = await ApiService.get<ApiResponse<{ url: string }>>(
      `/payment/invoices/${invoiceId}/download`
    );
    return response.data.data.url;
  },

  // Transaction History
  async getUserTransactions(): Promise<Transaction[]> {
    const response = await ApiService.get<ApiResponse<Transaction[]>>(
      "/payment/invoices"
    );
    return response.data.data;
  },

  // Checkout
  async createSubscriptionCheckout(planId: string): Promise<CheckoutSession> {
    const response = await ApiService.post<ApiResponse<CheckoutSession>>(
      "/payment/checkout/subscription",
      {
        planId,
      }
    );
    return response.data.data;
  },

  async createCreditPackCheckout(
    packId: string,
    quantity: number = 1
  ): Promise<CheckoutSession> {
    const response = await ApiService.post<ApiResponse<CheckoutSession>>(
      "/payment/checkout/credit-pack",
      {
        packId,
        quantity,
      }
    );
    return response.data.data;
  },

  // Usage
  async getUsageStats(): Promise<{
    credits: {
      total: number;
      used: number;
      remaining: number;
    };
    usage: {
      video: number;
      image: number;
      backgroundRemoval: number;
    };
    history: Array<{
      date: string;
      credits: number;
      type: string;
    }>;
  }> {
    const response = await ApiService.get<
      ApiResponse<{
        credits: {
          total: number;
          used: number;
          remaining: number;
        };
        usage: {
          video: number;
          image: number;
          backgroundRemoval: number;
        };
        history: Array<{
          date: string;
          credits: number;
          type: string;
        }>;
      }>
    >("/payment/usage");
    return response.data.data;
  },
};

export default PaymentService;
