import ApiService from "./api.service";
import type { ApiResponse } from "./api.service";
import type { User } from "./auth.service";

// Credit-related interfaces
export interface CreditBalance {
  id: string;
  userId: string;
  balance: number;
  spent: number;
  lastUpdatedAt: string;
}

export interface CreditTransaction {
  id: string;
  userId: string;
  amount: number;
  type: string;
  description: string;
  createdAt: string;
  jobId?: string;
}

export interface CreditPackage {
  id: string;
  name: string;
  description: string | null;
  creditsAmount: number;
  price: number;
  currency: string;
  isActive: boolean;
}

export interface CreditPurchaseOrder {
  id: string;
  userId: string;
  user: User; // Assuming you have a User type defined elsewhere
  creditPackageId: string;
  creditPackage: CreditPackage; // Assuming you have a CreditPackage type defined
  creditsToGrant: number;
  amountPaid: number | null;
  currency: string | null;
  paymentProvider: "STRIPE" | "PAYPAL" | string; // Union with string for other possible providers
  paymentIntentId: string | null;
  status:
    | "PENDING"
    | "PROCESSING"
    | "COMPLETED"
    | "FAILED"
    | "REFUNDED"
    | string;
  providerMetadata: Record<string, unknown> | null; // Use a generic object type for unknown structures
  createdAt: Date;
  updatedAt: Date;
  creditTransaction: CreditTransaction | null; // Assuming you have a CreditTransaction type
  creditTransactionId: string | null;
}

// You might also want to create enums for the string literals:
// enum PaymentProvider {
//   STRIPE = 'STRIPE',
//   PAYPAL = 'PAYPAL'
// }

// enum OrderStatus {
//   PENDING = 'PENDING',
//   PROCESSING = 'PROCESSING',
//   COMPLETED = 'COMPLETED',
//   FAILED = 'FAILED',
//   REFUNDED = 'REFUNDED'
// }

export interface ServiceCost {
  imageGeneration: number;
  videoGeneration: number;
  backgroundRemoval: number;
  virtualTryOn: number;
  speedpaint: number;
}

export interface TransactionHistoryResponse {
  transactions: CreditTransaction[];
}

export interface StripeCheckoutResponse {
  url: string;
}

export interface StripePaymentIntentResponse {
  clientSecret: string;
  paymentIntentId: string;
  amount: number;
  currency: string;
  orderId: string;
}

export interface PayPalOrderResponse {
  id: string;
  status: string;
}

/**
 * Service for handling credit-related API calls
 */
const CreditService = {
  /**
   * Get the current user's credit balance
   */
  getUserCreditBalance(): Promise<ApiResponse<CreditBalance>> {
    return ApiService.get<ApiResponse<CreditBalance>>("/credits/balance").then(
      (response) => response.data
    );
  },

  /**
   * Get the user's transaction history
   */
  getTransactionHistory(): Promise<ApiResponse<TransactionHistoryResponse>> {
    return ApiService.get<ApiResponse<TransactionHistoryResponse>>(
      "/credits/history"
    ).then((response) => response.data);
  },

  /**
   * Get available credit packages
   */
  getCreditPackages(): Promise<ApiResponse<CreditPackage[]>> {
    return ApiService.get<ApiResponse<CreditPackage[]>>(
      "/credits/packages"
    ).then((response) => response.data);
  },

  /**
   * Get service costs in credits
   */
  getServiceCosts(): Promise<ApiResponse<ServiceCost>> {
    return ApiService.get<ApiResponse<ServiceCost>>("/credits/costs").then(
      (response) => response.data
    );
  },

  /**
   * Create a Stripe checkout session for purchasing credits
   */
  createStripeCheckoutSession(
    packageId: string,
    successUrl: string,
    cancelUrl: string
  ): Promise<ApiResponse<StripeCheckoutResponse>> {
    return ApiService.post<ApiResponse<StripeCheckoutResponse>>(
      "/credits/purchase/stripe/create-checkout-session",
      { packageId, successUrl, cancelUrl }
    ).then((response) => response.data);
  },

  /**
   * Create a PayPal order for purchasing credits
   */
  createPayPalOrder(
    packageId: string,
    successUrl: string,
    cancelUrl: string
  ): Promise<ApiResponse<PayPalOrderResponse>> {
    return ApiService.post<ApiResponse<PayPalOrderResponse>>(
      "/credits/purchase/paypal/create-order",
      { packageId, successUrl, cancelUrl }
    ).then((response) => response.data);
  },

  /**
   * Capture a PayPal payment
   */
  capturePayPalOrder(
    orderId: string
  ): Promise<ApiResponse<{ success: boolean }>> {
    return ApiService.post<ApiResponse<{ success: boolean }>>(
      `/credits/purchase/paypal/capture-order`,
      { orderId }
    ).then((response) => response.data);
  },

  /**
   * Create a Stripe Payment Intent for purchasing credits
   * This allows using Stripe Elements directly on the site instead of redirecting
   */
  createStripePaymentIntent(
    packageId: string
  ): Promise<ApiResponse<StripePaymentIntentResponse>> {
    return ApiService.post<ApiResponse<StripePaymentIntentResponse>>(
      `/credits/purchase/stripe/create-payment-intent`,
      { packageId }
    ).then((response) => response.data);
  },
};

export default CreditService;
