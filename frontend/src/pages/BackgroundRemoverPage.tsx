import {
  useState,
  useEffect,
  useContext,
  type ChangeEvent,
  type DragEvent,
} from "react";
import {
  Upload,
  ChevronDown,
  Download,
  RefreshCw,
  AlertCircle,
  Loader2,
} from "lucide-react";
import ReactBeforeSliderComponent from "react-before-after-slider-component";
import "react-before-after-slider-component/dist/build.css";
import AiCustomBgSelectionModal from "@/components/aiTools/AiCustomBgSelectionModal";
import { AppContext } from "@/contexts/AppContext";
import BgRemovalService from "@/services/bgRemoval.service";
import type { BgRemovalJob } from "@/services/bgRemoval.service";
import CreditService from "@/services/credit.service";
import type { ServiceCost } from "@/services/credit.service";

const BackgroundRemoverPage = () => {
  const { isAuthenticated, setUserCredits, userCredits } =
    useContext(AppContext);
  const [isDragOver, setIsDragOver] = useState<boolean>(false);
  const [uploadedImage, setUploadedImage] = useState<string | null>(null);
  const [uploadedFile, setUploadedFile] = useState<File | null>(null);
  const [isOpenBgSelectModal, setIsOpenBgSelectModal] =
    useState<boolean>(false);
  const [isProcessing, setIsProcessing] = useState<boolean>(false);
  const [processedImage, setProcessedImage] = useState<string | null>(null);
  const [, setCurrentJob] = useState<BgRemovalJob | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [serviceCost, setServiceCost] = useState<number | null>(null);
  const [insufficientCredits, setInsufficientCredits] =
    useState<boolean>(false);
  // const [isHovering, setIsHovering] = useState<boolean>(false);

  const handleDragOver = (e: DragEvent<HTMLDivElement>): void => {
    e.preventDefault();
    setIsDragOver(true);
  };

  const handleDragLeave = (e: DragEvent<HTMLDivElement>): void => {
    e.preventDefault();
    setIsDragOver(false);
  };

  const handleDrop = (e: DragEvent<HTMLDivElement>): void => {
    e.preventDefault();
    setIsDragOver(false);
    const files: FileList = e.dataTransfer.files;
    if (files.length > 0) {
      const file: File = files[0];
      setUploadedFile(file);
      const reader: FileReader = new FileReader();
      reader.onload = (e: ProgressEvent<FileReader>): void => {
        if (e.target?.result) {
          setUploadedImage(e.target.result as string);
          setProcessedImage(null);
          setCurrentJob(null);
          setError(null);
        }
      };
      reader.readAsDataURL(file);
    }
  };

  const handleFileSelect = (e: ChangeEvent<HTMLInputElement>): void => {
    const files: FileList | null = e.target.files;
    if (files && files.length > 0) {
      const file: File = files[0];
      setUploadedFile(file);
      const reader: FileReader = new FileReader();
      reader.onload = (e: ProgressEvent<FileReader>): void => {
        if (e.target?.result) {
          setUploadedImage(e.target.result as string);
          setProcessedImage(null);
          setCurrentJob(null);
          setError(null);
        }
      };
      reader.readAsDataURL(file);
    }
  };

  const handleReset = (): void => {
    setUploadedImage(null);
    setUploadedFile(null);
    setProcessedImage(null);
    setCurrentJob(null);
    setError(null);
    setIsProcessing(false);
  };

  const handleDownload = (): void => {
    if (processedImage) {
      fetch(processedImage)
        .then((response) => response.blob())
        .then((blob) => {
          const link = document.createElement("a");
          const url = URL.createObjectURL(blob);
          link.href = url;
          link.download = `bg-removed-${Date.now()}.png`;
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
          URL.revokeObjectURL(url);
        })
        .catch((error) => {
          console.error("Error fetching image:", error);
          alert("Failed to download the image.");
        });
    } else {
      console.log("No generated image to download");
      alert("No generated image available to download.");
    }
  };

  const handleProcessImage = async (): Promise<void> => {
    if (!uploadedFile || !isAuthenticated) return;

    try {
      setError(null);
      setIsProcessing(true);
      const startTime = Date.now();
      const response = await BgRemovalService.directRemoveBackground(
        uploadedFile
      );
      const processingTime = Date.now() - startTime;
      console.log(`Background removal completed in ${processingTime}ms`);

      if (response.success && response.data.imageUrl) {
        setProcessedImage(response.data.imageUrl);
        if (response.data.jobId) {
          setCurrentJob({
            id: response.data.jobId,
            userId: "",
            originalImageUrl: uploadedImage || "",
            resultImageUrl: response.data.imageUrl,
            status: "COMPLETED",
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          });
        }
        if (response.data.cached) {
          console.log("Used cached result for faster processing");
        }
        setIsProcessing(false);
      } else {
        throw new Error(response.message || "Failed to remove background");
      }
    } catch (error: unknown) {
      console.error("Error processing image:", error);
      if (error && typeof error === "object" && "response" in error) {
        const apiError = error as {
          response?: { data?: { error?: { code?: string; message?: string } } };
        };
        if (apiError.response?.data?.error?.code === "INSUFFICIENT_CREDITS") {
          setInsufficientCredits(true);
          setError(
            "You don't have enough credits for this operation. Please purchase more credits."
          );
        } else if (
          apiError.response?.data?.error?.code === "RATE_LIMIT_EXCEEDED"
        ) {
          setError("Rate limit exceeded. Please try again in a moment.");
        } else {
          setError(
            "Failed to process image: " +
              (apiError.response?.data?.error?.message || "Unknown error")
          );
        }
      } else if (error instanceof Error) {
        setError("Failed to process image: " + error.message);
      } else {
        setError("Failed to process image: Unknown error");
      }
      setIsProcessing(false);
    }
  };

  useEffect(() => {
    const fetchCreditInfo = async () => {
      try {
        const costsResponse = await CreditService.getServiceCosts();
        const costs: ServiceCost = costsResponse.data;
        setServiceCost(costs.backgroundRemoval);
        if (isAuthenticated) {
          const balanceResponse = await CreditService.getUserCreditBalance();
          setUserCredits(balanceResponse.data);
        }
      } catch (error) {
        console.error("Error fetching credit information:", error);
      }
    };
    fetchCreditInfo();
  }, [isAuthenticated, setUserCredits]);

  return (
    <div className="min-h-screen flex items-center justify-center relative">
      <img
        loading="lazy"
        src="/png/image_gene_shadow_1.png"
        className="absolute bottom-[-24px] right-[-24px]"
      />
      <div className="absolute inset-0">
        <div
          className="grid grid-cols-12 h-full opacity-10"
          style={{
            maskImage:
              "radial-gradient(ellipse at top left, transparent 20%, black 50%)",
            WebkitMaskImage:
              "radial-gradient(ellipse at top left, transparent 20%, black 50%)",
          }}
        >
          {Array.from({ length: 144 }).map((_, i: number) => (
            <div key={i} className="border border-white/20"></div>
          ))}
        </div>
      </div>
      <div className="absolute top-20 right-20 w-2 h-2 bg-white/30 rounded-full animate-pulse"></div>
      <div className="absolute top-40 right-40 w-1 h-1 bg-white/20 rounded-full animate-pulse delay-1000"></div>
      <div className="absolute bottom-32 left-32 w-1.5 h-1.5 bg-white/25 rounded-full animate-pulse delay-500"></div>
      <div className="absolute bottom-20 right-60 w-1 h-1 bg-white/20 rounded-full animate-pulse delay-700"></div>
      <div className="relative z-10 w-full max-w-2xl px-4 sm:px-0">
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-white mb-4">
            Background Remover
          </h1>
          <p className="text-gray-500 text-base">
            Erase image backgrounds for free and replace it with different
            backgrounds of your choosing.
          </p>
        </div>
        <div
          className={`relative bg-gray-800/50 backdrop-blur-sm border-2 min-h-[400px] border-dashed rounded-2xl text-center flex flex-col justify-center transition-all duration-300 ${
            isDragOver
              ? "border-purple-400 bg-purple-900/30"
              : "border-gray-600 hover:border-gray-500"
          }`}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
        >
          {!uploadedImage ? (
            <div className="p-12">
              <div className="mb-6">
                <label className="inline-flex items-center gap-2 px-6 py-3 bg-purple-600 hover:bg-purple-700 text-white font-medium rounded-lg cursor-pointer transition-colors">
                  <Upload size={20} />
                  Choose an Image or Drag & Drop
                  <input
                    type="file"
                    className="hidden"
                    accept=".png,.jpg,.jpeg,.webp"
                    onChange={handleFileSelect}
                    multiple
                  />
                </label>
              </div>
              <div className="flex items-center gap-4 mb-6">
                <div className="flex-1 h-px bg-gray-600"></div>
                <span className="text-gray-400 text-sm font-medium">OR</span>
                <div className="flex-1 h-px bg-gray-600"></div>
              </div>
              <p className="text-gray-400 text-sm">
                Original image must be .png, .jpg, .jpeg or .webp format and
                30mb max size.
              </p>
            </div>
          ) : (
            <div className="relative">
              <div className="relative p-4">
                {processedImage ? (
                  <div className="relative">
                    <div className="relative w-full overflow-hidden rounded-lg bg-checkerboard">
                      <ReactBeforeSliderComponent
                        firstImage={{
                          imageUrl: processedImage,
                          alt: "Processed Image",
                        }}
                        secondImage={{
                          imageUrl: uploadedImage,
                          alt: "Original Image",
                        }}
                        currentPercentPosition={50}
                        className="w-full h-full"
                        // imageClassName="object-contain w-full h-full"
                        withResizeFeel={true}
                        delimiterIconStyles={{
                          backgroundColor: "#8b5cf6",
                          borderColor: "#8b5cf6",
                        }}
                        delimiterColor="#8b5cf6"
                        // onMouseEnter={() => setIsHovering(true)}
                        // onMouseLeave={() => setIsHovering(false)}
                      />
                      <div className="absolute top-2 left-2 bg-black/50 text-white text-xs px-2 py-1 rounded">
                        Original
                      </div>
                      <div className="absolute top-2 right-2 bg-black/50 text-white text-xs px-2 py-1 rounded">
                        Processed
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className="relative w-full h-80 rounded-lg overflow-hidden bg-checkerboard">
                    <img
                      loading="lazy"
                      src={uploadedImage}
                      alt="Uploaded"
                      className="w-full h-full object-contain mx-auto"
                    />
                  </div>
                )}
                {isProcessing && (
                  <div className="absolute inset-0 bg-black/70 flex flex-col items-center justify-center rounded-lg">
                    <div className="w-16 h-16 border-4 border-purple-500 border-t-transparent rounded-full animate-spin mb-4"></div>
                    <div className="text-white text-center">
                      <p className="font-medium mb-2">Removing Background...</p>
                      <p className="text-sm text-gray-300">
                        This may take a few seconds
                      </p>
                    </div>
                  </div>
                )}
              </div>
              <div className="absolute bottom-4 left-4 right-4 bg-gray-900/90 backdrop-blur-sm rounded-lg p-3 flex items-center justify-between">
                <button
                  onClick={handleReset}
                  className="flex items-center gap-2 px-3 py-3 bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition-colors"
                >
                  <RefreshCw />
                </button>
                <button
                  onClick={() => setIsOpenBgSelectModal(true)}
                  className="flex gap-2 items-center px-3 py-2 bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition-colors"
                >
                  <span>AI background</span>
                  <ChevronDown
                    size={16}
                    className="text-gray-400 pointer-events-none"
                  />
                </button>
                {processedImage ? (
                  <button
                    onClick={handleDownload}
                    className="flex items-center gap-2 px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors"
                  >
                    <Download size={18} />
                    Download
                  </button>
                ) : (
                  <button
                    onClick={handleProcessImage}
                    disabled={isProcessing || insufficientCredits}
                    className={`flex items-center gap-2 px-4 py-2 ${
                      isProcessing || insufficientCredits
                        ? "bg-gray-600 cursor-not-allowed"
                        : "bg-purple-600 hover:bg-purple-700"
                    } text-white rounded-lg transition-colors`}
                  >
                    {isProcessing ? (
                      <Loader2 className="animate-spin" size={18} />
                    ) : null}
                    {isProcessing ? "Processing..." : "Remove Background"}
                  </button>
                )}
              </div>
            </div>
          )}
          {isDragOver && (
            <div className="absolute inset-0 bg-purple-600/20 border-2 border-purple-400 rounded-2xl flex items-center justify-center">
              <div className="text-center">
                <Upload size={32} className="text-purple-300 mx-auto mb-2" />
                <p className="text-purple-200 font-medium">
                  Drop your files here
                </p>
              </div>
            </div>
          )}
        </div>
        <div className="mt-8 text-center">
          {serviceCost !== null && (
            <p className="text-gray-400 text-sm">
              This operation costs{" "}
              <span className="text-purple-400 font-semibold">
                {serviceCost} credit{serviceCost !== 1 ? "s" : ""}
              </span>
              {isAuthenticated && userCredits?.balance && (
                <span>
                  {" "}
                  • You have{" "}
                  <span className="text-purple-400 font-semibold">
                    {userCredits?.balance} credit
                    {userCredits?.balance !== 1 ? "s" : ""}
                  </span>
                </span>
              )}
            </p>
          )}
          <p className="text-gray-400 text-sm mt-2">
            Supported formats: PNG, JPG, JPEG, WEBP • Max file size: 10MB
          </p>
        </div>
        {error && (
          <div className="mt-4 p-3 bg-red-900/30 border border-red-700 rounded-lg flex items-center gap-2">
            <AlertCircle className="text-red-500" size={18} />
            <p className="text-red-400 text-sm">{error}</p>
          </div>
        )}
        {!isAuthenticated && (
          <div className="mt-4 p-3 bg-yellow-900/30 border border-yellow-700 rounded-lg flex items-center gap-2">
            <AlertCircle className="text-yellow-500" size={18} />
            <p className="text-yellow-400 text-sm">
              You need to{" "}
              <a href="/auth/login" className="underline hover:text-yellow-300">
                log in
              </a>{" "}
              to use this feature.
            </p>
          </div>
        )}
      </div>
      <AiCustomBgSelectionModal
        isOpen={isOpenBgSelectModal}
        onClose={() => setIsOpenBgSelectModal(false)}
        onSelect={() => {}}
      />
    </div>
  );
};

export default BackgroundRemoverPage;

// import {
//   useState,
//   useEffect,
//   useContext,
//   type ChangeEvent,
//   type DragEvent,
// } from "react";
// import {
//   Upload,
//   ChevronDown,
//   Download,
//   RefreshCw,
//   AlertCircle,
//   Loader2,
// } from "lucide-react";
// import ReactBeforeSliderComponent from "react-before-after-slider-component";
// import "react-before-after-slider-component/dist/build.css";
// import AiCustomBgSelectionModal from "@/components/AllTools/AiCustomBgSelectionModal";
// import { AppContext } from "@/contexts/AppContext";
// import BgRemovalService from "@/services/bgRemoval.service";
// import type { BgRemovalJob } from "@/services/bgRemoval.service";
// import CreditService from "@/services/credit.service";
// import type { ServiceCost } from "@/services/credit.service";

// const BackgroundRemoverPage = () => {
//   const { isAuthenticated, setUserCredits, userCredits } = useContext(AppContext);
//   const [isDragOver, setIsDragOver] = useState<boolean>(false);
//   const [uploadedImage, setUploadedImage] = useState<string | null>(null);
//   const [uploadedFile, setUploadedFile] = useState<File | null>(null);
//   const [isOpenBgSelectModal, setIsOpenBgSelectModal] = useState<boolean>(false);
//   const [isProcessing, setIsProcessing] = useState<boolean>(false);
//   const [processedImage, setProcessedImage] = useState<string | null>(null);
//   const [currentJob, setCurrentJob] = useState<BgRemovalJob | null>(null);
//   const [error, setError] = useState<string | null>(null);
//   const [serviceCost, setServiceCost] = useState<number | null>(null);
//   const [insufficientCredits, setInsufficientCredits] = useState<boolean>(false);
//   const [isHovering, setIsHovering] = useState<boolean>(false);

//   const handleDragOver = (e: DragEvent<HTMLDivElement>): void => {
//     e.preventDefault();
//     setIsDragOver(true);
//   };

//   const handleDragLeave = (e: DragEvent<HTMLDivElement>): void => {
//     e.preventDefault();
//     setIsDragOver(false);
//   };

//   const handleDrop = (e: DragEvent<HTMLDivElement>): void => {
//     e.preventDefault();
//     setIsDragOver(false);
//     const files: FileList = e.dataTransfer.files;
//     if (files.length > 0) {
//       const file: File = files[0];
//       setUploadedFile(file);
//       const reader: FileReader = new FileReader();
//       reader.onload = (e: ProgressEvent<FileReader>): void => {
//         if (e.target?.result) {
//           setUploadedImage(e.target.result as string);
//           setProcessedImage(null);
//           setCurrentJob(null);
//           setError(null);
//         }
//       };
//       reader.readAsDataURL(file);
//     }
//   };

//   const handleFileSelect = (e: ChangeEvent<HTMLInputElement>): void => {
//     const files: FileList | null = e.target.files;
//     if (files && files.length > 0) {
//       const file: File = files[0];
//       setUploadedFile(file);
//       const reader: FileReader = new FileReader();
//       reader.onload = (e: ProgressEvent<FileReader>): void => {
//         if (e.target?.result) {
//           setUploadedImage(e.target.result as string);
//           setProcessedImage(null);
//           setCurrentJob(null);
//           setError(null);
//         }
//       };
//       reader.readAsDataURL(file);
//     }
//   };

//   const handleReset = (): void => {
//     setUploadedImage(null);
//     setUploadedFile(null);
//     setProcessedImage(null);
//     setCurrentJob(null);
//     setError(null);
//     setIsProcessing(false);
//   };

//   const handleDownload = (): void => {
//     if (processedImagecurrentJob?.resultImageUrl) {
//       fetch(processedImage)
//         .then((response) => response.blob())
//         .then((blob) => {
//           const link = document.createElement("a");
//           const url = URL.createObjectURL(blob);
//           link.href = url;
//           link.download = `bg-removed-${Date.now()}.jpg`;
//           document.body.appendChild(link);
//           link.click();
//           document.body.removeChild(link);
//           URL.revokeObjectURL(url);
//         })
//         .catch((error) => {
//           console.error("Error fetching image:", error);
//           alert("Failed to download the image.");
//         });
//     } else {
//       console.log("No generated image to download");
//       alert("No generated image available to download.");
//     }
//   };

//   const handleProcessImage = async (): Promise<void> => {
//     if (!uploadedFile || !isAuthenticated) return;

//     try {
//       setError(null);
//       setIsProcessing(true);
//       const startTime = Date.now();
//       const response = await BgRemovalService.directRemoveBackground(uploadedFile);
//       const processingTime = Date.now() - startTime;
//       console.log(`Background removal completed in ${processingTime}ms`);

//       if (response.success && response.data.imageUrl) {
//         setProcessedImage(response.data.imageUrl);
//         if (response.data.jobId) {
//           setCurrentJob({
//             id: response.data.jobId,
//             userId: "",
//             originalImageUrl: uploadedImage || "",
//             resultImageUrl: response.data.imageUrl,
//             status: "COMPLETED",
//             createdAt: new Date().toISOString(),
//             updatedAt: new Date().toISOString(),
//           });
//         }
//         if (response.data.cached) {
//           console.log("Used cached result for faster processing");
//         }
//         setIsProcessing(false);
//       } else {
//         throw new Error(response.message || "Failed to remove background");
//       }
//     } catch (error: unknown) {
//       console.error("Error processing image:", error);
//       if (error && typeof error === "object" && "response" in error) {
//         const apiError = error as {
//           response?: { data?: { error?: { code?: string; message?: string } } };
//         };
//         if (apiError.response?.data?.error?.code === "INSUFFICIENT_CREDITS") {
//           setInsufficientCredits(true);
//           setError(
//             "You don't have enough credits for this operation. Please purchase more credits."
//           );
//         } else if (apiError.response?.data?.error?.code === "RATE_LIMIT_EXCEEDED") {
//           setError("Rate limit exceeded. Please try again in a moment.");
//         } else {
//           setError(
//             "Failed to process image: " +
//               (apiError.response?.data?.error?.message || "Unknown error")
//           );
//         }
//       } else if (error instanceof Error) {
//         setError("Failed to process image: " + error.message);
//       } else {
//         setError("Failed to process image: Unknown error");
//       }
//       setIsProcessing(false);
//     }
//   };

//   useEffect(() => {
//     const fetchCreditInfo = async () => {
//       try {
//         const costsResponse = await CreditService.getServiceCosts();
//         const costs: ServiceCost = costsResponse.data;
//         setServiceCost(costs.backgroundRemoval);
//         if (isAuthenticated) {
//           const balanceResponse = await CreditService.getUserCreditBalance();
//           setUserCredits(balanceResponse.data);
//         }
//       } catch (error) {
//         console.error("Error fetching credit information:", error);
//       }
//     };
//     fetchCreditInfo();
//   }, [isAuthenticated, setUserCredits]);

//   return (
//     <div className="min-h-screen flex items-center justify-center relative">
//       <img loading="lazy"
//         src="/png/image_gene_shadow_1.png"
//         className="absolute bottom-[-24px] right-[-24px]"
//       />
//       <div className="absolute inset-0">
//         <div
//           className="grid grid-cols-12 h-full opacity-10"
//           style={{
//             maskImage: "radial-gradient(ellipse at top left, transparent 20%, black 50%)",
//             WebkitMaskImage: "radial-gradient(ellipse at top left, transparent 20%, black 50%)",
//           }}
//         >
//           {Array.from({ length: 144 }).map((_, i: number) => (
//             <div key={i} className="border border-white/20"></div>
//           ))}
//         </div>
//       </div>
//       <div className="absolute top-20 right-20 w-2 h-2 bg-white/30 rounded-full animate-pulse"></div>
//       <div className="absolute top-40 right-40 w-1 h-1 bg-white/20 rounded-full animate-pulse delay-1000"></div>
//       <div className="absolute bottom-32 left-32 w-1.5 h-1.5 bg-white/25 rounded-full animate-pulse delay-500"></div>
//       <div className="absolute bottom-20 right-60 w-1 h-1 bg-white/20 rounded-full animate-pulse delay-700"></div>
//       <div className="relative z-10 w-full max-w-2xl px-4 sm:px-0">
//         <div className="text-center mb-8">
//           <h1 className="text-4xl font-bold text-white mb-4">Background Remover</h1>
//           <p className="text-gray-500 text-base">
//             Erase image backgrounds for free and replace it with different backgrounds of your choosing.
//           </p>
//         </div>
//         <div
//           className={`relative bg-gray-800/50 backdrop-blur-sm border-2 min-h-[400px] border-dashed rounded-2xl text-center flex flex-col justify-center transition-all duration-300 ${
//             isDragOver ? "border-purple-400 bg-purple-900/30" : "border-gray-600 hover:border-gray-500"
//           }`}
//           onDragOver={handleDragOver}
//           onDragLeave={handleDragLeave}
//           onDrop={handleDrop}
//         >
//           {!uploadedImage ? (
//             <div className="p-12">
//               <div className="mb-6">
//                 <label className="inline-flex items-center gap-2 px-6 py-3 bg-purple-600 hover:bg-purple-700 text-white font-medium rounded-lg cursor-pointer transition-colors">
//                   <Upload size={20} />
//                   Choose an Image or Drag & Drop
//                   <input
//                     type="file"
//                     className="hidden"
//                     accept=".png,.jpg,.jpeg,.webp"
//                     onChange={handleFileSelect}
//                     multiple
//                   />
//                 </label>
//               </div>
//               <div className="flex items-center gap-4 mb-6">
//                 <div className="flex-1 h-px bg-gray-600"></div>
//                 <span className="text-gray-400 text-sm font-medium">OR</span>
//                 <div className="flex-1 h-px bg-gray-600"></div>
//               </div>
//               <p className="text-gray-400 text-sm">
//                 Original image must be .png, .jpg, .jpeg or .webp format and 30mb max size.
//               </p>
//             </div>
//           ) : (
//             <div className="relative">
//               <div className="relative p-4">
//                 {processedImage ? (
//                   <div className="relative">
//                     <div className="relative w-full h-80 overflow-hidden rounded-lg bg-checkerboard">
//                       <ReactBeforeSliderComponent
//                         firstImage={{
//                           imageUrl: processedImage,
//                           alt: "Processed Image",
//                         }}
//                         secondImage={{
//                           imageUrl: uploadedImage,
//                           alt: "Original Image",
//                         }}
//                         currentPercentPosition={isHovering ? 50 : 0}
//                         className="w-full h-full object-cover"
//                         withResizeFeel={true}
//                         delimiterIconStyles={{
//                           backgroundColor: "#8b5cf6",
//                           borderColor: "#8b5cf6",
//                         }}
//                         delimiterColor="#8b5cf6"
//                       />
//                       <div className="absolute top-2 left-2 bg-black/50 text-white text-xs px-2 py-1 rounded">
//                         Original
//                       </div>
//                       <div className="absolute top-2 right-2 bg-black/50 text-white text-xs px-2 py-1 rounded">
//                         Processed
//                       </div>
//                     </div>
//                   </div>
//                 ) : (
//                   <div className="relative w-full h-80 rounded-lg overflow-hidden bg-checkerboard">
//                     <img loading="lazy"
//                       src={uploadedImage}
//                       alt="Uploaded"
//                       className="w-full h-full object-contain mx-auto"
//                     />
//                   </div>
//                 )}
//                 {isProcessing && (
//                   <div className="absolute inset-0 bg-black/70 flex flex-col items-center justify-center rounded-lg">
//                     <div className="w-16 h-16 border-4 border-purple-500 border-t-transparent rounded-full animate-spin mb-4"></div>
//                     <div className="text-white text-center">
//                       <p className="font-medium mb-2">Removing Background...</p>
//                       <p className="text-sm text-gray-300">This may take a few seconds</p>
//                     </div>
//                   </div>
//                 )}
//               </div>
//               <div className="absolute bottom-4 left-4 right-4 bg-gray-900/90 backdrop-blur-sm rounded-lg p-3 flex items-center justify-between">
//                 <button
//                   onClick={handleReset}
//                   className="flex items-center gap-2 px-3 py-3 bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition-colors"
//                 >
//                   <RefreshCw />
//                 </button>
//                 <button
//                   onClick={() => setIsOpenBgSelectModal(true)}
//                   className="flex gap-2 items-center px-3 py-2 bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition-colors"
//                 >
//                   <span>AI background</span>
//                   <ChevronDown
//                     size={16}
//                     className="text-gray-400 pointer-events-none"
//                   />
//                 </button>
//                 {processedImage ? (
//                   <button
//                     onClick={handleDownload}
//                     className="flex items-center gap-2 px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors"
//                   >
//                     <Download size={18} />
//                     Download
//                   </button>
//                 ) : (
//                   <button
//                     onClick={handleProcessImage}
//                     disabled={isProcessing || insufficientCredits}
//                     className={`flex items-center gap-2 px-4 py-2 ${
//                       isProcessing || insufficientCredits
//                         ? "bg-gray-600 cursor-not-allowed"
//                         : "bg-purple-600 hover:bg-purple-700"
//                     } text-white rounded-lg transition-colors`}
//                   >
//                     {isProcessing ? (
//                       <Loader2 className="animate-spin" size={18} />
//                     ) : null}
//                     {isProcessing ? "Processing..." : "Remove Background"}
//                   </button>
//                 )}
//               </div>
//             </div>
//           )}
//           {isDragOver && (
//             <div className="absolute inset-0 bg-purple-600/20 border-2 border-purple-400 rounded-2xl flex items-center justify-center">
//               <div className="text-center">
//                 <Upload size={32} className="text-purple-300 mx-auto mb-2" />
//                 <p className="text-purple-200 font-medium">Drop your files here</p>
//               </div>
//             </div>
//           )}
//         </div>
//         <div className="mt-8 text-center">
//           {serviceCost !== null && (
//             <p className="text-gray-400 text-sm">
//               This operation costs{" "}
//               <span className="text-purple-400 font-semibold">
//                 {serviceCost} credit{serviceCost !== 1 ? "s" : ""}
//               </span>
//               {isAuthenticated && userCredits?.balance && (
//                 <span>
//                   {" "}• You have{" "}
//                   <span className="text-purple-400 font-semibold">
//                     {userCredits?.balance} credit{userCredits?.balance !== 1 ? "s" : ""}
//                   </span>
//                 </span>
//               )}
//             </p>
//           )}
//           <p className="text-gray-400 text-sm mt-2">
//             Supported formats: PNG, JPG, JPEG, WEBP • Max file size: 10MB
//           </p>
//         </div>
//         {error && (
//           <div className="mt-4 p-3 bg-red-900/30 border border-red-700 rounded-lg flex items-center gap-2">
//             <AlertCircle className="text-red-500" size={18} />
//             <p className="text-red-400 text-sm">{error}</p>
//           </div>
//         )}
//         {!isAuthenticated && (
//           <div className="mt-4 p-3 bg-yellow-900/30 border border-yellow-700 rounded-lg flex items-center gap-2">
//             <AlertCircle className="text-yellow-500" size={18} />
//             <p className="text-yellow-400 text-sm">
//               You need to{" "}
//               <a href="/auth/login" className="underline hover:text-yellow-300">
//                 log in
//               </a>{" "}
//               to use this feature.
//             </p>
//           </div>
//         )}
//       </div>
//       <AiCustomBgSelectionModal
//         isOpen={isOpenBgSelectModal}
//         onClose={() => setIsOpenBgSelectModal(false)}
//         onSelect={() => {}}
//       />
//     </div>
//   );
// };

// export default BackgroundRemoverPage;
