import Section from "@/components/layout/Section";
import { Text } from "@/components/ui/text";
import Links from "./Links";

const Hero = () => {
  return (
    <Section
      style={{
        background: `url("/png/api_banner.png")`,
        backgroundPosition: "bottom center",
        backgroundSize: "contain",
        backgroundRepeat: "no-repeat",
      }}
      className="relative pt-28 sm:pb-[250px] md:pb-[400px]"
    >
      <div className="flex flex-col justify-center items-center gap-12">
        <div className="flex flex-col justify-center items-center">
          <Text variant={"page_title"} className="text-white text-center">
            Supercharge Your Business <br />
            with the <span className="text-secondary">MiragicAI API</span>
          </Text>
          <Text variant={"card_title_large"} className=" text-center">
            Powerful and Simple to Use
          </Text>
        </div>
        <Links />
      </div>
    </Section>
  );
};

export default Hero;
