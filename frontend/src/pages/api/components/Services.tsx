import Section from "@/components/layout/Section";
import { But<PERSON> } from "@/components/ui/button";
import { Text } from "@/components/ui/text";
import { ImgComparisonSlider } from "@img-comparison-slider/react";
import { Swiper, SwiperSlide } from "swiper/react";
import { Swiper as SwiperType } from "swiper"; // 🧠 Type import
import "swiper/css";

// import 'swiper/css/effect-cards';
import { EffectCards } from "swiper/modules";

import { useRef, useState } from "react";

import SliderBtnArrow from "@/components/common/SliderBtnArrow";

const Services = () => {
  return (
    <Section className="bg-[#000000] overflow-hidden relative pt-38 pb-24">
      <img
        loading="lazy"
        className="absolute w-full h-full top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2  "
        src="/png/service_bg.png"
      />
      <div className="grid mb-20 gap-5 grid-cols-1 sm:grid-cols-2 md:grid-cols-3">
        <div className="flex z-1  overflow-hidden rounded-[12px] bg-[#222] flex-col min-h-[600px]">
          <img
            loading="lazy"
            className="min-h-[220px] w-auto"
            src="/png/api_service_1.png"
          />
          <div className="flex p-5 flex-col justify-between h-full">
            <div className="flex text-white flex-col gap-4">
              <Text variant={"card_title_small"} className="text-white">
                Speedpaint
              </Text>
              <Text variant={"card_body"} className="">
                Discover how Miragic’s AI-powered speedpainting tools help
                creatives bring ideas to life faster—turning rough concepts into
                stunning visuals in minutes.
              </Text>
            </div>
            <Button className="border h-[50px]" variant={"animeShine"}>
              API Documentation
            </Button>
          </div>
        </div>
        <div className="flex z-1 relative overflow-hidden rounded-[12px] bg-[#222] flex-col min-h-[600px]">
          <div className="absolute bg-white/20 px-2.5 py-1.5 rounded-xl z-20 right-5 top-5">
            <Text variant={"card_body"} className="text-white">
              After
            </Text>
          </div>
          <div className="absolute bg-white/20 px-2.5 py-1.5 rounded-xl z-20 left-5 top-5">
            <Text variant={"card_body"} className="text-white">
              Before
            </Text>
          </div>
          <div className="w-full">
            <ImgComparisonSlider className="w-full min-h-[220px] ">
              <img
                loading="lazy"
                className="min-h-[220px] w-auto"
                slot="first"
                src="https://img-comparison-slider.sneas.io/demo/images/before.webp"
              />
              <img
                loading="lazy"
                className="min-h-[220px] w-auto"
                slot="second"
                src="https://img-comparison-slider.sneas.io/demo/images/after.webp"
              />
            </ImgComparisonSlider>
          </div>
          <div className="flex p-5 flex-col justify-between h-full">
            <div className="flex text-white flex-col gap-4">
              <Text variant={"card_title_small"} className="text-white">
                Background Remover
              </Text>
              <Text variant={"card_body"} className="">
                See how companies use AI-powered background removal to produce
                high-quality, professional images at scale — faster and smarter
                than ever.
              </Text>
            </div>
            <Button className="border h-[50px]" variant={"animeShine"}>
              API Documentation
            </Button>
          </div>
        </div>
        <div className="flex z-1  overflow-hidden rounded-[12px] bg-[#222] flex-col min-h-[600px]">
          <img
            loading="lazy"
            className="min-h-[220px] w-auto"
            src="/png/api_service_3.png"
          />
          <div className="flex p-5 flex-col justify-between h-full">
            <div className="flex text-white flex-col gap-4">
              <Text variant={"card_title_small"} className="text-white">
                Virtual Try On
              </Text>
              <Text variant={"card_body"} className="">
                Discover how our clients are leveraging Virtual Try-On and
                Generative AI to boost engagement, reduce returns, and elevate
                customer experience.
              </Text>
            </div>
            <Button className="border h-[50px]" variant={"animeShine"}>
              API Documentation
            </Button>
          </div>
        </div>
      </div>
      <ServiceSlider />
      <Platform />
    </Section>
  );
};

const ServiceSlider = () => {
  const swiperRef = useRef<SwiperType | null>(null);
  const [isBeginning, setIsBeginning] = useState(false);
  const [isEnd, setIsEnd] = useState(false);

  return (
    <div className="grid z-10 grid-cols-[160px_auto] gap-[120px]">
      <div className="flex z-10 flex-col gap-8 justify-center items-start">
        <Text variant={"section_title"} className="text-white">
          Video Tutorial
        </Text>
        <Text variant={"body"} className="">
          Quick and Easy to Use, and Controlled Cost
        </Text>
        <SliderBtnArrow
          isEnd={isEnd}
          isBeginning={isBeginning}
          swiperRef={swiperRef}
        />
      </div>
      <div className="w-full min-w-[500px] mx-auto mt-10">
        <Swiper
          effect="cards"
          grabCursor={true}
          modules={[EffectCards]}
          className="mySwiper"
          onSwiper={(swiper) => {
            swiperRef.current = swiper;
            setIsBeginning(swiper.isBeginning);
            setIsEnd(swiper.isEnd);
          }}
          onSlideChange={(swiper) => {
            setIsBeginning(swiper.isBeginning);
            setIsEnd(swiper.isEnd);
          }}
        >
          {[...Array(9)].map((_, index) => (
            <SwiperSlide key={index}>
              <img
                loading="lazy"
                className="rounded-[12px] w-full h-[470px]"
                src="/png/api_service_slider_1.png"
              />
            </SwiperSlide>
          ))}
        </Swiper>
      </div>
    </div>
  );
};

const Platform = () => {
  return (
    <div className="flex flex-col gap-12 pt-20">
      <Text variant={"section_title"} className="text-white z-10 text-center">
        API Services
      </Text>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-10">
        <div className="flex relative z-10 flex-col items-start gap-[18px] p-[34px] pb-[52px] rounded-[12px] border-[2px] border-[#2F2F2F] bg-[linear-gradient(298deg,_#511F9F_0%,_#181818_25%,_#181818_100%)]">
          <div className="absolute right-2 bottom-2">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="112"
              height="112"
              viewBox="0 0 112 112"
              fill="none"
            >
              <g opacity="0.5">
                <path
                  d="M15.9932 32.0015L10.9799 21.0148L-0.00683594 16.0015L10.9799 10.9881L15.9932 0.00146484L21.0065 10.9881L31.9932 16.0015L21.0065 21.0148L15.9932 32.0015Z"
                  fill="url(#paint0_linear_2001_215)"
                />
                <path
                  d="M77.3262 32.0015L72.3129 21.0148L61.3262 16.0015L72.3129 10.9881L77.3262 0.00146484L82.3396 10.9881L93.3264 16.0015L82.3396 21.0148L77.3262 32.0015Z"
                  fill="url(#paint1_linear_2001_215)"
                />
                <path
                  d="M15.9932 61.3348L21.0065 72.3215L31.9932 77.3348L21.0065 82.3481L15.9932 93.335L10.9799 82.3481L-0.00683594 77.3348L10.9799 72.3215L15.9932 61.3348Z"
                  fill="url(#paint2_linear_2001_215)"
                />
                <path
                  d="M28.2065 43.3081L43.2999 28.2148C44.3665 27.2015 45.6999 26.6681 47.0865 26.6681C48.4732 26.6681 49.8065 27.2015 50.8732 28.2148L110.447 87.7881C112.527 89.8681 112.527 93.228 110.447 95.308L95.3537 110.401C94.2867 111.468 92.9537 112.001 91.5665 112.001C90.1799 112.001 88.8465 111.468 87.7799 110.455L28.2065 50.8815C26.1265 48.8015 26.1265 45.3881 28.2065 43.3081ZM47.0865 39.5748L39.5665 47.0948L45.8065 53.3348L53.3265 45.8148L47.0865 39.5748ZM91.5665 99.148L99.0867 91.6281L60.8465 53.3348L53.3265 60.8548L91.5665 99.148Z"
                  fill="url(#paint3_linear_2001_215)"
                />
              </g>
              <defs>
                <linearGradient
                  id="paint0_linear_2001_215"
                  x1="89.6011"
                  y1="79.2015"
                  x2="106.002"
                  y2="94.002"
                  gradientUnits="userSpaceOnUse"
                >
                  <stop stop-color="#8639FF" />
                  <stop offset="1" stop-color="#6300FF" stop-opacity="0" />
                </linearGradient>
                <linearGradient
                  id="paint1_linear_2001_215"
                  x1="89.6008"
                  y1="79.2015"
                  x2="106.001"
                  y2="94.002"
                  gradientUnits="userSpaceOnUse"
                >
                  <stop stop-color="#8639FF" />
                  <stop offset="1" stop-color="#6300FF" stop-opacity="0" />
                </linearGradient>
                <linearGradient
                  id="paint2_linear_2001_215"
                  x1="89.6011"
                  y1="79.2015"
                  x2="106.002"
                  y2="94.002"
                  gradientUnits="userSpaceOnUse"
                >
                  <stop stop-color="#8639FF" />
                  <stop offset="1" stop-color="#6300FF" stop-opacity="0" />
                </linearGradient>
                <linearGradient
                  id="paint3_linear_2001_215"
                  x1="89.6011"
                  y1="79.2015"
                  x2="106.002"
                  y2="94.002"
                  gradientUnits="userSpaceOnUse"
                >
                  <stop stop-color="#8639FF" />
                  <stop offset="1" stop-color="#6300FF" stop-opacity="0" />
                </linearGradient>
              </defs>
            </svg>
          </div>
          <Text
            variant={"card_title_small"}
            className="text-white bg-clip-text bg-gradient-to-r from-white via-white to-gray-500"
          >
            AI Immersive Consumer Experiences
          </Text>
          <div className="flex flex-col gap-0">
            <Text variant={"card_body"} className=" list-disc  list-item">
              Professional Generated Consumer Content
            </Text>
            <Text variant={"card_body"} className=" list-disc  list-item">
              User Generated Content
            </Text>
            <Text variant={"card_body"} className=" list-disc  list-item">
              Personalized Gaming Experiences
            </Text>
            <Text
              variant={"card_body"}
              className=" list-disc cursor-pointer list-item"
            >
              More...
            </Text>
          </div>
        </div>
        <div className="flex relative z-10 flex-col items-start gap-[18px] p-[34px] pb-[52px] rounded-[12px] border-[2px] border-[#2F2F2F] bg-[linear-gradient(298deg,_#511F9F_0%,_#181818_25%,_#181818_100%)]">
          <div className="absolute right-2 bottom-2">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="128"
              height="128"
              viewBox="0 0 128 128"
              fill="none"
            >
              <path
                d="M106.667 10.6666H21.3337C15.467 10.6666 10.7203 15.4666 10.7203 21.3333L10.667 74.6666C10.667 80.5333 15.467 85.3333 21.3337 85.3333H106.667C112.534 85.3333 117.334 80.5333 117.334 74.6666V21.3333C117.334 15.4666 112.534 10.6666 106.667 10.6666ZM106.667 74.6666H21.3337V21.3333H106.667V74.6666ZM58.667 26.6666H69.3337V37.3333H58.667V26.6666ZM58.667 42.6666H69.3337V53.3333H58.667V42.6666ZM42.667 26.6666H53.3337V37.3333H42.667V26.6666ZM42.667 42.6666H53.3337V53.3333H42.667V42.6666ZM26.667 42.6666H37.3337V53.3333H26.667V42.6666ZM26.667 26.6666H37.3337V37.3333H26.667V26.6666ZM42.667 58.6666H85.3337V69.3333H42.667V58.6666ZM74.667 42.6666H85.3337V53.3333H74.667V42.6666ZM74.667 26.6666H85.3337V37.3333H74.667V26.6666ZM90.667 42.6666H101.334V53.3333H90.667V42.6666ZM90.667 26.6666H101.334V37.3333H90.667V26.6666ZM64.0003 117.333L85.3337 96H42.667L64.0003 117.333Z"
                fill="url(#paint0_linear_2001_128)"
              />
              <defs>
                <linearGradient
                  id="paint0_linear_2001_128"
                  x1="10.667"
                  y1="17.0666"
                  x2="101.334"
                  y2="108.8"
                  gradientUnits="userSpaceOnUse"
                >
                  <stop stop-color="#8639FF" />
                  <stop offset="1" stop-color="#6300FF" stop-opacity="0" />
                </linearGradient>
              </defs>
            </svg>
          </div>
          <Text
            variant={"card_title_small"}
            className="text-white bg-clip-text bg-gradient-to-r from-white via-white to-gray-500"
          >
            Engineer Support
          </Text>
          <div className="flex flex-col gap-0">
            <Text variant={"card_body"} className=" list-disc  list-item">
              Data personalisation/confidentiality
            </Text>
            <Text variant={"card_body"} className=" list-disc  list-item">
              Independent data storage
            </Text>
            <Text variant={"card_body"} className=" list-disc  list-item">
              Customised model training
            </Text>
            <Text
              variant={"card_body"}
              className=" list-disc cursor-pointer list-item"
            >
              Model acceleration and optimisation
            </Text>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Services;
