import { Button } from "@/components/ui/button";
import { ChevronRight } from "lucide-react";
import { useState } from "react";

const Links = () => {
  const [isHovered, setIsHovered] = useState(false);
  const [btnName, setBtnName] = useState("github");
  const handleMouseEnter = (name: "github" | "postman", isHover: boolean) => {
    setIsHovered(isHover);
    setBtnName(name);
  };
  return (
    <div className="flex flex-col item-center justify-center gap-6">
      <div className="flex  item-center justify-center items-center gap-5">
        <Button
          className="z-10 h-[50px]"
          size={"lg"}
          outline={false}
          variant={"animeShine"}
        >
          <span className="flex gap-5 items-center">
            <span> Get API Credentials </span>{" "}
            <ChevronRight className="w-5 h-5" />
          </span>
        </Button>
        <Button
          className="group z-10 bg-transparent  hover:bg-secondary h-[50px]"
          size={"lg"}
          variant={"animeShine"}
        >
          <span className="flex gap-5 items-center">
            <span className="group-hover:text-white"> API Document</span>{" "}
            <ChevronRight className="w-5 h-5 group-hover:text-white" />
          </span>
        </Button>
      </div>
      <div className="flex  item-center justify-center items-center gap-5">
        <Button
          onMouseEnter={() => {
            handleMouseEnter("github", true);
          }}
          onMouseLeave={() => {
            handleMouseEnter("github", false);
          }}
          className="group  z-10 !bg-transparent hover:!bg-secondary text-primary-foreground hover:text-white"
          size="lg"
          variant="primary"
        >
          <span className="flex gap-5 items-center">
            <span>
              <svg
                style={{
                  width: "30px",
                  height: "30px",
                }}
                className="w-full"
                xmlns="http://www.w3.org/2000/svg"
                width="40"
                height="40"
                viewBox="0 0 31 30"
                fill="none"
              >
                <path
                  d="M15.7196 3.75C13.0369 3.75456 10.4469 4.73159 8.42956 6.5C6.38433 8.29408 5.0341 10.7494 4.61456 13.4375C4.18592 16.1365 4.70913 18.9006 6.09456 21.2563C7.45902 23.5843 9.61112 25.3476 12.1621 26.2275C12.7246 26.3287 12.9346 25.9825 12.9346 25.68C12.9346 25.405 12.9221 24.4975 12.9221 23.5312C10.0946 24.065 9.36331 22.825 9.13831 22.1762C8.8905 21.5483 8.49469 20.9896 7.98456 20.5475C7.59206 20.3312 7.02956 19.7975 7.97206 19.7825C8.33081 19.8225 8.67581 19.9512 8.97706 20.1575C9.27706 20.3625 9.52706 20.64 9.70081 20.965C9.85456 21.2487 10.0621 21.4987 10.3096 21.7C10.5583 21.9 10.8421 22.05 11.1471 22.1375C11.4512 22.226 11.7702 22.252 12.0847 22.214C12.3992 22.176 12.7027 22.0747 12.9771 21.9162C13.0271 21.33 13.2808 20.7825 13.6946 20.3737C11.1908 20.0862 8.57581 19.0912 8.57581 14.6787C8.55789 13.5375 8.96921 12.4311 9.72831 11.5787C9.38454 10.5814 9.42465 9.49183 9.84081 8.5225C9.84081 8.5225 10.7833 8.22 12.9346 9.705C14.7736 9.18591 16.7205 9.18591 18.5596 9.705C20.7121 8.205 21.6533 8.5225 21.6533 8.5225C22.0696 9.48875 22.1108 10.5825 21.7658 11.5787C22.5246 12.4225 22.9383 13.5325 22.9196 14.6787C22.9196 19.105 20.2896 20.0862 17.7871 20.3737C18.0546 20.6537 18.2621 20.9888 18.3933 21.3562C18.5233 21.725 18.5758 22.1175 18.5458 22.5075C18.5458 24.0512 18.5321 25.2912 18.5321 25.68C18.5321 25.9825 18.7433 26.3425 19.3058 26.2275C21.8519 25.3401 23.9973 23.5728 25.3558 21.2437C26.7353 18.8869 27.2536 16.1241 26.8221 13.4275C26.3999 10.7419 25.0489 8.28949 23.0046 6.4975C20.988 4.73143 18.4001 3.75541 15.7196 3.75Z"
                  fill={isHovered && btnName === "github" ? "#fff" : "#4f2884"}
                />
              </svg>
            </span>
            <span className=" text-secondary group-hover:text-white">
              MIRAGIC Github
            </span>
            <ChevronRight className="w-5 h-5 transition-all duration-200 text-secondary group-hover:text-white" />
          </span>
        </Button>
        <Button
          onMouseEnter={() => {
            handleMouseEnter("postman", true);
          }}
          onMouseLeave={() => {
            handleMouseEnter("postman", false);
          }}
          className="group z-10 !bg-transparent hover:!bg-secondary text-primary-foreground hover:text-white"
          size="lg"
          variant="primary"
        >
          <span className="flex gap-5 items-center">
            <span>
              <svg
                style={{
                  width: "30px",
                  height: "30px",
                }}
                className="transition-all duration-200 group-hover:fill-white"
                xmlns="http://www.w3.org/2000/svg"
                width="31"
                height="30"
                viewBox="0 0 31 30"
                fill="none"
              >
                <g clip-path="url(#clip0_2001_163)">
                  <path
                    d="M6.73822 3.48816C13.0966 -1.42266 22.2317 -0.248768 27.1418 6.10931C32.051 12.4658 30.8782 21.5982 24.5233 26.5091C18.1668 31.4209 9.0309 30.249 4.11913 23.8918C-0.791739 17.5344 0.380792 8.39902 6.73822 3.48816ZM6.82679 22.983L9.1742 23.1349L8.12134 22.0821L7.91764 21.8914L6.82679 22.983ZM12.6534 17.1034L8.24539 21.5762L9.67546 23.0182C9.68877 23.0301 9.70609 23.0367 9.72397 23.0367C9.74185 23.0367 9.7592 23.0301 9.77251 23.0182C9.78142 23.0058 9.78624 22.9909 9.78624 22.9756C9.78622 22.9604 9.78142 22.9454 9.77251 22.9331L9.52989 21.8784C9.51081 21.8028 9.51847 21.7228 9.55163 21.6522C9.58485 21.5816 9.64163 21.5246 9.71209 21.4911C11.8827 20.4401 13.9177 19.1292 15.7719 17.5873L14.9333 16.7491L12.6534 17.1034ZM15.2867 16.3879L16.1718 17.2723C18.3609 15.2042 20.4602 13.0757 20.8563 11.505L15.2867 16.3879ZM13.3124 16.5083L14.5744 16.3538L14.324 16.0973L14.3057 16.0945C14.3041 16.088 14.3028 16.0814 14.3016 16.0748L14.0279 15.7947L14.027 15.7938L13.3124 16.5083ZM20.1546 9.93509C19.9752 9.93504 19.8029 10.0065 19.6758 10.1331L14.3117 15.4486L14.9251 16.062L20.6087 11.1152C20.6784 11.0546 20.7349 10.98 20.7746 10.8966C20.8143 10.8132 20.8363 10.7225 20.8394 10.6302C20.8423 10.5375 20.8257 10.4451 20.7902 10.3594C20.7547 10.2737 20.7011 10.1965 20.6334 10.1331C20.5063 10.0064 20.334 9.93511 20.1546 9.93509ZM11.2998 15.7945L13.5568 15.3117L12.7333 14.4885L12.6692 14.4251L11.2998 15.7945ZM19.2253 9.24134C18.9688 9.2359 18.7163 9.30588 18.4993 9.44276C17.4456 9.91503 15.7504 11.3561 12.9782 14.1151L14.0274 15.1643L19.3727 9.84284C19.5428 9.6732 19.7634 9.56304 20.0012 9.52882C19.7805 9.35168 19.5081 9.25074 19.2253 9.24134ZM22.1605 5.87862C21.2973 5.88331 20.5245 6.40249 20.2004 7.19698C19.8762 7.99078 20.0521 8.90571 20.6581 9.51509C20.9591 9.81778 21.3445 10.0226 21.7638 10.1026C22.1832 10.1826 22.6172 10.1341 23.0085 9.96347L21.5787 8.53388H21.5782C21.5554 8.51562 21.5369 8.49246 21.5242 8.46612C21.5115 8.43975 21.505 8.41069 21.505 8.38144C21.507 8.33304 21.5268 8.28703 21.5608 8.25258C21.5662 8.24454 21.5722 8.23685 21.5787 8.22968L23.4697 6.32721C23.0939 6.039 22.6341 5.88148 22.1605 5.87862ZM22.101 8.31368L22.0387 8.37593L22.2644 8.60161L23.4184 9.73756C23.509 9.67385 23.5951 9.60378 23.6757 9.52791C24.0782 9.1264 24.3047 8.58141 24.3056 8.01293C24.3068 7.52764 24.1385 7.05696 23.8293 6.6829L23.8291 6.68243L22.101 8.31368ZM23.6146 7.67304C23.6552 7.65502 23.7011 7.65232 23.7434 7.66573C23.786 7.67918 23.8222 7.70789 23.8451 7.74628V7.74537C23.9121 7.88817 23.9376 8.04722 23.9183 8.20383C23.899 8.36037 23.8358 8.50842 23.7361 8.63067C23.7168 8.65228 23.6919 8.66836 23.6642 8.67692C23.6365 8.68547 23.6068 8.68622 23.5786 8.67921C23.5474 8.69114 23.5128 8.69112 23.4816 8.67921C23.4523 8.64828 23.4368 8.60698 23.4358 8.56385C23.4349 8.54267 23.4387 8.5215 23.4466 8.50181C23.4545 8.48215 23.4664 8.46413 23.4816 8.44941C23.5413 8.3731 23.5785 8.28157 23.5892 8.18529C23.5998 8.08892 23.5835 7.9913 23.5418 7.90376C23.5313 7.88379 23.5248 7.8619 23.5228 7.83945C23.5208 7.81699 23.5234 7.79433 23.5301 7.77284C23.544 7.72974 23.5744 7.69385 23.6146 7.67304Z"
                    fill={
                      isHovered && btnName === "postman" ? "#fff" : "#4f2884"
                    }
                  />
                </g>
                <defs>
                  <clipPath id="clip0_2001_163">
                    <rect
                      width="30"
                      height="30"
                      fill="white"
                      transform="translate(0.629883)"
                    />
                  </clipPath>
                </defs>
              </svg>
            </span>
            <span className=" text-secondary group-hover:text-white">
              Postman
            </span>
            <ChevronRight className="w-5 h-5 transition-all duration-200 text-secondary group-hover:text-white" />
          </span>
        </Button>
        <Button
          className="group z-10 !bg-transparent  hover:!bg-secondary h-[50px]"
          size={"lg"}
          variant={"animeShine"}
        >
          <span className="flex  group-hover:!text-white gap-5 items-center">
            <span className=""> Contact Sales</span>
          </span>
        </Button>
      </div>
    </div>
  );
};

export default Links;
