import ShadowButton from "@/components/ui/shadowButton";
import BackgroundElements from "@/components/VirtualTryOn/BackgroundElements";
import ClothesModal from "@/components/VirtualTryOn/ClothesModal";
import ClothingUpload from "@/components/VirtualTryOn/ClothingUpload";
import ModelSelection from "@/components/VirtualTryOn/ModelSelection";
import RecentItems from "@/components/VirtualTryOn/RecentItems";
import ResultPanel from "@/components/VirtualTryOn/ResultPanel";
import { MODELS_IMAGES } from "@/utils/DUMMY_DATA";
import React, { useState, useEffect } from "react";
import VirtualTryOnService from "@/services/virtualTryOn.service";
import type {
  ModelImage,
  ClothingItem as BackendClothingItem,
} from "@/services/virtualTryOn.service";
import { useApp } from "@/contexts/useApp";
import { toast } from "sonner";
import { useCredits } from "@/hooks/useCredits";

// Define interfaces
interface ClothingItem {
  id: string;
  image: string;
  type: string;
  name?: string;
  category?: string;
}

interface ModelImageParams {
  mode: "single" | "top_bottom";
  fit_type?: string;
  cloth_category?: string;
  human_image?: File;
  cloth_image?: File;
  low_cloth_image?: File;
  garment_type?: string;
  modelImageId?: string;
  saveAsModel?: boolean;
  modelName?: string;
  gender?: "MALE" | "FEMALE" | "UNISEX";
}

const VirtualTryOnPage: React.FC = () => {
  // Authentication and user context
  const { user, isAuthenticated } = useApp();
  const { refreshCredits, hasEnoughCredits } = useCredits();

  // Drag states
  const [isDragOver, setIsDragOver] = useState<boolean>(false);
  const [isDragOverTop, setIsDragOverTop] = useState<boolean>(false);
  const [isDragOverBottom, setIsDragOverBottom] = useState<boolean>(false);

  // Image display states
  const [uploadedImage, setUploadedImage] = useState<string | null>(null);
  const [uploadedTopImage, setUploadedTopImage] = useState<string | null>(null);
  const [uploadedBottomImage, setUploadedBottomImage] = useState<string | null>(
    null
  );
  const [uploadedModelImage, setUploadedModelImage] = useState<string | null>(
    null
  );

  // File states for API calls
  const [uploadedImageFile, setUploadedImageFile] = useState<File | null>(null);
  const [uploadedTopImageFile, setUploadedTopImageFile] = useState<File | null>(
    null
  );
  const [uploadedBottomImageFile, setUploadedBottomImageFile] =
    useState<File | null>(null);
  const [uploadedModelFile, setUploadedModelFile] = useState<File | null>(null);

  // Selection states
  const [selectedClothingType, setSelectedClothingType] = useState<
    "Single clothes" | "Top & Bottom"
  >("Single clothes");
  const [selectedModel, setSelectedModel] = useState<string | null>(null);
  const [selectedRecentItem, setSelectedRecentItem] =
    useState<ClothingItem | null>(null);

  // Modal states
  const [isModalOpen, setIsModalOpen] = useState<boolean>(false);
  const [modalTab, setModalTab] = useState<
    "all" | "top" | "bottom" | "full_set"
  >("all");

  // Processing state
  const [isProcessing, setIsProcessing] = useState<boolean>(false);

  const [selectedTopItem, setSelectedTopItem] = useState<ClothingItem | null>(
    null
  );
  const [selectedBottomItem, setSelectedBottomItem] =
    useState<ClothingItem | null>(null);

  const [selectedFullSetItem, setSelectedFullSetItem] =
    useState<ClothingItem | null>(null);
  const [generatedImage, setGeneratedImage] = useState<string | null>(null);
  const [selectedFit, setSelectedFit] = useState<string>("Regular Fit");
  const [selectedCategory, setSelectedCategory] = useState<string>("Top");

  // Backend data states
  const [modelImages, setModelImages] = useState<ModelImage[]>([]);
  const [recentClothingItems, setRecentClothingItems] = useState<
    BackendClothingItem[]
  >([]);
  const [isLoadingData, setIsLoadingData] = useState<boolean>(true);

  // Processing states
  const [currentJobId, setCurrentJobId] = useState<string | null>(null);
  const [processingProgress, setProcessingProgress] = useState<string>("");

  // Load data from backend on component mount
  useEffect(() => {
    const loadData = async () => {
      try {
        setIsLoadingData(true);

        // Load model images
        const modelsResponse = await VirtualTryOnService.getModelImages();
        if (modelsResponse.success) {
          setModelImages(modelsResponse.data);
        }

        // Load recent clothing items
        const recentClothingResponse =
          await VirtualTryOnService.getRecentClothingItems();
        if (recentClothingResponse.success) {
          setRecentClothingItems(recentClothingResponse.data);
        }

        // Note: Recent try-ons loading removed as it's not currently used
      } catch (error) {
        console.error("Error loading virtual try-on data:", error);
      } finally {
        setIsLoadingData(false);
      }
    };

    loadData();
  }, []);

  const processFile = async (
    file: File,
    type: "single" | "top" | "bottom" | "model"
  ): Promise<void> => {
    const maxSize = 30 * 1024 * 1024; // 30MB
    if (file.size > maxSize) {
      toast.error("File size exceeds 30MB limit.");
      return;
    }
    if (
      !["image/png", "image/jpeg", "image/jpg", "image/webp"].includes(
        file.type
      )
    ) {
      toast.error("Invalid file format. Use PNG, JPG, JPEG, or WEBP.");
      return;
    }

    // Show immediate preview
    const reader: FileReader = new FileReader();
    reader.onload = (e: ProgressEvent<FileReader>): void => {
      if (e.target?.result) {
        const result = e.target.result as string;
        switch (type) {
          case "single":
            setUploadedImage(result);
            setUploadedImageFile(file);
            setSelectedRecentItem(null);
            break;
          case "top":
            setUploadedTopImage(result);
            setUploadedTopImageFile(file);
            break;
          case "bottom":
            setUploadedBottomImage(result);
            setUploadedBottomImageFile(file);
            break;
          case "model":
            setUploadedModelImage(result);
            setUploadedModelFile(file);
            setSelectedModel(null);
            break;
        }
      }
    };
    reader.readAsDataURL(file);

    // Upload instantly to backend
    try {
      if (type === "model") {
        const uploadResponse = await VirtualTryOnService.uploadModelImage(
          file,
          "USER"
        );
        if (uploadResponse.success) {
          toast.success("Model image uploaded successfully!");
          // Refresh model images list
          const modelsResponse = await VirtualTryOnService.getModelImages();
          if (modelsResponse.success) {
            setModelImages(modelsResponse.data);
          }
        } else {
          toast.error(uploadResponse.message || "Failed to upload model image");
        }
      } else if (type === "single" || type === "top" || type === "bottom") {
        // Determine category based on type
        let category = "TOP";
        if (type === "bottom") category = "BOTTOM";
        else if (selectedCategory === "Dress/Suit") category = "FULL_SET";
        console.log("category", category);
        const uploadResponse = await VirtualTryOnService.uploadClothingItem(
          file,
          `${type} clothing`,
          category,
          "CASUAL",
          `Uploaded ${type} clothing item`,
          "USER"
        );

        if (uploadResponse.success) {
          const aiDetection = uploadResponse.data?.aiDetection;
          if (aiDetection && aiDetection.confidence > 0.7) {
            toast.success(
              `Clothing uploaded! AI detected: ${
                aiDetection.detectedType
              } (${Math.round(aiDetection.confidence * 100)}% confidence)`
            );
          } else if (aiDetection) {
            toast.success(
              `Clothing uploaded! AI detected: ${
                aiDetection.detectedType
              } (${Math.round(
                aiDetection.confidence * 100
              )}% confidence - please verify)`
            );
          } else {
            toast.success("Clothing item uploaded successfully!");
          }
          // Refresh clothing items list if needed
        } else {
          toast.error(
            uploadResponse.message || "Failed to upload clothing item"
          );
        }
      }
    } catch (error) {
      console.error("Upload error:", error);
      toast.error("Failed to upload image. Please try again.");
    }
  };

  const handleReset = (): void => {
    setUploadedImage(null);
    setUploadedImageFile(null);
    setUploadedTopImage(null);
    setUploadedTopImageFile(null);
    setUploadedBottomImage(null);
    setUploadedBottomImageFile(null);
    setUploadedModelImage(null);
    setUploadedModelFile(null);
    setSelectedModel(null);
    setSelectedRecentItem(null);
    setSelectedTopItem(null);
    setSelectedBottomItem(null);
    setSelectedFullSetItem(null);
    setGeneratedImage(null);
  };

  const handleDropdownChange = (fit: string, category: string): void => {
    setSelectedFit(fit);
    setSelectedCategory(category);
    console.log("Dropdown values changed:", { fit, category });
  };

  const handleDownload = (): void => {
    if (generatedImage) {
      // Fetch the image as a blob
      fetch(generatedImage)
        .then((response) => response.blob())
        .then((blob) => {
          // Create a temporary link to download the blob
          const link = document.createElement("a");
          const url = URL.createObjectURL(blob);
          link.href = url;
          link.download = `virtual-tryon-${Date.now()}.jpg`;
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
          URL.revokeObjectURL(url); // Clean up the object URL
        })
        .catch((error) => {
          console.error("Error fetching image:", error);
          toast("Failed to download the image.");
        });
    } else {
      console.log("No generated image to download");
      toast("No generated image available to download.");
    }
  };

  const urlToFile = async (url: string, filename: string): Promise<File> => {
    const response = await fetch(url);
    const blob = await response.blob();
    return new File([blob], filename, { type: blob.type });
  };

  const handleGenerate = async (): Promise<void> => {
    // Check authentication
    if (!isAuthenticated || !user) {
      toast.error("Please log in to use virtual try-on");
      return;
    }

    // Check credits
    if (!hasEnoughCredits("virtualTryOn")) {
      toast.error(
        "Insufficient credits. Please purchase more credits to continue."
      );
      return;
    }

    setIsProcessing(true);
    setProcessingProgress("Initializing...");
    setCurrentJobId(null);
    console.log("🚀 Starting virtual try-on generation...");

    try {
      // Validate required inputs
      const validation = validateInputs();
      if (!validation.isValid) {
        toast(validation.message);
        return;
      }

      // Prepare parameters for the backend API service
      const params = await buildApiParams();
      if (!params) {
        return; // Error already handled in buildApiParams
      }

      console.log("🎯 Final params object:", params);

      // Call the backend API service
      console.log("🌐 Calling VirtualTryOnService.processVirtualTryOn...");
      const response = await VirtualTryOnService.processVirtualTryOn({
        ...params,
        cloth_image: params.cloth_image!,
      });

      console.log("📡 Virtual try-on API response:", response);

      if (response.success && response.data?.jobId) {
        console.log(
          "🎉 Virtual try-on job created successfully:",
          response.data
        );

        // Start polling for job completion
        await pollJobStatus(response.data.jobId);
      } else {
        console.error("❌ Virtual try-on failed:", response.message);
        toast.error(
          response.message ||
            "Failed to start virtual try-on processing. Please try again."
        );
        resetProcessingState();
      }
    } catch (error) {
      console.error("💥 Error generating virtual try-on:", error);
      toast.error(
        "An error occurred while generating the virtual try-on. Please check your connection and try again."
      );
      resetProcessingState();
    }
  };

  // Helper function to validate inputs
  const validateInputs = (): { isValid: boolean; message: string } => {
    let hasRequiredClothing = false;
    let hasRequiredModel = false;

    // Check if we have required model
    if (uploadedModelFile || selectedModel) {
      hasRequiredModel = true;
    }

    // Check if we have required clothing
    if (selectedClothingType === "Single clothes") {
      if (uploadedImageFile || selectedRecentItem) {
        hasRequiredClothing = true;
      }
    } else {
      // Top & Bottom mode - need at least one piece
      if (
        uploadedTopImageFile ||
        selectedTopItem ||
        uploadedBottomImageFile ||
        selectedBottomItem
      ) {
        hasRequiredClothing = true;
      }
    }

    if (!hasRequiredModel) {
      return {
        isValid: false,
        message: "Please select or upload a model to continue.",
      };
    }

    if (!hasRequiredClothing) {
      return {
        isValid: false,
        message: "Please select or upload clothing items to continue.",
      };
    }

    return { isValid: true, message: "" };
  };

  // Helper function to build API parameters
  const buildApiParams = async (): Promise<ModelImageParams | null> => {
    const params: ModelImageParams = {
      mode: selectedClothingType === "Single clothes" ? "single" : "top_bottom",
    };

    try {
      // Set human image (model selection)
      await setHumanImage(params);

      // Set clothing images based on selection type
      if (selectedClothingType === "Single clothes") {
        await setSingleClothingParams(params);
      } else {
        await setTopBottomClothingParams(params);
      }

      // Final validation
      if (!params.cloth_image) {
        toast.error("Please select at least one clothing item to continue.");
        resetProcessingState();
        return null;
      }

      if (!params.human_image && !params.modelImageId) {
        toast.error("Please select or upload a model to continue.");
        resetProcessingState();
        return null;
      }

      return params;
    } catch (error) {
      console.error("❌ Error building API params:", error);
      toast.error("Error processing selected items. Please try again.");
      resetProcessingState();
      return null;
    }
  };

  // Helper function to set human image
  const setHumanImage = async (params: ModelImageParams): Promise<void> => {
    if (uploadedModelFile) {
      console.log("👤 Using uploaded model file");
      params.human_image = uploadedModelFile;
    } else if (selectedModel) {
      console.log("👤 Converting selected model to file");
      const selectedModelData = MODELS_IMAGES.find(
        (m) => m.id === selectedModel
      );
      if (selectedModelData) {
        const modelFile = await urlToFile(
          selectedModelData.image,
          `model_${selectedModel.replace("Human_model/", "")}.jpg`
        );
        params.human_image = modelFile;
      }
      params.modelImageId = selectedModel.replace("Human_model/", "");
    }
  };

  // Helper function to set single clothing parameters
  const setSingleClothingParams = async (
    params: ModelImageParams
  ): Promise<void> => {
    console.log("selectedCategory in clothing", selectedCategory);
    params.fit_type = selectedFit;
    params.cloth_category = selectedCategory;

    if (uploadedImageFile) {
      params.cloth_image = uploadedImageFile;
      params.garment_type = getGarmentType(selectedCategory);
    } else if (selectedRecentItem) {
      const clothFile = await urlToFile(
        selectedRecentItem.image,
        `cloth_${selectedRecentItem.id}.jpg`
      );
      params.cloth_image = clothFile;
      params.garment_type = getGarmentTypeFromItem(
        selectedCategory || selectedRecentItem.type
      );
    }
  };

  // Helper function to set top & bottom clothing parameters
  const setTopBottomClothingParams = async (
    params: ModelImageParams
  ): Promise<void> => {
    let hasTopClothing = false;
    let hasBottomClothing = false;

    // Handle TOP clothing
    if (uploadedTopImageFile) {
      params.cloth_image = uploadedTopImageFile;
      hasTopClothing = true;
    } else if (selectedTopItem) {
      const topFile = await urlToFile(
        selectedTopItem.image,
        `top_${selectedTopItem.id}.jpg`
      );
      params.cloth_image = topFile;
      hasTopClothing = true;
    }

    // Handle BOTTOM clothing
    if (uploadedBottomImageFile) {
      params.low_cloth_image = uploadedBottomImageFile;
      hasBottomClothing = true;
      if (!hasTopClothing) {
        params.cloth_image = uploadedBottomImageFile;
      }
    } else if (selectedBottomItem) {
      const bottomFile = await urlToFile(
        selectedBottomItem.image,
        `bottom_${selectedBottomItem.id}.jpg`
      );
      params.low_cloth_image = bottomFile;
      hasBottomClothing = true;
      if (!hasTopClothing) {
        params.cloth_image = bottomFile;
      }
    }

    // Set empty garment_type for combination mode
    params.garment_type = "";

    if (!hasTopClothing && !hasBottomClothing) {
      throw new Error(
        "Please select at least one clothing item (top or bottom) to continue."
      );
    }
  };

  // Helper function to get garment type
  const getGarmentType = (category: string): string => {
    console.log("category", category);
    switch (category) {
      case "Top":
        return "upper";
      case "Bottom":
        return "lower";
      case "Dress_Suit":
        return "full_set";
      default:
        return "upper";
    }
  };

  // Helper function to get garment type from item
  const getGarmentTypeFromItem = (type: string): string => {
    console.log("type from item", type);
    switch (type) {
      case "top":
        return "upper";
      case "Top":
        return "upper";
      case "bottom":
        return "lower";
      case "Bottom":
        return "lower";
      default:
        return "full_set";
    }
  };

  // Optimized polling function with proper state management
  const pollJobStatus = async (jobId: string): Promise<void> => {
    setCurrentJobId(jobId);
    setProcessingProgress("Processing your virtual try-on...");
    console.log("🔄 Starting job polling:", jobId);

    let pollCount = 0;
    const maxPollCount = 150; // 5 minutes with 2-second intervals

    const pollInterval = setInterval(async () => {
      try {
        pollCount++;
        console.log(
          `📊 Polling attempt ${pollCount}/${maxPollCount} for job:`,
          jobId
        );

        const jobResponse = await VirtualTryOnService.getJob(jobId);

        if (!jobResponse.success || !jobResponse.data) {
          console.error("❌ Failed to get job status:", jobResponse.message);
          clearInterval(pollInterval);
          handleJobError("Failed to check job status. Please try again.");
          return;
        }

        const { status, resultImagePath, errorMessage } = jobResponse.data;

        // Update progress based on status
        updateProgressMessage(status);

        switch (status) {
          case "COMPLETED":
            if (resultImagePath) {
              clearInterval(pollInterval);
              await handleJobSuccess(resultImagePath);
            } else {
              console.error("❌ Job completed but no result image provided");
              clearInterval(pollInterval);
              handleJobError(
                "Job completed but no result was generated. Please try again."
              );
            }
            break;

          case "FAILED":
            clearInterval(pollInterval);
            console.error("❌ Virtual try-on job failed:", errorMessage);
            handleJobError(
              errorMessage ||
                "Virtual try-on processing failed. Please try again."
            );
            break;

          case "PENDING":
          case "PROCESSING":
            // Continue polling
            if (pollCount >= maxPollCount) {
              clearInterval(pollInterval);
              handleJobTimeout();
            }
            break;

          default:
            console.warn("⚠️ Unknown job status:", status);
            // Continue polling for unknown statuses
            if (pollCount >= maxPollCount) {
              clearInterval(pollInterval);
              handleJobTimeout();
            }
            break;
        }
      } catch (error) {
        console.error("❌ Error polling job status:", error);
        clearInterval(pollInterval);
        handleJobError("Error checking job status. Please refresh the page.");
      }
    }, 2000); // Poll every 2 seconds
  };

  // Helper function to update progress message
  const updateProgressMessage = (status: string): void => {
    switch (status) {
      case "PENDING":
        setProcessingProgress("Waiting in queue...");
        break;
      case "PROCESSING":
        setProcessingProgress("AI is generating your try-on...");
        break;
      case "COMPLETED":
        setProcessingProgress("Finalizing results...");
        break;
      default:
        setProcessingProgress("Processing your virtual try-on...");
        break;
    }
  };

  // Helper function to handle successful job completion
  const handleJobSuccess = async (resultImagePath: string): Promise<void> => {
    console.log("🎉 Job completed successfully!");

    setGeneratedImage(resultImagePath);
    setProcessingProgress("✅ Virtual try-on completed successfully!");

    // Refresh credits after successful completion
    try {
      await refreshCredits();
    } catch (error) {
      console.warn("⚠️ Failed to refresh credits:", error);
    }

    toast.success("Virtual try-on completed successfully!");

    // Reset processing state after showing success message
    setTimeout(() => {
      setIsProcessing(false);
      setCurrentJobId(null);
      setProcessingProgress("");
    }, 2000);
  };

  // Helper function to handle job errors
  const handleJobError = (message: string): void => {
    console.error("❌ Job error:", message);
    toast.error(message);
    resetProcessingState();
  };

  // Helper function to handle job timeout
  const handleJobTimeout = (): void => {
    console.warn("⏰ Job polling timeout");
    toast.error(
      "Processing is taking longer than expected. Please check back later."
    );
    resetProcessingState();
  };

  // Helper function to reset processing state
  const resetProcessingState = (): void => {
    setIsProcessing(false);
    setCurrentJobId(null);
    setProcessingProgress("");
  };

  const handleRecentItemClick = (item: ClothingItem): void => {
    console.log("Selected recent clothing item:", {
      id: item.id,
      name: item.name,
      type: item.type,
      category: item.category,
      image: item.image,
    });

    if (selectedClothingType === "Single clothes") {
      // Single clothes mode
      setSelectedRecentItem(item);
      setUploadedImage(item.image);
      setUploadedImageFile(null);

      // Auto-update category based on item type
      if (item.type === "top") {
        setSelectedCategory("Top");
        toast.success(
          `${item.name || "Top garment"} selected - Category set to Top`
        );
      } else if (item.type === "bottom") {
        setSelectedCategory("Bottom");
        toast.success(
          `${item.name || "Bottom garment"} selected - Category set to Bottom`
        );
      } else if (item.type === "dress" || item.type === "full_set") {
        setSelectedCategory("Dress_Suit");
        toast.success(
          `${
            item.name || "Full set garment"
          } selected - Category set to Dress_Suit`
        );
      } else {
        toast.success(`${item.name || "Clothing item"} selected`);
      }
    } else {
      // Top & Bottom combination mode - Smart auto-assignment
      if (item.type === "top") {
        setSelectedTopItem(item);
        setUploadedTopImage(item.image);
        setUploadedTopImageFile(null);
        toast.success(
          `${item.name || "Top garment"} detected - Added to Top section`
        );
      } else if (item.type === "bottom") {
        setSelectedBottomItem(item);
        setUploadedBottomImage(item.image);
        setUploadedBottomImageFile(null);
        toast.success(
          `${item.name || "Bottom garment"} detected - Added to Bottom section`
        );
      } else if (item.type === "dress" || item.type === "full_set") {
        setSelectedFullSetItem(item);
        setUploadedImage(item.image);
        setUploadedImageFile(null);
        toast.success(
          `${
            item.name || "Full set garment"
          } selected - Category set to Full Set`
        );
      } else {
        // For full_set or dress items, default to top section with warning
        setSelectedTopItem(item);
        setUploadedTopImage(item.image);
        setUploadedTopImageFile(null);
        toast.warning(
          `${
            item.name || "Full set garment"
          } added to Top section - Please verify placement`
        );
      }
    }

    // Clear generated image
    setGeneratedImage(null);
  };

  const handleSeeAllClick = (): void => {
    setIsModalOpen(true);
    setModalTab("all");
  };

  // const handleModalItemClick = (item: ClothingItem): void => {
  //   setSelectedRecentItem(item);
  //   setUploadedImage(null);
  //   setUploadedImageFile(null);
  //   setIsModalOpen(false);
  // };

  // Update the modal item click handler
  const handleModalItemClick = (
    item: ClothingItem,
    targetType?: "single" | "top" | "bottom" | "full_set"
  ): void => {
    console.log("Modal item clicked:", {
      item,
      targetType,
      clothingMode: selectedClothingType,
    });

    if (selectedClothingType === "Single clothes") {
      // Single clothes mode
      setSelectedRecentItem(item);
      setUploadedImage(item.image);
      setUploadedImageFile(null);

      // Auto-update category based on item type
      if (item.type === "top") {
        setSelectedCategory("Top");
        toast.success(
          `${item.name || "Top garment"} selected - Category set to Top`
        );
      } else if (item.type === "bottom") {
        setSelectedCategory("Bottom");
        toast.success(
          `${item.name || "Bottom garment"} selected - Category set to Bottom`
        );
      } else if (item.type === "dress" || item.type === "full_set") {
        setSelectedCategory("Dress_Suit");
        toast.success(
          `${
            item.name || "Full set garment"
          } selected - Category set to Dress/Suit`
        );
      } else {
        toast.success(`${item.name || "Clothing item"} selected`);
      }
    } else {
      // Top & Bottom combination mode
      if (targetType === "top") {
        setSelectedTopItem(item);
        setUploadedTopImage(item.image);
        setUploadedTopImageFile(null);
        toast.success(`${item.name || "Top garment"} added to Top section`);
      } else if (targetType === "bottom") {
        setSelectedBottomItem(item);
        setUploadedBottomImage(item.image);
        setUploadedBottomImageFile(null);
        toast.success(
          `${item.name || "Bottom garment"} added to Bottom section`
        );
      } else {
        // Smart auto-assignment based on item type when no specific target
        if (item.type === "top") {
          setSelectedTopItem(item);
          setUploadedTopImage(item.image);
          setUploadedTopImageFile(null);
          toast.success(
            `${item.name || "Top garment"} detected - Added to Top section`
          );
        } else if (item.type === "bottom") {
          setSelectedBottomItem(item);
          setUploadedBottomImage(item.image);
          setUploadedBottomImageFile(null);
          toast.success(
            `${
              item.name || "Bottom garment"
            } detected - Added to Bottom section`
          );
        } else {
          // Default to top section for unclear items
          setSelectedTopItem(item);
          setUploadedTopImage(item.image);
          setUploadedTopImageFile(null);
          toast.warning(
            `${
              item.name || "Garment"
            } added to Top section - Please verify placement`
          );
        }
      }
    }

    // Clear generated image and close modal
    setGeneratedImage(null);
    setIsModalOpen(false);
  };

  const handleModelSelect = (modelId: string): void => {
    setSelectedModel(modelId);
    setUploadedModelImage(null);
    setUploadedModelFile(null);
  };

  const handleModelFileSelect = (file: File): void => {
    processFile(file, "model");
  };

  const handleClearUploadedModel = (): void => {
    setUploadedModelImage(null);
    setUploadedModelFile(null);
  };

  return (
    <div className="min-h-screen flex items-center justify-center relative">
      <BackgroundElements />

      <div className="relative z-10 w-full max-w-7xl px-6">
        {/* Header */}
        <div className="text-left mb-8">
          <h1 className="text-4xl font-inter font-semibold text-white mb-4">
            Virtual Try On
          </h1>
        </div>

        <div className="flex gap-8 items-center">
          {/* Left Panel - Controls */}
          <div className="w-1/2 max-w-[400px]">
            <div className="bg-white/10 backdrop-blur-sm border border-gray-600 rounded-2xl p-6">
              {/* Clothing Selection */}
              <div className="mb-6">
                <h3 className="text-white text-lg font-medium mb-4">
                  Select Clothes
                </h3>

                {/* Clothing Type Buttons */}
                <div className="flex gap-8 mb-4 justify-between">
                  <ShadowButton
                    onClick={() => setSelectedClothingType("Single clothes")}
                    className={`flex-1 px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                      selectedClothingType === "Single clothes"
                        ? ""
                        : "!bg-gray-700 !text-gray-300 hover:!bg-gray-600"
                    }`}
                  >
                    Single Clothes
                  </ShadowButton>
                  <ShadowButton
                    onClick={() => setSelectedClothingType("Top & Bottom")}
                    className={`flex-1 px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                      selectedClothingType === "Top & Bottom"
                        ? ""
                        : "!bg-gray-700 !text-gray-300 hover:!bg-gray-600"
                    }`}
                  >
                    Top & Bottom
                  </ShadowButton>
                </div>

                {/* Clothing Upload Component */}
                <ClothingUpload
                  selectedClothingType={selectedClothingType}
                  isDragOver={isDragOver}
                  setIsDragOver={setIsDragOver}
                  uploadedImage={uploadedImage}
                  setUploadedImage={setUploadedImage}
                  selectedRecentItem={selectedRecentItem}
                  setSelectedRecentItem={setSelectedRecentItem}
                  isDragOverTop={isDragOverTop}
                  setIsDragOverTop={setIsDragOverTop}
                  isDragOverBottom={isDragOverBottom}
                  setIsDragOverBottom={setIsDragOverBottom}
                  uploadedTopImage={uploadedTopImage}
                  setUploadedTopImage={setUploadedTopImage}
                  uploadedBottomImage={uploadedBottomImage}
                  setUploadedBottomImage={setUploadedBottomImage}
                  onFileSelect={processFile}
                  selectedTopItem={selectedTopItem}
                  setSelectedTopItem={setSelectedTopItem}
                  selectedBottomItem={selectedBottomItem}
                  setSelectedBottomItem={setSelectedBottomItem}
                  onDropdownChange={handleDropdownChange}
                  selectedCategory={selectedCategory}
                  setSelectedCategory={setSelectedCategory}
                />
              </div>

              {/* Recent Items Component */}
              <RecentItems
                items={recentClothingItems.map((item) => ({
                  id: item.id,
                  image: item.imagePath || "",
                  type: item.clothingType?.toLowerCase() || "clothing",
                  name: item.name,
                  category: item.category,
                }))}
                selectedItem={selectedRecentItem}
                onItemClick={handleRecentItemClick}
                onSeeAllClick={handleSeeAllClick}
                isLoading={isLoadingData}
                clothingMode={selectedClothingType}
              />

              {/* Model Selection Component */}
              <ModelSelection
                models={
                  isLoadingData
                    ? MODELS_IMAGES
                    : modelImages.map((model) => ({
                        id: model.id,
                        image: model.imagePath || "",
                        type: model.isDefault ? "admin" : "user",
                      }))
                }
                selectedModel={selectedModel}
                uploadedModelImage={uploadedModelImage}
                onModelSelect={handleModelSelect}
                onFileSelect={handleModelFileSelect}
                onClearUploadedModel={handleClearUploadedModel}
              />

              {/* Generate Button */}
              <ShadowButton
                onClick={handleGenerate}
                disabled={isProcessing}
                className="w-full py-3 font-medium"
              >
                {isProcessing ? "Generating..." : "Generate (1 Credit)"}
              </ShadowButton>
            </div>
          </div>

          {/* Right Panel - Result */}
          <div className="flex-1">
            <ResultPanel
              onReset={handleReset}
              onDownload={handleDownload}
              generatedImage={generatedImage}
              isProcessing={isProcessing}
              processingProgress={processingProgress}
              currentJobId={currentJobId}
            />
          </div>
        </div>
      </div>

      {/* Clothes Modal */}
      <ClothesModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        selectedTab={modalTab}
        onTabChange={setModalTab}
        allItems={recentClothingItems.map((item) => ({
          id: item.id,
          image: item.imagePath || "",
          type: item.clothingType?.toLowerCase() || "clothing",
          name: item.name,
          category: item.category,
        }))}
        topItems={recentClothingItems
          .filter((item) => item.clothingType?.toLowerCase() === "top")
          .map((item) => ({
            id: item.id,
            image: item.imagePath || "",
            type: item.clothingType?.toLowerCase() || "clothing",
            name: item.name,
            category: item.category,
          }))}
        bottomItems={recentClothingItems
          .filter((item) => item.clothingType?.toLowerCase() === "bottom")
          .map((item) => ({
            id: item.id,
            image: item.imagePath || "",
            type: item.clothingType?.toLowerCase() || "clothing",
            name: item.name,
            category: item.category,
          }))}
        selectedItem={selectedRecentItem}
        onItemClick={handleModalItemClick}
        selectedTopItem={selectedTopItem}
        selectedBottomItem={selectedBottomItem}
        selectedFullSetItem={selectedFullSetItem}
        clothingMode={selectedClothingType}
        fullSetItems={recentClothingItems
          .filter((item) => item.clothingType?.toLowerCase() === "full_set")
          .map((item) => ({
            id: item.id,
            image: item.imagePath || "",
            type: item.clothingType?.toLowerCase() || "clothing",
            name: item.name,
            category: item.category,
          }))}
      />
    </div>
  );
};

export default VirtualTryOnPage;
