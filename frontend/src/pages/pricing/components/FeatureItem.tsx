import { Text } from "@/components/ui/text";

interface FeatureItemProps {
  Icon: string;
  text: string;
}

export const FeatureItem = ({ Icon, text }: FeatureItemProps) => {
  return (
    <div className="flex flex-col items-center text-center p-4">
      <div className="xl:w-[100px] xl:h-[100px]  w-20 h-20 rounded-full flex items-center justify-center mb-4">
        <img loading="lazy" src={Icon} className="w-full h-full max-h-16" />
      </div>
      <Text
        variant={"body"}
        className="text-[#D9D9D9] text-xl lg:text-2xl sm:text-xl font-semibold"
      >
        {text}
      </Text>
    </div>
  );
};
