import GradientText from "@/components/common/GradientText";

export const CoreFeature = () => {
  const coreFeatured = [
    {
      feature: "Background Remover",
      options: [
        {
          imageSizeType: "Up to 0.25 MP (e.g., 625×400)",
        },
        {
          creditsPerImage: 0,
        },
      ],
    },
    {
      feature: "Speed Painting",
      options: [
        {
          imageSizeType: "Up to 0.25 MP (e.g., 625×400)",
        },
        {
          creditsPerImage: 5,
        },
      ],
    },
    {
      feature: "Virtual Try On",
      options: [
        {
          imageSizeType: "Up to 0.25 MP (e.g., 625×400)",
        },
        {
          creditsPerImage: 1,
        },
      ],
    },
  ];

  return (
    <>
      <div className="mx-auto rounded-xl overflow-hidden border border-white/15 bg-[#16243D] text-[#D9D9D9] z-10 text-xl font-normal mt-[120px]">
        <div className="grid z-10 grid-cols-3 items-center border border-white/15">
          <div className="py-2 uppercase z-10 px-6 row-span-2">
            <GradientText>Features</GradientText>
          </div>
          <div className="py-2 uppercase z-10 px-6 flex flex-col gap-3">
            <GradientText>Image Size / Type</GradientText>
          </div>
          <div className="py-2 uppercase z-10 px-6 flex flex-col gap-3 text-right">
            <GradientText>Credits / Image</GradientText>
          </div>
        </div>
        {coreFeatured.map((feature, index) => (
          <div
            key={index}
            className="grid grid-cols-3 items-center border border-white/15"
          >
            <div className="py-5 px-6 z-10 row-span-2">{feature.feature}</div>

            {feature.options.map((optional, optIndex) => (
              <div
                key={optIndex}
                className={`py-5 px-6 z-10 flex flex-col gap-3 ${
                  optIndex === 1 ? "text-right" : ""
                }`}
              >
                <span>
                  {optional.imageSizeType ||
                    `${optional.creditsPerImage} Credits`}
                </span>
              </div>
            ))}
          </div>
        ))}
      </div>
    </>
  );
};
