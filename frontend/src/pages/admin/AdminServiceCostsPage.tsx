import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { toast } from "sonner";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { useAdminHeader } from "@/contexts/AdminHeaderContext";
import { Save } from "lucide-react";
import AdminService from "@/services/admin.service";

// Form schema
const serviceCostsSchema = z.object({
  imageGeneration: z.coerce.number().min(0, "Cost must be a positive number"),
  videoGeneration: z.coerce.number().min(0, "Cost must be a positive number"),
  backgroundRemoval: z.coerce.number().min(0, "Cost must be a positive number"),
  virtualTryOn: z.coerce.number().min(0, "Cost must be a positive number"),
  speedpaint: z.coerce.number().min(0, "Cost must be a positive number"),
});

type ServiceCostsFormValues = z.infer<typeof serviceCostsSchema>;

const AdminServiceCostsPage = () => {
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const adminHeader = useAdminHeader();

  const form = useForm<ServiceCostsFormValues>({
    resolver: zodResolver(serviceCostsSchema),
    defaultValues: {
      imageGeneration: 2,
      videoGeneration: 50,
      backgroundRemoval: 1,
      virtualTryOn: 3,
      speedpaint: 5,
    },
  });

  useEffect(() => {
    adminHeader.setHeaderContent({ title: "Service Costs" });

    const fetchServiceCosts = async () => {
      setLoading(true);
      try {
        const costs = await AdminService.getServiceCosts();
        console.log("costs", costs);
        form.reset({
          imageGeneration: costs.imageGeneration ?? 2,
          videoGeneration: costs.videoGeneration ?? 50,
          backgroundRemoval: costs.backgroundRemoval ?? 1,
          virtualTryOn: costs.virtualTryOn ?? 3,
          speedpaint: costs.speedpaint ?? 5,
        });
      } catch (error) {
        console.error("Failed to fetch service costs:", error);
        toast.error("Failed to fetch service costs");
      } finally {
        setLoading(false);
      }
    };

    fetchServiceCosts();
  }, []);

  const onSubmit = async (
    data: ServiceCostsFormValues,
    e?: React.BaseSyntheticEvent
  ) => {
    e?.preventDefault(); // Prevent page reload
    setSaving(true);
    try {
      await AdminService.updateServiceCosts(data);
      toast.success("Service costs updated successfully");
    } catch (error) {
      console.error("Failed to update service costs:", error);
      toast.error("Failed to update service costs");
    } finally {
      setSaving(false);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter") {
      e.preventDefault();
    }
  };

  return (
    <div className="container mx-auto py-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Service Costs</CardTitle>
          <CardDescription>
            Set the credit cost for each service offered by Miragic-AI.
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="text-center py-4">Loading service costs...</div>
          ) : (
            <Form {...form}>
              <form
                onSubmit={form.handleSubmit(onSubmit)}
                className="space-y-6"
              >
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <FormField
                    control={form.control}
                    name="imageGeneration"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          Image Generation (credits per image)
                        </FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            min="0"
                            step="0.1"
                            {...field}
                            onKeyDown={handleKeyDown}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="videoGeneration"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          Video Generation (credits per minute)
                        </FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            min="0"
                            step="0.1"
                            {...field}
                            onKeyDown={handleKeyDown}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="backgroundRemoval"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          Background Removal (credits per image)
                        </FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            min="0"
                            step="0.1"
                            {...field}
                            onKeyDown={handleKeyDown}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="virtualTryOn"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Virtual Try-On (credits per use)</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            min="0"
                            step="0.1"
                            {...field}
                            onKeyDown={handleKeyDown}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="speedpaint"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          Speedpaint (credits per generation)
                        </FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            min="0"
                            step="0.1"
                            {...field}
                            onKeyDown={handleKeyDown}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div className="flex justify-end">
                  <Button type="submit" disabled={saving}>
                    {saving ? (
                      "Saving..."
                    ) : (
                      <>
                        <Save className="mr-2 h-4 w-4" /> Save Changes
                      </>
                    )}
                  </Button>
                </div>
              </form>
            </Form>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default AdminServiceCostsPage;
