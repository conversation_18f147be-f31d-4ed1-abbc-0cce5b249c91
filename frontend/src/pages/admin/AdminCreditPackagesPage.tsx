import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { toast } from "sonner";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Switch } from "@/components/ui/switch";
import { useAdminHeader } from "@/contexts/AdminHeaderContext";

import { Edit, Plus, Trash2 } from "lucide-react";
import AdminService from "@/services/admin.service";
import { type CreditPackage } from "@/types/admin.types";
import { Textarea } from "@/components/ui/textarea";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";

// Form schema
const creditPackageSchema = z.object({
  name: z.string().min(2, "Name must be at least 2 characters").max(50),
  description: z.string().optional(),
  creditsAmount: z.coerce.number().positive("Credits amount must be positive"),
  price: z.coerce.number().positive("Price must be positive"),
  currency: z.enum(["USD", "EUR", "GBP"]),
  stripePriceId: z.string().optional(),
  paypalPlanId: z.string().optional(),
  isActive: z.boolean(),
});

type CreditPackageFormValues = z.infer<typeof creditPackageSchema>;

const CreditPackageForm: React.FC<{
  onSubmit: (data: CreditPackageFormValues) => Promise<void>;
  onClose: () => void;
  defaultValues?: CreditPackageFormValues;
  isEdit?: boolean;
}> = ({ onSubmit, defaultValues, isEdit, onClose }) => {
  const form = useForm<CreditPackageFormValues>({
    resolver: zodResolver(creditPackageSchema),
    defaultValues: defaultValues || {
      name: "",
      description: "",
      creditsAmount: 0,
      price: 0,
      currency: "USD",
      isActive: true,
    },
  });

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Package Name</FormLabel>
              <FormControl>
                <Input placeholder="e.g. Small Package" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="description"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Description</FormLabel>
              <FormControl>
                <Textarea
                  placeholder="Package description"
                  {...field}
                  value={field.value || ""}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name="creditsAmount"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Credits Amount</FormLabel>
                <FormControl>
                  <Input type="number" min="1" step="1" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="price"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Price</FormLabel>
                <FormControl>
                  <Input type="number" min="0" step="0.01" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <FormField
          control={form.control}
          name="currency"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Currency</FormLabel>
              <Select onValueChange={field.onChange} defaultValue={field.value}>
                <FormControl>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {["USD", "EUR", "GBP"].map((currency) => (
                    <SelectItem key={currency} value={currency}>
                      {currency}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="stripePriceId"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Stripe Price ID (Optional)</FormLabel>
              <FormControl>
                <Input
                  placeholder="price_..."
                  {...field}
                  value={field.value || ""}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="paypalPlanId"
          render={({ field }) => (
            <FormItem>
              <FormLabel>PayPal Plan ID (Optional)</FormLabel>
              <FormControl>
                <Input
                  placeholder="P-..."
                  {...field}
                  value={field.value || ""}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="isActive"
          render={({ field }) => (
            <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3 shadow-sm">
              <div className="space-y-0.5">
                <FormLabel>Active</FormLabel>
                <FormDescription>
                  Make this package available for purchase
                </FormDescription>
              </div>
              <FormControl>
                <Switch
                  checked={field.value}
                  onCheckedChange={field.onChange}
                />
              </FormControl>
            </FormItem>
          )}
        />

        <div className="flex justify-end space-x-2 pt-4">
          <Button variant="outline" type="button" onClick={onClose}>
            Cancel
          </Button>
          <Button type="submit">
            {isEdit ? "Update Package" : "Create Package"}
          </Button>
        </div>
      </form>
    </Form>
  );
};

const AdminCreditPackagesPage = () => {
  const [packages, setPackages] = useState<CreditPackage[]>([]);
  const [loading, setLoading] = useState(true);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [selectedPackage, setSelectedPackage] = useState<CreditPackage | null>(
    null
  );
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);

  const adminHeader = useAdminHeader();
  const fetchCreditPackages = async () => {
    setLoading(true);
    try {
      const packages = await AdminService.getCreditPackages();
      setPackages(packages);
    } catch (error) {
      console.error("Failed to fetch credit packages:", error);
      toast.error("Failed to fetch credit packages");
    } finally {
      setLoading(false);
    }
  };
  useEffect(() => {
    adminHeader.setHeaderContent({ title: "Credit Packages" });
    fetchCreditPackages();
  }, []);

  const handleCreatePackage = async (data: CreditPackageFormValues) => {
    try {
      await AdminService.createCreditPackage(data);
      toast.success("Credit package created successfully");
      setIsCreateDialogOpen(false);
      fetchCreditPackages();
    } catch (error) {
      console.error("Failed to create credit package:", error);
      toast.error("Failed to create credit package");
    }
  };

  const handleEditPackage = (pkg: CreditPackage) => {
    // Keep the original package in state to preserve the id field
    setSelectedPackage(pkg);
    setIsEditDialogOpen(true);
  };

  const handleUpdatePackage = async (data: CreditPackageFormValues) => {
    if (!selectedPackage) return;

    try {
      await AdminService.updateCreditPackage(selectedPackage.id, data);
      toast.success("Credit package updated successfully");
      setIsEditDialogOpen(false);
      fetchCreditPackages();
    } catch (error) {
      console.error("Failed to update credit package:", error);
      toast.error("Failed to update credit package");
    }
  };

  const handleDeleteClick = (pkg: CreditPackage) => {
    setSelectedPackage(pkg);
    setIsDeleteDialogOpen(true);
  };

  const handleDeletePackage = async () => {
    if (!selectedPackage) return;

    try {
      await AdminService.deleteCreditPackage(selectedPackage.id);
      toast.success("Credit package deleted successfully");
      setIsDeleteDialogOpen(false);
      fetchCreditPackages();
    } catch (error) {
      console.error("Failed to delete credit package:", error);
      toast.error("Failed to delete credit package");
    }
  };

  const formatPrice = (price: number, currency: string) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency,
      minimumFractionDigits: 0,
      maximumFractionDigits: 2,
    }).format(price);
  };

  const calculatePricePerCredit = (price: number, credits: number) => {
    if (credits === 0) return "N/A";
    const pricePerCredit = price / credits;
    return pricePerCredit.toFixed(4);
  };

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">Credit Packages</h1>
        <Button onClick={() => setIsCreateDialogOpen(true)}>
          <Plus className="mr-2 h-4 w-4" /> Add Package
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Manage Credit Packages</CardTitle>
          <CardDescription>
            Create and manage credit packages that users can purchase to top up
            their credits.
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="text-center py-4">Loading packages...</div>
          ) : packages.length === 0 ? (
            <div className="text-center py-4">
              No credit packages found. Create your first package.
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Name</TableHead>
                  <TableHead>Credits</TableHead>
                  <TableHead>Price</TableHead>
                  <TableHead>Price per Credit</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {packages.map((pkg) => (
                  <TableRow key={pkg.id}>
                    <TableCell className="font-medium">{pkg.name}</TableCell>
                    <TableCell>{pkg.creditsAmount}</TableCell>
                    <TableCell>
                      {formatPrice(pkg.price, pkg.currency)}
                    </TableCell>
                    <TableCell>
                      {pkg.currency}{" "}
                      {calculatePricePerCredit(pkg.price, pkg.creditsAmount)}
                    </TableCell>
                    <TableCell>
                      <span
                        className={`px-2 py-1 rounded-full text-xs ${
                          pkg.isActive
                            ? "bg-green-100 text-green-800"
                            : "bg-gray-100 text-gray-800"
                        }`}
                      >
                        {pkg.isActive ? "Active" : "Inactive"}
                      </span>
                    </TableCell>
                    <TableCell className="text-right">
                      <div className="flex justify-end space-x-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleEditPackage(pkg)}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleDeleteClick(pkg)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>

      {/* Create Dialog */}
      <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Create Credit Package</DialogTitle>
            <DialogDescription>
              Add a new credit package that users can purchase.
            </DialogDescription>
          </DialogHeader>
          <CreditPackageForm
            onSubmit={handleCreatePackage}
            onClose={() => setIsCreateDialogOpen(false)}
          />
        </DialogContent>
      </Dialog>

      {/* Edit Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Edit Credit Package</DialogTitle>
            <DialogDescription>
              Update the details of this credit package.
            </DialogDescription>
          </DialogHeader>
          {selectedPackage && (
            <CreditPackageForm
              onSubmit={handleUpdatePackage}
              onClose={() => setIsEditDialogOpen(false)}
              defaultValues={{
                name: selectedPackage.name,
                description: selectedPackage.description || "",
                creditsAmount: selectedPackage.creditsAmount,
                price: selectedPackage.price,
                currency:
                  selectedPackage.currency === "USD" ||
                  selectedPackage.currency === "EUR" ||
                  selectedPackage.currency === "GBP"
                    ? (selectedPackage.currency as "USD" | "EUR" | "GBP")
                    : "USD",
                stripePriceId: selectedPackage.stripePriceId,
                paypalPlanId: selectedPackage.paypalPlanId,
                isActive: selectedPackage.isActive,
              }}
              isEdit
            />
          )}
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <AlertDialog
        open={isDeleteDialogOpen}
        onOpenChange={setIsDeleteDialogOpen}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This will permanently delete the credit package "
              {selectedPackage?.name}". This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleDeletePackage}>
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};

export default AdminCreditPackagesPage;
