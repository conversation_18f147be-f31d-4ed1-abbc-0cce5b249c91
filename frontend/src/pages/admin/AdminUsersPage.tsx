import React, { useEffect, useState, useCallback } from "react";
import ShadowButton from "@/components/ui/shadowButton";
import { useAdminHeader } from "@/contexts/AdminHeaderContext";
import { But<PERSON> } from "@/components/ui/button";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import {
  Trash2,
  ChevronLeft,
  ChevronRight,
  ChevronDown,
  Mail,
  Send,
  X,
} from "lucide-react";
import { Input } from "@/components/ui/input";
import AdminService from "@/services/admin.service";
import { toast } from "sonner";
import { Spinner } from "@/components/ui/spinner";
import { formatDateToYMD } from "@/lib/utils";
import type {
  SubscriptionPlan,
  User,
  User<PERSON>ist<PERSON>ara<PERSON>,
  User<PERSON>tatus,
} from "@/types/admin.types";
import { UPLOAD_URL } from "@/services/api.service";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";

// Type definitions
type TabType = "all" | "active" | "inactive";

interface TabItem {
  id: TabType;
  label: string;
}

// User type is now imported from admin.types.ts

interface DialogState {
  open: boolean;
  mode: "edit" | "view" | "delete";
  user: User | null;
}

// Mock formatCurrency function
const formatCurrency = (value: string | number, currency: string): string =>
  `${currency}${parseFloat(value.toString()).toFixed(2)}`;

// Remove mock data - fetch everything from backend

const AdminUsersPage: React.FC = () => {
  const { setHeaderContent } = useAdminHeader();
  const [activeTab, setActiveTab] = useState<TabType>("all");
  const [selectedPlan, setSelectedPlan] = useState<string>("all");
  const [searchQuery, setSearchQuery] = useState<string>("");
  const [dialogState, setDialogState] = useState<DialogState>({
    open: false,
    mode: "view",
    user: null,
  });
  const [users, setUsers] = useState<User[]>([]);
  const [plans, setPlans] = useState<SubscriptionPlan[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [pagination, setPagination] = useState({
    total: 0,
    totalPages: 0,
    hasNextPage: false,
    hasPrevPage: false,
  });
  const [searchDebounce, setSearchDebounce] = useState("");
  const [selectedUsers, setSelectedUsers] = useState<string[]>([]);
  const [bulkActionLoading, setBulkActionLoading] = useState(false);
  const [planDropdownOpen, setPlanDropdownOpen] = useState(false);
  const [emailModalOpen, setEmailModalOpen] = useState(false);
  const [emailData, setEmailData] = useState({
    subject: "",
    message: "",
    template: "welcome",
  });
  const [sendingEmail, setSendingEmail] = useState(false);
  const usersPerPage = 10;

  const tabs: TabItem[] = [
    { id: "all", label: "All Users" },
    { id: "active", label: "Active" },
    { id: "inactive", label: "Inactive" },
  ];

  // const plans: PlanItem[] = [
  //   { id: "all", label: "All Plans" },
  //   { id: "free", label: "Free" },
  //   { id: "starter", label: "Starter" },
  //   { id: "pro", label: "Pro" },
  //   { id: "business", label: "Business" },
  // ];

  // Debounce search query
  useEffect(() => {
    const timer = setTimeout(() => {
      setSearchDebounce(searchQuery);
    }, 500);

    return () => clearTimeout(timer);
  }, [searchQuery]);

  // Fetch users from API
  const fetchUsers = useCallback(async () => {
    try {
      setIsLoading(true);
      const params: UserListParams = {
        page: currentPage.toString(),
        limit: usersPerPage.toString(),
        search: searchDebounce || undefined,
        status: activeTab === "all" ? "all" : (activeTab as UserStatus),
        role: "all",
        sortBy: "createdAt",
        sortOrder: "desc",
      };

      const response = await AdminService.getUsers(params);
      setUsers(response.users);
      setPagination(response.pagination);
      setError("");
    } catch (err) {
      console.error("Failed to fetch users:", err);
      setError("Failed to load users. Please try again.");
      toast.error("Failed to load users");
      setUsers([]);
      setPagination({
        total: 0,
        totalPages: 0,
        hasNextPage: false,
        hasPrevPage: false,
      });
    } finally {
      setIsLoading(false);
    }
  }, [currentPage, usersPerPage, searchDebounce, activeTab]);

  const allPlans = async () => {
    try {
      const response = await AdminService.getSubscriptionPlans();
      setPlans(response);
      return response;
    } catch (err) {
      console.error("Failed to fetch plans:", err);
      toast.error("Failed to load plans");
      return [];
    }
  };

  useEffect(() => {
    fetchUsers();
    allPlans();
  }, [fetchUsers]);
  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element;
      if (planDropdownOpen && !target.closest(".plan-dropdown")) {
        setPlanDropdownOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [planDropdownOpen]);

  // Set header
  useEffect(() => {
    setHeaderContent({
      title: "User Management",
      description:
        "Manage your users, view their activity and adjust their plans",
      content: null,
    });
    return () => setHeaderContent({});
  }, [setHeaderContent]);

  const handleTabChange = (tabId: TabType): void => {
    setActiveTab(tabId);
    setCurrentPage(1);
  };

  const handlePlanChange = (planId: string): void => {
    setSelectedPlan(planId);
    setCurrentPage(1);
  };

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>): void => {
    setSearchQuery(e.target.value);
    setCurrentPage(1);
  };

  const handleDeleteUser = async (id: string): Promise<void> => {
    try {
      setIsLoading(true);
      await AdminService.deleteUser(id);
      // Refetch users to ensure consistency
      await fetchUsers();
      toast.success("User deleted successfully");
      setDialogState({ open: false, mode: "view", user: null });
    } catch (err) {
      console.error("Failed to delete user:", err);
      const errorMessage =
        err instanceof Error ? err.message : "Failed to delete user";
      toast.error(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const handleEditUser = async (updatedUser: User): Promise<void> => {
    try {
      setIsLoading(true);
      // Transform updatedUser to match UserUpdateData type
      const userData = {
        role: updatedUser.role,
        emailVerified: updatedUser.emailVerified,
        profile: updatedUser.profile
          ? {
              firstName: updatedUser.profile.firstName,
              lastName: updatedUser.profile.lastName,
              avatarUrl: updatedUser.profile.avatarUrl,
            }
          : undefined,
        credit: updatedUser.credit
          ? {
              balance: updatedUser.credit.balance,
            }
          : undefined,
      };

      const updated = await AdminService.updateUser(updatedUser.id, userData);

      // Update the user in the local state
      setUsers(users.map((u) => (u.id === updated.id ? updated : u)));
      toast.success("User updated successfully");
      setDialogState({ open: false, mode: "view", user: null });
    } catch (err) {
      console.error("Failed to update user:", err);
      const errorMessage =
        err instanceof Error ? err.message : "Failed to update user";
      toast.error(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  // Filter users by plan (client-side for now, can be moved to server-side later)
  const filteredUsers = users.filter((user) => {
    const matchesPlan =
      selectedPlan === "all" ||
      user.plan === selectedPlan ||
      plans.find((p) => p.id === selectedPlan)?.name === user.plan;
    return matchesPlan;
  });

  const handlePageChange = (pageNumber: number) => {
    setCurrentPage(pageNumber);
  };

  // Bulk operations
  const handleSelectAll = () => {
    if (selectedUsers.length === filteredUsers.length) {
      setSelectedUsers([]);
    } else {
      setSelectedUsers(filteredUsers.map((user) => user.id));
    }
  };

  const handleSelectUser = (userId: string) => {
    setSelectedUsers((prev) =>
      prev.includes(userId)
        ? prev.filter((id) => id !== userId)
        : [...prev, userId]
    );
  };

  const handleBulkDelete = async () => {
    if (selectedUsers.length === 0) return;

    if (
      !window.confirm(
        `Are you sure you want to delete ${selectedUsers.length} users? This action cannot be undone.`
      )
    ) {
      return;
    }

    try {
      setBulkActionLoading(true);
      // Delete users one by one (could be optimized with a bulk delete API)
      for (const userId of selectedUsers) {
        await AdminService.deleteUser(userId);
      }
      await fetchUsers();
      setSelectedUsers([]);
      toast.success(`${selectedUsers.length} users deleted successfully`);
    } catch (err) {
      console.error("Failed to delete users:", err);
      const errorMessage =
        err instanceof Error ? err.message : "Failed to delete users";
      toast.error(errorMessage);
    } finally {
      setBulkActionLoading(false);
    }
  };

  const handleBulkStatusChange = async (status: "active" | "inactive") => {
    if (selectedUsers.length === 0) return;

    try {
      setBulkActionLoading(true);
      // Update users one by one (could be optimized with a bulk update API)
      for (const userId of selectedUsers) {
        await AdminService.updateUser(userId, {
          emailVerified: status === "active",
        });
      }
      await fetchUsers();
      setSelectedUsers([]);
      toast.success(`${selectedUsers.length} users updated successfully`);
    } catch (err) {
      console.error("Failed to update users:", err);
      const errorMessage =
        err instanceof Error ? err.message : "Failed to update users";
      toast.error(errorMessage);
    } finally {
      setBulkActionLoading(false);
    }
  };

  const handleCreditAdjustment = async (userId: string, adjustment: number) => {
    try {
      const user = users.find((u) => u.id === userId);
      if (!user) return;

      const newBalance = (user.credit?.balance || 0) + adjustment;
      if (newBalance < 0) {
        toast.error("Credit balance cannot be negative");
        return;
      }

      await AdminService.updateUser(userId, {
        credit: { balance: newBalance },
      });

      await fetchUsers();
      toast.success(
        `Credits ${adjustment > 0 ? "added" : "deducted"} successfully`
      );
    } catch (err) {
      console.error("Failed to adjust credits:", err);
      const errorMessage =
        err instanceof Error ? err.message : "Failed to adjust credits";
      toast.error(errorMessage);
    }
  };

  // Email functionality
  const handleSendEmail = async () => {
    if (!dialogState.user) return;

    try {
      setSendingEmail(true);

      // Create email payload
      const emailPayload = {
        to: dialogState.user.email,
        subject:
          emailData.subject ||
          `Hello ${
            dialogState.user.name || dialogState.user.email.split("@")[0]
          }`,
        message:
          emailData.message ||
          getTemplateMessage(emailData.template, dialogState.user),
        template: emailData.template,
        userId: dialogState.user.id,
      };

      // Here you would call your email API
      // await AdminService.sendEmail(emailPayload);
      console.log("Email payload:", emailPayload);
      await AdminService.sendEmail(emailPayload);
      // For now, simulate API call
      await new Promise((resolve) => setTimeout(resolve, 2000));

      toast.success(`Email sent successfully to ${dialogState.user.email}`);
      setEmailModalOpen(false);
      setEmailData({ subject: "", message: "", template: "welcome" });
    } catch (err) {
      console.error("Failed to send email:", err);
      const errorMessage =
        err instanceof Error ? err.message : "Failed to send email";
      toast.error(errorMessage);
    } finally {
      setSendingEmail(false);
    }
  };

  const getTemplateMessage = (template: string, user: User): string => {
    const userName = user.name || user.email.split("@")[0];

    switch (template) {
      case "welcome":
        return `Hi ${userName},\n\nWelcome to our platform! We're excited to have you on board.\n\nBest regards,\nThe Team`;
      case "reminder":
        return `Hi ${userName},\n\nThis is a friendly reminder about your account.\n\nBest regards,\nThe Team`;
      case "promotion":
        return `Hi ${userName},\n\nWe have an exciting promotion just for you! Check out our latest offers.\n\nBest regards,\nThe Team`;
      case "custom":
        return "";
      default:
        return `Hi ${userName},\n\nThank you for being a valued member of our platform.\n\nBest regards,\nThe Team`;
    }
  };

  return (
    <div className="min-h-screen text-white">
      <div className="p-2">
        {/* Navigation and Controls */}
        <div className="flex items-center justify-between mb-8 pb-5 border-b-2 border-gray-500">
          {/* Tabs */}
          <div className="flex gap-10 space-x-1 pb-1 p-2">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => handleTabChange(tab.id)}
                className={`text-sm font-medium rounded-lg cursor-pointer transition-colors ${
                  activeTab === tab.id
                    ? "text-white"
                    : "text-gray-400 hover:text-purple-500"
                }`}
                type="button"
              >
                {tab.label}
              </button>
            ))}
          </div>

          {/* Plan Filter, Search, and Controls */}
          <div className="flex items-center space-x-4">
            {/* Plan Filter Dropdown */}
            <div className="relative plan-dropdown">
              <button
                onClick={() => setPlanDropdownOpen(!planDropdownOpen)}
                className="flex items-center justify-between px-4 py-2 text-sm font-medium rounded-lg transition-colors border border-gray-600 text-gray-300 hover:bg-gray-700 hover:text-white bg-white/5 min-w-[140px]"
                type="button"
              >
                <span>
                  {selectedPlan === "all"
                    ? "All Plans"
                    : plans.find((p) => p.id === selectedPlan)?.name ||
                      "All Plans"}
                </span>
                <ChevronDown
                  className={`ml-2 h-4 w-4 transition-transform ${
                    planDropdownOpen ? "rotate-180" : ""
                  }`}
                />
              </button>

              {planDropdownOpen && (
                <div className="absolute top-full left-0 mt-1 w-full bg-gray-800 border border-gray-600 rounded-lg shadow-lg z-50 overflow-hidden">
                  {/* All Plans Option */}
                  <button
                    onClick={() => {
                      handlePlanChange("all");
                      setPlanDropdownOpen(false);
                    }}
                    className={`w-full px-4 py-2 text-sm text-left transition-colors hover:bg-gray-700 ${
                      selectedPlan === "all"
                        ? "bg-purple-600/20 text-purple-400"
                        : "text-gray-300"
                    }`}
                    type="button"
                  >
                    All Plans
                  </button>

                  {/* Individual Plans */}
                  {plans?.map((plan) => (
                    <button
                      key={plan.id}
                      onClick={() => {
                        handlePlanChange(plan.id);
                        setPlanDropdownOpen(false);
                      }}
                      className={`w-full px-4 py-2 text-sm text-left transition-colors hover:bg-gray-700 ${
                        selectedPlan === plan.id
                          ? "bg-purple-600/20 text-purple-400"
                          : "text-gray-300"
                      }`}
                      type="button"
                    >
                      {plan.name}
                    </button>
                  ))}
                </div>
              )}
            </div>

            {/* Search Box */}
            <div className="relative">
              <input
                type="text"
                placeholder="Search users..."
                value={searchQuery}
                onChange={handleSearchChange}
                className="px-4 py-2 text-sm bg-white/5 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent w-64"
              />
            </div>
          </div>
        </div>

        {/* Error Message */}
        {error && (
          <div className="bg-red-500/10 text-red-500 text-sm p-3 rounded-md mb-4">
            {error}
          </div>
        )}

        {/* Users Section */}
        <div className="bg-white/5 rounded-lg p-6">
          <div className="mb-4">
            <h2 className="text-2xl font-semibold text-white mb-2">Users</h2>
            <div className="flex items-center justify-between">
              <p className="text-gray-400 text-sm">
                Showing {filteredUsers.length} of {pagination.total} users
              </p>

              {/* Bulk Actions */}
              {selectedUsers.length > 0 && (
                <div className="flex items-center gap-2">
                  <span className="text-sm text-gray-400">
                    {selectedUsers.length} selected
                  </span>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => handleBulkStatusChange("active")}
                    disabled={bulkActionLoading}
                    className="bg-transparent border border-green-500 hover:bg-green-500/20 text-green-500"
                  >
                    Activate
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => handleBulkStatusChange("inactive")}
                    disabled={bulkActionLoading}
                    className="bg-transparent border border-yellow-500 hover:bg-yellow-500/20 text-yellow-500"
                  >
                    Deactivate
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={handleBulkDelete}
                    disabled={bulkActionLoading}
                    className="bg-transparent border border-red-500 hover:bg-red-500/20 text-red-500"
                  >
                    Delete
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => setSelectedUsers([])}
                    disabled={bulkActionLoading}
                  >
                    Clear
                  </Button>
                </div>
              )}
            </div>
          </div>

          {/* Table */}
          <div className="overflow-x-auto">
            <div className="rounded-lg overflow-hidden">
              <div className="relative w-full overflow-x-auto">
                {isLoading && users.length === 0 ? (
                  <div className="flex justify-center items-center py-8">
                    <div className="text-center">
                      <Spinner className="h-8 w-8 mx-auto mb-4" />
                      <p className="text-gray-400">Loading users...</p>
                    </div>
                  </div>
                ) : error && users.length === 0 ? (
                  <div className="flex justify-center items-center py-8">
                    <div className="text-center">
                      <p className="text-red-500 mb-4">{error}</p>
                      <Button
                        onClick={fetchUsers}
                        variant="outline"
                        disabled={isLoading}
                      >
                        {isLoading ? "Retrying..." : "Try Again"}
                      </Button>
                    </div>
                  </div>
                ) : (
                  <table className="w-full caption-bottom text-sm">
                    <thead>
                      <tr className="">
                        <th className="h-12 px-4 text-left align-middle font-semibold text-gray-200 w-12">
                          <input
                            type="checkbox"
                            checked={
                              selectedUsers.length === filteredUsers.length &&
                              filteredUsers.length > 0
                            }
                            onChange={handleSelectAll}
                            className="rounded border-gray-600 bg-transparent"
                          />
                        </th>
                        {[
                          "User",
                          "Email",
                          "Plan",
                          "Status",
                          "Join Date",
                          "Credits",
                          "Total Spent",
                          "Actions",
                        ].map((head) => (
                          <th
                            key={head}
                            className="h-12 px-4 text-left align-middle font-semibold text-gray-200 whitespace-nowrap"
                          >
                            {head}
                          </th>
                        ))}
                      </tr>
                      <tr className="relative">
                        <td
                          colSpan={8}
                          className="absolute bottom-0 h-0 pointer-events-none"
                          style={{
                            left: "10%",
                            right: "10%",
                            background:
                              "linear-gradient(to right, transparent, #e5e7eb 30%, #e5e7eb 60%, transparent)",
                            height: "2px",
                          }}
                        />
                      </tr>
                    </thead>
                    <tbody>
                      {filteredUsers.length > 0 ? (
                        filteredUsers.map((user, index) => (
                          <tr
                            key={user.id}
                            className="transition-colors relative text-gray-200"
                          >
                            {/* Checkbox */}
                            <td className="p-4 align-middle whitespace-nowrap">
                              <input
                                type="checkbox"
                                checked={selectedUsers.includes(user.id)}
                                onChange={(e) => {
                                  e.stopPropagation();
                                  handleSelectUser(user.id);
                                }}
                                className="rounded border-gray-600 bg-transparent"
                              />
                            </td>

                            {/* User Info */}
                            <td
                              className="p-4 align-middle whitespace-nowrap font-medium cursor-pointer"
                              onClick={() =>
                                setDialogState({
                                  open: true,
                                  mode: "view",
                                  user,
                                })
                              }
                            >
                              <div className="flex items-center gap-3">
                                <img
                                  src={
                                    user.profile?.avatarUrl ||
                                    "/png/avatar1.png"
                                  }
                                  className="w-[40px] h-[40px] rounded-full object-cover"
                                  alt={user.name}
                                />
                                <div>
                                  <div className="font-medium text-white">
                                    {user.name ||
                                      `${user.profile?.firstName || ""} ${
                                        user.profile?.lastName || ""
                                      }`.trim() ||
                                      user.email.split("@")[0]}
                                  </div>
                                </div>
                              </div>
                            </td>

                            {/* Email */}
                            <td
                              className="p-4 align-middle whitespace-nowrap cursor-pointer text-gray-300"
                              onClick={() =>
                                setDialogState({
                                  open: true,
                                  mode: "view",
                                  user,
                                })
                              }
                            >
                              {user.email}
                            </td>

                            {/* Plan */}
                            <td
                              className="p-4 align-middle whitespace-nowrap cursor-pointer"
                              onClick={() =>
                                setDialogState({
                                  open: true,
                                  mode: "view",
                                  user,
                                })
                              }
                            >
                              <span className="px-3 py-1 rounded-full text-xs font-medium bg-blue-600/20 text-blue-400 border border-blue-600/30">
                                {user.plan}
                              </span>
                            </td>

                            {/* Status */}
                            <td
                              className="p-4 align-middle whitespace-nowrap cursor-pointer"
                              onClick={() =>
                                setDialogState({
                                  open: true,
                                  mode: "view",
                                  user,
                                })
                              }
                            >
                              <span
                                className={`px-3 py-1 rounded-full text-xs font-medium ${
                                  user.status === "active"
                                    ? "bg-green-600/20 text-green-400 border border-green-600/30"
                                    : "bg-red-600/20 text-red-400 border border-red-600/30"
                                }`}
                              >
                                {user?.status?.charAt(0).toUpperCase() +
                                  user?.status?.slice(1)}
                              </span>
                            </td>

                            {/* Join Date */}
                            <td
                              className="p-4 align-middle whitespace-nowrap cursor-pointer text-gray-300"
                              onClick={() =>
                                setDialogState({
                                  open: true,
                                  mode: "view",
                                  user,
                                })
                              }
                            >
                              {user.joinDate
                                ? user.joinDate
                                : user.createdAt
                                ? formatDateToYMD(user.createdAt)
                                : "N/A"}
                            </td>

                            {/* Credits */}
                            <td
                              className="p-4 align-middle whitespace-nowrap cursor-pointer"
                              onClick={() =>
                                setDialogState({
                                  open: true,
                                  mode: "view",
                                  user,
                                })
                              }
                            >
                              <span className="px-3 py-1 rounded-full text-xs font-medium bg-purple-600/20 text-purple-400 border border-purple-600/30">
                                {user.credits ?? user.credit?.balance ?? 0}
                              </span>
                            </td>

                            {/* Total Spent */}
                            <td
                              className="p-4 align-middle whitespace-nowrap cursor-pointer text-gray-300"
                              onClick={() =>
                                setDialogState({
                                  open: true,
                                  mode: "view",
                                  user,
                                })
                              }
                            >
                              {formatCurrency(
                                user.totalSpent ?? user.credit?.spent ?? 0,
                                "$"
                              )}
                            </td>
                            <td className="p-4 align-middle whitespace-nowrap">
                              <div className="flex items-center gap-2">
                                <Button
                                  variant="outline"
                                  size="sm"
                                  className="bg-transparent border border-gray-500 hover:bg-gray-400"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    setDialogState({
                                      open: true,
                                      mode: "edit",
                                      user,
                                    });
                                  }}
                                >
                                  Edit
                                </Button>
                                <AlertDialog>
                                  <AlertDialogTrigger asChild>
                                    <Button
                                      className="bg-transparent border border-gray-500 hover:bg-gray-400"
                                      size="sm"
                                      onClick={(e) => e.stopPropagation()}
                                    >
                                      <Trash2 />
                                    </Button>
                                  </AlertDialogTrigger>
                                  <AlertDialogContent className="bg-primary/90">
                                    <AlertDialogHeader>
                                      <AlertDialogTitle>
                                        Confirm Deletion
                                      </AlertDialogTitle>
                                      <AlertDialogDescription>
                                        This action is permanent and will delete
                                        the user "
                                        {user.name ||
                                          `${user.profile?.firstName} ${user.profile?.lastName}`}
                                        ".
                                      </AlertDialogDescription>
                                    </AlertDialogHeader>
                                    <AlertDialogFooter>
                                      <AlertDialogCancel className="hover:bg-gray-400 cursor-pointer">
                                        Cancel
                                      </AlertDialogCancel>
                                      <AlertDialogAction
                                        onClick={() =>
                                          handleDeleteUser(user.id)
                                        }
                                        className={`p-2 px-5 cursor-pointer border border-gray-500 rounded-md transition-all hover:border-gray-400 bg-purple-600 hover:bg-purple-700 !text-white flex justify-center items-center`}
                                        style={{
                                          boxShadow:
                                            "inset 0 0 10px rgba(255, 255, 255, 0.6)",
                                        }}
                                      >
                                        Delete
                                      </AlertDialogAction>
                                    </AlertDialogFooter>
                                  </AlertDialogContent>
                                </AlertDialog>
                              </div>
                            </td>
                            {index !== filteredUsers.length - 1 && (
                              <td
                                className="absolute bottom-0 h-0 pointer-events-none"
                                style={{
                                  left: "10%",
                                  right: "10%",
                                  background:
                                    "linear-gradient(to right, transparent, #e5e7eb 30%, #e5e7eb 60%, transparent)",
                                  height: "2px",
                                }}
                              />
                            )}
                          </tr>
                        ))
                      ) : (
                        <tr>
                          <td
                            colSpan={8}
                            className="p-12 text-center text-gray-400"
                          >
                            No users found
                          </td>
                        </tr>
                      )}
                    </tbody>
                  </table>
                )}
              </div>
            </div>
          </div>

          {/* Pagination */}
          {pagination.totalPages > 1 && (
            <div className="flex justify-center mt-6">
              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handlePageChange(currentPage - 1)}
                  disabled={!pagination.hasPrevPage}
                  className="bg-transparent border border-gray-500 hover:bg-gray-400"
                >
                  <ChevronLeft className="h-4 w-4" />
                  Previous
                </Button>

                <div className="flex space-x-1">
                  {Array.from(
                    { length: Math.min(pagination.totalPages, 5) },
                    (_, i) => {
                      let pageNumber;
                      if (pagination.totalPages <= 5) {
                        pageNumber = i + 1;
                      } else if (currentPage <= 3) {
                        pageNumber = i + 1;
                      } else if (currentPage >= pagination.totalPages - 2) {
                        pageNumber = pagination.totalPages - 4 + i;
                      } else {
                        pageNumber = currentPage - 2 + i;
                      }

                      return (
                        <Button
                          key={pageNumber}
                          variant={
                            currentPage === pageNumber ? "default" : "outline"
                          }
                          size="sm"
                          onClick={() => handlePageChange(pageNumber)}
                          className={
                            currentPage === pageNumber
                              ? "bg-purple-600 hover:bg-purple-700 text-white"
                              : "bg-transparent border border-gray-500 hover:bg-gray-400"
                          }
                        >
                          {pageNumber}
                        </Button>
                      );
                    }
                  )}
                </div>

                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handlePageChange(currentPage + 1)}
                  disabled={!pagination.hasNextPage}
                  className="bg-transparent border border-gray-500 hover:bg-gray-400"
                >
                  Next
                  <ChevronRight className="h-4 w-4" />
                </Button>
              </div>
            </div>
          )}

          {/* Pagination Info */}
          {pagination.total > 0 && (
            <div className="flex justify-center mt-4">
              <p className="text-sm text-gray-400">
                Page {currentPage} of {pagination.totalPages} (
                {pagination.total} total users)
              </p>
            </div>
          )}
        </div>

        {/* User Detail Modal */}
        {dialogState.open &&
          dialogState.mode === "view" &&
          dialogState.user && (
            <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-[9999] p-4">
              <div className="bg-white/5 rounded-lg shadow-lg max-w-2xl w-full max-h-[90vh] overflow-hidden flex flex-col text-white">
                <div className="flex justify-between items-center p-4 border-b border-gray-600">
                  <h3 className="text-lg font-medium">User Details</h3>
                  <Button
                    onClick={() =>
                      setDialogState({ open: false, mode: "view", user: null })
                    }
                    className="text-gray-400 hover:text-white"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                      strokeWidth={1.5}
                      stroke="currentColor"
                      className="w-6 h-6"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        d="M6 18L18 6M6 6l12 12"
                      />
                    </svg>
                  </Button>
                </div>
                <div className="flex-1 overflow-auto p-4 bg-gray-700">
                  <div className="flex flex-col items-center mb-6">
                    <Avatar className="h-20 w-20 rounded-full bg-gray-600 flex items-center justify-center text-2xl font-medium mb-2">
                      <AvatarImage
                        src={UPLOAD_URL + dialogState?.user?.profile?.avatarUrl}
                      />
                      <AvatarFallback className="text-xl font-bold text-black">
                        {dialogState?.user?.profile?.firstName
                          ?.charAt(0)
                          .toUpperCase() || ""}
                        {dialogState?.user?.profile?.lastName
                          ?.charAt(0)
                          .toUpperCase() || ""}
                      </AvatarFallback>
                    </Avatar>
                    <h2 className="text-xl font-bold">
                      {dialogState.user.name ||
                        `${dialogState.user.profile?.firstName} ${dialogState.user.profile?.lastName}`}
                    </h2>
                    <p className="text-gray-400">{dialogState.user.email}</p>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                    <div className="space-y-1">
                      <p className="text-sm text-gray-400">User ID</p>
                      <p className="font-medium">{dialogState.user.id}</p>
                    </div>
                    <div className="space-y-1">
                      <p className="text-sm text-gray-400">Status</p>
                      <p className="font-medium capitalize">
                        {dialogState.user.status}
                      </p>
                    </div>
                    <div className="space-y-1">
                      <p className="text-sm text-gray-400">Plan</p>
                      <p className="font-medium">{dialogState.user.plan}</p>
                    </div>
                    <div className="space-y-1">
                      <p className="text-sm text-gray-400">Join Date</p>
                      <p className="font-medium">
                        {dialogState.user.joinDate
                          ? dialogState.user.joinDate
                          : dialogState.user.createdAt
                          ? formatDateToYMD(dialogState.user.createdAt)
                          : "N/A"}
                      </p>
                    </div>
                    <div className="space-y-1">
                      <p className="text-sm text-gray-400">Last Active</p>
                      <p className="font-medium">
                        {dialogState.user.lastActive
                          ? dialogState.user.lastActive
                          : dialogState.user.lastLogin
                          ? formatDateToYMD(dialogState.user.lastLogin)
                          : "N/A"}
                      </p>
                    </div>
                    <div className="space-y-1">
                      <p className="text-sm text-gray-400">Credits Remaining</p>
                      <div className="flex items-center gap-2">
                        <p className="font-medium">
                          {dialogState.user.credits ??
                            dialogState.user.credit?.balance ??
                            0}
                        </p>
                        <div className="flex gap-1">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() =>
                              handleCreditAdjustment(dialogState.user!.id, 10)
                            }
                            className="bg-transparent border border-green-500 hover:bg-green-500/20 text-green-500 text-xs px-2 py-1"
                          >
                            +10
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() =>
                              handleCreditAdjustment(dialogState.user!.id, -10)
                            }
                            className="bg-transparent border border-red-500 hover:bg-red-500/20 text-red-500 text-xs px-2 py-1"
                          >
                            -10
                          </Button>
                        </div>
                      </div>
                    </div>
                    <div className="space-y-1">
                      <p className="text-sm text-gray-400">Total Spent</p>
                      <p className="font-medium">
                        {formatCurrency(
                          dialogState.user.totalSpent ??
                            dialogState.user.credit?.spent ??
                            0,
                          "$"
                        )}
                      </p>
                    </div>
                  </div>

                  <div className="space-y-4">
                    <h3 className="font-medium">Recent Activity</h3>
                    <div className="bg-gray-600/50 p-4 rounded-md text-center">
                      <p className="text-gray-400">
                        User activity chart would be displayed here
                      </p>
                    </div>
                  </div>
                </div>
                <div className="p-4 border-t border-gray-600 flex justify-between">
                  <Button
                    className="bg-transparent border border-gray-500 hover:bg-gray-400"
                    onClick={() =>
                      setDialogState({
                        open: true,
                        mode: "delete",
                        user: dialogState.user,
                      })
                    }
                  >
                    Delete User
                  </Button>
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      className="cursor-pointer border border-gray-500 rounded-md transition-all hover:border-gray-400 bg-purple-600 hover:bg-purple-700 !text-white flex justify-center items-center"
                      onClick={() =>
                        setDialogState({
                          open: true,
                          mode: "edit",
                          user: dialogState.user,
                        })
                      }
                    >
                      Edit User
                    </Button>
                    <Button
                      onClick={() => setEmailModalOpen(true)}
                      className="bg-purple-600 hover:bg-purple-700 text-white"
                    >
                      <Mail className="w-4 h-4 mr-2" />
                      Send Email
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          )}

        {/* Edit User Modal */}
        {dialogState.open &&
          dialogState.mode === "edit" &&
          dialogState.user && (
            <div className="fixed inset-0 bg-black/90 flex items-center justify-center z-50 p-4">
              <div className="bg-gray-800 rounded-lg shadow-lg max-w-md w-full max-h-[90vh] overflow-hidden flex flex-col text-white">
                <div className="flex justify-between items-center p-4 border-b border-gray-600">
                  <h3 className="text-lg font-medium">Edit User</h3>
                  <button
                    onClick={() =>
                      setDialogState({ open: false, mode: "view", user: null })
                    }
                    className="text-gray-400 hover:text-white"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                      strokeWidth={1.5}
                      stroke="currentColor"
                      className="w-6 h-6"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        d="M6 18L18 6M6 6l12 12"
                      />
                    </svg>
                  </button>
                </div>
                <div className="flex-1 overflow-auto p-4">
                  <form className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <label
                          htmlFor="firstName"
                          className="text-sm font-medium"
                        >
                          First Name
                        </label>
                        <Input
                          id="firstName"
                          className="bg-gray-800 border-gray-600 text-white focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                          defaultValue={
                            dialogState.user.profile?.firstName || ""
                          }
                          onChange={(e) => {
                            if (dialogState.user) {
                              setDialogState({
                                ...dialogState,
                                user: {
                                  ...dialogState.user,
                                  profile: {
                                    ...dialogState.user.profile,
                                    firstName: e.target.value,
                                    lastName:
                                      dialogState.user.profile?.lastName || "",
                                    fullName: `${e.target.value} ${
                                      dialogState.user.profile?.lastName || ""
                                    }`.trim(),
                                    avatarUrl:
                                      dialogState.user.profile?.avatarUrl || "",
                                  },
                                  name: `${e.target.value} ${
                                    dialogState.user.profile?.lastName || ""
                                  }`.trim(),
                                } as User,
                              });
                            }
                          }}
                        />
                      </div>
                      <div className="space-y-2">
                        <label
                          htmlFor="lastName"
                          className="text-sm font-medium"
                        >
                          Last Name
                        </label>
                        <Input
                          id="lastName"
                          className="bg-gray-800 border-gray-600 text-white focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                          defaultValue={
                            dialogState.user.profile?.lastName || ""
                          }
                          onChange={(e) => {
                            if (dialogState.user) {
                              setDialogState({
                                ...dialogState,
                                user: {
                                  ...dialogState.user,
                                  profile: {
                                    ...dialogState.user.profile,
                                    firstName:
                                      dialogState.user.profile?.firstName || "",
                                    lastName: e.target.value,
                                    fullName: `${
                                      dialogState.user.profile?.firstName || ""
                                    } ${e.target.value}`.trim(),
                                    avatarUrl:
                                      dialogState.user.profile?.avatarUrl || "",
                                  },
                                  name: `${
                                    dialogState.user.profile?.firstName || ""
                                  } ${e.target.value}`.trim(),
                                } as User,
                              });
                            }
                          }}
                        />
                      </div>
                    </div>

                    <div className="space-y-2">
                      <label htmlFor="email" className="text-sm font-medium">
                        Email
                      </label>
                      <Input
                        id="email"
                        type="email"
                        defaultValue={dialogState.user.email}
                        className="bg-gray-800 border-gray-600 text-white focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                        disabled
                        title="Email cannot be changed"
                      />
                      <p className="text-xs text-gray-400">
                        Email cannot be changed for security reasons
                      </p>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <label htmlFor="role" className="text-sm font-medium">
                          Role
                        </label>
                        <select
                          id="role"
                          className="w-full px-3 py-2 border border-gray-600 rounded-md bg-gray-800 text-white focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                          defaultValue={dialogState.user.role}
                          onChange={(e) => {
                            if (dialogState.user) {
                              setDialogState({
                                ...dialogState,
                                user: {
                                  ...dialogState.user,
                                  role: e.target.value as "USER" | "ADMIN",
                                } as User,
                              });
                            }
                          }}
                        >
                          <option
                            className="bg-gray-800 text-white"
                            value="USER"
                          >
                            User
                          </option>
                          <option
                            className="bg-gray-800 text-white"
                            value="ADMIN"
                          >
                            Admin
                          </option>
                        </select>
                      </div>

                      <div className="space-y-2">
                        <label
                          htmlFor="emailVerified"
                          className="text-sm font-medium"
                        >
                          Email Status
                        </label>
                        <select
                          id="emailVerified"
                          className="w-full px-3 py-2 border border-gray-600 rounded-md bg-gray-800 text-white focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                          defaultValue={
                            dialogState.user.emailVerified
                              ? "verified"
                              : "unverified"
                          }
                          onChange={(e) => {
                            if (dialogState.user) {
                              setDialogState({
                                ...dialogState,
                                user: {
                                  ...dialogState.user,
                                  emailVerified: e.target.value === "verified",
                                  status:
                                    e.target.value === "verified"
                                      ? "active"
                                      : "inactive",
                                } as User,
                              });
                            }
                          }}
                        >
                          <option
                            className="bg-gray-800 text-white"
                            value="verified"
                          >
                            Verified
                          </option>
                          <option
                            className="bg-gray-800 text-white"
                            value="unverified"
                          >
                            Unverified
                          </option>
                        </select>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <label htmlFor="credits" className="text-sm font-medium">
                        Credit Balance
                      </label>
                      <Input
                        id="credits"
                        type="number"
                        min="0"
                        step="0.1"
                        className="bg-gray-800 border-gray-600 text-white focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                        defaultValue={dialogState.user.credit?.balance ?? 0}
                        onChange={(e) => {
                          if (dialogState.user) {
                            const newBalance = parseFloat(e.target.value) || 0;
                            setDialogState({
                              ...dialogState,
                              user: {
                                ...dialogState.user,
                                credit: {
                                  balance: newBalance,
                                  spent: dialogState.user.credit?.spent || 0,
                                },
                                totalSpent: dialogState.user.credit?.spent || 0,
                              } as User,
                            });
                          }
                        }}
                      />
                      <p className="text-xs text-gray-400">
                        Current spent: ${dialogState.user.credit?.spent || 0}
                      </p>
                    </div>
                  </form>
                </div>
                <div className="p-4 border-t border-gray-600 flex items-center justify-end space-x-2">
                  <Button
                    variant="outline"
                    className="bg-transparent border text-white border-gray-500 hover:bg-gray-400 !py-5"
                    onClick={() =>
                      setDialogState({ open: false, mode: "view", user: null })
                    }
                  >
                    Cancel
                  </Button>
                  <ShadowButton
                    className="px-4"
                    onClick={() => {
                      if (dialogState.user) {
                        handleEditUser(dialogState.user);
                      }
                    }}
                  >
                    Save Changes
                  </ShadowButton>
                </div>
              </div>
            </div>
          )}

        {/* Delete Confirmation Modal */}
        {dialogState.open &&
          dialogState.mode === "delete" &&
          dialogState.user && (
            <AlertDialog open={dialogState.open}>
              <AlertDialogContent className="bg-primary/90">
                <AlertDialogHeader>
                  <AlertDialogTitle>Confirm Deletion</AlertDialogTitle>
                  <AlertDialogDescription>
                    This action is permanent and will delete the user "
                    {dialogState.user.name ||
                      `${dialogState.user.profile?.firstName} ${dialogState.user.profile?.lastName}`}
                    ".
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel
                    className="hover:bg-gray-400 cursor-pointer"
                    onClick={() =>
                      setDialogState({ open: false, mode: "view", user: null })
                    }
                  >
                    Cancel
                  </AlertDialogCancel>
                  <AlertDialogAction
                    onClick={() => handleDeleteUser(dialogState.user!.id)}
                    className={`p-2 px-5 cursor-pointer border border-gray-500 rounded-md transition-all hover:border-gray-400 bg-purple-600 hover:bg-purple-700 !text-white flex justify-center items-center`}
                    style={{
                      boxShadow: "inset 0 0 10px rgba(255, 255, 255, 0.6)",
                    }}
                  >
                    Delete
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          )}

        {/* Email Modal */}
        {emailModalOpen && dialogState.user && (
          <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-[9999] p-4">
            <div className="bg-gray-800 rounded-lg shadow-lg max-w-2xl w-full max-h-[90vh] overflow-hidden flex flex-col text-white">
              <div className="flex justify-between items-center p-4 border-b border-gray-600">
                <h3 className="text-lg font-medium flex items-center">
                  <Mail className="w-5 h-5 mr-2" />
                  Send Email to{" "}
                  {dialogState.user.name || dialogState.user.email}
                </h3>
                <button
                  onClick={() => setEmailModalOpen(false)}
                  className="text-gray-400 hover:text-white"
                >
                  <X className="w-6 h-6" />
                </button>
              </div>

              <div className="flex-1 overflow-auto p-4">
                <div className="space-y-4">
                  {/* Email Template Selection */}
                  <div className="space-y-2">
                    <label className="text-sm font-medium">
                      Email Template
                    </label>
                    <select
                      value={emailData.template}
                      onChange={(e) =>
                        setEmailData({
                          ...emailData,
                          template: e.target.value,
                          message: getTemplateMessage(
                            e.target.value,
                            dialogState.user!
                          ),
                        })
                      }
                      className="w-full px-3 py-2 border border-gray-600 rounded-md bg-gray-800 text-white focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                    >
                      <option
                        value="welcome"
                        className="bg-gray-800 text-white"
                      >
                        Welcome Message
                      </option>
                      <option
                        value="reminder"
                        className="bg-gray-800 text-white"
                      >
                        Account Reminder
                      </option>
                      <option
                        value="promotion"
                        className="bg-gray-800 text-white"
                      >
                        Promotion
                      </option>
                      <option value="custom" className="bg-gray-800 text-white">
                        Custom Message
                      </option>
                    </select>
                  </div>

                  {/* Subject */}
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Subject</label>
                    <Input
                      value={emailData.subject}
                      onChange={(e) =>
                        setEmailData({ ...emailData, subject: e.target.value })
                      }
                      placeholder={`Hello ${
                        dialogState.user.name ||
                        dialogState.user.email.split("@")[0]
                      }`}
                      className="bg-gray-800 border-gray-600 text-white focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                    />
                  </div>

                  {/* Message */}
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Message</label>
                    <textarea
                      value={emailData.message}
                      onChange={(e) =>
                        setEmailData({ ...emailData, message: e.target.value })
                      }
                      placeholder="Enter your message here..."
                      rows={8}
                      className="w-full px-3 py-2 border border-gray-600 rounded-md bg-gray-800 text-white focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent resize-none"
                    />
                  </div>

                  {/* Preview */}
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Preview</label>
                    <div className="p-3 bg-gray-700 rounded-md border border-gray-600">
                      <div className="text-sm text-gray-300 mb-2">
                        <strong>To:</strong> {dialogState.user.email}
                      </div>
                      <div className="text-sm text-gray-300 mb-2">
                        <strong>Subject:</strong>{" "}
                        {emailData.subject ||
                          `Hello ${
                            dialogState.user.name ||
                            dialogState.user.email.split("@")[0]
                          }`}
                      </div>
                      <div className="text-sm text-gray-300">
                        <strong>Message:</strong>
                      </div>
                      <div className="mt-2 p-2 bg-gray-600 rounded text-sm whitespace-pre-wrap">
                        {emailData.message ||
                          getTemplateMessage(
                            emailData.template,
                            dialogState.user
                          )}
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="p-4 border-t border-gray-600 flex items-center justify-end space-x-2">
                <Button
                  variant="outline"
                  onClick={() => setEmailModalOpen(false)}
                  disabled={sendingEmail}
                  className="border border-gray-500 hover:bg-gray-400"
                >
                  Cancel
                </Button>
                <Button
                  onClick={handleSendEmail}
                  disabled={sendingEmail}
                  className="bg-purple-600 hover:bg-purple-700 text-white"
                >
                  {sendingEmail ? (
                    <>
                      <Spinner className="w-4 h-4 mr-2" />
                      Sending...
                    </>
                  ) : (
                    <>
                      <Send className="w-4 h-4 mr-2" />
                      Send Email
                    </>
                  )}
                </Button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default AdminUsersPage;

// import React, { useEffect, useState } from "react";
// import ShadowButton from "@/components/ui/shadowButton";
// import { useAdminHeader } from "@/contexts/AdminHeaderContext";
// import { Button } from "@/components/ui/button";
// import {
//   AlertDialog,
//   AlertDialogAction,
//   AlertDialogCancel,
//   AlertDialogContent,
//   AlertDialogDescription,
//   AlertDialogFooter,
//   AlertDialogHeader,
//   AlertDialogTitle,
//   AlertDialogTrigger,
// } from "@/components/ui/alert-dialog";
// import { Trash2 } from "lucide-react";

// // Type definitions
// type TabType = "all" | "active" | "inactive";
// type PlanType = "all" | "free" | "starter" | "pro" | "business";

// interface TabItem {
//   id: TabType;
//   label: string;
// }

// interface PlanItem {
//   id: PlanType;
//   label: string;
// }

// interface User {
//   id: string;
//   name: string;
//   email: string;
//   plan: string;
//   status: "Active" | "Inactive";
//   joinDate: string;
//   lastActive: string;
//   credits: number;
//   totalSpent: number;
//   avatar: string;
// }

// interface DialogState {
//   open: boolean;
//   mode: "edit" | "view";
//   plan: User | null;
// }

// // Mock formatCurrency function (replace with actual implementation)
// const formatCurrency = (value: number, currency: string): string =>
//   `${currency}${value.toFixed(2)}`;

// // Mock handleDelete function (replace with actual implementation)
// const handleDelete = (id: string): void => {
//   console.log(`Deleting user with id: ${id}`);
// };

// const AdminUsersPage: React.FC = () => {
//   const { setHeaderContent } = useAdminHeader();
//   const [activeTab, setActiveTab] = useState<TabType>("all");
//   const [selectedPlan, setSelectedPlan] = useState<PlanType>("all");
//   const [searchQuery, setSearchQuery] = useState<string>("");
//   const [dialogState, setDialogState] = useState<DialogState>({
//     open: false,
//     mode: "view",
//     plan: null,
//   });

//   const tabs: TabItem[] = [
//     { id: "all", label: "All Users" },
//     { id: "active", label: "Active" },
//     { id: "inactive", label: "Inactive" },
//   ];

//   const plans: PlanItem[] = [
//     { id: "all", label: "All Plans" },
//     { id: "free", label: "Free" },
//     { id: "starter", label: "Starter" },
//     { id: "pro", label: "Pro" },
//     { id: "business", label: "Business" },
//   ];

//   // Mock user data
//   const [users] = useState<User[]>([
//     {
//       id: "1",
//       name: "HIDILSON DOHO",
//       email: "<EMAIL>",
//       plan: "Free",
//       status: "Active",
//       joinDate: "2025-01-01",
//       lastActive: "2025-05-28",
//       credits: 100,
//       totalSpent: 50,
//       avatar: "/api/placeholder/32/32",
//     },
//     {
//       id: "2",
//       name: "Azhar Naeem",
//       email: "<EMAIL>",
//       plan: "Pro",
//       status: "Active",
//       joinDate: "2025-01-01",
//       lastActive: "2025-05-28",
//       credits: 100,
//       totalSpent: 50,
//       avatar: "/api/placeholder/32/32",
//     },
//   ]);

//   // Set header
//   useEffect(() => {
//     setHeaderContent({
//       title: "User Management",
//       description:
//         "Manage your users, view their activity and adjust their plans",
//       content: null,
//     });
//     return () => setHeaderContent({});
//   }, []);

//   const handleTabChange = (tabId: TabType): void => {
//     setActiveTab(tabId);
//   };

//   const handlePlanChange = (planId: PlanType): void => {
//     setSelectedPlan(planId);
//   };

//   const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>): void => {
//     setSearchQuery(e.target.value);
//   };

//   const filteredUsers = users.filter((user) => {
//     const matchesTab =
//       activeTab === "all" ||
//       (activeTab === "active" && user.status === "Active") ||
//       (activeTab === "inactive" && user.status === "Inactive");

//     const matchesPlan =
//       selectedPlan === "all" || user.plan.toLowerCase() === selectedPlan;

//     const matchesSearch =
//       user.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
//       user.email.toLowerCase().includes(searchQuery.toLowerCase());

//     return matchesTab && matchesPlan && matchesSearch;
//   });

//   return (
//     <div className="min-h-screen text-white">
//       <div className="p-2">
//         {/* Navigation and Controls */}
//         <div className="flex items-center justify-between mb-8 pb-5 border-b-2 border-gray-500">
//           {/* Tabs */}
//           <div className="flex gap-10 space-x-1 pb-1 p-2">
//             {tabs.map((tab) => (
//               <button
//                 key={tab.id}
//                 onClick={() => handleTabChange(tab.id)}
//                 className={`text-sm font-medium rounded-lg cursor-pointer transition-colors ${
//                   activeTab === tab.id
//                     ? "text-white"
//                     : "text-gray-400 hover:text-purple-500"
//                 }`}
//                 type="button"
//               >
//                 {tab.label}
//               </button>
//             ))}
//           </div>

//           {/* Plan Filter, Search, and Controls */}
//           <div className="flex items-center space-x-4">
//             {/* Plan Filter */}
//             <div className="flex space-x-1">
//               {plans.map((plan) => {
//                 if (selectedPlan === plan.id) {
//                   return (
//                     <ShadowButton
//                       key={plan.id}
//                       onClick={() => handlePlanChange(plan.id)}
//                       className="px-4 py-1 text-sm font-medium rounded-lg"
//                     >
//                       {plan.label}
//                     </ShadowButton>
//                   );
//                 }
//                 return (
//                   <button
//                     key={plan.id}
//                     onClick={() => handlePlanChange(plan.id)}
//                     className="px-4 py-2 text-sm font-medium rounded-lg transition-colors border border-gray-600 text-gray-300 hover:bg-gray-700 hover:text-white"
//                     type="button"
//                   >
//                     {plan.label}
//                   </button>
//                 );
//               })}
//             </div>

//             {/* Search Box */}
//             <div className="relative">
//               <input
//                 type="text"
//                 placeholder="Search"
//                 value={searchQuery}
//                 onChange={handleSearchChange}
//                 className="px-4 py-2 text-sm bg-white/5 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent w-64"
//               />
//             </div>
//           </div>
//         </div>

//         {/* Users Section */}
//         <div className="bg-white/5 rounded-lg p-6">
//           <div className="mb-4">
//             <h2 className="text-2xl font-semibold text-white mb-2">Users</h2>
//             <p className="text-gray-400 text-sm">
//               Showing {filteredUsers.length} of {users.length} users
//             </p>
//           </div>

//           {/* Table */}
//           <div className="overflow-x-auto">
//             <div className="rounded-lg overflow-hidden">
//               <div className="relative w-full overflow-x-auto">
//                 <table className="w-full caption-bottom text-sm">
//                   <thead>
//                     <tr className="">
//                       {[
//                         "User",
//                         "Plan",
//                         "Status",
//                         "Join Date",
//                         "Last Active",
//                         "Credits",
//                         "Total Spent",
//                         "Actions",
//                       ].map((head) => (
//                         <th
//                           key={head}
//                           className="h-12 px-4 text-left align-middle font-semibold text-gray-200 whitespace-nowrap"
//                         >
//                           {head}
//                         </th>
//                       ))}
//                     </tr>
//                     <tr className="relative">
//                       <td
//                         colSpan={8}
//                         className="absolute bottom-0 h-0 pointer-events-none"
//                         style={{
//                           left: "10%",
//                           right: "10%",
//                           background:
//                             "linear-gradient(to right, transparent, #e5e7eb 30%, #e5e7eb 60%, transparent)",
//                           height: "2px",
//                         }}
//                       />
//                     </tr>
//                   </thead>
//                   <tbody>
//                     {filteredUsers.length > 0 ? (
//                       filteredUsers.map((user, index) => (
//                         <tr
//                           key={user.id}
//                           className="transition-colors relative text-gray-200"
//                         >
//                           <td className="p-4 align-middle whitespace-nowrap font-medium">
//                             {user.name}
//                           </td>
//                           <td className="p-4 align-middle whitespace-nowrap">
//                             {user.email}
//                           </td>
//                           <td className="p-4 align-middle whitespace-nowrap">
//                             <span
//                               className={`px-2 py-2 rounded-full text-xs font-medium ${
//                                 user.status === "Active"
//                                   ? "bg-[#8DBE8E]/70 text-white"
//                                   : "bg-gray-100 text-gray-600"
//                               }`}
//                             >
//                               {user.status}
//                             </span>
//                           </td>
//                           <td className="p-4 align-middle whitespace-nowrap">
//                             <span
//                               className={`px-2 py-2 rounded-full text-xs font-medium bg-[#D83C3C]/70 text-white`}
//                             >
//                               {!user.joinDate ? "N/A" : user.joinDate}
//                             </span>
//                           </td>
//                           <td className="p-4 align-middle whitespace-nowrap">
//                             {user.plan}
//                           </td>
//                           <td className="p-4 align-middle whitespace-nowrap">
//                             {user.credits}
//                           </td>
//                           <td className="p-4 align-middle whitespace-nowrap">
//                             {formatCurrency(user.totalSpent, "$")}
//                           </td>
//                           <td className="p-4 align-middle whitespace-nowrap">
//                             <div className="flex items-center gap-2">
//                               <Button
//                                 variant="outline"
//                                 size="sm"
//                                 className="bg-transparent border border-gray-500 hover:bg-gray-400"
//                                 onClick={() =>
//                                   setDialogState({
//                                     open: true,
//                                     mode: "edit",
//                                     plan: user,
//                                   })
//                                 }
//                               >
//                                 Edit
//                               </Button>
//                               <AlertDialog>
//                                 <AlertDialogTrigger>
//                                   <Button
//                                     className="bg-transparent border border-gray-500 hover:bg-gray-400"
//                                     size="sm"
//                                   >
//                                     <Trash2 />
//                                   </Button>
//                                 </AlertDialogTrigger>
//                                 <AlertDialogContent className="bg-primary/90">
//                                   <AlertDialogHeader>
//                                     <AlertDialogTitle>
//                                       Confirm Deletion
//                                     </AlertDialogTitle>
//                                     <AlertDialogDescription>
//                                       This action is permanent and will delete
//                                       the user "{user.name}".
//                                     </AlertDialogDescription>
//                                   </AlertDialogHeader>
//                                   <AlertDialogFooter>
//                                     <AlertDialogCancel className="hover:bg-gray-400 cursor-pointer">
//                                       Cancel
//                                     </AlertDialogCancel>
//                                     <AlertDialogAction
//                                       onClick={() => handleDelete(user.id)}
//                                       className={`p-2 px-5 cursor-pointer border border-gray-500 rounded-md transition-all hover:border-gray-400 bg-purple-600 hover:bg-purple-700 !text-white flex justify-center items-center`}
//                                       style={{
//                                         boxShadow:
//                                           "inset 0 0 10px rgba(255, 255, 255, 0.6)",
//                                       }}
//                                     >
//                                       Delete
//                                     </AlertDialogAction>
//                                   </AlertDialogFooter>
//                                 </AlertDialogContent>
//                               </AlertDialog>
//                             </div>
//                           </td>
//                           {index !== filteredUsers.length - 1 && (
//                             <td
//                               className="absolute bottom-0 h-0 pointer-events-none"
//                               style={{
//                                 left: "10%",
//                                 right: "10%",
//                                 background:
//                                   "linear-gradient(to right, transparent, #e5e7eb 30%, #e5e7eb 60%, transparent)",
//                                 height: "2px",
//                               }}
//                             />
//                           )}
//                         </tr>
//                       ))
//                     ) : (
//                       <tr>
//                         <td
//                           colSpan={8}
//                           className="p-12 text-center text-gray-400"
//                         >
//                           No users found
//                         </td>
//                       </tr>
//                     )}
//                   </tbody>
//                 </table>
//               </div>
//             </div>
//           </div>
//         </div>
//       </div>
//     </div>
//   );
// };

// export default AdminUsersPage;

// import { useState, useEffect } from "react";
// import { Button } from "@/components/ui/button";
// import { Input } from "@/components/ui/input";
// import {
//   Card,
//   CardContent,
//   CardDescription,
//   CardHeader,
//   CardTitle,
// } from "@/components/ui/card";
// import AdminService from "@/services/admin.service";
// import type { User } from "@/services/admin.service";
// import { toast } from "sonner";
// import { Spinner } from "@/components/ui/spinner";
// import { formatDateToYMD } from "@/lib/utils";

// // Fallback data in case API fails

// type UserStatus = "all" | "active" | "inactive";
// type UserPlan = "all" | "free" | "starter" | "pro" | "business";

// const AdminUsersPage = () => {
//   const [users, setUsers] = useState<User[]>([]);
//   const [isLoading, setIsLoading] = useState(true);
//   const [error, setError] = useState("");
//   const [searchQuery, setSearchQuery] = useState("");
//   const [statusFilter, setStatusFilter] = useState<UserStatus>("all");
//   const [planFilter, setPlanFilter] = useState<UserPlan>("all");
//   const [selectedUser, setSelectedUser] = useState<User | null>(null);
//   const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
//   const [isEditModalOpen, setIsEditModalOpen] = useState(false);
//   const [currentPage, setCurrentPage] = useState(1);
//   const usersPerPage = 8;

//   // Fetch users from API
//   useEffect(() => {
//     const fetchUsers = async () => {
//       try {
//         setIsLoading(true);
//         const { users } = await AdminService.getUsers();
//         console.log("data", users);

//         setUsers(users);
//         setError("");
//       } catch (err) {
//         console.error("Failed to fetch users:", err);
//         setError("Failed to load users. Please try again.");
//         toast.error("Failed to load users");
//       } finally {
//         setIsLoading(false);
//       }
//     };

//     fetchUsers();
//   }, []);

//   // Filter users based on search query and filters
//   const filteredUsers = users?.filter((user) => {
//     const matchesSearch =
//       user.profile?.firstName
//         ?.toLowerCase()
//         .includes(searchQuery?.toLowerCase()) ||
//       user.email?.toLowerCase().includes(searchQuery?.toLowerCase()) ||
//       user.id?.toLowerCase().includes(searchQuery?.toLowerCase());

//     const matchesStatus =
//       statusFilter === "all" || user.status === statusFilter;

//     const matchesPlan =
//       planFilter === "all" || user.plan?.toLowerCase() === planFilter;

//     return matchesSearch && matchesStatus && matchesPlan;
//   });

//   // Pagination
//   const indexOfLastUser = currentPage * usersPerPage;
//   const indexOfFirstUser = indexOfLastUser - usersPerPage;
//   const currentUsers = filteredUsers.slice(indexOfFirstUser, indexOfLastUser);
//   const totalPages = Math.ceil(filteredUsers.length / usersPerPage);

//   const handlePageChange = (pageNumber: number) => {
//     setCurrentPage(pageNumber);
//   };

//   const handleUserClick = (user: User) => {
//     setSelectedUser(user);
//   };

//   const handleDeleteUser = async (id: string) => {
//     try {
//       setIsLoading(true);
//       await AdminService.deleteUser(id);
//       setUsers(users?.filter((user) => user.id !== id));
//       toast.success("User deleted successfully");
//       setIsDeleteModalOpen(false);
//     } catch (err) {
//       console.error("Failed to delete user:", err);
//       toast.error("Failed to delete user");
//     } finally {
//       setIsLoading(false);
//     }
//   };

//   const handleEditUser = async (user: User) => {
//     try {
//       setIsLoading(true);
//       const updatedUser = await AdminService.updateUser(user.id, user);
//       setUsers(users.map((u) => (u.id === updatedUser.id ? updatedUser : u)));
//       toast.success("User updated successfully");
//       setIsEditModalOpen(false);
//     } catch (err) {
//       console.error("Failed to update user:", err);
//       toast.error("Failed to update user");
//     } finally {
//       setIsLoading(false);
//     }
//   };
//   console.log("user.profile?.avatar", selectedUser);
//   return (
//     <div className="space-y-6">
//       <div>
//         <h1 className="text-3xl font-bold tracking-tight">User Management</h1>
//         <p className="text-muted-foreground">
//           Manage your users, view their activity, and adjust their plans.
//         </p>
//       </div>

//       {error && (
//         <div className="bg-destructive/10 text-destructive text-sm p-3 rounded-md">
//           {error}
//         </div>
//       )}

//       {/* Filters and Search */}
//       <div className="flex flex-col md:flex-row justify-between gap-4">
//         <div className="flex flex-wrap gap-2">
//           <Button
//             variant={statusFilter === "all" ? "default" : "outline"}
//             size="sm"
//             onClick={() => setStatusFilter("all")}
//           >
//             All Users
//           </Button>
//           <Button
//             variant={statusFilter === "active" ? "default" : "outline"}
//             size="sm"
//             onClick={() => setStatusFilter("active")}
//           >
//             Active
//           </Button>
//           <Button
//             variant={statusFilter === "inactive" ? "default" : "outline"}
//             size="sm"
//             onClick={() => setStatusFilter("inactive")}
//           >
//             Inactive
//           </Button>
//         </div>
//         <div className="flex flex-wrap gap-2">
//           <Button
//             variant={planFilter === "all" ? "default" : "outline"}
//             size="sm"
//             onClick={() => setPlanFilter("all")}
//           >
//             All Plans
//           </Button>
//           <Button
//             variant={planFilter === "free" ? "default" : "outline"}
//             size="sm"
//             onClick={() => setPlanFilter("free")}
//           >
//             Free
//           </Button>
//           <Button
//             variant={planFilter === "starter" ? "default" : "outline"}
//             size="sm"
//             onClick={() => setPlanFilter("starter")}
//           >
//             Starter
//           </Button>
//           <Button
//             variant={planFilter === "pro" ? "default" : "outline"}
//             size="sm"
//             onClick={() => setPlanFilter("pro")}
//           >
//             Pro
//           </Button>
//           <Button
//             variant={planFilter === "business" ? "default" : "outline"}
//             size="sm"
//             onClick={() => setPlanFilter("business")}
//           >
//             Business
//           </Button>
//         </div>
//         <div className="w-full md:w-64">
//           <Input
//             placeholder="Search users..."
//             value={searchQuery}
//             onChange={(e) => setSearchQuery(e.target.value)}
//           />
//         </div>
//       </div>

//       {/* Users Table */}
//       <Card>
//         <CardHeader>
//           <CardTitle>Users</CardTitle>
//           <CardDescription>
//             Showing {currentUsers.length} of {filteredUsers.length} users
//           </CardDescription>
//         </CardHeader>
//         <CardContent>
//           <div className="overflow-x-auto">
//             {isLoading && users.length === 0 ? (
//               <div className="flex justify-center items-center py-8">
//                 <Spinner className="h-8 w-8" />
//               </div>
//             ) : (
//               <table className="w-full text-sm">
//                 <thead>
//                   <tr className="border-b">
//                     <th className="text-left font-medium py-3 px-4">User</th>
//                     <th className="text-left font-medium py-3 px-4">Plan</th>
//                     <th className="text-left font-medium py-3 px-4">Status</th>
//                     <th className="text-left font-medium py-3 px-4">
//                       Join Date
//                     </th>
//                     <th className="text-left font-medium py-3 px-4">
//                       Last Active
//                     </th>
//                     <th className="text-left font-medium py-3 px-4">Credits</th>
//                     <th className="text-left font-medium py-3 px-4">
//                       Total Spent
//                     </th>
//                     <th className="text-left font-medium py-3 px-4">Actions</th>
//                   </tr>
//                 </thead>
//                 <tbody>
//                   {currentUsers.map((user: User) => (
//                     <tr
//                       key={user.id}
//                       className="border-b hover:bg-muted/50 cursor-pointer"
//                       onClick={() => handleUserClick(user)}
//                     >
//                       <td className="py-3 px-4">
//                         <div className="flex items-center space-x-3">
//                           <div className="h-8 w-8 rounded-full bg-muted flex items-center justify-center text-sm font-medium">
//                             <img loading="lazy"
//                               src={user.profile?.avatarUrl}
//                               alt="user profile picture"
//                             />
//                           </div>
//                           <span>
//                             {user.profile?.firstName} {user.profile?.lastName}
//                           </span>
//                         </div>
//                       </td>
//                       <td className="py-3 px-4">{user.email}</td>
//                       <td className="py-3 px-4">
//                         <span
//                           className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
//                           ${
//                             user.plan === "Free"
//                               ? "bg-gray-100 text-gray-800"
//                               : user.plan === "Starter"
//                               ? "bg-blue-100 text-blue-800"
//                               : user.plan === "Pro"
//                               ? "bg-purple-100 text-purple-800"
//                               : "bg-green-100 text-green-800"
//                           }`}
//                         >
//                           {user.plan}
//                         </span>
//                       </td>
//                       <td className="py-3 px-4">
//                         <span
//                           className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
//                           ${
//                             user.status === "active"
//                               ? "bg-green-100 text-green-800"
//                               : "bg-red-100 text-red-800"
//                           }`}
//                         >
//                           {user.status?.charAt(0).toUpperCase() +
//                             user.status?.slice(1)}
//                         </span>
//                       </td>
//                       <td className="py-3 px-4">{user.joinDate}</td>
//                       <td className="py-3 px-4">{user.lastActive}</td>
//                       <td className="py-3 px-4 text-right">
//                         <div className="flex justify-end space-x-2">
//                           <Button
//                             variant="ghost"
//                             size="sm"
//                             onClick={(e) => {
//                               e.stopPropagation();
//                               setSelectedUser(user);
//                               setIsEditModalOpen(true);
//                             }}
//                           >
//                             <svg
//                               xmlns="http://www.w3.org/2000/svg"
//                               fill="none"
//                               viewBox="0 0 24 24"
//                               strokeWidth={1.5}
//                               stroke="currentColor"
//                               className="w-4 h-4"
//                             >
//                               <path
//                                 strokeLinecap="round"
//                                 strokeLinejoin="round"
//                                 d="M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L10.582 16.07a4.5 4.5 0 01-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 011.13-1.897l8.932-8.931zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0115.75 21H5.25A2.25 2.25 0 013 18.75V8.25A2.25 2.25 0 015.25 6H10"
//                               />
//                             </svg>
//                           </Button>
//                           <Button
//                             variant="ghost"
//                             size="sm"
//                             onClick={(e) => {
//                               e.stopPropagation();
//                               setSelectedUser(user);
//                               setIsDeleteModalOpen(true);
//                             }}
//                           >
//                             <svg
//                               xmlns="http://www.w3.org/2000/svg"
//                               fill="none"
//                               viewBox="0 0 24 24"
//                               strokeWidth={1.5}
//                               stroke="currentColor"
//                               className="w-4 h-4 text-destructive"
//                             >
//                               <path
//                                 strokeLinecap="round"
//                                 strokeLinejoin="round"
//                                 d="M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"
//                               />
//                             </svg>
//                           </Button>
//                         </div>
//                       </td>
//                     </tr>
//                   ))}
//                 </tbody>
//               </table>
//             )}
//           </div>

//           {/* Pagination */}
//           {totalPages > 1 && (
//             <div className="flex justify-center mt-6">
//               <div className="flex space-x-2">
//                 <Button
//                   variant="outline"
//                   size="sm"
//                   onClick={() => handlePageChange(currentPage - 1)}
//                   disabled={currentPage === 1}
//                 >
//                   Previous
//                 </Button>
//                 {Array.from({ length: totalPages }, (_, i) => i + 1).map(
//                   (page) => (
//                     <Button
//                       key={page}
//                       variant={currentPage === page ? "default" : "outline"}
//                       size="sm"
//                       onClick={() => handlePageChange(page)}
//                     >
//                       {page}
//                     </Button>
//                   )
//                 )}
//                 <Button
//                   variant="outline"
//                   size="sm"
//                   onClick={() => handlePageChange(currentPage + 1)}
//                   disabled={currentPage === totalPages}
//                 >
//                   Next
//                 </Button>
//               </div>
//             </div>
//           )}
//         </CardContent>
//       </Card>

//       {/* User Detail Modal */}
//       {selectedUser && !isEditModalOpen && !isDeleteModalOpen && (
//         <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
//           <div className="bg-background rounded-lg shadow-lg max-w-2xl w-full max-h-[90vh] overflow-hidden flex flex-col">
//             <div className="flex justify-between items-center p-4 border-b">
//               <h3 className="text-lg font-medium">User Details</h3>
//               <Button
//                 onClick={() => setSelectedUser(null)}
//                 className="text-muted-foreground hover:text-foreground"
//               >
//                 <svg
//                   xmlns="http://www.w3.org/2000/svg"
//                   fill="none"
//                   viewBox="0 0 24 24"
//                   strokeWidth={1.5}
//                   stroke="currentColor"
//                   className="w-6 h-6"
//                 >
//                   <path
//                     strokeLinecap="round"
//                     strokeLinejoin="round"
//                     d="M6 18L18 6M6 6l12 12"
//                   />
//                 </svg>
//               </Button>
//             </div>
//             <div className="flex-1 overflow-auto p-4">
//               <div className="flex flex-col items-center mb-6">
//                 <div className="h-20 w-20 rounded-full bg-muted flex items-center justify-center text-2xl font-medium mb-2">
//                   <img loading="lazy"
//                     src={selectedUser?.profile?.avatarUrl}
//                     alt="user profile picture"
//                   />
//                 </div>
//                 <h2 className="text-xl font-bold">
//                   {selectedUser?.profile?.firstName}{" "}
//                   {selectedUser?.profile?.lastName}
//                 </h2>
//                 <p className="text-muted-foreground">{selectedUser?.email}</p>
//               </div>

//               <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
//                 <div className="space-y-1">
//                   <p className="text-sm text-muted-foreground">User ID</p>
//                   <p className="font-medium">{selectedUser.id}</p>
//                 </div>
//                 <div className="space-y-1">
//                   <p className="text-sm text-muted-foreground">Status</p>
//                   <p className="font-medium capitalize">
//                     {selectedUser.status}
//                   </p>
//                 </div>
//                 <div className="space-y-1">
//                   <p className="text-sm text-muted-foreground">Plan</p>
//                   <p className="font-medium">{selectedUser.plan}</p>
//                 </div>
//                 <div className="space-y-1">
//                   <p className="text-sm text-muted-foreground">Join Date</p>
//                   <p className="font-medium">
//                     {formatDateToYMD(selectedUser.createdAt!)}
//                   </p>
//                 </div>
//                 <div className="space-y-1">
//                   <p className="text-sm text-muted-foreground">Last Active</p>
//                   <p className="font-medium">
//                     {formatDateToYMD(selectedUser?.lastLogin)}
//                   </p>
//                 </div>
//                 <div className="space-y-1">
//                   <p className="text-sm text-muted-foreground">
//                     Credits Remaining
//                   </p>
//                   <p className="font-medium">{selectedUser?.credit?.balance}</p>
//                 </div>
//                 <div className="space-y-1">
//                   <p className="text-sm text-muted-foreground">Total Spent</p>
//                   <p className="font-medium">{selectedUser?.credit?.spent}</p>
//                 </div>
//               </div>

//               <div className="space-y-4">
//                 <h3 className="font-medium">Recent Activity</h3>
//                 <div className="bg-muted p-4 rounded-md text-center">
//                   <p className="text-muted-foreground">
//                     User activity chart would be displayed here
//                   </p>
//                 </div>
//               </div>
//             </div>
//             <div className="p-4 border-t flex justify-between">
//               <Button
//                 variant="destructive"
//                 onClick={() => setIsDeleteModalOpen(true)}
//               >
//                 Delete User
//               </Button>
//               <div className="flex gap-2">
//                 <Button
//                   variant="outline"
//                   onClick={() => setIsEditModalOpen(true)}
//                 >
//                   Edit User
//                 </Button>
//                 <Button>Send Email</Button>
//               </div>
//             </div>
//           </div>
//         </div>
//       )}

//       {/* Edit User Modal */}
//       {selectedUser && isEditModalOpen && (
//         <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
//           <div className="bg-background rounded-lg shadow-lg max-w-md w-full max-h-[90vh] overflow-hidden flex flex-col">
//             <div className="flex justify-between items-center p-4 border-b">
//               <h3 className="text-lg font-medium">Edit User</h3>
//               <button
//                 onClick={() => setIsEditModalOpen(false)}
//                 className="text-muted-foreground hover:text-foreground"
//               >
//                 <svg
//                   xmlns="http://www.w3.org/2000/svg"
//                   fill="none"
//                   viewBox="0 0 24 24"
//                   strokeWidth={1.5}
//                   stroke="currentColor"
//                   className="w-6 h-6"
//                 >
//                   <path
//                     strokeLinecap="round"
//                     strokeLinejoin="round"
//                     d="M6 18L18 6M6 6l12 12"
//                   />
//                 </svg>
//               </button>
//             </div>
//             <div className="flex-1 overflow-auto p-4">
//               <form className="space-y-4">
//                 <div className="space-y-2">
//                   <label htmlFor="name" className="text-sm font-medium">
//                     Name
//                   </label>
//                   <Input id="name" defaultValue={selectedUser.name} />
//                 </div>
//                 <div className="space-y-2">
//                   <label htmlFor="email" className="text-sm font-medium">
//                     Email
//                   </label>
//                   <Input
//                     id="email"
//                     type="email"
//                     defaultValue={selectedUser.email}
//                   />
//                 </div>
//                 <div className="space-y-2">
//                   <label htmlFor="plan" className="text-sm font-medium">
//                     Plan
//                   </label>
//                   <select
//                     id="plan"
//                     className="w-full px-3 py-2 border rounded-md"
//                     defaultValue={selectedUser.plan}
//                   >
//                     <option value="Free">Free</option>
//                     <option value="Starter">Starter</option>
//                     <option value="Pro">Pro</option>
//                     <option value="Business">Business</option>
//                   </select>
//                 </div>
//                 <div className="space-y-2">
//                   <label htmlFor="status" className="text-sm font-medium">
//                     Status
//                   </label>
//                   <select
//                     id="status"
//                     className="w-full px-3 py-2 border rounded-md"
//                     defaultValue={selectedUser.status}
//                   >
//                     <option value="active">Active</option>
//                     <option value="inactive">Inactive</option>
//                   </select>
//                 </div>
//                 <div className="space-y-2">
//                   <label htmlFor="credits" className="text-sm font-medium">
//                     Credits
//                   </label>
//                   <Input
//                     id="credits"
//                     type="number"
//                     defaultValue={selectedUser.credit.balance}
//                   />
//                 </div>
//               </form>
//             </div>
//             <div className="p-4 border-t flex justify-end space-x-2">
//               <Button
//                 variant="outline"
//                 onClick={() => setIsEditModalOpen(false)}
//               >
//                 Cancel
//               </Button>
//               <Button onClick={() => handleEditUser(selectedUser)}>
//                 Save Changes
//               </Button>
//             </div>
//           </div>
//         </div>
//       )}

//       {/* Delete Confirmation Modal */}
//       {selectedUser && isDeleteModalOpen && (
//         <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
//           <div className="bg-background rounded-lg shadow-lg max-w-md w-full p-6">
//             <h3 className="text-lg font-medium mb-2">Delete User</h3>
//             <p className="text-muted-foreground mb-4">
//               Are you sure you want to delete the user "
//               {selectedUser.profile?.firstName} {selectedUser.profile?.lastName}
//               "? This action cannot be undone and will remove all associated
//               data.
//             </p>
//             <div className="flex justify-end gap-2">
//               <Button
//                 variant="outline"
//                 onClick={() => setIsDeleteModalOpen(false)}
//               >
//                 Cancel
//               </Button>
//               <Button
//                 variant="destructive"
//                 onClick={() => handleDeleteUser(selectedUser.id)}
//               >
//                 Delete
//               </Button>
//             </div>
//           </div>
//         </div>
//       )}
//     </div>
//   );
// };

// export default AdminUsersPage;
