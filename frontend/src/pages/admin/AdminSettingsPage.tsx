import AdminApiKeysSettings from '@/components/admin/SettingPageComponent/AdminApiKeysSettings';
import AdminBlogSettings from '@/components/admin/SettingPageComponent/AdminBlogSettings';
import AdminEmailSettings from '@/components/admin/SettingPageComponent/AdminEmailSettings';
import AdminGeneralSettings from '@/components/admin/SettingPageComponent/AdminGeneralSettings';
import AdminPaymentSettings from '@/components/admin/SettingPageComponent/AdminPaymentSettings';
import AdminUsageLimitsSettings from '@/components/admin/SettingPageComponent/AdminUsageLimitsSettings';
import { useAdminHeader } from '@/contexts/AdminHeaderContext';
import { useEffect, useState } from 'react';

// Main AdminSettingsPage Component
const AdminSettingsPage = () => {
  const { setHeaderContent } = useAdminHeader();
  const [activeTab, setActiveTab] = useState('general');

  // Set header
    useEffect(() => {
      setHeaderContent({
        title: "System Settings",
        description:
          "Configure system-wide settings for the Miragic AI platform",
        content: null,
      });
      return () => setHeaderContent({});
    }, []);

  const tabs = [
    { id: 'general', label: 'General' },
    { id: 'api-keys', label: 'API Keys' },
    { id: 'email', label: 'Email' },
    { id: 'payment', label: 'Payment' },
    { id: 'usage-limits', label: 'Usage Limits' },
    { id: 'blog', label: 'Blog' }
  ];

  const handleTabChange = (tabId: string) => {
    setActiveTab(tabId);
  };

  const renderTabContent = () => {
    switch (activeTab) {
      case 'general':
        return <AdminGeneralSettings />;
      case 'api-keys':
        return <AdminApiKeysSettings />;
      case 'email':
        return <AdminEmailSettings />;
      case 'payment':
        return <AdminPaymentSettings />;
      case 'usage-limits':
        return <AdminUsageLimitsSettings />;
      case 'blog':
        return <AdminBlogSettings />;
      default:
        return <AdminGeneralSettings />;
    }
  };

  return (
    <div className="min-h-screen text-white p-3">
      <div className="max-w-6xl mx-auto">
        {/* Tabs */}
        <div className="flex space-x-1 mb-8 border-b border-gray-700">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => handleTabChange(tab.id)}
              className={`text-sm font-medium cursor-pointer transition-colors px-4 py-2 ${
                activeTab === tab.id
                  ? "text-white"
                  : "text-gray-400 hover:text-purple-500"
              }`}
              type="button"
            >
              {tab.label}
            </button>
          ))}
        </div>

        {/* Tab Content */}
        {renderTabContent()}
      </div>
    </div>
  );
};



export default AdminSettingsPage;



// import { useState } from "react";
// import { Button } from "@/components/ui/button";
// import { Input } from "@/components/ui/input";
// import { Textarea } from "@/components/ui/textarea";
// import { Switch } from "@/components/ui/switch";
// import {
//   Card,
//   CardContent,
//   CardDescription,
//   CardFooter,
//   CardHeader,
//   CardTitle,
// } from "@/components/ui/card";
// import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";

// // Mock initial settings
// const initialSettings = {
//   general: {
//     siteName: "Miragic-AI",
//     siteDescription:
//       "AI-powered content generation platform for videos, images, and background removal",
//     contactEmail: "<EMAIL>",
//     supportPhone: "+****************",
//     maintenanceMode: false,
//   },
//   api: {
//     videoApiKey: "syn_api_********************",
//     imageApiKey: "stb_api_********************",
//     backgroundApiKey: "rmv_api_********************",
//     videoApiEndpoint: "https://api.synthesia.io/v1",
//     imageApiEndpoint: "https://api.stability.ai/v1",
//     backgroundApiEndpoint: "https://api.removal.ai/v1",
//   },
//   email: {
//     smtpServer: "smtp.resend.com",
//     smtpPort: "587",
//     smtpUsername: "apikey",
//     smtpPassword: "re_********************",
//     senderEmail: "<EMAIL>",
//     senderName: "Miragic-AI",
//   },
//   payment: {
//     stripePublicKey: "pk_test_********************",
//     stripeSecretKey: "sk_test_********************",
//     paypalClientId: "client_id_********************",
//     paypalSecret: "client_secret_********************",
//     enableStripe: true,
//     enablePaypal: true,
//     testMode: true,
//   },
//   limits: {
//     freeVideoLimit: 1,
//     freeImageLimit: 5,
//     freeBackgroundLimit: 3,
//     starterVideoLimit: 10,
//     starterImageLimit: 50,
//     starterBackgroundLimit: 30,
//     proVideoLimit: 50,
//     proImageLimit: 200,
//     proBackgroundLimit: 100,
//     businessVideoLimit: "Unlimited",
//     businessImageLimit: "Unlimited",
//     businessBackgroundLimit: "Unlimited",
//   },
//   blog: {
//     postsPerPage: 10,
//     enableComments: true,
//     moderateComments: true,
//     allowGuestComments: false,
//     featuredPostsCount: 3,
//   },
// };

// const AdminSettingsPage = () => {
//   const [settings, setSettings] = useState(initialSettings);
//   const [activeTab, setActiveTab] = useState("general");
//   const [isSaving, setIsSaving] = useState(false);
//   const [saveSuccess, setSaveSuccess] = useState(false);
//   console.log(activeTab);
//   const handleInputChange = (
//     category: keyof typeof settings,
//     field: string,
//     value: string | boolean | number
//   ) => {
//     setSettings({
//       ...settings,
//       [category]: {
//         ...settings[category],
//         [field]: value,
//       },
//     });
//   };

//   const handleSaveSettings = () => {
//     setIsSaving(true);

//     // Simulate API call to save settings
//     setTimeout(() => {
//       setIsSaving(false);
//       setSaveSuccess(true);

//       // Hide success message after 3 seconds
//       setTimeout(() => {
//         setSaveSuccess(false);
//       }, 3000);
//     }, 1000);
//   };

//   return (
//     <div className="space-y-8">
//       <div>
//         <h1 className="text-3xl font-bold tracking-tight">System Settings</h1>
//         <p className="text-muted-foreground">
//           Configure system-wide settings for the Miragic-AI platform
//         </p>
//       </div>

//       {saveSuccess && (
//         <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative">
//           <span className="block sm:inline">Settings saved successfully!</span>
//         </div>
//       )}

//       <Tabs
//         defaultValue="general"
//         className="w-full"
//         onValueChange={setActiveTab}
//       >
//         <TabsList className="grid grid-cols-6 mb-8">
//           <TabsTrigger value="general">General</TabsTrigger>
//           <TabsTrigger value="api">API Keys</TabsTrigger>
//           <TabsTrigger value="email">Email</TabsTrigger>
//           <TabsTrigger value="payment">Payment</TabsTrigger>
//           <TabsTrigger value="limits">Usage Limits</TabsTrigger>
//           <TabsTrigger value="blog">Blog</TabsTrigger>
//         </TabsList>

//         <TabsContent value="general">
//           <Card>
//             <CardHeader>
//               <CardTitle>General Settings</CardTitle>
//               <CardDescription>
//                 Basic settings for your Miragic-AI platform
//               </CardDescription>
//             </CardHeader>
//             <CardContent className="space-y-4">
//               <div className="space-y-2">
//                 <label htmlFor="siteName" className="text-sm font-medium">
//                   Site Name
//                 </label>
//                 <Input
//                   id="siteName"
//                   value={settings.general.siteName}
//                   onChange={(e) =>
//                     handleInputChange("general", "siteName", e.target.value)
//                   }
//                 />
//               </div>
//               <div className="space-y-2">
//                 <label
//                   htmlFor="siteDescription"
//                   className="text-sm font-medium"
//                 >
//                   Site Description
//                 </label>
//                 <Textarea
//                   id="siteDescription"
//                   value={settings.general.siteDescription}
//                   onChange={(e) =>
//                     handleInputChange(
//                       "general",
//                       "siteDescription",
//                       e.target.value
//                     )
//                   }
//                   rows={3}
//                 />
//               </div>
//               <div className="space-y-2">
//                 <label htmlFor="contactEmail" className="text-sm font-medium">
//                   Contact Email
//                 </label>
//                 <Input
//                   id="contactEmail"
//                   type="email"
//                   value={settings.general.contactEmail}
//                   onChange={(e) =>
//                     handleInputChange("general", "contactEmail", e.target.value)
//                   }
//                 />
//               </div>
//               <div className="space-y-2">
//                 <label htmlFor="supportPhone" className="text-sm font-medium">
//                   Support Phone
//                 </label>
//                 <Input
//                   id="supportPhone"
//                   value={settings.general.supportPhone}
//                   onChange={(e) =>
//                     handleInputChange("general", "supportPhone", e.target.value)
//                   }
//                 />
//               </div>
//               <div className="flex items-center justify-between">
//                 <div className="space-y-0.5">
//                   <label
//                     htmlFor="maintenanceMode"
//                     className="text-sm font-medium"
//                   >
//                     Maintenance Mode
//                   </label>
//                   <p className="text-xs text-muted-foreground">
//                     When enabled, the site will display a maintenance message to
//                     all non-admin users
//                   </p>
//                 </div>
//                 <Switch
//                   id="maintenanceMode"
//                   checked={settings.general.maintenanceMode}
//                   onCheckedChange={(checked) =>
//                     handleInputChange("general", "maintenanceMode", checked)
//                   }
//                 />
//               </div>
//             </CardContent>
//             <CardFooter>
//               <Button onClick={handleSaveSettings} disabled={isSaving}>
//                 {isSaving ? "Saving..." : "Save Changes"}
//               </Button>
//             </CardFooter>
//           </Card>
//         </TabsContent>

//         <TabsContent value="api">
//           <Card>
//             <CardHeader>
//               <CardTitle>API Configuration</CardTitle>
//               <CardDescription>
//                 Configure API keys and endpoints for third-party services
//               </CardDescription>
//             </CardHeader>
//             <CardContent className="space-y-4">
//               <div className="space-y-4">
//                 <h3 className="text-lg font-medium">
//                   Video Generation (Synthesia)
//                 </h3>
//                 <div className="space-y-2">
//                   <label htmlFor="videoApiKey" className="text-sm font-medium">
//                     API Key
//                   </label>
//                   <Input
//                     id="videoApiKey"
//                     type="password"
//                     value={settings.api.videoApiKey}
//                     onChange={(e) =>
//                       handleInputChange("api", "videoApiKey", e.target.value)
//                     }
//                   />
//                 </div>
//                 <div className="space-y-2">
//                   <label
//                     htmlFor="videoApiEndpoint"
//                     className="text-sm font-medium"
//                   >
//                     API Endpoint
//                   </label>
//                   <Input
//                     id="videoApiEndpoint"
//                     value={settings.api.videoApiEndpoint}
//                     onChange={(e) =>
//                       handleInputChange(
//                         "api",
//                         "videoApiEndpoint",
//                         e.target.value
//                       )
//                     }
//                   />
//                 </div>
//               </div>

//               <div className="space-y-4">
//                 <h3 className="text-lg font-medium">
//                   Image Generation (Stability AI)
//                 </h3>
//                 <div className="space-y-2">
//                   <label htmlFor="imageApiKey" className="text-sm font-medium">
//                     API Key
//                   </label>
//                   <Input
//                     id="imageApiKey"
//                     type="password"
//                     value={settings.api.imageApiKey}
//                     onChange={(e) =>
//                       handleInputChange("api", "imageApiKey", e.target.value)
//                     }
//                   />
//                 </div>
//                 <div className="space-y-2">
//                   <label
//                     htmlFor="imageApiEndpoint"
//                     className="text-sm font-medium"
//                   >
//                     API Endpoint
//                   </label>
//                   <Input
//                     id="imageApiEndpoint"
//                     value={settings.api.imageApiEndpoint}
//                     onChange={(e) =>
//                       handleInputChange(
//                         "api",
//                         "imageApiEndpoint",
//                         e.target.value
//                       )
//                     }
//                   />
//                 </div>
//               </div>

//               <div className="space-y-4">
//                 <h3 className="text-lg font-medium">
//                   Background Removal (Removal.ai)
//                 </h3>
//                 <div className="space-y-2">
//                   <label
//                     htmlFor="backgroundApiKey"
//                     className="text-sm font-medium"
//                   >
//                     API Key
//                   </label>
//                   <Input
//                     id="backgroundApiKey"
//                     type="password"
//                     value={settings.api.backgroundApiKey}
//                     onChange={(e) =>
//                       handleInputChange(
//                         "api",
//                         "backgroundApiKey",
//                         e.target.value
//                       )
//                     }
//                   />
//                 </div>
//                 <div className="space-y-2">
//                   <label
//                     htmlFor="backgroundApiEndpoint"
//                     className="text-sm font-medium"
//                   >
//                     API Endpoint
//                   </label>
//                   <Input
//                     id="backgroundApiEndpoint"
//                     value={settings.api.backgroundApiEndpoint}
//                     onChange={(e) =>
//                       handleInputChange(
//                         "api",
//                         "backgroundApiEndpoint",
//                         e.target.value
//                       )
//                     }
//                   />
//                 </div>
//               </div>
//             </CardContent>
//             <CardFooter>
//               <Button onClick={handleSaveSettings} disabled={isSaving}>
//                 {isSaving ? "Saving..." : "Save Changes"}
//               </Button>
//             </CardFooter>
//           </Card>
//         </TabsContent>

//         <TabsContent value="email">
//           <Card>
//             <CardHeader>
//               <CardTitle>Email Configuration</CardTitle>
//               <CardDescription>
//                 Configure email settings for notifications and user
//                 communications
//               </CardDescription>
//             </CardHeader>
//             <CardContent className="space-y-4">
//               <div className="space-y-2">
//                 <label htmlFor="smtpServer" className="text-sm font-medium">
//                   SMTP Server
//                 </label>
//                 <Input
//                   id="smtpServer"
//                   value={settings.email.smtpServer}
//                   onChange={(e) =>
//                     handleInputChange("email", "smtpServer", e.target.value)
//                   }
//                 />
//               </div>
//               <div className="space-y-2">
//                 <label htmlFor="smtpPort" className="text-sm font-medium">
//                   SMTP Port
//                 </label>
//                 <Input
//                   id="smtpPort"
//                   value={settings.email.smtpPort}
//                   onChange={(e) =>
//                     handleInputChange("email", "smtpPort", e.target.value)
//                   }
//                 />
//               </div>
//               <div className="space-y-2">
//                 <label htmlFor="smtpUsername" className="text-sm font-medium">
//                   SMTP Username
//                 </label>
//                 <Input
//                   id="smtpUsername"
//                   value={settings.email.smtpUsername}
//                   onChange={(e) =>
//                     handleInputChange("email", "smtpUsername", e.target.value)
//                   }
//                 />
//               </div>
//               <div className="space-y-2">
//                 <label htmlFor="smtpPassword" className="text-sm font-medium">
//                   SMTP Password
//                 </label>
//                 <Input
//                   id="smtpPassword"
//                   type="password"
//                   value={settings.email.smtpPassword}
//                   onChange={(e) =>
//                     handleInputChange("email", "smtpPassword", e.target.value)
//                   }
//                 />
//               </div>
//               <div className="space-y-2">
//                 <label htmlFor="senderEmail" className="text-sm font-medium">
//                   Sender Email
//                 </label>
//                 <Input
//                   id="senderEmail"
//                   type="email"
//                   value={settings.email.senderEmail}
//                   onChange={(e) =>
//                     handleInputChange("email", "senderEmail", e.target.value)
//                   }
//                 />
//               </div>
//               <div className="space-y-2">
//                 <label htmlFor="senderName" className="text-sm font-medium">
//                   Sender Name
//                 </label>
//                 <Input
//                   id="senderName"
//                   value={settings.email.senderName}
//                   onChange={(e) =>
//                     handleInputChange("email", "senderName", e.target.value)
//                   }
//                 />
//               </div>
//             </CardContent>
//             <CardFooter>
//               <Button onClick={handleSaveSettings} disabled={isSaving}>
//                 {isSaving ? "Saving..." : "Save Changes"}
//               </Button>
//             </CardFooter>
//           </Card>
//         </TabsContent>

//         <TabsContent value="payment">
//           <Card>
//             <CardHeader>
//               <CardTitle>Payment Configuration</CardTitle>
//               <CardDescription>
//                 Configure payment gateways and settings
//               </CardDescription>
//             </CardHeader>
//             <CardContent className="space-y-4">
//               <div className="space-y-4">
//                 <h3 className="text-lg font-medium">Stripe</h3>
//                 <div className="flex items-center justify-between">
//                   <div className="space-y-0.5">
//                     <label
//                       htmlFor="enableStripe"
//                       className="text-sm font-medium"
//                     >
//                       Enable Stripe
//                     </label>
//                     <p className="text-xs text-muted-foreground">
//                       Allow users to pay with Stripe
//                     </p>
//                   </div>
//                   <Switch
//                     id="enableStripe"
//                     checked={settings.payment.enableStripe}
//                     onCheckedChange={(checked) =>
//                       handleInputChange("payment", "enableStripe", checked)
//                     }
//                   />
//                 </div>
//                 <div className="space-y-2">
//                   <label
//                     htmlFor="stripePublicKey"
//                     className="text-sm font-medium"
//                   >
//                     Stripe Public Key
//                   </label>
//                   <Input
//                     id="stripePublicKey"
//                     value={settings.payment.stripePublicKey}
//                     onChange={(e) =>
//                       handleInputChange(
//                         "payment",
//                         "stripePublicKey",
//                         e.target.value
//                       )
//                     }
//                   />
//                 </div>
//                 <div className="space-y-2">
//                   <label
//                     htmlFor="stripeSecretKey"
//                     className="text-sm font-medium"
//                   >
//                     Stripe Secret Key
//                   </label>
//                   <Input
//                     id="stripeSecretKey"
//                     type="password"
//                     value={settings.payment.stripeSecretKey}
//                     onChange={(e) =>
//                       handleInputChange(
//                         "payment",
//                         "stripeSecretKey",
//                         e.target.value
//                       )
//                     }
//                   />
//                 </div>
//               </div>

//               <div className="space-y-4">
//                 <h3 className="text-lg font-medium">PayPal</h3>
//                 <div className="flex items-center justify-between">
//                   <div className="space-y-0.5">
//                     <label
//                       htmlFor="enablePaypal"
//                       className="text-sm font-medium"
//                     >
//                       Enable PayPal
//                     </label>
//                     <p className="text-xs text-muted-foreground">
//                       Allow users to pay with PayPal
//                     </p>
//                   </div>
//                   <Switch
//                     id="enablePaypal"
//                     checked={settings.payment.enablePaypal}
//                     onCheckedChange={(checked) =>
//                       handleInputChange("payment", "enablePaypal", checked)
//                     }
//                   />
//                 </div>
//                 <div className="space-y-2">
//                   <label
//                     htmlFor="paypalClientId"
//                     className="text-sm font-medium"
//                   >
//                     PayPal Client ID
//                   </label>
//                   <Input
//                     id="paypalClientId"
//                     value={settings.payment.paypalClientId}
//                     onChange={(e) =>
//                       handleInputChange(
//                         "payment",
//                         "paypalClientId",
//                         e.target.value
//                       )
//                     }
//                   />
//                 </div>
//                 <div className="space-y-2">
//                   <label htmlFor="paypalSecret" className="text-sm font-medium">
//                     PayPal Secret
//                   </label>
//                   <Input
//                     id="paypalSecret"
//                     type="password"
//                     value={settings.payment.paypalSecret}
//                     onChange={(e) =>
//                       handleInputChange(
//                         "payment",
//                         "paypalSecret",
//                         e.target.value
//                       )
//                     }
//                   />
//                 </div>
//               </div>

//               <div className="flex items-center justify-between">
//                 <div className="space-y-0.5">
//                   <label htmlFor="testMode" className="text-sm font-medium">
//                     Test Mode
//                   </label>
//                   <p className="text-xs text-muted-foreground">
//                     When enabled, payments will be processed in test/sandbox
//                     mode
//                   </p>
//                 </div>
//                 <Switch
//                   id="testMode"
//                   checked={settings.payment.testMode}
//                   onCheckedChange={(checked) =>
//                     handleInputChange("payment", "testMode", checked)
//                   }
//                 />
//               </div>
//             </CardContent>
//             <CardFooter>
//               <Button onClick={handleSaveSettings} disabled={isSaving}>
//                 {isSaving ? "Saving..." : "Save Changes"}
//               </Button>
//             </CardFooter>
//           </Card>
//         </TabsContent>

//         <TabsContent value="limits">
//           <Card>
//             <CardHeader>
//               <CardTitle>Usage Limits</CardTitle>
//               <CardDescription>
//                 Configure usage limits for different subscription plans
//               </CardDescription>
//             </CardHeader>
//             <CardContent>
//               <div className="space-y-6">
//                 <div>
//                   <h3 className="text-lg font-medium mb-4">Free Plan</h3>
//                   <div className="grid grid-cols-3 gap-4">
//                     <div className="space-y-2">
//                       <label
//                         htmlFor="freeVideoLimit"
//                         className="text-sm font-medium"
//                       >
//                         Video Generation
//                       </label>
//                       <Input
//                         id="freeVideoLimit"
//                         type="number"
//                         value={settings.limits.freeVideoLimit}
//                         onChange={(e) =>
//                           handleInputChange(
//                             "limits",
//                             "freeVideoLimit",
//                             parseInt(e.target.value)
//                           )
//                         }
//                       />
//                     </div>
//                     <div className="space-y-2">
//                       <label
//                         htmlFor="freeImageLimit"
//                         className="text-sm font-medium"
//                       >
//                         Image Generation
//                       </label>
//                       <Input
//                         id="freeImageLimit"
//                         type="number"
//                         value={settings.limits.freeImageLimit}
//                         onChange={(e) =>
//                           handleInputChange(
//                             "limits",
//                             "freeImageLimit",
//                             parseInt(e.target.value)
//                           )
//                         }
//                       />
//                     </div>
//                     <div className="space-y-2">
//                       <label
//                         htmlFor="freeBackgroundLimit"
//                         className="text-sm font-medium"
//                       >
//                         Background Removal
//                       </label>
//                       <Input
//                         id="freeBackgroundLimit"
//                         type="number"
//                         value={settings.limits.freeBackgroundLimit}
//                         onChange={(e) =>
//                           handleInputChange(
//                             "limits",
//                             "freeBackgroundLimit",
//                             parseInt(e.target.value)
//                           )
//                         }
//                       />
//                     </div>
//                   </div>
//                 </div>

//                 <div>
//                   <h3 className="text-lg font-medium mb-4">Starter Plan</h3>
//                   <div className="grid grid-cols-3 gap-4">
//                     <div className="space-y-2">
//                       <label
//                         htmlFor="starterVideoLimit"
//                         className="text-sm font-medium"
//                       >
//                         Video Generation
//                       </label>
//                       <Input
//                         id="starterVideoLimit"
//                         type="number"
//                         value={settings.limits.starterVideoLimit}
//                         onChange={(e) =>
//                           handleInputChange(
//                             "limits",
//                             "starterVideoLimit",
//                             parseInt(e.target.value)
//                           )
//                         }
//                       />
//                     </div>
//                     <div className="space-y-2">
//                       <label
//                         htmlFor="starterImageLimit"
//                         className="text-sm font-medium"
//                       >
//                         Image Generation
//                       </label>
//                       <Input
//                         id="starterImageLimit"
//                         type="number"
//                         value={settings.limits.starterImageLimit}
//                         onChange={(e) =>
//                           handleInputChange(
//                             "limits",
//                             "starterImageLimit",
//                             parseInt(e.target.value)
//                           )
//                         }
//                       />
//                     </div>
//                     <div className="space-y-2">
//                       <label
//                         htmlFor="starterBackgroundLimit"
//                         className="text-sm font-medium"
//                       >
//                         Background Removal
//                       </label>
//                       <Input
//                         id="starterBackgroundLimit"
//                         type="number"
//                         value={settings.limits.starterBackgroundLimit}
//                         onChange={(e) =>
//                           handleInputChange(
//                             "limits",
//                             "starterBackgroundLimit",
//                             parseInt(e.target.value)
//                           )
//                         }
//                       />
//                     </div>
//                   </div>
//                 </div>

//                 <div>
//                   <h3 className="text-lg font-medium mb-4">Pro Plan</h3>
//                   <div className="grid grid-cols-3 gap-4">
//                     <div className="space-y-2">
//                       <label
//                         htmlFor="proVideoLimit"
//                         className="text-sm font-medium"
//                       >
//                         Video Generation
//                       </label>
//                       <Input
//                         id="proVideoLimit"
//                         type="number"
//                         value={settings.limits.proVideoLimit}
//                         onChange={(e) =>
//                           handleInputChange(
//                             "limits",
//                             "proVideoLimit",
//                             parseInt(e.target.value)
//                           )
//                         }
//                       />
//                     </div>
//                     <div className="space-y-2">
//                       <label
//                         htmlFor="proImageLimit"
//                         className="text-sm font-medium"
//                       >
//                         Image Generation
//                       </label>
//                       <Input
//                         id="proImageLimit"
//                         type="number"
//                         value={settings.limits.proImageLimit}
//                         onChange={(e) =>
//                           handleInputChange(
//                             "limits",
//                             "proImageLimit",
//                             parseInt(e.target.value)
//                           )
//                         }
//                       />
//                     </div>
//                     <div className="space-y-2">
//                       <label
//                         htmlFor="proBackgroundLimit"
//                         className="text-sm font-medium"
//                       >
//                         Background Removal
//                       </label>
//                       <Input
//                         id="proBackgroundLimit"
//                         type="number"
//                         value={settings.limits.proBackgroundLimit}
//                         onChange={(e) =>
//                           handleInputChange(
//                             "limits",
//                             "proBackgroundLimit",
//                             parseInt(e.target.value)
//                           )
//                         }
//                       />
//                     </div>
//                   </div>
//                 </div>

//                 <div>
//                   <h3 className="text-lg font-medium mb-4">Business Plan</h3>
//                   <div className="grid grid-cols-3 gap-4">
//                     <div className="space-y-2">
//                       <label
//                         htmlFor="businessVideoLimit"
//                         className="text-sm font-medium"
//                       >
//                         Video Generation
//                       </label>
//                       <Input
//                         id="businessVideoLimit"
//                         value={settings.limits.businessVideoLimit}
//                         onChange={(e) =>
//                           handleInputChange(
//                             "limits",
//                             "businessVideoLimit",
//                             e.target.value
//                           )
//                         }
//                         placeholder="Unlimited"
//                       />
//                     </div>
//                     <div className="space-y-2">
//                       <label
//                         htmlFor="businessImageLimit"
//                         className="text-sm font-medium"
//                       >
//                         Image Generation
//                       </label>
//                       <Input
//                         id="businessImageLimit"
//                         value={settings.limits.businessImageLimit}
//                         onChange={(e) =>
//                           handleInputChange(
//                             "limits",
//                             "businessImageLimit",
//                             e.target.value
//                           )
//                         }
//                         placeholder="Unlimited"
//                       />
//                     </div>
//                     <div className="space-y-2">
//                       <label
//                         htmlFor="businessBackgroundLimit"
//                         className="text-sm font-medium"
//                       >
//                         Background Removal
//                       </label>
//                       <Input
//                         id="businessBackgroundLimit"
//                         value={settings.limits.businessBackgroundLimit}
//                         onChange={(e) =>
//                           handleInputChange(
//                             "limits",
//                             "businessBackgroundLimit",
//                             e.target.value
//                           )
//                         }
//                         placeholder="Unlimited"
//                       />
//                     </div>
//                   </div>
//                 </div>
//               </div>
//             </CardContent>
//             <CardFooter>
//               <Button onClick={handleSaveSettings} disabled={isSaving}>
//                 {isSaving ? "Saving..." : "Save Changes"}
//               </Button>
//             </CardFooter>
//           </Card>
//         </TabsContent>

//         <TabsContent value="blog">
//           <Card>
//             <CardHeader>
//               <CardTitle>Blog Settings</CardTitle>
//               <CardDescription>
//                 Configure settings for the blog functionality
//               </CardDescription>
//             </CardHeader>
//             <CardContent className="space-y-4">
//               <div className="space-y-2">
//                 <label htmlFor="postsPerPage" className="text-sm font-medium">
//                   Posts Per Page
//                 </label>
//                 <Input
//                   id="postsPerPage"
//                   type="number"
//                   value={settings.blog.postsPerPage}
//                   onChange={(e) =>
//                     handleInputChange(
//                       "blog",
//                       "postsPerPage",
//                       parseInt(e.target.value)
//                     )
//                   }
//                 />
//               </div>
//               <div className="space-y-2">
//                 <label
//                   htmlFor="featuredPostsCount"
//                   className="text-sm font-medium"
//                 >
//                   Featured Posts Count
//                 </label>
//                 <Input
//                   id="featuredPostsCount"
//                   type="number"
//                   value={settings.blog.featuredPostsCount}
//                   onChange={(e) =>
//                     handleInputChange(
//                       "blog",
//                       "featuredPostsCount",
//                       parseInt(e.target.value)
//                     )
//                   }
//                 />
//               </div>
//               <div className="flex items-center justify-between">
//                 <div className="space-y-0.5">
//                   <label
//                     htmlFor="enableComments"
//                     className="text-sm font-medium"
//                   >
//                     Enable Comments
//                   </label>
//                   <p className="text-xs text-muted-foreground">
//                     Allow users to comment on blog posts
//                   </p>
//                 </div>
//                 <Switch
//                   id="enableComments"
//                   checked={settings.blog.enableComments}
//                   onCheckedChange={(checked) =>
//                     handleInputChange("blog", "enableComments", checked)
//                   }
//                 />
//               </div>
//               <div className="flex items-center justify-between">
//                 <div className="space-y-0.5">
//                   <label
//                     htmlFor="moderateComments"
//                     className="text-sm font-medium"
//                   >
//                     Moderate Comments
//                   </label>
//                   <p className="text-xs text-muted-foreground">
//                     Require admin approval before comments are published
//                   </p>
//                 </div>
//                 <Switch
//                   id="moderateComments"
//                   checked={settings.blog.moderateComments}
//                   onCheckedChange={(checked) =>
//                     handleInputChange("blog", "moderateComments", checked)
//                   }
//                 />
//               </div>
//               <div className="flex items-center justify-between">
//                 <div className="space-y-0.5">
//                   <label
//                     htmlFor="allowGuestComments"
//                     className="text-sm font-medium"
//                   >
//                     Allow Guest Comments
//                   </label>
//                   <p className="text-xs text-muted-foreground">
//                     Allow non-registered users to comment on blog posts
//                   </p>
//                 </div>
//                 <Switch
//                   id="allowGuestComments"
//                   checked={settings.blog.allowGuestComments}
//                   onCheckedChange={(checked) =>
//                     handleInputChange("blog", "allowGuestComments", checked)
//                   }
//                 />
//               </div>
//             </CardContent>
//             <CardFooter>
//               <Button onClick={handleSaveSettings} disabled={isSaving}>
//                 {isSaving ? "Saving..." : "Save Changes"}
//               </Button>
//             </CardFooter>
//           </Card>
//         </TabsContent>
//       </Tabs>
//     </div>
//   );
// };

// export default AdminSettingsPage;
