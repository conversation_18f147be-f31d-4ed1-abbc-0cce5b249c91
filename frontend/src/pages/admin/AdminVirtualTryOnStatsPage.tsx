import { useEffect, useMemo, useState } from "react";
import { useAdminHeader } from "@/contexts/AdminHeaderContext";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Spinner } from "@/components/ui/spinner";
import { toast } from "sonner";
import {
  Users,
  Shirt,
  Activity,
  CheckCircle,
  XCircle,
  Clock,
} from "lucide-react";
import AdminService from "@/services/admin.service";

interface VirtualTryOnStats {
  jobs: {
    total: number;
    completed: number;
    failed: number;
    pending: number;
    successRate: number;
  };
  models: {
    total: number;
    admin: number;
    user: number;
  };
  clothing: {
    total: number;
    admin: number;
    user: number;
  };
  recentJobs: Array<{
    id: string;
    status: string;
    mode: string;
    createdAt: string;
    user: {
      id: string;
      email: string;
      firstName: string;
      lastName: string;
    };
  }>;
}

const AdminVirtualTryOnStatsPage: React.FC = () => {
  const { setHeaderContent } = useAdminHeader();
  const [stats, setStats] = useState<VirtualTryOnStats | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);

  const headerContent = useMemo(() => null, []);

  useEffect(() => {
    setHeaderContent({
      title: "Virtual Try-On Statistics",
      description: "Analytics and insights for the virtual try-on feature",
      content: headerContent,
    });

    return () => setHeaderContent({});
  }, [headerContent, setHeaderContent]);

  const fetchStats = async () => {
    try {
      setIsLoading(true);
      const response = await AdminService.getVirtualTryOnStats();

      if (response.success) {
        setStats(response.data as VirtualTryOnStats);
      } else {
        toast.error("Failed to fetch statistics");
      }
    } catch (error) {
      console.error("Error fetching statistics:", error);
      toast.error("Failed to fetch statistics");
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchStats();
  }, []);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "COMPLETED":
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case "FAILED":
        return <XCircle className="w-4 h-4 text-red-500" />;
      case "PROCESSING":
        return <Activity className="w-4 h-4 text-blue-500" />;
      default:
        return <Clock className="w-4 h-4 text-yellow-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "COMPLETED":
        return "bg-green-100 text-green-800";
      case "FAILED":
        return "bg-red-100 text-red-800";
      case "PROCESSING":
        return "bg-blue-100 text-blue-800";
      default:
        return "bg-yellow-100 text-yellow-800";
    }
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center py-12">
        <Spinner className="w-8 h-8" />
      </div>
    );
  }

  if (!stats) {
    return (
      <div className="text-center py-12 text-gray-400">
        Failed to load statistics
      </div>
    );
  }

  return (
    <div className="space-y-8 px-4 sm:px-6 lg:px-8">
      {/* Overview Stats */}
      <div className="grid gap-4 grid-cols-1 sm:grid-cols-2 lg:grid-cols-4">
        <Card className="bg-white/5 border border-gray-600 text-white rounded-2xl">
          <CardHeader className="space-y-0 pb-2">
            <CardTitle className="text-sm font-normal flex items-center gap-2">
              <Activity className="w-4 h-4" />
              Total Jobs
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-semibold">{stats.jobs.total}</div>
            <p className="text-xs text-muted-foreground">
              {stats.jobs.successRate.toFixed(1)}% success rate
            </p>
          </CardContent>
        </Card>

        <Card className="bg-white/5 border border-gray-600 text-white rounded-2xl">
          <CardHeader className="space-y-0 pb-2">
            <CardTitle className="text-sm font-normal flex items-center gap-2">
              <CheckCircle className="w-4 h-4 text-green-500" />
              Completed Jobs
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-semibold">{stats.jobs.completed}</div>
            <p className="text-xs text-muted-foreground">
              {stats.jobs.total > 0
                ? ((stats.jobs.completed / stats.jobs.total) * 100).toFixed(1)
                : 0}
              % of total
            </p>
          </CardContent>
        </Card>

        <Card className="bg-white/5 border border-gray-600 text-white rounded-2xl">
          <CardHeader className="space-y-0 pb-2">
            <CardTitle className="text-sm font-normal flex items-center gap-2">
              <Users className="w-4 h-4" />
              Model Images
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-semibold">{stats.models.total}</div>
            <p className="text-xs text-muted-foreground">
              {stats.models.admin} admin, {stats.models.user} user
            </p>
          </CardContent>
        </Card>

        <Card className="bg-white/5 border border-gray-600 text-white rounded-2xl">
          <CardHeader className="space-y-0 pb-2">
            <CardTitle className="text-sm font-normal flex items-center gap-2">
              <Shirt className="w-4 h-4" />
              Clothing Items
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-semibold">{stats.clothing.total}</div>
            <p className="text-xs text-muted-foreground">
              {stats.clothing.admin} admin, {stats.clothing.user} user
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Job Status Breakdown */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        <Card className="bg-white/5 border border-gray-600 text-white rounded-2xl">
          <CardHeader>
            <CardTitle>Job Status Breakdown</CardTitle>
            <CardDescription>Distribution of job statuses</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <CheckCircle className="w-4 h-4 text-green-500" />
                  <span>Completed</span>
                </div>
                <div className="text-right">
                  <div className="font-semibold">{stats.jobs.completed}</div>
                  <div className="text-xs text-gray-400">
                    {stats.jobs.total > 0
                      ? (
                          (stats.jobs.completed / stats.jobs.total) *
                          100
                        ).toFixed(1)
                      : 0}
                    %
                  </div>
                </div>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <XCircle className="w-4 h-4 text-red-500" />
                  <span>Failed</span>
                </div>
                <div className="text-right">
                  <div className="font-semibold">{stats.jobs.failed}</div>
                  <div className="text-xs text-gray-400">
                    {stats.jobs.total > 0
                      ? ((stats.jobs.failed / stats.jobs.total) * 100).toFixed(
                          1
                        )
                      : 0}
                    %
                  </div>
                </div>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Clock className="w-4 h-4 text-yellow-500" />
                  <span>Pending</span>
                </div>
                <div className="text-right">
                  <div className="font-semibold">{stats.jobs.pending}</div>
                  <div className="text-xs text-gray-400">
                    {stats.jobs.total > 0
                      ? ((stats.jobs.pending / stats.jobs.total) * 100).toFixed(
                          1
                        )
                      : 0}
                    %
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-white/5 border border-gray-600 text-white rounded-2xl">
          <CardHeader>
            <CardTitle>Content Distribution</CardTitle>
            <CardDescription>Admin vs user generated content</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <div className="flex justify-between items-center mb-2">
                  <span>Model Images</span>
                  <span className="text-sm text-gray-400">
                    {stats.models.total} total
                  </span>
                </div>
                <div className="w-full bg-gray-700 rounded-full h-2">
                  <div
                    className="bg-purple-600 h-2 rounded-full"
                    style={{
                      width:
                        stats.models.total > 0
                          ? `${
                              (stats.models.admin / stats.models.total) * 100
                            }%`
                          : "0%",
                    }}
                  ></div>
                </div>
                <div className="flex justify-between text-xs text-gray-400 mt-1">
                  <span>Admin: {stats.models.admin}</span>
                  <span>User: {stats.models.user}</span>
                </div>
              </div>
              <div>
                <div className="flex justify-between items-center mb-2">
                  <span>Clothing Items</span>
                  <span className="text-sm text-gray-400">
                    {stats.clothing.total} total
                  </span>
                </div>
                <div className="w-full bg-gray-700 rounded-full h-2">
                  <div
                    className="bg-blue-600 h-2 rounded-full"
                    style={{
                      width:
                        stats.clothing.total > 0
                          ? `${
                              (stats.clothing.admin / stats.clothing.total) *
                              100
                            }%`
                          : "0%",
                    }}
                  ></div>
                </div>
                <div className="flex justify-between text-xs text-gray-400 mt-1">
                  <span>Admin: {stats.clothing.admin}</span>
                  <span>User: {stats.clothing.user}</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Recent Jobs */}
      <Card className="bg-white/5 border border-gray-600 text-white rounded-2xl">
        <CardHeader>
          <CardTitle>Recent Jobs</CardTitle>
          <CardDescription>
            Latest virtual try-on processing jobs
          </CardDescription>
        </CardHeader>
        <CardContent>
          {stats.recentJobs.length === 0 ? (
            <div className="text-center py-8 text-gray-400">
              No recent jobs found
            </div>
          ) : (
            <div className="space-y-4">
              {stats.recentJobs.map((job) => (
                <div
                  key={job.id}
                  className="flex items-center justify-between border-b border-gray-600 pb-4 last:border-0 last:pb-0"
                >
                  <div className="flex items-center gap-3">
                    {getStatusIcon(job.status)}
                    <div>
                      <p className="font-medium">
                        {job.mode === "SINGLE" ? "Single Item" : "Combination"}{" "}
                        Try-On
                      </p>
                      <p className="text-sm text-gray-400">
                        {job.user.firstName} {job.user.lastName} (
                        {job.user.email})
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <span
                      className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(
                        job.status
                      )}`}
                    >
                      {job.status}
                    </span>
                    <p className="text-xs text-gray-400 mt-1">
                      {new Date(job.createdAt).toLocaleDateString()}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default AdminVirtualTryOnStatsPage;
