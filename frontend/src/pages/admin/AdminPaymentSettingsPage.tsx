import ShadowButton from "@/components/ui/shadowButton";
import { useAdminHeader } from "@/contexts/AdminHeaderContext";
import { useEffect, useState } from "react";

// Define types for the payment methods
type PaymentMethods = {
  stripe: boolean;
  paypal: boolean;
};

// Define types for the Stripe configuration
type StripeConfig = {
  apiKey: string;
  secretKey: string;
  webhookSecret: string;
};

// Define type for the field parameter in handleInputChange
type StripeConfigField = keyof StripeConfig;

const AdminPaymentSettingsPage = () => {
  const { setHeaderContent } = useAdminHeader();
  const [activeTab, setActiveTab] = useState<"stripe" | "paypal">("stripe");
  const [paymentMethods, setPaymentMethods] = useState<PaymentMethods>({
    stripe: true,
    paypal: true,
  });
  const [testMode, setTestMode] = useState<boolean>(true);
  const [stripeConfig, setStripeConfig] = useState<StripeConfig>({
    apiKey: "pk_test",
    secretKey: "pk_test",
    webhookSecret: "pk_test",
  });

  // Set header with button
  useEffect(() => {
    setHeaderContent({
      title: "Subscription Plans",
      description: "Manage your payment gateways and configurations",
      content: null,
    });
    return () => setHeaderContent({});
  }, []);

  const togglePaymentMethod = (method: keyof PaymentMethods) => {
    setPaymentMethods((prev) => ({
      ...prev,
      [method]: !prev[method],
    }));
  };

  const handleInputChange = (field: StripeConfigField, value: string) => {
    setStripeConfig((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleSaveConfiguration = () => {
    console.log("Saving configuration:", stripeConfig);
    // Add your save logic here
  };

  const handleTestConnection = () => {
    console.log("Testing connection...");
    // Add your test connection logic here
  };

  return (
    <div className="min-h-screen text-white p-4">
      <div className="mx-auto">
        {/* Payment Methods Section */}
        <div className="bg-white/5 border border-gray-500 rounded-xl px-6 pt-4 pb-10 mb-6">
          <div className="mb-4">
            <h2 className="text-lg font-medium mb-1 font-inter">
              Payment Methods
            </h2>
            <p className="text-gray-300 text-sm">
              Enable or disable payment methods for your application
            </p>
          </div>

          <div className="space-y-4">
            {/* Stripe Toggle */}
            <div className="flex items-center justify-between px-4 py-2 bg-white/10 rounded-lg">
              <div className="flex items-center space-x-5">
                <div className="w-[69px] flex items-center justify-center">
                  <img loading="lazy" src="/png/logos_stripe.png" />
                </div>
                <div>
                  <div className="font-medium">Stripe</div>
                  <div className="text-gray-400 text-sm">Disabled</div>
                </div>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={paymentMethods.stripe}
                  onChange={() => togglePaymentMethod("stripe")}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-purple-500 after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-purple-300"></div>
              </label>
            </div>

            {/* PayPal Toggle */}
            <div className="flex items-center justify-between px-4 py-2 bg-white/10 rounded-lg">
              <div className="flex items-center space-x-5">
                <div className="w-[69px] flex items-center justify-center">
                  <img loading="lazy" src="/png/logos_paypal.png" />
                </div>
                <div>
                  <div className="font-medium">PayPal</div>
                  <div className="text-gray-400 text-sm">Disabled</div>
                </div>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={paymentMethods.paypal}
                  onChange={() => togglePaymentMethod("paypal")}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-purple-500 after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-purple-300"></div>
              </label>
            </div>
          </div>
        </div>

        {/* Configuration Tabs */}
        <div className="flex space-x-1 mb-6 pb-2 border-b-2 border-gray-600">
          <button
            onClick={() => setActiveTab("stripe")}
            className={`px-4 py-2 text-sm font-medium rounded-lg transition-colors ${
              activeTab === "stripe"
                ? "text-white"
                : "text-gray-400 hover:text-purple-500 cursor-pointer"
            }`}
          >
            Stripe Configurations
          </button>
          <button
            onClick={() => setActiveTab("paypal")}
            className={`px-4 py-2 text-sm font-medium rounded-lg transition-colors ${
              activeTab === "paypal"
                ? "text-white"
                : "text-gray-400 hover:text-purple-500 cursor-pointer"
            }`}
          >
            PayPal Configurations
          </button>
        </div>

        {/* Stripe Configuration */}
        {activeTab === "stripe" && (
          <div className="bg-white/5 border border-gray-600 rounded-xl p-6">
            <div className="mb-6">
              <h2 className="text-2xl font-medium mb-2">
                Stripe Configuration
              </h2>
              <p className="text-gray-400 text-sm">
                Configure your Stripe payment gateway settings
              </p>
            </div>

            <div className="space-y-6">
              {/* API Key */}
              <div>
                <label className="block text-sm font-medium mb-2">
                  API Key (Publishable Key)
                </label>
                <input
                  type="text"
                  value={stripeConfig.apiKey}
                  onChange={(e) => handleInputChange("apiKey", e.target.value)}
                  className="w-full px-3 py-2 bg-primary/30 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-600 focus:border-transparent"
                  placeholder="pk_test..."
                />
              </div>

              {/* Secret Key */}
              <div>
                <label className="block text-sm font-medium mb-2">
                  Secret Key
                </label>
                <input
                  type="password"
                  value={stripeConfig.secretKey}
                  onChange={(e) =>
                    handleInputChange("secretKey", e.target.value)
                  }
                  className="w-full px-3 py-2 bg-primary/30 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-600 focus:border-transparent"
                  placeholder="pk_test..."
                />
              </div>

              {/* Webhook Secret */}
              <div>
                <label className="block text-sm font-medium mb-2">
                  Webhook Secret
                </label>
                <input
                  type="password"
                  value={stripeConfig.webhookSecret}
                  onChange={(e) =>
                    handleInputChange("webhookSecret", e.target.value)
                  }
                  className="w-full px-3 py-2 bg-primary/30 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-600 focus:border-transparent"
                  placeholder="pk_test..."
                />
              </div>

              {/* Test Mode Toggle */}
              <div className="flex items-center space-x-3">
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    checked={testMode}
                    onChange={(e) => setTestMode(e.target.checked)}
                    className="sr-only peer"
                  />
                  <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-purple-600"></div>
                </label>
                <span className="text-sm font-medium">Test Mode</span>
              </div>

              {/* Action Buttons */}
              <div className="flex space-x-3 pt-4">
                <ShadowButton
                  onClick={handleSaveConfiguration}
                  className="px-6 py-2 bg-purple-600 hover:bg-purple-700 text-white font-medium rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-purple-600 focus:ring-offset-2 focus:ring-offset-gray-800"
                >
                  Save Configuration
                </ShadowButton>
                <button
                  onClick={handleTestConnection}
                  className="px-6 py-2 cursor-pointer border border-gray-500 hover:bg-gray-600 text-white font-medium rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-gray-600 focus:ring-offset-2 focus:ring-offset-gray-800"
                >
                  Test Connection
                </button>
              </div>
            </div>
          </div>
        )}

        {/* PayPal Configuration */}
        {activeTab === "paypal" && (
          <div className="bg-gray-800 rounded-lg p-6">
            <div className="mb-6">
              <h2 className="text-lg font-medium mb-2">PayPal Configuration</h2>
              <p className="text-gray-400 text-sm">
                Configure your PayPal payment gateway settings
              </p>
            </div>
            <div className="text-center py-8 text-gray-400">
              PayPal configuration panel would go here
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default AdminPaymentSettingsPage;

// import PaymentConfig from "@/components/dashboard/PaymentConfig";

// const AdminPaymentSettingsPage = () => {
//   return (
//     <div className="container mx-auto py-6 space-y-8">
//       <div>
//         <h1 className="text-3xl font-bold">Payment Settings</h1>
//         <p className="text-muted-foreground">
//           Manage your payment gateways and configuration
//         </p>
//       </div>

//       <PaymentConfig />
//     </div>
//   );
// };

// export default AdminPaymentSettingsPage;
