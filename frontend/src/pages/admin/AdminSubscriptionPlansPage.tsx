import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { toast } from "sonner";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Switch } from "@/components/ui/switch";
import { useAdminHeader } from "@/contexts/AdminHeaderContext";
import { Edit, Plus, Trash2 } from "lucide-react";
import AdminService from "@/services/admin.service";
import { type SubscriptionPlan } from "@/types/admin.types";
import {
  type SubscriptionPlanFormValues,
  subscriptionPlanSchema,
} from "@/types/form.types";

/**
 * NOTE: This file uses type assertions (as any) to work around TypeScript errors
 * caused by incompatible types between different versions of react-hook-form.
 * This is a temporary solution until we can upgrade all dependencies to compatible versions.
 */

// Placeholder for your custom modal (replace with your actual import)
interface CustomModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  children: React.ReactNode;
  title?: string;
}
// Replace this with your actual CustomModal import
const CustomModal: React.FC<CustomModalProps> = ({
  open,
  // onOpenChange,
  children,
  title,
}) => (
  <div
    className={`fixed inset-0 z-50 flex items-center justify-center bg-primary/90 ${
      open ? "" : "hidden"
    }`}
  >
    <div className="h-[90%] bg-gray-800 text-white rounded-2xl overflow-auto border border-gray-500 p-6 w-full max-w-[600px] mx-4">
      {title && <h2 className="text-lg font-semibold mb-4">{title}</h2>}
      <div>{children}</div>
    </div>
  </div>
);

// Form schema is imported from @/types/form.types

const PlanForm: React.FC<{
  onSubmit: (data: SubscriptionPlanFormValues) => Promise<void>;
  onClose: () => void;
  defaultValues?: SubscriptionPlanFormValues;
  isEdit?: boolean;
}> = ({ onSubmit, defaultValues, isEdit, onClose = () => {} }) => {
  const form = useForm<SubscriptionPlanFormValues>({
    // Using type assertion to bypass type conflicts between different versions of react-hook-form
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    resolver: zodResolver(subscriptionPlanSchema) as any,
    defaultValues: defaultValues || {
      name: "",
      displayName: "",
      description: "",
      price: 0,
      currency: "USD",
      interval: "month",
      features: {
        videoGenerationQuota: 0,
        imageGenerationQuota: 0,
        backgroundRemovalQuota: 0,
      },
      creditsAmount: 0,
      featureHighlights: [],
      stripePriceId: "",
      paypalPlanId: "",
      isActive: true,
    },
  });

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit((data) =>
          onSubmit(data as SubscriptionPlanFormValues)
        )}
        className="space-y-4"
      >
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Plan ID/Name</FormLabel>
              <FormControl>
                <Input placeholder="e.g. pro_plan" {...field} />
              </FormControl>
              <FormMessage />
              <p className="text-xs text-muted-foreground">
                Internal identifier for the plan
              </p>
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="displayName"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Display Name</FormLabel>
              <FormControl>
                <Input placeholder="e.g. Pro Plan" {...field} />
              </FormControl>
              <FormMessage />
              <p className="text-xs text-muted-foreground">
                User-friendly name shown to customers
              </p>
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="description"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Description</FormLabel>
              <FormControl>
                <Input
                  placeholder="Short description of the plan"
                  {...field}
                  value={field.value || ""}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name="price"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Price</FormLabel>
                <FormControl>
                  <Input type="number" min="0" step="0.01" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="currency"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Currency</FormLabel>
                <Select
                  onValueChange={field.onChange}
                  defaultValue={field.value}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {["USD", "EUR", "GBP"].map((currency) => (
                      <SelectItem key={currency} value={currency}>
                        {currency}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
        <FormField
          control={form.control}
          name="interval"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Billing Interval</FormLabel>
              <Select onValueChange={field.onChange} defaultValue={field.value}>
                <FormControl>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  <SelectItem value="month">Monthly</SelectItem>
                  <SelectItem value="year">Yearly</SelectItem>
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="creditsAmount"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Credits Amount</FormLabel>
              <FormControl>
                <Input type="number" min="0" {...field} />
              </FormControl>
              <FormMessage />
              <p className="text-xs text-muted-foreground">
                Number of credits granted with this subscription
              </p>
            </FormItem>
          )}
        />

        <div className="space-y-2">
          <FormLabel>Features</FormLabel>
          <div className="grid gap-4 border rounded-md p-4">
            {[
              {
                name: "features.videoGenerationQuota",
                label: "Video Generation Quota",
              },
              {
                name: "features.imageGenerationQuota",
                label: "Image Generation Quota",
              },
              {
                name: "features.backgroundRemovalQuota",
                label: "Background Removal Quota",
              },
            ].map(({ name, label }) => (
              <FormField
                key={name}
                control={form.control}
                // eslint-disable-next-line @typescript-eslint/no-explicit-any
                name={name as any}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{label}</FormLabel>
                    <FormControl>
                      <Input type="number" min="0" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            ))}
          </div>
        </div>

        <div className="space-y-2">
          <FormLabel>Feature Highlights</FormLabel>
          <div className="border rounded-md p-4">
            <p className="text-xs text-muted-foreground mb-2">
              Add marketing highlights for this plan (one per line)
            </p>
            <FormField
              control={form.control}
              name="featureHighlights"
              render={({ field }) => (
                <FormItem>
                  <FormControl>
                    <textarea
                      className="w-full min-h-[100px] bg-transparent border rounded-md p-2"
                      placeholder="e.g.\nUnlimited video generation\nPriority support\nAdvanced features"
                      value={field.value.join("\n")}
                      onChange={(e) => {
                        const value = e.target.value;
                        const highlights = value
                          .split("\n")
                          .filter((line) => line.trim() !== "");
                        field.onChange(highlights);
                      }}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        </div>
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name="stripePriceId"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Stripe Price ID (Optional)</FormLabel>
                <FormControl>
                  <Input
                    placeholder="price_..."
                    {...field}
                    value={field.value || ""}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="paypalPlanId"
            render={({ field }) => (
              <FormItem>
                <FormLabel>PayPal Plan ID (Optional)</FormLabel>
                <FormControl>
                  <Input
                    placeholder="P-..."
                    {...field}
                    value={field.value || ""}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
        <FormField
          control={form.control}
          name="isActive"
          render={({ field }) => (
            <FormItem className="flex items-center justify-between border p-3 rounded-lg">
              <div>
                <FormLabel>Active</FormLabel>
                <p className="text-sm text-muted-foreground">
                  Make this plan available
                </p>
              </div>
              <FormControl>
                <Switch
                  checked={field.value}
                  onCheckedChange={field.onChange}
                />
              </FormControl>
            </FormItem>
          )}
        />
        <div className="flex justify-end gap-2">
          <Button
            className="text-black"
            type="button"
            variant="outline"
            onClick={() => {
              form.reset();
              onClose();
            }}
          >
            Cancel
          </Button>
          <Button type="submit">
            {isEdit ? "Save Changes" : "Create Plan"}
          </Button>
        </div>
      </form>
    </Form>
  );
};

const AdminSubscriptionPlansPage: React.FC = () => {
  const { setHeaderContent } = useAdminHeader();
  const [plans, setPlans] = useState<SubscriptionPlan[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [dialogState, setDialogState] = useState<{
    open: boolean;
    mode: "create" | "edit";
    plan?: SubscriptionPlan;
  }>({ open: false, mode: "create" });

  // Fetch plans
  useEffect(() => {
    const fetchPlans = async () => {
      try {
        setLoading(true);
        const data = await AdminService.getSubscriptionPlans();
        setPlans(data);
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
      } catch (error) {
        toast.error("Failed to fetch subscription plans");
      } finally {
        setLoading(false);
      }
    };
    fetchPlans();
  }, []);

  // Set header with button
  useEffect(() => {
    setHeaderContent({
      title: "Subscription Plans",
      description: "Manage subscription plans for your application",
      content: (
        <Button
          className="bg-transparent border border-gray-500 flex items-center gap-2"
          onClick={() => setDialogState({ open: true, mode: "create" })}
        >
          <Plus className="w-4 h-4" />
          <span className="text-purple-500">Add New Plan</span>
        </Button>
      ),
    });
    return () => setHeaderContent({});
  }, [setHeaderContent]);

  // Handlers
  const handleSubmit = async (data: SubscriptionPlanFormValues) => {
    try {
      // Transform the data to ensure null values become undefined
      const transformedData = {
        ...data,
        description: data.description || undefined,
        stripePriceId: data.stripePriceId || undefined,
        paypalPlanId: data.paypalPlanId || undefined,
      };

      if (dialogState.mode === "create") {
        await AdminService.createSubscriptionPlan(transformedData);
        toast.success("Plan created successfully");
      } else if (dialogState.plan) {
        await AdminService.updateSubscriptionPlan(
          dialogState.plan.id,
          transformedData
        );
        toast.success("Plan updated successfully");
      }
      setDialogState({ ...dialogState, open: false });
      const data1 = await AdminService.getSubscriptionPlans();
      setPlans(data1);
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
    } catch (error) {
      toast.error(
        `Failed to ${dialogState.mode === "create" ? "create" : "update"} plan`
      );
    }
  };

  const handleDelete = async (planId: string) => {
    try {
      await AdminService.deleteSubscriptionPlan(planId);
      toast.success("Plan deleted successfully");
      const data = await AdminService.getSubscriptionPlans();
      setPlans(data);
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
    } catch (error) {
      toast.error("Failed to delete plan");
    }
  };

  const formatCurrency = (amount: number, currency: string) =>
    new Intl.NumberFormat("en-US", { style: "currency", currency }).format(
      amount
    );

  const tableRowStyle: React.CSSProperties = {
    position: "relative",
    borderBottom: "none",
  };

  return (
    <div className="space-y-6 px-4 sm:px-6 lg:px-8">
      <Card className="bg-white/5 text-white rounded-2xl border border-gray-500">
        <CardHeader>
          <CardTitle>All Subscription Plans</CardTitle>
          <CardDescription className="text-gray-400">
            View and manage all subscription plans
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex justify-center items-center h-40">
              Loading...
            </div>
          ) : plans.length === 0 ? (
            <div className="flex flex-col justify-center items-center h-40 gap-4">
              <p className="text-gray-400">No subscription plans found</p>
              <Button
                onClick={() => setDialogState({ open: true, mode: "create" })}
              >
                Create Your First Plan
              </Button>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <div className="rounded-lg overflow-hidden">
                <div className="relative w-full overflow-x-auto">
                  <table className="w-full caption-bottom text-sm">
                    <thead>
                      <tr className="">
                        {[
                          "Name",
                          "Price",
                          "Interval",
                          "Video Quota",
                          "Image Quota",
                          "BG Removal",
                          "Status",
                          "Actions",
                        ].map((head) => (
                          <th
                            key={head}
                            className="h-12 px-4 text-left align-middle font-semibold text-gray-200 whitespace-nowrap"
                          >
                            {head}
                          </th>
                        ))}
                      </tr>
                      <tr className="relative">
                        <td
                          colSpan={8}
                          className="absolute bottom-0 h-0 pointer-events-none"
                          style={{
                            left: "10%",
                            right: "10%",
                            background:
                              "linear-gradient(to right, transparent, #e5e7eb 30%, #e5e7eb 60%, transparent)",
                            height: "2px",
                          }}
                        />
                      </tr>
                    </thead>
                    <tbody>
                      {plans.map((plan, index) => (
                        <tr
                          key={plan.id}
                          className="transition-colors relative text-gray-200"
                          style={tableRowStyle}
                        >
                          <td className="p-4 align-middle whitespace-nowrap font-medium">
                            {plan.name}
                          </td>
                          <td className="p-4 align-middle whitespace-nowrap">
                            {formatCurrency(plan.price, plan.currency)}
                          </td>
                          <td className="p-4 align-middle whitespace-nowrap capitalize">
                            {plan.interval}
                          </td>
                          <td className="p-4 align-middle whitespace-nowrap">
                            {plan.features.videoGenerationQuota}
                          </td>
                          <td className="p-4 align-middle whitespace-nowrap">
                            {plan.features.imageGenerationQuota}
                          </td>
                          <td className="p-4 align-middle whitespace-nowrap">
                            {plan.features.backgroundRemovalQuota}
                          </td>
                          <td className="p-4 align-middle whitespace-nowrap">
                            <span
                              className={`px-2 py-1 rounded-full text-xs font-medium ${
                                plan.isActive
                                  ? "bg-white/70 text-green-800"
                                  : "bg-gray-100 text-gray-600"
                              }`}
                            >
                              {plan.isActive ? "Active" : "Inactive"}
                            </span>
                          </td>
                          <td className="p-4 align-middle whitespace-nowrap">
                            <div className="flex items-center gap-2">
                              <Button
                                variant="outline"
                                size="sm"
                                className="bg-transparent border border-gray-500 hover:bg-gray-400"
                                onClick={() =>
                                  setDialogState({
                                    open: true,
                                    mode: "edit",
                                    plan,
                                  })
                                }
                              >
                                <Edit className="w-4 h-4 mr-1" />
                                Edit
                              </Button>
                              <AlertDialog>
                                <AlertDialogTrigger>
                                  <Button
                                    className="bg-transparent border border-gray-500 hover:bg-gray-400"
                                    size="sm"
                                  >
                                    <Trash2 className="w-4 h-4 mr-1" />
                                  </Button>
                                </AlertDialogTrigger>
                                <AlertDialogContent>
                                  <AlertDialogHeader>
                                    <AlertDialogTitle>
                                      Confirm Deletion
                                    </AlertDialogTitle>
                                    <AlertDialogDescription>
                                      This action is permanent and will delete
                                      the plan "{plan.name}".
                                    </AlertDialogDescription>
                                  </AlertDialogHeader>
                                  <AlertDialogFooter>
                                    <AlertDialogCancel>
                                      Cancel
                                    </AlertDialogCancel>
                                    <AlertDialogAction
                                      onClick={() => handleDelete(plan.id)}
                                    >
                                      Delete
                                    </AlertDialogAction>
                                  </AlertDialogFooter>
                                </AlertDialogContent>
                              </AlertDialog>
                            </div>
                          </td>
                          {index !== plans.length - 1 && (
                            <td
                              className="absolute bottom-0 h-0 pointer-events-none"
                              style={{
                                left: "10%",
                                right: "10%",
                                background:
                                  "linear-gradient(to right, transparent, #e5e7eb 30%, #e5e7eb 60%, transparent)",
                                height: "2px",
                              }}
                            />
                          )}
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
              {/* <Table>
                <TableHeader>
                  <TableRow className="text-white">
                    {[
                      "Name",
                      "Price",
                      "Interval",
                      "Video Quota",
                      "Image Quota",
                      "BG Removal",
                      "Status",
                      "Actions",
                    ].map((head) => (
                      <TableHead
                        key={head}
                        className={head === "" ? "text-right text-white font-normal" : " text-white font-normal"}
                      >
                        {head}
                      </TableHead>
                    ))}
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {plans.map((plan) => (
                    <TableRow key={plan.id}>
                      <TableCell className="font-medium">{plan.name}</TableCell>
                      <TableCell>
                        {formatCurrency(plan.price, plan.currency)}
                      </TableCell>
                      <TableCell className="capitalize">
                        {plan.interval}
                      </TableCell>
                      <TableCell>
                        {plan.features.videoGenerationQuota}
                      </TableCell>
                      <TableCell>
                        {plan.features.imageGenerationQuota}
                      </TableCell>
                      <TableCell>
                        {plan.features.backgroundRemovalQuota}
                      </TableCell>
                      <TableCell>
                        <span
                          className={`px-2 py-1 rounded-full text-xs ${
                            plan.isActive
                              ? "bg-green-100 text-green-800"
                              : "bg-gray-100 text-gray-800"
                          }`}
                        >
                          {plan.isActive ? "Active" : "Inactive"}
                        </span>
                      </TableCell>
                      <TableCell className="text-right">
                        <div className="flex justify-end gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() =>
                              setDialogState({ open: true, mode: "edit", plan })
                            }
                          >
                            Edit
                          </Button>
                          <AlertDialog>
                            <AlertDialogTrigger asChild>
                              <Button variant="destructive" size="sm">
                                Delete
                              </Button>
                            </AlertDialogTrigger>
                            <AlertDialogContent>
                              <AlertDialogHeader>
                                <AlertDialogTitle>
                                  Confirm Deletion
                                </AlertDialogTitle>
                                <AlertDialogDescription>
                                  This action is permanent and will delete the
                                  plan.
                                </AlertDialogDescription>
                              </AlertDialogHeader>
                              <AlertDialogFooter>
                                <AlertDialogCancel>Cancel</AlertDialogCancel>
                                <AlertDialogAction
                                  onClick={() => handleDelete(plan.id)}
                                >
                                  Delete
                                </AlertDialogAction>
                              </AlertDialogFooter>
                            </AlertDialogContent>
                          </AlertDialog>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table> */}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Create/Edit Modal */}
      <CustomModal
        open={dialogState.open}
        onOpenChange={(open) => setDialogState({ ...dialogState, open })}
        title={
          dialogState.mode === "create"
            ? "Create Subscription Plan"
            : "Edit Subscription Plan"
        }
      >
        <PlanForm
          onSubmit={handleSubmit}
          onClose={() => setDialogState({ open: false, mode: "create" })}
          // defaultValues={
          //   dialogState.mode === "edit" && dialogState.plan
          //     ? {
          //         name: dialogState.plan.name,
          //         price: dialogState.plan.price,
          //         currency: dialogState.plan.currency,
          //         interval: dialogState.plan.interval,
          //         features: dialogState.plan.features,
          //         stripePriceId: dialogState.plan.stripePriceId,
          //         paypalPlanId: dialogState.plan.paypalPlanId,
          //         isActive: dialogState.plan.isActive,
          //       }
          //     : undefined
          // }
          isEdit={dialogState.mode === "edit"}
        />
      </CustomModal>
    </div>
  );
};

export default AdminSubscriptionPlansPage;
