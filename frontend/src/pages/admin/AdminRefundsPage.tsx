import { useState, useEffect } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { toast } from "sonner";
import { useAdminHeader } from "@/contexts/AdminHeaderContext";
import { format } from "date-fns";
import { Badge } from "@/components/ui/badge";
import ApiService from "@/services/api.service";
import { Button } from "@/components/ui/button";
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import type { ApiResponse } from "@/types/api.types";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
// import {
//   Select,
//   SelectContent,
//   SelectItem,
//   SelectTrigger,
//   SelectValue,
// } from "@/components/ui/select";

// Types
interface Refund {
  id: string;
  userId: string;
  userName: string;
  userEmail: string;
  paymentId: string;
  amount: number;
  currency: string;
  reason: string;
  status: "PENDING" | "APPROVED" | "REJECTED";
  notes: string;
  createdAt: string;
  updatedAt: string;
}

interface CanceledSubscription {
  id: string;
  userId: string;
  userName: string;
  userEmail: string;
  subscriptionId: string;
  planName: string;
  cancelReason: string;
  canceledAt: string;
  cancellationDate: string;
}

const AdminRefundsPage: React.FC = () => {
  const { setHeaderContent } = useAdminHeader();
  const [refunds, setRefunds] = useState<Refund[]>([]);
  const [canceledSubscriptions, setCanceledSubscriptions] = useState<
    CanceledSubscription[]
  >([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [activeTab, setActiveTab] = useState<string>("refunds");
  const [dialogState, setDialogState] = useState<{
    open: boolean;
    refundId: string;
    action: "approve" | "reject";
    notes: string;
  }>({
    open: false,
    refundId: "",
    action: "approve",
    notes: "",
  });

  // Fetch data
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);

        // Fetch refunds
        const refundsResponse = await ApiService.get<ApiResponse<Refund[]>>(
          "/admin/payment-management/refunds"
        );
        setRefunds(refundsResponse.data.data);

        // Fetch canceled subscriptions
        const subscriptionsResponse = await ApiService.get<
          ApiResponse<CanceledSubscription[]>
        >("/admin/payment-management/canceled-subscriptions");
        setCanceledSubscriptions(subscriptionsResponse.data.data);
      } catch (error) {
        toast.error("Failed to fetch data");
        console.error(error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  // Set header
  useEffect(() => {
    setHeaderContent({
      title: "Refunds & Cancellations",
      description: "Manage refund requests and subscription cancellations",
    });

    return () => setHeaderContent({});
  }, [setHeaderContent]);

  // Process refund request
  const handleProcessRefund = async () => {
    try {
      const { refundId, action, notes } = dialogState;

      await ApiService.patch(
        `/admin/payment-management/refunds/${refundId}/process`,
        {
          action,
          notes,
        }
      );

      toast.success(
        `Refund request ${action === "approve" ? "approved" : "rejected"}`
      );

      // Refresh refunds
      const refundsResponse = await ApiService.get<ApiResponse<Refund[]>>(
        "/admin/payment-management/refunds"
      );
      setRefunds(refundsResponse.data.data);

      // Close dialog
      setDialogState((prev) => ({ ...prev, open: false }));
    } catch (error) {
      toast.error("Failed to process refund");
      console.error(error);
    }
  };

  // Open dialog
  const openDialog = (refundId: string, action: "approve" | "reject") => {
    setDialogState({
      open: true,
      refundId,
      action,
      notes: "",
    });
  };

  // Format currency
  const formatCurrency = (amount: number, currency: string) =>
    new Intl.NumberFormat("en-US", { style: "currency", currency }).format(
      amount
    );

  // Format date
  const formatDate = (dateString: string) => {
    console.log("dateString", dateString);
    return format(new Date(dateString), "MMM d, yyyy h:mm a");
  };

  // Get status badge
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "PENDING":
        return (
          <Badge
            variant="outline"
            className="bg-yellow-500/20 text-yellow-500 border-yellow-500"
          >
            Pending
          </Badge>
        );
      case "APPROVED":
        return (
          <Badge
            variant="outline"
            className="bg-green-500/20 text-green-500 border-green-500"
          >
            Approved
          </Badge>
        );
      case "REJECTED":
        return (
          <Badge
            variant="outline"
            className="bg-red-500/20 text-red-500 border-red-500"
          >
            Rejected
          </Badge>
        );
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  return (
    <div className="space-y-6 px-4 sm:px-6 lg:px-8">
      <Card className="bg-white/5 text-white rounded-2xl border border-gray-500">
        <CardHeader>
          <CardTitle>Refunds & Cancellations</CardTitle>
          <CardDescription className="text-gray-400">
            Manage refund requests and subscription cancellations
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="mb-6">
              <TabsTrigger value="refunds">Refund Requests</TabsTrigger>
              <TabsTrigger value="cancellations">
                Canceled Subscriptions
              </TabsTrigger>
            </TabsList>

            <TabsContent value="refunds">
              {loading ? (
                <div className="flex justify-center items-center h-40">
                  <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
                </div>
              ) : refunds.length === 0 ? (
                <div className="flex flex-col justify-center items-center h-40">
                  <p className="text-gray-400">No refund requests found</p>
                </div>
              ) : (
                <div className="overflow-x-auto">
                  <table className="w-full caption-bottom text-sm">
                    <thead>
                      <tr>
                        <th className="h-12 px-4 text-left align-middle font-semibold text-gray-200">
                          ID
                        </th>
                        <th className="h-12 px-4 text-left align-middle font-semibold text-gray-200">
                          User
                        </th>
                        <th className="h-12 px-4 text-left align-middle font-semibold text-gray-200">
                          Amount
                        </th>
                        <th className="h-12 px-4 text-left align-middle font-semibold text-gray-200">
                          Reason
                        </th>
                        <th className="h-12 px-4 text-left align-middle font-semibold text-gray-200">
                          Status
                        </th>
                        <th className="h-12 px-4 text-left align-middle font-semibold text-gray-200">
                          Date
                        </th>
                        <th className="h-12 px-4 text-left align-middle font-semibold text-gray-200">
                          Actions
                        </th>
                      </tr>
                    </thead>
                    <tbody>
                      {refunds.map((refund) => (
                        <tr
                          key={refund.id}
                          className="border-b border-gray-700"
                        >
                          <td className="p-4 align-middle font-mono text-xs">
                            {refund.id}
                          </td>
                          <td className="p-4 align-middle">
                            <div>
                              <div className="font-medium">
                                {refund.userName}
                              </div>
                              <div className="text-xs text-gray-400">
                                {refund.userEmail}
                              </div>
                            </div>
                          </td>
                          <td className="p-4 align-middle">
                            {formatCurrency(refund.amount, refund.currency)}
                          </td>
                          <td className="p-4 align-middle">
                            <div
                              className="max-w-xs truncate"
                              title={refund.reason}
                            >
                              {refund.reason}
                            </div>
                          </td>
                          <td className="p-4 align-middle">
                            {getStatusBadge(refund.status)}
                          </td>
                          <td className="p-4 align-middle">
                            {formatDate(refund?.createdAt)}
                          </td>
                          <td className="p-4 align-middle">
                            {refund.status === "PENDING" && (
                              <div className="flex gap-2">
                                <Button
                                  size="sm"
                                  variant="outline"
                                  className="bg-green-500/20 hover:bg-green-500/30 text-green-500 border-green-500"
                                  onClick={() =>
                                    openDialog(refund.id, "approve")
                                  }
                                >
                                  Approve
                                </Button>
                                <Button
                                  size="sm"
                                  variant="outline"
                                  className="bg-red-500/20 hover:bg-red-500/30 text-red-500 border-red-500"
                                  onClick={() =>
                                    openDialog(refund.id, "reject")
                                  }
                                >
                                  Reject
                                </Button>
                              </div>
                            )}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}
            </TabsContent>

            <TabsContent value="cancellations">
              {loading ? (
                <div className="flex justify-center items-center h-40">
                  <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
                </div>
              ) : canceledSubscriptions.length === 0 ? (
                <div className="flex flex-col justify-center items-center h-40">
                  <p className="text-gray-400">
                    No canceled subscriptions found
                  </p>
                </div>
              ) : (
                <div className="overflow-x-auto">
                  <table className="w-full caption-bottom text-sm">
                    <thead>
                      <tr>
                        <th className="h-12 px-4 text-left align-middle font-semibold text-gray-200">
                          ID
                        </th>
                        <th className="h-12 px-4 text-left align-middle font-semibold text-gray-200">
                          User
                        </th>
                        <th className="h-12 px-4 text-left align-middle font-semibold text-gray-200">
                          Plan
                        </th>
                        <th className="h-12 px-4 text-left align-middle font-semibold text-gray-200">
                          Reason
                        </th>
                        <th className="h-12 px-4 text-left align-middle font-semibold text-gray-200">
                          Canceled At
                        </th>
                      </tr>
                    </thead>
                    <tbody>
                      {canceledSubscriptions?.map((sub) => (
                        <tr key={sub.id} className="border-b border-gray-700">
                          <td className="p-4 align-middle font-mono text-xs">
                            {sub.id}
                          </td>
                          <td className="p-4 align-middle">
                            <div>
                              <div className="font-medium">{sub.userName}</div>
                              <div className="text-xs text-gray-400">
                                {sub.userEmail}
                              </div>
                            </div>
                          </td>
                          <td className="p-4 align-middle">{sub.planName}</td>
                          <td className="p-4 align-middle">
                            <div
                              className="max-w-xs truncate"
                              title={sub.cancelReason}
                            >
                              {sub.cancelReason}
                            </div>
                          </td>
                          <td className="p-4 align-middle">
                            {formatDate(sub?.cancellationDate)}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      {/* Process Refund Dialog */}
      <Dialog
        open={dialogState.open}
        onOpenChange={(open) => setDialogState((prev) => ({ ...prev, open }))}
      >
        <DialogContent className="bg-gray-900 text-white border-gray-700">
          <DialogHeader>
            <DialogTitle>
              {dialogState.action === "approve" ? "Approve" : "Reject"} Refund
              Request
            </DialogTitle>
            <DialogDescription className="text-gray-400">
              {dialogState.action === "approve"
                ? "This will approve the refund request and process the refund through the payment provider."
                : "This will reject the refund request and notify the user."}
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="notes">Notes (optional)</Label>
              <Textarea
                id="notes"
                placeholder="Add notes about this decision..."
                value={dialogState.notes}
                onChange={(e) =>
                  setDialogState((prev) => ({ ...prev, notes: e.target.value }))
                }
                className="bg-gray-800 border-gray-700"
              />
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() =>
                setDialogState((prev) => ({ ...prev, open: false }))
              }
            >
              Cancel
            </Button>
            <Button
              onClick={handleProcessRefund}
              className={
                dialogState.action === "approve"
                  ? "bg-green-600 hover:bg-green-700"
                  : "bg-red-600 hover:bg-red-700"
              }
            >
              {dialogState.action === "approve"
                ? "Approve Refund"
                : "Reject Refund"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default AdminRefundsPage;
