import CreateBlogModal from "@/components/Blogs/AdminCreateBlogModal";
import ShadowButton from "@/components/ui/shadowButton";
import { useAdminHeader } from "@/contexts/AdminHeaderContext";
import AdminBlogService from "@/services/adminBlog.service";
import type { BlogCategory, BlogPost } from "@/services/adminBlog.service";
import BlogService from "@/services/blog.service";
import * as Select from "@radix-ui/react-select";
import { ChevronDownIcon, EyeIcon } from "lucide-react";
import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { toast } from "sonner";

type PostStatus = "all" | "published" | "draft";
type PostCategory = "all" | string;

interface ApiBlogPost {
  id: string;
  authorId: string;
  title: string;
  slug: string;
  content: string;
  excerpt: string;
  featuredImageUrl: string | null;
  status: "DRAFT" | "PUBLISHED" | "ARCHIVED" | "SCHEDULED";
  publishedAt: string | null;
  createdAt: string;
  updatedAt: string;
  author: {
    id: string;
    email: string;
    profile: {
      firstName: string;
      lastName: string;
      avatar?: string;
    };
  };
  categories: Array<{
    categoryId: string;
    category: { id: string; name: string; slug: string; description?: string };
  }>;
  tags: Array<{
    tagId: string;
    tag: { id: string; name: string; slug: string };
  }>;
}

interface PaginatedApiBlogPosts {
  data: ApiBlogPost[];
  // Add other pagination properties if needed, e.g., total, page, etc.
}

const AdminBlogPage = () => {
  const { setHeaderContent } = useAdminHeader();
  const [searchQuery, setSearchQuery] = useState("");
  const [activeTab, setActiveTab] = useState<PostStatus>("all");
  const [categoryFilter, setCategoryFilter] = useState<PostCategory>("all");
  const [selectedPost, setSelectedPost] = useState<BlogPost | null>(null);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [blogData, setBlogData] = useState<BlogPost[]>([]);
  const [apiBlogData, setApiBlogData] = useState<ApiBlogPost[]>([]);
  const [categories, setCategories] = useState<BlogCategory[]>([]);
  const [loading, setLoading] = useState(true);
  const navigate = useNavigate();

  const fetchBlogPosts = async () => {
    try {
      setLoading(true);
      const response =
        (await AdminBlogService.getPosts()) as unknown as PaginatedApiBlogPosts;

      console.log(response, "ascmsakcmasmsamcsasc");

      // Store raw API data
      setApiBlogData(response.data);

      // Transform to BlogPost
      const transformedData: BlogPost[] = response.data.map(
        (post: ApiBlogPost) => ({
          id: post.id,
          title: post.title,
          slug: post.slug,
          content: post.content,
          excerpt: post.excerpt || "",
          status: post.status,
          authorId: post.authorId,
          categoryIds: post.categories.map((cat) => cat.categoryId),
          tagIds: post.tags.map((tag) => tag.tagId),
          publishedAt: post.publishedAt || undefined,
          createdAt: post.createdAt,
          updatedAt: post.updatedAt,
          featuredImage: post.featuredImageUrl || undefined,
        })
      );

      setBlogData(transformedData);
    } catch (error) {
      console.error("Error fetching blog posts:", error);
      toast.error("Failed to fetch blog posts");
    } finally {
      setLoading(false);
    }
  };

  const fetchCategories = async () => {
    try {
      const categoriesData = await BlogService.getCategories();
      setCategories(categoriesData);
    } catch (error) {
      console.error("Failed to fetch categories:", error);
    }
  };

  useEffect(() => {
    setHeaderContent({
      title: "Blog Management",
      description: "Create, edit, and manage blog posts",
      content: (
        <button
          onClick={() => setIsEditModalOpen(true)}
          className="px-4 py-2 cursor-pointer rounded-lg border-2 border-gray-500 flex items-center gap-2 transition-colors"
        >
          <span className="text-white text-lg">+ |</span>
          <span className="text-purple-500">Add New Post</span>
        </button>
      ),
    });
    return () => setHeaderContent({});
  }, [setHeaderContent]);

  useEffect(() => {
    fetchCategories();
    fetchBlogPosts();
  }, []);

  const tabs = [
    { id: "all", label: "All Posts" },
    { id: "published", label: "Published" },
    { id: "draft", label: "Drafts" },
  ];

  const handleTabChange = (tabId: PostStatus) => {
    setActiveTab(tabId);
  };

  const handleCreateSuccess = (newPost: BlogPost) => {
    console.log("New post created:", newPost);
    setIsEditModalOpen(false);
    toast.success("Blog post successfully added!");
    fetchBlogPosts();
  };

  const handleEditSuccess = (updatedPost: BlogPost) => {
    console.log("Post updated:", updatedPost);
    setIsEditModalOpen(false);
    toast.success("Blog post successfully updated!");
    fetchBlogPosts();
  };

  const filteredPosts = blogData.filter((post) => {
    const matchesSearch =
      post.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      post.excerpt?.toLowerCase()?.includes(searchQuery.toLowerCase());

    const matchesStatus =
      activeTab === "all" || post.status.toLowerCase() === activeTab;

    const matchesCategory =
      categoryFilter === "all" || post.categoryIds?.includes(categoryFilter);

    return matchesSearch && matchesStatus && matchesCategory;
  });

  const handleDeletePost = async (id: string) => {
    try {
      await AdminBlogService.deletePost(id);
      setBlogData((prev) => prev.filter((post) => post.id !== id));
      setApiBlogData((prev) => prev.filter((post) => post.id !== id));
      toast.success("Blog post deleted successfully!");
    } catch (error) {
      console.error("Error deleting post:", error);
      toast.error("Failed to delete blog post");
    } finally {
      setIsDeleteModalOpen(false);
      setSelectedPost(null);
    }
  };

  const formatDate = (dateString: string) => {
    if (!dateString) return "Not published";
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  const getCategoryName = (categoryId: string): string => {
    const category = categories.find((cat) => cat.id === categoryId);
    return category?.name || "Uncategorized";
  };

  if (loading) {
    return (
      <div className="min-h-screen text-white p-6 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-transparent border-gray-200 mx-auto mb-4"></div>
          <p className="text-gray-400">Loading blog posts...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen text-white p-6">
      <div className="mx-auto max-w-7xl">
        <div className="flex justify-between items-center border-b-2 border-gray-200 pb-3 mb-6">
          <div className="flex gap-10 space-x-1 px-2">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => handleTabChange(tab.id as PostStatus)}
                className={`text-sm font-semibold cursor-pointer transition-colors ${
                  activeTab === tab.id
                    ? "text-white border-b-2 border-blue-500"
                    : "text-gray-400 hover:text-purple-500"
                }`}
                type="button"
              >
                {tab.label}
              </button>
            ))}
          </div>
          <div className="flex justify-between items-center gap-4">
            <Select.Root
              value={categoryFilter}
              onValueChange={setCategoryFilter}
            >
              <Select.Trigger
                className="flex items-center justify-between bg-gray-800 border border-gray-600 rounded-lg px-4 py-2 text-sm text-gray-200 hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-purple-500 w-full max-w-[220px]"
                aria-label="Category"
              >
                <Select.Value placeholder="Select a category" />
                <Select.Icon>
                  <ChevronDownIcon className="w-5 h-5" />
                </Select.Icon>
              </Select.Trigger>
              <Select.Portal>
                <Select.Content className="bg-gray-800 border border-gray-600 rounded-lg shadow-lg z-50 overflow-hidden">
                  <Select.Viewport className="p-2">
                    <Select.Item
                      value="all"
                      className="text-gray-200 text-sm cursor-pointer px-4 py-2 hover:bg-gray-700 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                    >
                      <Select.ItemText>All posts</Select.ItemText>
                    </Select.Item>
                    {categories.map((category) => (
                      <Select.Item
                        key={category.id}
                        value={category.id}
                        className="text-gray-200 text-sm cursor-pointer px-4 py-2 hover:bg-gray-700 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                      >
                        <Select.ItemText>{category.name}</Select.ItemText>
                      </Select.Item>
                    ))}
                  </Select.Viewport>
                </Select.Content>
              </Select.Portal>
            </Select.Root>
            <div className="relative">
              <input
                type="text"
                placeholder="Search posts..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="bg-white/10 border border-gray-600 text-white px-4 py-1 pl-10 rounded-md text-lg w-full focus:outline-none focus:ring-2 focus:ring-purple-500"
              />
              <svg
                className="w-5 h-5 text-gray-400 absolute left-3 top-1/2 transform -translate-y-1/2"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                />
              </svg>
            </div>
          </div>
        </div>

        {filteredPosts.length > 0 ? (
          <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
            {filteredPosts.map((post) => {
              const apiPost = apiBlogData.find((p) => p.id === post.id);
              return (
                <div
                  key={post.id}
                  className="bg-white/5 rounded-md overflow-hidden border border-gray-700 hover:border-gray-600 hover:bg-gray-800 transition-colors h-full flex flex-col"
                >
                  {/* Image Section - Fixed Height */}
                  <div className="relative flex-shrink-0">
                    <img
                      loading="lazy"
                      src={post.featuredImage}
                      alt={post.title}
                      className="w-full h-48 object-cover"
                      onError={(e) => {
                        e.currentTarget.src = "https://picsum.photos/200/300";
                      }}
                    />
                    <div className="absolute top-3 right-3">
                      <span
                        style={{
                          boxShadow: "inset 0 0 10px rgba(255, 255, 255, 0.6)",
                        }}
                        className="bg-purple-600 text-white px-3 py-1 rounded-md text-sm font-medium"
                      >
                        {post.status}
                      </span>
                    </div>
                  </div>

                  {/* Content Section - Flexible Height */}
                  <div className="p-4 flex flex-col flex-grow">
                    {/* Title and Excerpt - Fixed Height Container */}
                    <div className="flex-grow min-h-[120px] mb-4">
                      <h3 className="text-lg font-semibold text-white mb-2 line-clamp-2 h-14">
                        {post.title}
                      </h3>
                      <p className="text-gray-400 text-sm line-clamp-3 h-[60px]">
                        {post.excerpt}
                      </p>
                    </div>

                    {/* Category Section */}
                    <div className="mb-3">
                      {post.categoryIds && post.categoryIds.length > 0 ? (
                        <span className="text-white px-2 py-1 rounded-full text-sm border border-gray-400">
                          {getCategoryName(post.categoryIds[0])}
                        </span>
                      ) : (
                        <span className="text-white px-2 py-1 rounded-full text-sm border border-gray-400">
                          Uncategorised
                        </span>
                      )}
                    </div>

                    {/* Tags Section - Fixed Height */}
                    <div className="mb-3 h-8 overflow-hidden">
                      <div className="flex flex-wrap gap-1">
                        {post.tagIds?.slice(0, 3).map((tagId) => {
                          const tag = apiPost?.tags.find(
                            (t) => t.tagId === tagId
                          )?.tag.name;
                          return tag ? (
                            <span
                              key={tagId}
                              className="text-gray-300 px-2 py-1 rounded-full text-xs border border-gray-500"
                            >
                              {tag}
                            </span>
                          ) : null;
                        })}
                        {post.tagIds && post.tagIds.length > 3 && (
                          <span className="text-gray-400 text-xs px-2 py-1">
                            +{post.tagIds.length - 3} more
                          </span>
                        )}
                      </div>
                    </div>

                    {/* Author and Date Section */}
                    <div className="flex justify-between mb-3 items-center text-sm">
                      <div className="text-gray-400 truncate">
                        {/* Admin: {apiPost?.author.profile.firstName}{" "}
                        {apiPost?.author.profile.lastName} */}
                        By Admin
                      </div>
                      <div className="text-gray-400 text-right flex-shrink-0">
                        {post.status === "PUBLISHED"
                          ? formatDate(post.publishedAt || "")
                          : "Draft"}
                      </div>
                    </div>

                    {/* Action Buttons - Always at Bottom */}
                    <div className="flex justify-end gap-2 mt-auto pt-2 border-t border-gray-700">
                      <button
                        className="border border-gray-500 p-2 hover:bg-gray-600 rounded-md text-gray-200 cursor-pointer transition-colors"
                        onClick={() => navigate("/admin/blog/" + post.slug)}
                        title="View Post"
                      >
                        <EyeIcon className="w-4 h-4" />
                      </button>
                      <button
                        className="border border-gray-500 p-2 hover:bg-gray-600 rounded-md text-gray-200 cursor-pointer transition-colors"
                        onClick={() => {
                          setSelectedPost(post);
                          setIsEditModalOpen(true);
                        }}
                        title="Edit Post"
                      >
                        <svg
                          className="w-4 h-4"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"
                          />
                        </svg>
                      </button>
                      <button
                        className="border border-gray-500 p-2 hover:bg-gray-600 rounded-md text-gray-200 cursor-pointer transition-colors"
                        onClick={() => {
                          setSelectedPost(post);
                          setIsDeleteModalOpen(true);
                        }}
                        title="Delete Post"
                      >
                        <svg
                          className="w-4 h-4"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                          />
                        </svg>
                      </button>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        ) : (
          <div className="text-center py-16">
            <div className="text-gray-400 mb-4">
              <svg
                className="w-16 h-16 mx-auto"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                />
              </svg>
            </div>
            <h3 className="text-lg font-medium mb-2">No posts found</h3>
            <p className="text-gray-400 mb-6">
              {searchQuery
                ? `No results for "${searchQuery}"`
                : "No blog posts found for the selected filters."}
            </p>
            <div className="flex gap-4 justify-center">
              <button
                onClick={() => {
                  setSearchQuery("");
                  setActiveTab("all");
                  setCategoryFilter("all");
                }}
                className="bg-gray-800 hover:bg-gray-700 text-white px-4 py-2 rounded-md border border-gray-600 transition-colors"
              >
                Clear Filters
              </button>
              <ShadowButton
                onClick={() => setIsEditModalOpen(true)}
                className="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-md transition-colors"
              >
                Create New Post
              </ShadowButton>
            </div>
          </div>
        )}

        {selectedPost && isDeleteModalOpen && (
          <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50">
            <div className="bg-gray-800 p-6 rounded-md shadow-lg max-w-md w-full border border-gray-700">
              <h3 className="text-lg font-bold mb-2 text-white">
                Delete Blog Post
              </h3>
              <p className="text-gray-400 mb-6">
                Are you sure you want to delete the blog post "
                {selectedPost.title}"? This action cannot be undone.
              </p>
              <div className="flex justify-end gap-2">
                <button
                  onClick={() => setIsDeleteModalOpen(false)}
                  className="bg-gray-700 hover:bg-gray-600 text-white px-4 py-2 rounded-md transition-colors"
                >
                  Cancel
                </button>
                <button
                  onClick={() => handleDeletePost(selectedPost.id)}
                  className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md transition-colors"
                >
                  Delete
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
      <CreateBlogModal
        isOpen={isEditModalOpen}
        onClose={() => {
          setIsEditModalOpen(false);
          setSelectedPost(null);
        }}
        onSuccess={selectedPost ? handleEditSuccess : handleCreateSuccess}
        allCategories={categories}
        fetchCategories={fetchCategories}
        postToEdit={selectedPost}
      />
    </div>
  );
};

export default AdminBlogPage;

// import CreateBlogModal from "@/components/Blogs/AdminCreateBlogModal";
// import ShadowButton from "@/components/ui/shadowButton";
// import { useAdminHeader } from "@/contexts/AdminHeaderContext";
// import AdminBlogService from "@/services/adminBlog.service";
// import type { BlogCategory, BlogPost } from "@/services/adminBlog.service";
// import BlogService from "@/services/blog.service";
// import * as Select from "@radix-ui/react-select";
// import { ChevronDownIcon, EyeIcon } from "lucide-react";
// import { useEffect, useState } from "react";
// import { useNavigate } from "react-router-dom";
// import { toast } from "sonner";

// type PostStatus = "all" | "published" | "draft";
// type PostCategory = "all" | string;

// interface ApiBlogPost {
//   id: string;
//   authorId: string;
//   title: string;
//   slug: string;
//   content: string;
//   excerpt: string;
//   featuredImageUrl: string | null;
//   status: "DRAFT" | "PUBLISHED" | "ARCHIVED" | "SCHEDULED";
//   publishedAt: string | null;
//   createdAt: string;
//   updatedAt: string;
//   author: {
//     id: string;
//     email: string;
//     profile: {
//       firstName: string;
//       lastName: string;
//       avatar?: string;
//     };
//   };
//   categories: Array<{
//     categoryId: string;
//     category: { id: string; name: string; slug: string; description?: string };
//   }>;
//   tags: Array<{
//     tagId: string;
//     tag: { id: string; name: string; slug: string };
//   }>;
// }

// interface PaginatedApiBlogPosts {
//   data: ApiBlogPost[];
//   // Add other pagination properties if needed, e.g., total, page, etc.
// }

// const AdminBlogPage = () => {
//   const { setHeaderContent } = useAdminHeader();
//   const [searchQuery, setSearchQuery] = useState("");
//   const [activeTab, setActiveTab] = useState<PostStatus>("all");
//   const [categoryFilter, setCategoryFilter] = useState<PostCategory>("all");
//   const [selectedPost, setSelectedPost] = useState<BlogPost | null>(null);
//   const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
//   const [isEditModalOpen, setIsEditModalOpen] = useState(false);
//   const [blogData, setBlogData] = useState<BlogPost[]>([]);
//   const [apiBlogData, setApiBlogData] = useState<ApiBlogPost[]>([]);
//   const [categories, setCategories] = useState<BlogCategory[]>([]);
//   const [loading, setLoading] = useState(true);
//   const navigate = useNavigate();

//   const fetchBlogPosts = async () => {
//     try {
//       setLoading(true);
//       const response =
//         (await AdminBlogService.getPosts()) as unknown as PaginatedApiBlogPosts;

//       console.log(response, "ascmsakcmasmsamcsasc");

//       // Store raw API data
//       setApiBlogData(response.data);

//       // Transform to BlogPost
//       const transformedData: BlogPost[] = response.data.map(
//         (post: ApiBlogPost) => ({
//           id: post.id,
//           title: post.title,
//           slug: post.slug,
//           content: post.content,
//           excerpt: post.excerpt || "",
//           status: post.status,
//           authorId: post.authorId,
//           categoryIds: post.categories.map((cat) => cat.categoryId),
//           tagIds: post.tags.map((tag) => tag.tagId),
//           publishedAt: post.publishedAt || undefined,
//           createdAt: post.createdAt,
//           updatedAt: post.updatedAt,
//           featuredImage: post.featuredImageUrl || undefined,
//         })
//       );

//       setBlogData(transformedData);
//     } catch (error) {
//       console.error("Error fetching blog posts:", error);
//       toast.error("Failed to fetch blog posts");
//     } finally {
//       setLoading(false);
//     }
//   };

//   const fetchCategories = async () => {
//     try {
//       const categoriesData = await BlogService.getCategories();
//       setCategories(categoriesData);
//     } catch (error) {
//       console.error("Failed to fetch categories:", error);
//     }
//   };

//   useEffect(() => {
//     setHeaderContent({
//       title: "Blog Management",
//       description: "Create, edit, and manage blog posts",
//       content: (
//         <button
//           onClick={() => setIsEditModalOpen(true)}
//           className="px-4 py-2 cursor-pointer rounded-lg border-2 border-gray-500 flex items-center gap-2 transition-colors"
//         >
//           <span className="text-white text-lg">+ |</span>
//           <span className="text-purple-500">Add New Post</span>
//         </button>
//       ),
//     });
//     return () => setHeaderContent({});
//   }, [setHeaderContent]);

//   useEffect(() => {
//     fetchCategories();
//     fetchBlogPosts();
//   }, []);

//   const tabs = [
//     { id: "all", label: "All Posts" },
//     { id: "published", label: "Published" },
//     { id: "draft", label: "Drafts" },
//   ];

//   const handleTabChange = (tabId: PostStatus) => {
//     setActiveTab(tabId);
//   };

//   const handleCreateSuccess = (newPost: BlogPost) => {
//     console.log("New post created:", newPost);
//     setIsEditModalOpen(false);
//     toast.success("Blog post successfully added!");
//     fetchBlogPosts();
//   };

//   const handleEditSuccess = (updatedPost: BlogPost) => {
//     console.log("Post updated:", updatedPost);
//     setIsEditModalOpen(false);
//     toast.success("Blog post successfully updated!");
//     fetchBlogPosts();
//   };

//   const filteredPosts = blogData.filter((post) => {
//     const matchesSearch =
//       post.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
//       post.excerpt?.toLowerCase()?.includes(searchQuery.toLowerCase());

//     const matchesStatus =
//       activeTab === "all" || post.status.toLowerCase() === activeTab;

//     const matchesCategory =
//       categoryFilter === "all" || post.categoryIds?.includes(categoryFilter);

//     return matchesSearch && matchesStatus && matchesCategory;
//   });

//   const handleDeletePost = async (id: string) => {
//     try {
//       await AdminBlogService.deletePost(id);
//       setBlogData((prev) => prev.filter((post) => post.id !== id));
//       setApiBlogData((prev) => prev.filter((post) => post.id !== id));
//       toast.success("Blog post deleted successfully!");
//     } catch (error) {
//       console.error("Error deleting post:", error);
//       toast.error("Failed to delete blog post");
//     } finally {
//       setIsDeleteModalOpen(false);
//       setSelectedPost(null);
//     }
//   };

//   const formatDate = (dateString: string) => {
//     if (!dateString) return "Not published";
//     return new Date(dateString).toLocaleDateString("en-US", {
//       year: "numeric",
//       month: "short",
//       day: "numeric",
//     });
//   };

//   const getCategoryName = (categoryId: string): string => {
//     const category = categories.find((cat) => cat.id === categoryId);
//     return category?.name || "Uncategorized";
//   };

//   if (loading) {
//     return (
//       <div className="min-h-screen text-white p-6 flex items-center justify-center">
//         <div className="text-center">
//           <div className="animate-spin rounded-full h-12 w-12 border-t-transparent border-gray-200 mx-auto mb-4"></div>
//           <p className="text-gray-400">Loading blog posts...</p>
//         </div>
//       </div>
//     );
//   }

//   return (
//     <div className="min-h-screen text-white p-6">
//       <div className="mx-auto max-w-7xl">
//         <div className="flex justify-between items-center border-b-2 border-gray-200 pb-3 mb-6">
//           <div className="flex gap-10 space-x-1 px-2">
//             {tabs.map((tab) => (
//               <button
//                 key={tab.id}
//                 onClick={() => handleTabChange(tab.id as PostStatus)}
//                 className={`text-sm font-semibold cursor-pointer transition-colors ${
//                   activeTab === tab.id
//                     ? "text-white border-b-2 border-blue-500"
//                     : "text-gray-400 hover:text-purple-500"
//                 }`}
//                 type="button"
//               >
//                 {tab.label}
//               </button>
//             ))}
//           </div>
//           <div className="flex justify-between items-center gap-4">
//             <Select.Root
//               value={categoryFilter}
//               onValueChange={setCategoryFilter}
//             >
//               <Select.Trigger
//                 className="flex items-center justify-between bg-gray-800 border border-gray-600 rounded-lg px-4 py-2 text-sm text-gray-200 hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-purple-500 w-full max-w-[220px]"
//                 aria-label="Category"
//               >
//                 <Select.Value placeholder="Select a category" />
//                 <Select.Icon>
//                   <ChevronDownIcon className="w-5 h-5" />
//                 </Select.Icon>
//               </Select.Trigger>
//               <Select.Portal>
//                 <Select.Content className="bg-gray-800 border border-gray-600 rounded-lg shadow-lg z-50 overflow-hidden">
//                   <Select.Viewport className="p-2">
//                     <Select.Item
//                       value="all"
//                       className="text-gray-200 text-sm cursor-pointer px-4 py-2 hover:bg-gray-700 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
//                     >
//                       <Select.ItemText>All posts</Select.ItemText>
//                     </Select.Item>
//                     {categories.map((category) => (
//                       <Select.Item
//                         key={category.id}
//                         value={category.id}
//                         className="text-gray-200 text-sm cursor-pointer px-4 py-2 hover:bg-gray-700 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
//                       >
//                         <Select.ItemText>{category.name}</Select.ItemText>
//                       </Select.Item>
//                     ))}
//                   </Select.Viewport>
//                 </Select.Content>
//               </Select.Portal>
//             </Select.Root>
//             <div className="relative">
//               <input
//                 type="text"
//                 placeholder="Search posts..."
//                 value={searchQuery}
//                 onChange={(e) => setSearchQuery(e.target.value)}
//                 className="bg-white/10 border border-gray-600 text-white px-4 py-1 pl-10 rounded-md text-lg w-full focus:outline-none focus:ring-2 focus:ring-purple-500"
//               />
//               <svg
//                 className="w-5 h-5 text-gray-400 absolute left-3 top-1/2 transform -translate-y-1/2"
//                 fill="none"
//                 stroke="currentColor"
//                 viewBox="0 0 24 24"
//               >
//                 <path
//                   strokeLinecap="round"
//                   strokeLinejoin="round"
//                   strokeWidth={2}
//                   d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
//                 />
//               </svg>
//             </div>
//           </div>
//         </div>

//         {filteredPosts.length > 0 ? (
//           <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
//             {filteredPosts.map((post) => {
//               const apiPost = apiBlogData.find((p) => p.id === post.id);
//               return (
//                 <div
//                   key={post.id}
//                   className="bg-white/5 rounded-md overflow-hidden border border-gray-700 hover:border-gray-600 hover:bg-gray-800 transition-colors"
//                 >
//                   <div className="relative">
//                     <img loading="lazy"
//                       src={
//                         post.featuredImage
//                       }
//                       alt={post.title}
//                       className="w-full h-48 object-cover"
//                       onError={(e) => {
//                         e.currentTarget.src = "https://picsum.photos/200/300";
//                       }}
//                     />
//                     <div className="absolute top-3 right-3">
//                       <span
//                         style={{
//                           boxShadow: "inset 0 0 10px rgba(255, 255, 255, 0.6)",
//                         }}
//                         className="bg-purple-600 text-white px-3 py-1 rounded-md text-sm font-medium"
//                       >
//                         {post.status}
//                       </span>
//                     </div>
//                   </div>
//                   <div className="p-4">
//                     <h3 className="text-lg font-semibold text-white mb-2 line-clamp-2">
//                       {post.title}
//                     </h3>
//                     <p className="text-gray-400 text-sm mb-2 line-clamp-2">
//                       {post.excerpt}
//                     </p>
//                     <div className="flex justify-between mb-2 items-center">
//                       {post.categoryIds && post.categoryIds.length > 0 ? (
//                         <span className="text-white px-2 py-1 rounded-full text-sm border border-gray-400">
//                           {getCategoryName(post.categoryIds[0])}
//                         </span>
//                       ) : (
//                         <span className="text-white px-2 py-1 rounded-full text-sm border border-gray-400">
//                           Uncategorised
//                         </span>
//                       )}
//                     </div>
//                     <div className="flex flex-wrap gap-2 mb-2">
//                       {post.tagIds?.map((tagId) => {
//                         const tag = apiPost?.tags.find((t) => t.tagId === tagId)
//                           ?.tag.name;
//                         return tag ? (
//                           <span
//                             key={tagId}
//                             className="text-gray-300 px-2 py-1 rounded-full text-xs border border-gray-500"
//                           >
//                             {tag}
//                           </span>
//                         ) : null;
//                       })}
//                     </div>
//                     <div className="flex justify-between mb-2 items-center">
//                       <div className="text-gray-400 text-sm">
//                         By {apiPost?.author.profile.firstName}{" "}
//                         {apiPost?.author.profile.lastName}
//                       </div>
//                       <div className="text-gray-400 text-sm">
//                         {post.status === "PUBLISHED"
//                           ? formatDate(post.publishedAt || "")
//                           : "Draft"}
//                       </div>
//                     </div>
//                     <div className="flex justify-between items-center">
//                       <div className="flex items-center text-gray-400 text-sm">
//                         {/* <svg
//                           className="w-4 h-4 mr-1"
//                           fill="none"
//                           stroke="currentColor"
//                           viewBox="0 0 24 24"
//                         >
//                           <path
//                             strokeLinecap="round"
//                             strokeLinejoin="round"
//                             strokeWidth={2}
//                             d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
//                           />
//                           <path
//                             strokeLinecap="round"
//                             strokeLinejoin="round"
//                             strokeWidth={2}
//                             d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
//                           />
//                         </svg>
//                         <span className="text-sm">Views N/A</span> */}
//                       </div>
//                       <div className="flex gap-2">
//                         <button
//                           className="border border-gray-500 p-2 hover:bg-gray-600 rounded-md text-gray-200 cursor-pointer transition-colors"
//                           onClick={() => navigate("/admin/blog/" + post.slug)}
//                         >
//                           <EyeIcon className="w-4 h-4" />
//                         </button>
//                         <button
//                           className="border border-gray-500 p-2 hover:bg-gray-600 rounded-md text-gray-200 cursor-pointer transition-colors"
//                           onClick={() => {
//                             setSelectedPost(post);
//                             setIsEditModalOpen(true);
//                           }}
//                         >
//                           <svg
//                             className="w-4 h-4"
//                             fill="none"
//                             stroke="currentColor"
//                             viewBox="0 0 24 24"
//                           >
//                             <path
//                               strokeLinecap="round"
//                               strokeLinejoin="round"
//                               strokeWidth={2}
//                               d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"
//                             />
//                           </svg>
//                         </button>
//                         <button
//                           className="border border-gray-500 p-2 hover:bg-gray-600 rounded-md text-gray-200 cursor-pointer transition-colors"
//                           onClick={() => {
//                             setSelectedPost(post);
//                             setIsDeleteModalOpen(true);
//                           }}
//                         >
//                           <svg
//                             className="w-4 h-4"
//                             fill="none"
//                             stroke="currentColor"
//                             viewBox="0 0 24 24"
//                           >
//                             <path
//                               strokeLinecap="round"
//                               strokeLinejoin="round"
//                               strokeWidth={2}
//                               d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
//                             />
//                           </svg>
//                         </button>
//                       </div>
//                     </div>
//                   </div>
//                 </div>
//               );
//             })}
//           </div>
//         ) : (
//           <div className="text-center py-16">
//             <div className="text-gray-400 mb-4">
//               <svg
//                 className="w-16 h-16 mx-auto"
//                 fill="none"
//                 stroke="currentColor"
//                 viewBox="0 0 24 24"
//               >
//                 <path
//                   strokeLinecap="round"
//                   strokeLinejoin="round"
//                   strokeWidth={2}
//                   d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
//                 />
//               </svg>
//             </div>
//             <h3 className="text-lg font-medium mb-2">No posts found</h3>
//             <p className="text-gray-400 mb-6">
//               {searchQuery
//                 ? `No results for "${searchQuery}"`
//                 : "No blog posts found for the selected filters."}
//             </p>
//             <div className="flex gap-4 justify-center">
//               <button
//                 onClick={() => {
//                   setSearchQuery("");
//                   setActiveTab("all");
//                   setCategoryFilter("all");
//                 }}
//                 className="bg-gray-800 hover:bg-gray-700 text-white px-4 py-2 rounded-md border border-gray-600 transition-colors"
//               >
//                 Clear Filters
//               </button>
//               <ShadowButton
//                 onClick={() => setIsEditModalOpen(true)}
//                 className="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-md transition-colors"
//               >
//                 Create New Post
//               </ShadowButton>
//             </div>
//           </div>
//         )}

//         {selectedPost && isDeleteModalOpen && (
//           <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50">
//             <div className="bg-gray-800 p-6 rounded-md shadow-lg max-w-md w-full border border-gray-700">
//               <h3 className="text-lg font-bold mb-2 text-white">
//                 Delete Blog Post
//               </h3>
//               <p className="text-gray-400 mb-6">
//                 Are you sure you want to delete the blog post "
//                 {selectedPost.title}"? This action cannot be undone.
//               </p>
//               <div className="flex justify-end gap-2">
//                 <button
//                   onClick={() => setIsDeleteModalOpen(false)}
//                   className="bg-gray-700 hover:bg-gray-600 text-white px-4 py-2 rounded-md transition-colors"
//                 >
//                   Cancel
//                 </button>
//                 <button
//                   onClick={() => handleDeletePost(selectedPost.id)}
//                   className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md transition-colors"
//                 >
//                   Delete
//                 </button>
//               </div>
//             </div>
//           </div>
//         )}
//       </div>
//       <CreateBlogModal
//         isOpen={isEditModalOpen}
//         onClose={() => {
//           setIsEditModalOpen(false);
//           setSelectedPost(null);
//         }}
//         onSuccess={selectedPost ? handleEditSuccess : handleCreateSuccess}
//         allCategories={categories}
//         fetchCategories={fetchCategories}
//         postToEdit={selectedPost}
//       />
//     </div>
//   );
// };

// export default AdminBlogPage;
