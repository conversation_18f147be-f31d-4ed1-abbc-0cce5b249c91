import Section from "@/components/layout/Section";
import { Text } from "@/components/ui/text";

const MemberArea = () => {
  return (
    <Section className="" containerClassName="flex flex-col gap-8">
      <div className="flex overflow-hidden relative bg-[#FFFFFF0F]  items-start gap-[36px] border border-[#FFFFFF45] rounded-[25px] px-[30px] py-4">
        <img
          loading="lazy"
          className="w-[30px] absolute right-[30px] top-[30px]"
          src="/icons/linkedin.svg"
        />
        <img
          loading="lazy"
          className="max-w-[270px] rounded-[25px]  z-[10]"
          src="/png/member-sundar.png"
        />
        <div className="flex flex-col gap-2 z-[10] ">
          <div className="flex flex-col gap-2">
            <Text
              variant="card_title_large"
              className="text-[#9855FF] leading-[30px]"
            >
              Dr. <PERSON>
            </Text>
            <Text
              variant="card_title_small"
              className="text-[#9C9C9C96] leading-[30px]"
            >
              Co-Founder
            </Text>
          </div>
          <Text variant="body" className="leading-[30px]">
            I've tried several speedpainting products overflow the years, and
            this linear has exceeded my expectations.linear What really
            impressed member member this how efficient they are for batch
            pointing miniatures for doing quick concept start. Perfect for both
            hobbyists and professionals who want top save time without
            sacrificing quality.
          </Text>
        </div>
      </div>
      <div className="grid grid-cols-1 xs:grid-cols-2 md:grid-cols-3 gap-x-12 gap-y-8">
        <div className="flex  overflow-hidden relative bg-[#FFFFFF0F]  items-start gap-[36px] border border-[#FFFFFF45] rounded-[25px] px-[20px] py-4">
          <img
            loading="lazy"
            className="min-w-[270px] w-full rounded-[25px]  z-[10]"
            src="/png/member-andy.png"
          />
          <div className="absolute z-10 bottom-0 flex items-center justify-between gap-12 left-0 w-full bg-[linear-gradient(357.96deg,_#101010_9.38%,_rgba(15,_15,_15,_0)_81.16%)] px-[30px] py-5">
            <div className="flex flex-col">
              <Text variant={"card_body"} className="text-white">
                Dr. Andy
              </Text>
              <Text variant={"card_body"} className="text-[#9C9C9C96]">
                {"Co-Founder"}
              </Text>
            </div>
            <img
              loading="lazy"
              className="w-[30px] "
              src="/icons/linkedin.svg"
            />
          </div>
        </div>
        <div className="flex  overflow-hidden relative bg-[#FFFFFF0F]  items-start gap-[36px] border border-[#FFFFFF45] rounded-[25px] px-[20px] py-4">
          <img
            loading="lazy"
            className="min-w-[270px] w-full rounded-[25px]  z-[10]"
            src="/png/member-emma.png"
          />
          <div className="absolute z-10 bottom-0 flex items-center justify-between gap-12 left-0 w-full bg-[linear-gradient(357.96deg,_#101010_9.38%,_rgba(15,_15,_15,_0)_81.16%)] px-[30px] py-5">
            <div className="flex flex-col">
              <Text variant={"card_body"} className="text-white">
                Dr. Emma
              </Text>
              <Text variant={"card_body"} className="text-[#9C9C9C96]">
                {"CTO"}
              </Text>
            </div>
            <img
              loading="lazy"
              className="w-[30px] "
              src="/icons/linkedin.svg"
            />
          </div>
        </div>
        <div className="flex  overflow-hidden relative bg-[#FFFFFF0F]  items-start gap-[36px] border border-[#FFFFFF45] rounded-[25px] px-[20px] py-4">
          <img
            loading="lazy"
            className="min-w-[270px] w-full rounded-[25px]  z-[10]"
            src="/png/member-mary.png"
          />
          <div className="absolute z-10 bottom-0 flex items-center justify-between gap-12 left-0 w-full bg-[linear-gradient(357.96deg,_#101010_9.38%,_rgba(15,_15,_15,_0)_81.16%)] px-[30px] py-5">
            <div className="flex flex-col">
              <Text variant={"card_body"} className="text-white">
                Dr. Mary
              </Text>
              <Text variant={"card_body"} className="text-[#9C9C9C96]">
                {"COO"}
              </Text>
            </div>
            <img
              loading="lazy"
              className="w-[30px] "
              src="/icons/linkedin.svg"
            />
          </div>
        </div>
      </div>
    </Section>
  );
};

export default MemberArea;
