import Section from "@/components/layout/Section";
// import { Button } from "@/components/ui/button";
import { Text } from "@/components/ui/text";
import { Link } from "react-router-dom";

const MiragicAi = () => {
  return (
    <Section className="py-13 bg-cover bg-center bg-no-repeat">
      <div className="flex flex-col gap-10">
        <div className="flex flex-col gap-8">
          <div className="flex flex-col gap-5">
            <Text
              variant={"page_title"}
              className="bg-clip-text text-transparent bg-gradient-to-l from-white via-white to-gray-500"
            >
              MiragicAI is Here for You
            </Text>
            <Text variant={"sub_title"}>
              Select your area pf interest below to contact us today
            </Text>
          </div>

          <div className="grid grid-cols-1 xs:grid-cols-2 md:grid-cols-3 gap-x-12 gap-y-8">
            <div className="flex flex-col gap-7 justify-center items-center overflow-hidden relative bg-[#FFFFFF0F] border border-[#FFFFFF45] rounded-[25px] px-6 py-[40px]">
              <div className="flex flex-col gap-5  justify-center items-center">
                <Text variant={"body"} className="text-white text-center">
                  Get Product Support
                </Text>
                <Text variant={"body"} className="text-center">
                  We're available 24/7 for all of your questions and concerns.
                </Text>
              </div>
              <Link to={"/about"}>
                <button className="z-10 w-full h-[60px] flex items-center justify-center px-6 text-white text-base rounded-lg cursor-pointer bg-gradient-to-br from-[#4e1e95] to-[#3d0d6a] shadow-[0_0_10px_rgba(129,90,219,0.6)] border border-purple-500 hover:shadow-[0_0_15px_rgba(129,90,219,0.9)] transition">
                  Contact Support
                </button>
              </Link>
            </div>
            <div className="flex flex-col gap-7 justify-center items-center overflow-hidden relative bg-[#FFFFFF0F] border border-[#FFFFFF45] rounded-[25px] px-6 py-[40px]">
              <div className="flex flex-col gap-5  justify-center items-center">
                <Text variant={"body"} className="text-white text-center">
                  Become a Partner
                </Text>
                <Text variant={"body"} className="text-center">
                  Together, we're creating the future of Generative AI.
                </Text>
              </div>
              <button className="z-10 w-full h-[60px] flex items-center justify-center px-6 text-white text-base rounded-lg cursor-pointer bg-gradient-to-br from-[#4e1e95] to-[#3d0d6a] shadow-[0_0_10px_rgba(129,90,219,0.6)] border border-purple-500 hover:shadow-[0_0_15px_rgba(129,90,219,0.9)] transition">
                Contact Sales
              </button>
            </div>
            <div className="flex flex-col gap-7 justify-center items-center overflow-hidden relative bg-[#FFFFFF0F] border border-[#FFFFFF45] rounded-[25px] px-6 py-[40px]">
              <div className="flex flex-col gap-5  justify-center items-center">
                <Text variant={"body"} className="text-white text-center">
                  Inquire for Comment
                </Text>
                <Text variant={"body"} className="text-center">
                  You've got questions.MiragicAI We've got the Generative AI
                  experts.
                </Text>
              </div>
              <button className="z-10 w-full h-[60px] flex items-center justify-center px-6 text-white text-base rounded-lg cursor-pointer bg-gradient-to-br from-[#4e1e95] to-[#3d0d6a] shadow-[0_0_10px_rgba(129,90,219,0.6)] border border-purple-500 hover:shadow-[0_0_15px_rgba(129,90,219,0.9)] transition">
                Contact Media Team
              </button>
            </div>
          </div>
        </div>
        <div className="flex items-center gap-14 justify-start overflow-hidden relative bg-[#FFFFFF0F] border border-[#FFFFFF45] rounded-[25px] p-[40px]">
          <img
            loading="lazy"
            src="/icons/phone.svg"
            alt=""
            className="w-[80px]"
          />
          <div className="flex flex-col">
            <Text variant={"sub_title"} className="text-white ">
              Want To Talk To Us The Old-fashioned Way?
            </Text>
            <Text
              as="h3"
              variant={"card_title_large"}
              className="text-center text-[35px]"
            >
              <span className="text-white"> Give Us a Ring at</span>{" "}
              <span className="text-[#9855FF]">****************</span>
            </Text>
          </div>
        </div>
      </div>
    </Section>
  );
};

export default MiragicAi;
