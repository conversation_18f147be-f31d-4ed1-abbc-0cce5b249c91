import { Button } from "@/components/ui/button";

const ComingSoonPage = () => {
  const handleGoToHomepage = () => {
    window.location.href = "/";
  };
  return (
    <div className="min-h-screen bg-gray-900 text-white flex flex-col items-center justify-center p-4 sm:p-8 font-inter">
      <div className="text-center max-w-2xl mx-auto space-y-6">
        <h1 className="text-4xl sm:text-5xl lg:text-6xl font-extrabold leading-tight tracking-tight">
          Coming Soon!
        </h1>

        <p className="text-lg sm:text-xl text-gray-300 leading-relaxed max-w-prose mx-auto">
          Miragic AI is currently under development, meticulously crafted to
          bring you an unparalleled experience in generative AI and creative
          media.
        </p>

        <p className="text-md sm:text-lg text-gray-400 mt-8">
          Get ready to transform your creative workflow.
          <br />
          Stay tuned for exciting updates!
        </p>

        <div className="mt-8">
          <Button
            outline={false}
            variant={"animeGradient"}
            className="rounded-full bg-radial-[at_5%_5%] from-[#2C9BF7] to-[#8054F3] px-8 h-[50px]"
            onClick={handleGoToHomepage}
          >
            Go to Homepage
          </Button>
        </div>
      </div>
    </div>
  );
};

export default ComingSoonPage;
