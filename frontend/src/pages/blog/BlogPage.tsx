import { useState, useEffect } from "react";
// import { <PERSON> } from "react-router-dom";
// import { Button } from "@/components/ui/button";
// import {
//   Card,
//   CardContent,
//   CardDescription,
//   CardFooter,
//   <PERSON>Header,
//   CardTitle,
// } from "@/components/ui/card";
// import { Input } from "@/components/ui/input";
// import { Spinner } from "@/components/ui/spinner";
import BlogService from "@/services/blog.service";
import type { BlogPost } from "@/services/blog.service";
import { toast } from "sonner";
import Hero from "./components/Hero";
import BlogList from "./components/BlogList";

// Default fallback image if post image is not available
// const DEFAULT_IMAGE =
//   "https://images.unsplash.com/photo-1626544827763-d516dce335e2?w=800&auto=format&fit=crop&q=60&ixlib=rb-4.0.3";

const BlogPage = () => {
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [filteredPosts, setFilteredPosts] = useState<BlogPost[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  // Fetch blog posts from the API
  useEffect(() => {
    const fetchBlogPosts = async () => {
      try {
        setIsLoading(true);
        const response = await BlogService.getPosts({
          page: currentPage,
          limit: 9,
          search: searchTerm,
        });

        setFilteredPosts(response.posts);
        setTotalPages(response.totalPages);
      } catch (error) {
        console.error("Error fetching blog posts:", error);
        toast.error("Failed to load blog posts. Please try again later.");
        // Set empty array if there's an error
        setFilteredPosts([]);
      } finally {
        setIsLoading(false);
      }
    };

    fetchBlogPosts();
  }, [currentPage, searchTerm]);

  return (
    <div className="bg-[#000000] relative">
      <img
        loading="lazy"
        className="absolute top-0 left-0  "
        src="/png/about_hero_shadow.png"
      />
      <Hero searchTerm={searchTerm} setSearchTerm={setSearchTerm} />
      <BlogList
        blogs={filteredPosts}
        totalPages={totalPages}
        currentPage={currentPage}
        setCurrentPage={setCurrentPage}
        isLoading={isLoading}
      />
    </div>
  );
};

export default BlogPage;
