import { Text } from "@/components/ui/text";

const BlogCard241227 = () => {
  return (
    <div
      style={{
        background:
          "linear-gradient(#1f2937, #1f2937)  padding-box, linear-gradient(to bottom, #A166FC, #999999) border-box",
        border: "1px solid transparent",
      }}
      className="flex z-10 relative overflow-hidden rounded-xl  flex-col bg-white/5 border border-white/25 text-white  shadow-md w-full  "
    >
      <div className="absolute  bg-[#D9D9D9]/36 top-4 right-6 px-5 py-1 rounded-sm">
        <Text variant={"card_body"} className="text-white font-medium">
          Product Comparison
        </Text>
      </div>
      <img
        loading="lazy"
        className="w-full h-[350px] object-cover overflow-hidden rounded-xl"
        src="/png/blog/20241227.png"
      />
      <div className="flex flex-col gap-5 px-8 py-5">
        <div className="flex flex-col gap-2">
          <Text variant={"card_body"}>27th December, 2024</Text>
          <Text variant={"card_body"} className="text-white font-semibold">
            Shop with AI, Use AI to buy and try clothes on yourself virtually
          </Text>
        </div>
        <Text variant={"card_body"}>
          BY <span className=" font-semibold">Admin</span>
        </Text>
      </div>
    </div>
  );
};

export default BlogCard241227;
