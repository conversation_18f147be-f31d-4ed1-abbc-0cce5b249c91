import Section from "@/components/layout/Section";
import { ChevronLeft, ChevronRight, ChevronDown } from "lucide-react";
import { Text } from "@/components/ui/text";
import type { BlogCategory, BlogPost } from "@/services/blog.service";
import BlogCard from "@/components/Blogs/BlogCard";
import { Spinner } from "@/components/ui/spinner";
import * as Select from "@radix-ui/react-select";
import { useEffect, useState } from "react";
import BlogService from "@/services/blog.service";

interface BlogListProps {
  blogs: BlogPost[];
  totalPages: number;
  currentPage: number;
  setCurrentPage: (page: number) => void;
  isLoading: boolean;
}

const BlogList: React.FC<BlogListProps> = ({
  blogs,
  totalPages,
  currentPage,
  setCurrentPage,
  isLoading,
}) => {
  const [filterValue, setFilterValue] = useState("all");
  const [categories, setCategories] = useState<BlogCategory[]>([]);

  const handlePreviousPage = () => {
    if (currentPage > 1) {
      setCurrentPage(currentPage - 1);
    }
  };

  const handleNextPage = () => {
    if (currentPage < totalPages) {
      setCurrentPage(currentPage + 1);
    }
  };

  const handlePageClick = (pageNumber: number) => {
    setCurrentPage(pageNumber);
  };

  // Generate page numbers to display
  const getPageNumbers = () => {
    const pages = [];
    const maxVisiblePages = 5;

    if (totalPages <= maxVisiblePages) {
      // Show all pages if total is less than or equal to max visible
      for (let i = 1; i <= totalPages; i++) {
        pages.push(i);
      }
    } else {
      // Show pages around current page
      let startPage = Math.max(1, currentPage - 2);
      let endPage = Math.min(totalPages, currentPage + 2);

      // Adjust if we're near the beginning or end
      if (currentPage <= 3) {
        endPage = Math.min(totalPages, 5);
      } else if (currentPage >= totalPages - 2) {
        startPage = Math.max(1, totalPages - 4);
      }

      for (let i = startPage; i <= endPage; i++) {
        pages.push(i);
      }
    }

    return pages;
  };

  const fetchCategories = async () => {
    try {
      const categoriesData = await BlogService.getCategories();
      setCategories(categoriesData);
    } catch (error) {
      console.error("Failed to fetch categories:", error);
    }
  };

  useEffect(() => {
    fetchCategories();
  }, []);

  if (isLoading) {
    return (
      <div className="container mx-auto py-12 flex justify-center items-center min-h-[60vh]">
        <Spinner size="lg" />
        <span className="ml-2 text-white">Loading blog posts...</span>
      </div>
    );
  }

  if (!isLoading && blogs.length === 0) {
    return (
      <div className="container mx-auto py-12 text-center min-h-[60vh]">
        <h1 className="text-2xl font-bold mb-4 text-white">
          Blog posts not found
        </h1>
        <p className="text-gray-400 mb-6">
          No blog posts found. Please try adjusting your filters.
        </p>
      </div>
    );
  }

  return (
    <Section className="pb-[130px]">
      <div className="flex items-start flex-col gap-14">
        {/* Custom Select using Radix UI */}
        <Select.Root value={filterValue} onValueChange={setFilterValue}>
          <Select.Trigger className="bg-transparent z-10 min-w-[140px] border border-gray-600 rounded-lg px-4 py-2.5 text-sm font-normal text-white focus:outline-none focus:ring-2 focus:ring-blue-500 flex items-center justify-between hover:border-gray-500 transition-colors">
            <Select.Value placeholder="Select topic..." />
            <Select.Icon>
              <ChevronDown className="h-4 w-4" />
            </Select.Icon>
          </Select.Trigger>

          <Select.Portal>
            <Select.Content className="bg-gray-800 border border-gray-600 rounded-lg shadow-lg overflow-hidden z-50">
              <Select.Viewport className="p-1">
                <Select.Item
                  value="all"
                  className="px-3 py-2 text-sm text-white hover:bg-gray-700 cursor-pointer rounded focus:bg-gray-700 focus:outline-none"
                >
                  <Select.ItemText>All Topics</Select.ItemText>
                </Select.Item>

                {categories.map((category) => (
                  <Select.Item
                    key={category.id}
                    value={category.name}
                    className="px-3 py-2 text-sm text-white hover:bg-gray-700 cursor-pointer rounded focus:bg-gray-700 focus:outline-none"
                  >
                    <Select.ItemText>{category.name}</Select.ItemText>
                  </Select.Item>
                ))}
              </Select.Viewport>
            </Select.Content>
          </Select.Portal>
        </Select.Root>

        <div className="w-full items-center justify-center pb-20 grid grid-cols-1 sm:grid-cols-2 gap-y-8 gap-x-6 md:grid-cols-3">
          {blogs.map((blog) => (
            <BlogCard key={blog.id} blog={blog} />
          ))}
        </div>

        {/* Pagination Controls */}
        {totalPages > 1 && (
          <div className="flex w-full justify-between items-center">
            <div></div>

            {/* Navigation Controls */}
            <div className="flex items-center gap-4">
              <button
                onClick={handlePreviousPage}
                disabled={currentPage === 1}
                className={`${
                  currentPage === 1
                    ? "text-gray-500 cursor-not-allowed"
                    : "text-white hover:text-gray-300 cursor-pointer"
                } transition-colors`}
              >
                <ChevronLeft className="text-[32px]" />
              </button>

              {/* Page Numbers */}
              <div className="flex items-center gap-2">
                {getPageNumbers().map((pageNumber) => (
                  <button
                    key={pageNumber}
                    onClick={() => handlePageClick(pageNumber)}
                    className={`h-10 w-10 flex items-center text-center justify-center rounded-2xl transition-colors ${
                      pageNumber === currentPage
                        ? "bg-white text-gray-950"
                        : "border-2 border-gray-600 text-white hover:bg-gray-700"
                    }`}
                  >
                    <Text
                      variant={"body"}
                      className={
                        pageNumber === currentPage
                          ? "text-gray-950"
                          : "text-white"
                      }
                    >
                      {pageNumber}
                    </Text>
                  </button>
                ))}
              </div>

              <button
                onClick={handleNextPage}
                disabled={currentPage === totalPages}
                className={`${
                  currentPage === totalPages
                    ? "text-gray-500 cursor-not-allowed"
                    : "text-white hover:text-gray-300 cursor-pointer"
                } transition-colors`}
              >
                <ChevronRight className="text-[32px]" />
              </button>
            </div>

            {/* Page Info */}
            <div className="flex items-center gap-2">
              <div className="h-10 w-10 border-gray-600 border-2 flex items-center text-center justify-center rounded-2xl">
                <Text variant={"body"} className="text-center text-white">
                  {currentPage}
                </Text>
              </div>
              <Text variant={"body"} className="text-center text-gray-400">
                / {totalPages}
              </Text>
            </div>
          </div>
        )}
      </div>
    </Section>
  );
};

export default BlogList;