import { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, <PERSON>, useNavigate, useLocation } from "react-router-dom";
import {
  ArrowLeft,
  Calendar,
  User,
  Tag,
  Share2,
  Bookmark,
  Eye,
} from "lucide-react";
import Section from "@/components/layout/Section";
import { Button } from "@/components/ui/button";
import { Text } from "@/components/ui/text";
import { Spinner } from "@/components/ui/spinner";
import BlogService from "@/services/blog.service";
import type { BlogPost } from "@/services/blog.service";
import { toast } from "sonner";

const DEFAULT_IMAGE =
  "https://images.unsplash.com/photo-1626544827763-d516dce335e2?w=800&auto=format&fit=crop&q=60&ixlib=rb-4.0.3";

const BlogDetailsPage = () => {
  const { slug } = useParams<{ slug: string }>();
  const [blog, setBlog] = useState<BlogPost | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [relatedPosts, setRelatedPosts] = useState<BlogPost[]>([]);
  const [isBookmarked, setIsBookmarked] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();

  useEffect(() => {
    const fetchBlogDetails = async () => {
      if (!slug) return;

      try {
        setIsLoading(true);
        const blogData = await BlogService.getPostBySlug(slug);
        setBlog(blogData);

        // Fetch related posts based on categories
        if (blogData.categories && blogData.categories.length > 0) {
          const related = await BlogService.getPosts({
            page: 1,
            limit: 3,
            category: blogData.categories[0].slug,
          });
          setRelatedPosts(
            related.posts.filter((post) => post.id !== blogData.id)
          );
        }
      } catch (error) {
        console.error("Error fetching blog details:", error);
        toast.error("Failed to load blog post. Please try again later.");
      } finally {
        setIsLoading(false);
      }
    };

    fetchBlogDetails();
  }, [slug]);

  const formatDate = (dateString: string) => {
    const options: Intl.DateTimeFormatOptions = {
      year: "numeric",
      month: "long",
      day: "numeric",
    };
    return new Date(dateString).toLocaleDateString(undefined, options);
  };

  const handleShare = async () => {
    if (navigator.share && blog) {
      try {
        await navigator.share({
          title: blog.title,
          text: blog.excerpt,
          url: window.location.href,
        });
      } catch (error) {
        console.log("Error sharing:", error);
      }
    } else {
      // Fallback: copy to clipboard
      navigator.clipboard.writeText(window.location.href);
      toast.success("Link copied to clipboard!");
    }
  };

  const toggleBookmark = () => {
    setIsBookmarked(!isBookmarked);
    toast.success(
      isBookmarked ? "Removed from bookmarks" : "Added to bookmarks"
    );
  };

  //   // Helper function to get author name
  //   const getAuthorName = (author?: BlogPost["author"]) => {
  //     if (!author) return "Unknown Author";
  //     if ("name" in author) return author.name;
  //     return `${author.profile.firstName} ${author.profile.lastName}`;
  //   };

  //   // Helper function to get author avatar
  //   const getAuthorAvatar = (author?: BlogPost["author"]) => {
  //     if (!author) return null;
  //     if ("avatar" in author) return author.avatar;
  //     return author.profile.avatarUrl;
  //   };

  if (isLoading) {
    return (
      <div className="bg-[#000000] min-h-screen flex items-center justify-center">
        <img
          loading="lazy"
          className="absolute top-0 left-0"
          src="/png/about_hero_shadow.png"
          alt=""
        />
        <Spinner className="w-8 h-8 text-purple-500" />
      </div>
    );
  }

  if (!blog) {
    return (
      <div className="bg-[#000000] min-h-screen">
        <img
          loading="lazy"
          className="absolute top-0 left-0"
          src="/png/about_hero_shadow.png"
          alt=""
        />
        <Section className="py-20">
          <div className="text-center flex flex-col items-center">
            <Text
              variant="page_title"
              className="text-white mb-4 !text-4xl z-20"
            >
              Blog Not Found
            </Text>
            <Text
              variant="sub_title"
              className="text-gray-400 mb-8 !text-xl z-20"
            >
              The blog post you're looking for doesn't exist or has been
              removed.
            </Text>
            <Button
              className="z-20"
              variant="gradient"
              onClick={() => navigate("/blog")}
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Blog
            </Button>
          </div>
        </Section>
      </div>
    );
  }

  return (
    <div
      className={`${
        location.pathname.includes("admin") ? "" : "bg-[#000000]"
      } relative min-h-screen`}
    >
      {location.pathname.includes("admin") ? null : (
        <img
          loading="lazy"
          className="absolute top-0 left-0"
          src="/png/about_hero_shadow.png"
          alt=""
        />
      )}

      {/* Blog Content */}
      <Section className="pb-20">
        <article className="max-w-4xl mx-auto">
          {/* Category Badge */}
          <div className="my-6 flex justify-between items-center">
            <div className="inline-block px-4 py-2 rounded-lg border border-purple-600">
              <Text variant="card_body" className="text-white font-medium">
                {blog.categories && blog.categories.length > 0
                  ? blog.categories[0].name
                  : "Uncategorized"}
              </Text>
            </div>
            <div className="flex items-center gap-3">
              <Button
                variant="outline"
                size="sm"
                onClick={toggleBookmark}
                className={`bg-white/10 border-gray-700 hover:bg-white/20 ${
                  isBookmarked ? "text-yellow-400" : "text-white"
                }`}
              >
                <Bookmark
                  className={`w-4 h-4 ${isBookmarked ? "fill-current" : ""}`}
                />
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={handleShare}
                className="bg-white/10 border-gray-700 text-white hover:bg-white/20"
              >
                <Share2 className="w-4 h-4" />
              </Button>
            </div>
          </div>

          {/* Title */}
          <h1 className="text-4xl md:text-5xl font-bold text-white mb-6 leading-tight">
            {blog.title}
          </h1>

          {/* Excerpt */}
          <Text
            variant="sub_title"
            className="text-gray-300 mb-8 leading-relaxed"
            style={{
              wordBreak: "break-word",
              overflowWrap: "break-word",
              hyphens: "auto",
            }}
          >
            {blog.excerpt}
          </Text>

          {/* Meta Information */}
          <div className="flex flex-wrap items-center gap-6 mb-8 pb-6 border-b border-gray-700">
            <div className="flex items-center gap-3">
              {/* {getAuthorAvatar(blog.author) && (
                <img loading="lazy"
                  src={getAuthorAvatar(blog.author)!}
                  alt={getAuthorName(blog.author)}
                  className="w-10 h-10 rounded-full object-cover"
                />
              )} */}
              <div>
                <div className="flex items-center gap-2 text-white">
                  <User className="w-4 h-4" />
                  <Text variant="card_body" className="font-semibold">
                    {/* {getAuthorName(blog.author)} */}
                    {/* {blog.author?.profile?.firstName +
                      " " +
                      blog.author?.profile?.lastName} */}
                    By Admin
                  </Text>
                </div>
              </div>
            </div>

            <div className="flex items-center gap-2 text-gray-400">
              <Calendar className="w-4 h-4" />
              <Text variant="card_body">
                {blog.publishedAt
                  ? formatDate(blog.publishedAt)
                  : "Not published"}
              </Text>
            </div>

            <div className="flex items-center gap-2 text-gray-400">
              <Eye className="w-4 h-4" />
              <Text variant="card_body">5 min read</Text>
            </div>
          </div>

          {/* Featured Image */}
          <div className="mb-10">
            <img
              loading="lazy"
              src={blog.featuredImageUrl || DEFAULT_IMAGE}
              alt={blog.title}
              className="w-full h-[400px] md:h-[500px] object-cover rounded-xl shadow-2xl"
              onError={(e) => {
                (e.target as HTMLImageElement).src = DEFAULT_IMAGE;
              }}
            />
          </div>

          {/* Blog Content */}
          <div
            className="prose prose-lg prose-invert max-w-none mb-12"
            style={{
              color: "#e5e7eb",
              lineHeight: "1.8",
              wordBreak: "break-word",
              overflowWrap: "break-word",
              hyphens: "auto",
            }}
          >
            <div
              dangerouslySetInnerHTML={{ __html: blog.content }}
              className="text-gray-300 leading-relaxed text-lg"
            />
          </div>

          {/* Tags */}
          {blog.tags && blog.tags.length > 0 && (
            <div className="mb-12">
              <div className="flex items-center gap-2 mb-4">
                <Tag className="w-5 h-5 text-gray-400" />
                <Text
                  variant="card_body"
                  className="text-gray-400 font-semibold"
                >
                  Tags:
                </Text>
              </div>
              <div className="flex flex-wrap gap-2">
                {blog.tags.map((tag) => (
                  <span
                    key={tag.id}
                    className="px-3 py-1 bg-white/10 border border-gray-700 rounded-full text-sm text-gray-300 hover:bg-white/20 transition-colors cursor-pointer"
                  >
                    #{tag.name}
                  </span>
                ))}
              </div>
            </div>
          )}
        </article>

        {/* Related Posts */}
        {relatedPosts.length > 0 && (
          <div className="max-w-6xl mx-auto mt-16">
            <div className="mb-8">
              <Text variant="page_title" className="text-white mb-2">
                Related Posts
              </Text>
              <Text variant="sub_title" className="text-gray-400">
                You might also like these articles
              </Text>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {relatedPosts.map((post) => (
                <Link key={post.id} to={`/blog/${post.slug}`}>
                  <div
                    style={{
                      background:
                        "linear-gradient(#1f2937, #1f2937) padding-box, linear-gradient(to bottom, #A166FC, #999999) border-box",
                      border: "1px solid transparent",
                    }}
                    className="flex z-10 relative overflow-hidden rounded-xl flex-col bg-white/5 border border-white/25 text-white shadow-md w-full hover:transform hover:scale-105 transition-all duration-300"
                  >
                    <div className="absolute bg-[#D9D9D9]/36 top-4 right-6 px-3 py-1 rounded-sm z-10">
                      <Text
                        variant="card_body"
                        className="text-white font-medium text-xs"
                      >
                        {post.categories && post.categories.length > 0
                          ? post.categories[0].name
                          : "Uncategorized"}
                      </Text>
                    </div>

                    <img
                      loading="lazy"
                      className="w-full h-[200px] object-cover"
                      src={post.featuredImageUrl || DEFAULT_IMAGE}
                      alt={post.title}
                      onError={(e) => {
                        (e.target as HTMLImageElement).src = DEFAULT_IMAGE;
                      }}
                    />

                    <div className="flex flex-col gap-3 px-6 py-4">
                      <div className="flex flex-col gap-2">
                        <Text variant="card_body" className="text-gray-400">
                          {post.publishedAt
                            ? formatDate(post.publishedAt)
                            : "Draft"}
                        </Text>
                        <Text
                          variant="card_body"
                          className="text-white font-semibold line-clamp-2"
                        >
                          {post.title}
                        </Text>
                      </div>
                      <Text variant="card_body" className="text-gray-400">
                        By Admin
                        {/* <span className="font-semibold text-white">
                          {blog.author?.profile?.firstName +
                            " " +
                            blog.author?.profile?.lastName}
                        </span> */}
                      </Text>
                    </div>
                  </div>
                </Link>
              ))}
            </div>
          </div>
        )}
      </Section>
    </div>
  );
};

export default BlogDetailsPage;
