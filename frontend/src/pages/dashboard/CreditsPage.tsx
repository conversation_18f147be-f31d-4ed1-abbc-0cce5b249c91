import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Spinner } from "@/components/ui/spinner";
import { toast } from "sonner";
import ApiService from "@/services/api.service";
import { useApp } from "@/contexts/useApp";

interface CreditPack {
  id: string;
  name: string;
  credits: number;
  price: number;
  currency?: string;
  description?: string;
}

interface CreditTransaction {
  id: string;
  amount: number;
  type: string;
  description?: string;
  createdAt: string;
}

const CreditsPage = () => {
  const { user, refreshUserData } = useApp();
  const [creditPacks, setCreditPacks] = useState<CreditPack[]>([]);
  const [transactions, setTransactions] = useState<CreditTransaction[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isLoadingPurchase, setIsLoadingPurchase] = useState(false);
  const [selectedPack, setSelectedPack] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setIsLoading(true);
        // Fetch credit packs
        const packsResponse = await ApiService.get<{ data: CreditPack[] }>(
          "/payment/credit-packs"
        );
        setCreditPacks((packsResponse.data as any).data || []);

        // Fetch credit transactions
        const transactionsResponse = await ApiService.get<{
          data: CreditTransaction[];
        }>("/user/credits/transactions");
        setTransactions((transactionsResponse.data as any).data || []);
      } catch (error) {
        console.error("Error fetching credit data:", error);
        toast.error("Failed to load credit information");
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, []);

  const handlePurchaseCredits = async (packId: string) => {
    try {
      setIsLoadingPurchase(true);
      setSelectedPack(packId);

      const pack = creditPacks.find((p) => p.id === packId);
      if (!pack) return;

      // Create checkout session
      const response = await ApiService.post<{ data: { url: string } }>(
        "/payment/stripe/create-credit-checkout-session",
        {
          amount: pack.price,
          credits: pack.credits,
          successUrl: `${window.location.origin}/dashboard/credits?success=true`,
          cancelUrl: `${window.location.origin}/dashboard/credits?canceled=true`,
        }
      );

      // Redirect to Stripe checkout
      if ((response.data as any).data?.url) {
        window.location.href = (response.data as any).data.url;
      } else {
        throw new Error("No checkout URL returned");
      }
    } catch (error) {
      console.error("Error purchasing credits:", error);
      toast.error("Failed to initiate credit purchase");
    } finally {
      setIsLoadingPurchase(false);
      setSelectedPack(null);
    }
  };

  // Check for success or canceled query params
  useEffect(() => {
    const queryParams = new URLSearchParams(window.location.search);
    const success = queryParams.get("success");
    const canceled = queryParams.get("canceled");

    if (success === "true") {
      toast.success("Credit purchase successful!");
      refreshUserData();
      // Clean up URL
      window.history.replaceState({}, document.title, window.location.pathname);
    } else if (canceled === "true") {
      toast.error("Credit purchase was canceled");
      // Clean up URL
      window.history.replaceState({}, document.title, window.location.pathname);
    }
  }, [refreshUserData]);

  return (
    <div className="container mx-auto py-6 space-y-8">
      <div className="flex flex-col space-y-2">
        <h1 className="text-3xl font-bold tracking-tight">Credits</h1>
        <p className="text-muted-foreground">
          Manage your credits and purchase more to use our services
        </p>
      </div>

      {isLoading ? (
        <div className="flex justify-center items-center py-12">
          <Spinner className="h-8 w-8" />
        </div>
      ) : (
        <div className="grid gap-6">
          {/* Current Credits */}
          <Card>
            <CardHeader>
              <CardTitle>Current Credits</CardTitle>
              <CardDescription>
                Your available credits for AI generation services
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex flex-col items-center py-6">
                <div className="text-5xl font-bold mb-2">
                  {user?.credit?.balance || 0}
                </div>
                <p className="text-muted-foreground">Available Credits</p>
              </div>
            </CardContent>
          </Card>

          {/* Purchase Credits */}
          <Card>
            <CardHeader>
              <CardTitle>Purchase Credits</CardTitle>
              <CardDescription>
                Choose a credit pack to purchase
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {creditPacks.map((pack) => (
                  <Card
                    key={pack.id}
                    className={pack.id === "medium" ? "border-primary" : ""}
                  >
                    {pack.id === "medium" && (
                      <div className="bg-primary text-primary-foreground text-center py-1 text-sm font-medium">
                        Most Popular
                      </div>
                    )}
                    <CardContent className="pt-6">
                      <div className="text-center mb-4">
                        <h3 className="text-xl font-bold">{pack.name}</h3>
                        <div className="text-3xl font-bold mt-2">
                          {pack.credits}{" "}
                          <span className="text-sm font-normal">credits</span>
                        </div>
                        <div className="text-2xl font-bold mt-1">
                          ${pack.price}
                        </div>
                        <p className="text-sm text-muted-foreground mt-2">
                          {pack.description}
                        </p>
                      </div>
                      <Button
                        className="w-full"
                        onClick={() => handlePurchaseCredits(pack.id)}
                        disabled={isLoadingPurchase}
                      >
                        {isLoadingPurchase && selectedPack === pack.id ? (
                          <>
                            <Spinner className="mr-2 h-4 w-4" />
                            Processing...
                          </>
                        ) : (
                          "Buy Now"
                        )}
                      </Button>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Transaction History */}
          <Card>
            <CardHeader>
              <CardTitle>Transaction History</CardTitle>
              <CardDescription>Your recent credit transactions</CardDescription>
            </CardHeader>
            <CardContent>
              {transactions.length > 0 ? (
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead>
                      <tr className="border-b">
                        <th className="text-left py-3 px-4 font-medium">
                          Date
                        </th>
                        <th className="text-left py-3 px-4 font-medium">
                          Type
                        </th>
                        <th className="text-left py-3 px-4 font-medium">
                          Amount
                        </th>
                        <th className="text-left py-3 px-4 font-medium">
                          Description
                        </th>
                      </tr>
                    </thead>
                    <tbody>
                      {transactions.map((transaction) => (
                        <tr key={transaction.id} className="border-b">
                          <td className="py-3 px-4">
                            {new Date(
                              transaction.createdAt
                            ).toLocaleDateString()}
                          </td>
                          <td className="py-3 px-4 capitalize">
                            <span
                              className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                                transaction.type === "PURCHASE" ||
                                transaction.type === "BONUS"
                                  ? "bg-green-100 text-green-800"
                                  : transaction.type === "USAGE"
                                  ? "bg-yellow-100 text-yellow-800"
                                  : "bg-red-100 text-red-800"
                              }`}
                            >
                              {transaction.type.toLowerCase()}
                            </span>
                          </td>
                          <td className="py-3 px-4">
                            {transaction.type === "PURCHASE" ||
                            transaction.type === "BONUS"
                              ? `+${transaction.amount}`
                              : `-${transaction.amount}`}
                          </td>
                          <td className="py-3 px-4">
                            {transaction.description || "-"}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  No transactions found
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
};

export default CreditsPage;
