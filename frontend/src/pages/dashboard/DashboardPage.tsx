import DashboardCommunity from "@/components/dashboard/DashboardCommunity";
import RefferFormModal from "@/components/dashboard/RefferFormModal";
import FavoriteToolSlider from "@/components/home/<USER>";
import {
  SolarCardIconDashboard,
  UserIconDashboard,
  WebinarIconDashboard,
} from "@/lib/icons";
import { Plus, Settings } from "lucide-react";
import { useState } from "react";
import { useNavigate } from "react-router-dom";

const DashboardPage = () => {
  const [isOpenRefferModal, setIsOpenRefferModal] = useState(false);
  const navigate = useNavigate(); 

  const cards = [
    {
      title: "About Us",
      description: "Learn more about Miragic",
      Icon: WebinarIconDashboard,
      bgColor: "bg-[#7861FA]",
      href: "/about",
    },
    {
      title: "Referral Program",
      description: "Refer friends and earn rewards",
      Icon: UserIconDashboard,
      bgColor: "bg-[#157C53]",
      href: "/referral",
    },
    {
      title: "Blogs",
      description: "Read our latest blogs",
      Icon: SolarCardIconDashboard,
      bgColor: "bg-[#F56630]",
      href: "/blog",
    },
  ];

  return (
    <div className="px-6 pt-6">
      {/* Header Section */}
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-inter text-white">Home</h1>
        <div className="flex items-center space-x-3">
          <button
            onClick={() => setIsOpenRefferModal(true)}
            className="flex items-center space-x-2 text-white px-3 py-3 rounded-lg hover:bg-gray-700 transition-colors border border-gray-500"
          >
            <Plus className="w-4 h-4" />
            <span className="text-sm font-medium">Invite members</span>
          </button>
          <button className="text-gray-400 hover:text-gray-200 p-3 rounded-lg transition-colors border border-gray-500">
            <Settings className="w-5 h-5" />
          </button>
        </div>
      </div>

      {/* Divider */}
      <div className="h-px bg-gradient-to-r from-transparent via-purple-500 to-transparent mb-6"></div>

      {/* Cards Section */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {cards.map(({ title, description, Icon, bgColor, href }, index) => (
          <div
            key={index}
            onClick={()=>navigate(href)}
            className="bg-white/5 rounded-2xl py-5 px-7 flex items-center justify-center gap-4 hover:bg-gray-700 transition-colors border border-gray-500 cursor-pointer"
          >
            <div>
              <h3 className="text-sm font-medium text-white">{title}</h3>
              <p className="text-sm text-gray-400 mt-2">{description}</p>
            </div>
            <div
              className={`rounded-xl p-1 w-16 h-16 flex justify-center items-center ${bgColor}`}
            >
              <Icon />
            </div>
          </div>
        ))}
      </div>

      <div className="mt-16">
        <FavoriteToolSlider title="Favourite Tools" width={460} />
      </div>
      <div className="flex flex-col mt-10">
        <h2 className="text-3xl font-inter text-white mb-3">Community</h2>
        <DashboardCommunity />
      </div>
      <RefferFormModal
        isOpen={isOpenRefferModal}
        onClose={() => setIsOpenRefferModal(false)}
      />
    </div>
  );
};

export default DashboardPage;
