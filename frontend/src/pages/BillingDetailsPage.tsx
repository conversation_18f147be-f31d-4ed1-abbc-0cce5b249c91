import { useState, useEffect, useContext } from "react";
import { Zap, RefreshCw, History, DollarSign } from "lucide-react";
import { AppContext } from "@/contexts/AppContext";
import CreditService from "@/services/credit.service";
import type {
  CreditBalance,
  CreditTransaction,
  ServiceCost,
} from "@/services/credit.service";
import { Link } from "react-router-dom";

// Interfaces are now imported from credit.service.ts

const BillingDetailsPage = () => {
  const { user } = useContext(AppContext);
  const [activeTab, setActiveTab] = useState<string>("credits");
  const [loading, setLoading] = useState<boolean>(true);
  const [creditBalance, setCreditBalance] = useState<CreditBalance | null>(
    null
  );
  const [transactions, setTransactions] = useState<CreditTransaction[]>([]);
  // const [creditPackages, setCreditPackages] = useState<CreditPackage[]>([]);
  const [serviceCosts, setServiceCosts] = useState<ServiceCost | null>(null);
  // const [purchaseLoading, setPurchaseLoading] = useState<boolean>(false);

  // const currentPlanFeatures = [
  //   "25 images or 1.5 mins of video",
  //   "1 Customized Instant Avatar",
  //   "Upload file size limited to 150M and 5mins",
  //   "60+ free Public Studio Avatars",
  // ];

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);

        // Fetch credit balance
        const balanceResponse = await CreditService.getUserCreditBalance();
        setCreditBalance(balanceResponse.data);

        // Fetch transaction history
        const historyResponse = await CreditService.getTransactionHistory();
        setTransactions(historyResponse.data.transactions);

        // Fetch service costs
        const costsResponse = await CreditService.getServiceCosts();
        setServiceCosts(costsResponse.data);
      } catch (error) {
        console.error("Error fetching billing data:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  // const handlePurchaseCredits = async (packageId: string) => {
  //   try {
  //     setPurchaseLoading(true);

  //     // Create checkout session
  //     const response = await CreditService.createStripeCheckoutSession(
  //       packageId,
  //       `${window.location.origin}/billing?success=true`,
  //       `${window.location.origin}/billing?canceled=true`
  //     );

  //     // Redirect to Stripe checkout
  //     window.location.href = response.data.url;
  //   } catch (error) {
  //     console.error('Error creating checkout session:', error);
  //   } finally {
  //     setPurchaseLoading(false);
  //   }
  // };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString();
  };

  const formatTime = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleTimeString();
  };

  const getTransactionTypeColor = (type: string) => {
    switch (type) {
      case "PURCHASE":
        return "text-green-500";
      case "USAGE":
        return "text-blue-500";
      case "REFUND":
        return "text-purple-500";
      case "ADJUSTMENT_ADD":
        return "text-green-500";
      case "ADJUSTMENT_SUBTRACT":
        return "text-red-500";
      case "SUBSCRIPTION_GRANT":
        return "text-indigo-500";
      case "BONUS":
        return "text-yellow-500";
      default:
        return "text-gray-500";
    }
  };

  return (
    <div className="text-white p-6 min-h-screen">
      {/* Header */}
      <h1 className="text-2xl font-semibold mb-6 pb-4 border-b border-gray-700">
        Billing for {user?.email || "User"}
      </h1>

      {/* Tabs */}
      <div className="flex mb-6 border-b border-gray-700">
        <button
          className={`px-4 py-2 font-medium ${
            activeTab === "credits"
              ? "text-primary border-b-2 border-primary"
              : "text-gray-400"
          }`}
          onClick={() => setActiveTab("credits")}
        >
          <div className="flex items-center gap-2">
            <Zap size={16} />
            <span>Credits</span>
          </div>
        </button>
        <button
          className={`px-4 py-2 font-medium ${
            activeTab === "transactions"
              ? "text-primary border-b-2 border-primary"
              : "text-gray-400"
          }`}
          onClick={() => setActiveTab("transactions")}
        >
          <div className="flex items-center gap-2">
            <History size={16} />
            <span>Transactions</span>
          </div>
        </button>
        <button
          className={`px-4 py-2 font-medium ${
            activeTab === "costs"
              ? "text-primary border-b-2 border-primary"
              : "text-gray-400"
          }`}
          onClick={() => setActiveTab("costs")}
        >
          <div className="flex items-center gap-2">
            <DollarSign size={16} />
            <span>Service Costs</span>
          </div>
        </button>
      </div>

      {/* Credits Tab */}
      {activeTab === "credits" && (
        <>
          {/* Current Plan Section */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
            {/* Left Plan Card */}
            <div className="flex flex-col">
              <h2 className="text-lg font-semibold text-gray-300 mb-4">
                Current Plan
              </h2>
              <div className="bg-white/10 rounded-xl p-6 border border-gray-700 flex-grow">
                <div className="flex justify-between items-center mb-6">
                  <span className="text-lg text-gray-300"></span>
                  <Link to="/dashboard/subscription" className="">
                    <button className="hover:bg-gray-600 border border-gray-500 text-white px-4 py-2 rounded-lg text-sm transition-colors">
                      Change Plan
                    </button>
                  </Link>
                </div>

                <div className="space-y-3">
                  {/* {currentPlanFeatures.map((feature, index) => (
                    <div key={index} className="flex items-start gap-3">
                      <Check
                        size={16}
                        className="text-green-500 mt-0.5 flex-shrink-0"
                      />
                      <span className="text-sm text-gray-400">{feature}</span>
                    </div>
                  ))} */}
                </div>
              </div>
            </div>

            {/* Credit Balance Card */}
            <div className="flex flex-col">
              <h2 className="text-lg font-semibold text-gray-300 mb-4">
                Credit Balance
              </h2>
              <div className="bg-white/10 rounded-xl p-8 border border-gray-700 flex-grow">
                {loading ? (
                  <div className="flex items-center justify-center h-full">
                    <RefreshCw className="animate-spin h-8 w-8 text-primary" />
                  </div>
                ) : (
                  <div className="space-y-4 h-full flex flex-col justify-between">
                    <div>
                      <div className="text-sm text-gray-400 mb-1">
                        Available Credits
                      </div>
                      <div className="flex items-center gap-2">
                        <span className="text-4xl font-bold text-white">
                          {creditBalance?.balance.toLocaleString() || "0"}
                        </span>
                        <Zap size={24} className="text-yellow-500" />
                      </div>
                    </div>

                    <div>
                      <div className="text-sm text-gray-400 mb-1">
                        Total Spent
                      </div>
                      <div className="text-lg text-white">
                        {creditBalance?.spent.toLocaleString() || "0"} Credits
                      </div>
                    </div>

                    <div className="text-xs text-gray-500">
                      Last updated:{" "}
                      {creditBalance
                        ? new Date(creditBalance.lastUpdatedAt).toLocaleString()
                        : "N/A"}
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Credit Packages */}
          {/* <div className="mb-8">
            <h2 className="text-lg font-semibold text-gray-300 mb-4">Purchase Credits</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {loading ? (
                <div className="col-span-full flex items-center justify-center py-12">
                  <RefreshCw className="animate-spin h-8 w-8 text-primary" />
                </div>
              ) : (
                creditPackages.map((pkg) => (
                  <div key={pkg.id} className="bg-white/10 rounded-xl p-6 border border-gray-700 hover:border-primary transition-all">
                    <h3 className="text-xl font-bold text-white mb-2">{pkg.name}</h3>
                    <p className="text-sm text-gray-400 mb-4">{pkg.description}</p>
                    
                    <div className="flex items-center gap-2 mb-4">
                      <span className="text-2xl font-bold text-white">{pkg.creditsAmount.toLocaleString()}</span>
                      <Zap size={20} className="text-yellow-500" />
                    </div>
                    
                    <div className="text-lg font-bold text-primary mb-6">
                      {pkg.price.toLocaleString()} {pkg.currency}
                    </div>
                    
                    <button 
                      className="w-full bg-primary hover:bg-primary/80 text-white py-2 px-4 rounded-lg flex items-center justify-center gap-2 transition-colors"
                      onClick={() => handlePurchaseCredits(pkg.id)}
                      disabled={purchaseLoading}
                    >
                      {purchaseLoading ? (
                        <>
                          <RefreshCw className="animate-spin h-4 w-4" />
                          <span>Processing...</span>
                        </>
                      ) : (
                        <>
                          <CreditCard size={16} />
                          <span>Purchase</span>
                        </>
                      )}
                    </button>
                  </div>
                ))
              )}
            </div>
          </div> */}
        </>
      )}

      {/* Transactions Tab */}
      {activeTab === "transactions" && (
        <div className="bg-white/10 rounded-xl border border-gray-700 overflow-hidden">
          {/* Table Header */}
          <div className="grid grid-cols-4 gap-4 p-4 bg-white/10 border-b border-gray-600">
            <div className="text-sm text-gray-300 font-medium">Date</div>
            <div className="text-sm text-gray-300 font-medium">Type</div>
            <div className="text-sm text-gray-300 font-medium">Description</div>
            <div className="text-sm text-gray-300 font-medium text-right">
              Credit change
            </div>
          </div>

          {/* Table Body */}
          {loading ? (
            <div className="flex items-center justify-center py-12">
              <RefreshCw className="animate-spin h-8 w-8 text-primary" />
            </div>
          ) : transactions.length > 0 ? (
            <div className="divide-y divide-gray-700">
              {transactions.map((transaction) => (
                <div
                  key={transaction.id}
                  className="grid grid-cols-4 gap-4 p-4 hover:bg-gray-750 transition-colors"
                >
                  <div className="text-sm">
                    <div className="text-gray-300">
                      {formatDate(transaction.createdAt)}
                    </div>
                    <div className="text-gray-500 text-xs">
                      {formatTime(transaction.createdAt)}
                    </div>
                  </div>
                  <div
                    className={`text-sm font-medium ${getTransactionTypeColor(
                      transaction.type
                    )}`}
                  >
                    {transaction.type.replace("_", " ")}
                  </div>
                  <div className="text-sm text-gray-300">
                    {transaction.description}
                  </div>
                  <div
                    className={`text-sm text-right font-medium ${
                      transaction.amount >= 0
                        ? "text-green-500"
                        : "text-red-500"
                    }`}
                  >
                    {transaction.amount >= 0 ? "+" : ""}
                    {transaction.amount}
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-12 text-gray-400">
              No transactions found.
            </div>
          )}
        </div>
      )}

      {/* Service Costs Tab */}
      {activeTab === "costs" && (
        <div className="bg-white/10 rounded-xl border border-gray-700 overflow-hidden">
          {/* Table Header */}
          <div className="grid grid-cols-2 gap-4 p-4 bg-white/10 border-b border-gray-600">
            <div className="text-sm text-gray-300 font-medium">Service</div>
            <div className="text-sm text-gray-300 font-medium text-right">
              Cost (Credits)
            </div>
          </div>

          {/* Table Body */}
          {loading ? (
            <div className="flex items-center justify-center py-12">
              <RefreshCw className="animate-spin h-8 w-8 text-primary" />
            </div>
          ) : serviceCosts ? (
            <div className="divide-y divide-gray-700">
              {/* <div className="grid grid-cols-2 gap-4 p-4 hover:bg-gray-750 transition-colors">
                <div className="text-sm text-gray-300">Image Generation</div>
                <div className="text-sm text-right font-medium text-white">
                  {serviceCosts.imageGeneration} per image
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4 p-4 hover:bg-gray-750 transition-colors">
                <div className="text-sm text-gray-300">Video Generation</div>
                <div className="text-sm text-right font-medium text-white">
                  {serviceCosts.videoGeneration} per minute
                </div>
              </div> */}
              <div className="grid grid-cols-2 gap-4 p-4 hover:bg-gray-750 transition-colors">
                <div className="text-sm text-gray-300">Background Removal</div>
                <div className="text-sm text-right font-medium text-white">
                  {serviceCosts.backgroundRemoval} per image
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4 p-4 hover:bg-gray-750 transition-colors">
                <div className="text-sm text-gray-300">Virtual Try-On</div>
                <div className="text-sm text-right font-medium text-white">
                  {serviceCosts.virtualTryOn} per try-on
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4 p-4 hover:bg-gray-750 transition-colors">
                <div className="text-sm text-gray-300">Speedpaint</div>
                <div className="text-sm text-right font-medium text-white">
                  {serviceCosts.speedpaint} per speedpaint
                </div>
              </div>
            </div>
          ) : (
            <div className="text-center py-12 text-gray-400">
              No service cost information available.
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default BillingDetailsPage;
