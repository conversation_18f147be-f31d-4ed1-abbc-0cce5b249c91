import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import {
  Loader2,
  ArrowLeft,
  CreditCard,
  Calendar,
  RefreshCw,
} from "lucide-react";
import { toast } from "sonner";
import { useApp } from "@/contexts/useApp";
import SubscriptionService from "@/services/subscription.service";
import type {
  SubscriptionPlan,
  UserSubscription,
} from "@/services/subscription.service";
import SubscriptionPlanChange from "@/components/subscription/SubscriptionPlanChange";
import ScheduledChangesManager from "@/components/subscription/ScheduledChangesManager";
import SubscriptionCancellationModal from "@/components/subscription/SubscriptionCancellationModal";
import RefundRequestModal from "@/components/subscription/RefundRequestModal";

const SubscriptionManagement = () => {
  const { user } = useApp();
  const navigate = useNavigate();
  const [subscriptionPlans, setSubscriptionPlans] = useState<
    SubscriptionPlan[]
  >([]);
  const [userSubscription, setUserSubscription] =
    useState<UserSubscription | null>(null);
  const [loading, setLoading] = useState({
    plans: true,
    userSubscription: true,
  });
  const [error, setError] = useState({
    plans: "",
    userSubscription: "",
  });
  const [isCancellationModalOpen, setIsCancellationModalOpen] = useState(false);
  const [isRefundModalOpen, setIsRefundModalOpen] = useState(false);

  useEffect(() => {
    fetchUserSubscription();
    fetchPlans();
  }, [user]); // eslint-disable-line react-hooks/exhaustive-deps

  const fetchUserSubscription = async () => {
    if (!user) return;

    try {
      setLoading((prev) => ({ ...prev, userSubscription: true }));
      const subscription = await SubscriptionService.getUserSubscription();
      setUserSubscription(subscription);
      setError((prev) => ({ ...prev, userSubscription: "" }));
    } catch (err) {
      console.error("Error fetching user subscription:", err);
      setError((prev) => ({
        ...prev,
        userSubscription: "Failed to load user subscription",
      }));
    } finally {
      setLoading((prev) => ({ ...prev, userSubscription: false }));
    }
  };

  const fetchPlans = async () => {
    try {
      setLoading((prev) => ({ ...prev, plans: true }));
      const response = await SubscriptionService.getSubscriptionPlans();
      setSubscriptionPlans(response.filter((plan) => plan.isActive));
      setError((prev) => ({ ...prev, plans: "" }));
    } catch (err) {
      console.error("Error fetching subscription plans:", err);
      setError((prev) => ({
        ...prev,
        plans: "Failed to load subscription plans",
      }));
    } finally {
      setLoading((prev) => ({ ...prev, plans: false }));
    }
  };

  const handleRefresh = () => {
    fetchUserSubscription();
    fetchPlans();
  };

  const handleOpenCancellationModal = () => {
    setIsCancellationModalOpen(true);
  };

  const handleCancellationSuccess = () => {
    fetchUserSubscription();
    toast.success("Subscription cancelled successfully");
  };

  const handleOpenRefundModal = () => {
    setIsRefundModalOpen(true);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  const isLoading = loading.plans || loading.userSubscription;

  return (
    <div className="container mx-auto py-8 px-4 max-w-5xl">
      <div className="flex items-center mb-6">
        <Button
          variant="ghost"
          size="sm"
          className="mr-2"
          onClick={() => navigate("/dashboard")}
        >
          <ArrowLeft className="h-4 w-4 mr-1" />
          Back to Dashboard
        </Button>
        <h1 className="text-2xl font-bold flex-grow">
          Subscription Management
        </h1>
        <Button
          variant="outline"
          size="sm"
          className="text-purple-600"
          onClick={handleRefresh}
          disabled={isLoading}
        >
          {isLoading ? (
            <Loader2 className="h-4 w-4 animate-spin" />
          ) : (
            <>
              <RefreshCw className="h-4 w-4 mr-1" />
              Refresh
            </>
          )}
        </Button>
      </div>

      {error.userSubscription && (
        <div className="bg-red-50 border border-red-200 text-red-700 p-4 rounded-md mb-6">
          {error.userSubscription}
        </div>
      )}

      {isLoading ? (
        <div className="flex justify-center items-center p-12">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </div>
      ) : !userSubscription ? (
        <div className="bg-amber-50 border border-amber-200 text-amber-700 p-6 rounded-lg mb-6 text-center">
          <h2 className="text-xl font-semibold mb-2">No Active Subscription</h2>
          <p className="mb-4">You don't have an active subscription plan.</p>
          <Button onClick={() => navigate("/pricing")}>
            View Pricing Plans
          </Button>
        </div>
      ) : (
        <>
          {/* Current Subscription Details */}
          <div className="bg-white border rounded-lg shadow-sm overflow-hidden mb-6">
            <div className="bg-primary/10 px-6 py-4 border-b">
              <h2 className="text-xl font-semibold text-primary">
                Current Subscription
              </h2>
            </div>
            <div className="p-6 bg-primary/90">
              <div className="grid md:grid-cols-2 gap-6">
                <div>
                  <h3 className="text-lg font-medium mb-4">
                    {userSubscription.plan.displayName} Plan
                  </h3>
                  <div className="space-y-3">
                    <div className="flex items-start">
                      <CreditCard className="h-5 w-5 mr-2 text-gray-500 mt-0.5" />
                      <div>
                        <p className="font-medium">Billing Details</p>
                        <p className="text-sm text-gray-600">
                          ${userSubscription.plan.price}/
                          {userSubscription.plan.interval}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-start">
                      <Calendar className="h-5 w-5 mr-2 text-gray-500 mt-0.5" />
                      <div>
                        <p className="font-medium">Current Period</p>
                        <p className="text-sm text-gray-600">
                          Renews on:{" "}
                          {formatDate(userSubscription.currentPeriodEnd)}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-start">
                      <div
                        className={`h-3 w-3 rounded-full mr-2 mt-1.5 ${
                          userSubscription.status === "ACTIVE"
                            ? "bg-green-500"
                            : userSubscription.status === "CANCELED"
                            ? "bg-amber-500"
                            : "bg-gray-500"
                        }`}
                      />
                      <div>
                        <p className="font-medium">Status</p>
                        <p className="text-sm text-gray-600">
                          {userSubscription.status === "ACTIVE"
                            ? "Active"
                            : userSubscription.status === "CANCELED"
                            ? "Cancelled (Access until end of billing period)"
                            : userSubscription.status}
                        </p>
                      </div>
                    </div>
                  </div>

                  {userSubscription.status === "ACTIVE" && (
                    <div className="mt-6 space-x-3">
                      {!!userSubscription?.endDate &&
                      new Date(userSubscription.endDate) > new Date() ? (
                        <Button
                          outline={true}
                          variant={"outline"}
                          className="border-red-300 text-red-600 hover:bg-red-50"
                          disabled={true}
                        >
                          Already Cancelled
                        </Button>
                      ) : (
                        <Button
                          variant="outline"
                          className="border-red-300 text-red-600 hover:bg-red-50"
                          onClick={handleOpenCancellationModal}
                        >
                          Cancel Subscription
                        </Button>
                      )}

                      <Button
                        disabled={
                          (!!userSubscription?.endDate &&
                            new Date(userSubscription.endDate) > new Date()) ||
                          userSubscription.status !== "ACTIVE"
                        }
                        variant="outline"
                        onClick={handleOpenRefundModal}
                      >
                        Request Refund
                      </Button>
                    </div>
                  )}
                </div>

                <div className="border-t md:border-t-0 md:border-l pt-4 md:pt-0 md:pl-6 mt-4 md:mt-0">
                  <h3 className="text-lg font-medium mb-4">Plan Features</h3>
                  <ul className="space-y-2">
                    {Object.entries(userSubscription.plan.features)
                      .filter(
                        ([key]) => typeof key === "string" && !key.includes("_")
                      )
                      .map(([key, value]) => (
                        <li key={key} className="flex items-start">
                          <div className="h-5 w-5 rounded-full bg-primary/10 flex items-center justify-center mr-2 mt-0.5">
                            <div className="h-2 w-2 rounded-full bg-primary" />
                          </div>
                          <div>
                            <span className="capitalize">
                              {key.replace(/([A-Z])/g, " $1").trim()}:{" "}
                            </span>
                            <span className="font-medium">
                              {SubscriptionService.getFeatureDisplayValue(
                                key,
                                value
                              )}
                            </span>
                          </div>
                        </li>
                      ))}
                  </ul>
                </div>
              </div>
            </div>
          </div>

          {/* Scheduled Changes */}
          <ScheduledChangesManager onUpdate={handleRefresh} />

          {/* Available Plans for Upgrade/Downgrade */}
          {userSubscription.status === "ACTIVE" && (
            <div className="bg-white border rounded-lg shadow-sm overflow-hidden mt-6">
              <div className="bg-primary/10 px-6 py-4 border-b">
                <h2 className="text-xl font-semibold text-primary">
                  Change Subscription Plan
                </h2>
              </div>
              <div className="p-6 bg-primary/90">
                {error.plans ? (
                  <div className="bg-red-50 border border-red-200 text-red-700 p-4 rounded-md">
                    {error.plans}
                  </div>
                ) : (
                  <>
                    <p className="text-gray-600 mb-6">
                      You can upgrade your plan at any time (takes effect
                      immediately) or downgrade your plan (takes effect at the
                      end of your billing period).
                    </p>
                    <div className="grid gap-4 md:grid-cols-3">
                      {subscriptionPlans
                        .filter(
                          (plan) =>
                            plan.interval === userSubscription.plan.interval
                        ) // Only show plans with same interval
                        .sort((a, b) => a.price - b.price)
                        .map((plan) => (
                          <div
                            key={plan.id}
                            className={`border rounded-lg p-4 ${
                              plan.id === userSubscription.planId
                                ? "border-primary/50 bg-primary/5"
                                : "hover:border-gray-300"
                            }`}
                          >
                            <div className="flex justify-between items-start mb-3">
                              <h3 className="font-medium text-lg">
                                {plan.displayName}
                              </h3>
                              {plan.id === userSubscription.planId && (
                                <span className="bg-primary/20 text-white text-xs px-2 py-1 rounded-full">
                                  Current
                                </span>
                              )}
                            </div>
                            <p className="text-2xl font-bold mb-2">
                              ${plan.price}
                              <span className="text-sm font-normal text-gray-500">
                                /{plan.interval}
                              </span>
                            </p>
                            <ul className="mb-4 space-y-2 text-sm">
                              {plan.featureHighlights
                                .slice(0, 3)
                                .map((highlight, idx) => (
                                  <li key={idx} className="flex items-start">
                                    <div className="h-5 w-5 rounded-full bg-primary/10 flex items-center justify-center mr-2 mt-0.5">
                                      <div className="h-2 w-2 rounded-full bg-primary" />
                                    </div>
                                    <span>{highlight.title}</span>
                                  </li>
                                ))}
                            </ul>
                            <SubscriptionPlanChange
                              currentSubscription={userSubscription}
                              targetPlan={plan}
                              onSuccess={handleRefresh}
                              onCancel={() => {}}
                            />
                          </div>
                        ))}
                    </div>
                  </>
                )}
              </div>
            </div>
          )}

          {/* Modals */}
          <SubscriptionCancellationModal
            isOpen={isCancellationModalOpen}
            onClose={() => setIsCancellationModalOpen(false)}
            onSuccess={handleCancellationSuccess}
            subscription={userSubscription}
            onRefundRequest={handleOpenRefundModal}
          />
          <RefundRequestModal
            isOpen={isRefundModalOpen}
            onClose={() => setIsRefundModalOpen(false)}
            onSuccess={handleRefresh}
            subscriptionId={userSubscription.id}
            subscriptionName={userSubscription.plan.displayName}
          />
        </>
      )}
    </div>
  );
};

export default SubscriptionManagement;
