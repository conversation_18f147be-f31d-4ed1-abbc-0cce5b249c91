import React, { useState, useEffect } from "react";
import { Search, Filter, Download } from "lucide-react";
import ShadowButton from "@/components/ui/shadowButton";
import DepositModal from "@/components/Accounts/DepositModal";
import TransactionDetailsModal from "@/components/Accounts/TransactionDetailsModal";
import PaymentService from "@/services/payment.service";
import type { Transaction as ApiTransaction } from "@/services/payment.service";
import { toast } from "sonner";
import { useApp } from "@/contexts/useApp";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Link } from "react-router-dom";

// Define the Transaction interface for UI
interface Transaction {
  id: string;
  name: string;
  date: string;
  time: string;
  amount: string;
  avatar: string;
  status: string;
  paymentType: string;
  transactionId: string;
  invoicePdf?: string;
  hostedInvoiceUrl?: string;
  metadata?: {
    planName?: string;
    planInterval?: string;
    creditAmount?: number;
    packageName?: string;
    refundReason?: string;
  };
}

// Define the type for grouped transactions
type GroupedTransactions = {
  [key: string]: Transaction[];
};

const TransactionHistoryPage: React.FC = () => {
  const { user } = useApp();
  const [searchTerm, setSearchTerm] = useState<string>("");
  const [isShowDepositModal, setIsShowDepositModal] = useState<boolean>(false);
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string>("");
  const [selectedTransaction, setSelectedTransaction] =
    useState<Transaction | null>(null);
  const [isTransactionModalOpen, setIsTransactionModalOpen] =
    useState<boolean>(false);

  // Fetch transaction data
  useEffect(() => {
    const fetchTransactions = async () => {
      try {
        setLoading(true);
        const data = await PaymentService.getUserTransactions();

        // Transform API data to our UI format
        const formattedTransactions = data.map(
          (transaction: ApiTransaction) => {
            const date = new Date(transaction.createdAt);
            const formattedDate = date.toLocaleDateString();
            const formattedTime = date.toLocaleTimeString([], {
              hour: "2-digit",
              minute: "2-digit",
            });

            // Create initials from user name or use default
            const userName =
              user?.profile?.firstName + " " + user?.profile?.lastName ||
              "User";
            const nameParts = userName.split(" ");
            const initials =
              nameParts.length > 1
                ? `${nameParts[0][0]}${nameParts[1][0]}`
                : nameParts[0].substring(0, 2);

            return {
              id: transaction.id,
              name: userName,
              date: formattedDate,
              time: formattedTime,
              amount: `${transaction.amount} ${transaction.currency}`,
              avatar: user?.profile?.avatarUrl || initials.toUpperCase(),
              status: transaction.status,
              paymentType: transaction.paymentType,
              transactionId: transaction.transactionId,
              invoicePdf: transaction.invoicePdf,
              hostedInvoiceUrl: transaction.hostedInvoiceUrl,
              metadata: transaction.metadata,
            };
          }
        );

        setTransactions(formattedTransactions);
      } catch (err) {
        console.error("Error fetching transactions:", err);
        setError("Failed to load transaction history");
        toast.error("Failed to load transaction history");
      } finally {
        setLoading(false);
      }
    };

    fetchTransactions();
  }, [user]);

  // Filter transactions based on search term
  const filteredTransactions = transactions.filter(
    (transaction) =>
      transaction.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      transaction.amount.toLowerCase().includes(searchTerm.toLowerCase()) ||
      transaction.transactionId.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Helper functions for transaction status display
  const formatStatus = (status: string): string => {
    const statusMap: Record<string, string> = {
      succeeded: "Completed",
      processing: "Processing",
      pending: "Pending",
      failed: "Failed",
      refunded: "Refunded",
      canceled: "Canceled",
    };
    return statusMap[status.toLowerCase()] || status;
  };

  const getStatusColor = (status: string): string => {
    const statusColorMap: Record<string, string> = {
      succeeded: "bg-green-500/20 text-green-400",
      processing: "bg-blue-500/20 text-blue-400",
      pending: "bg-yellow-500/20 text-yellow-400",
      failed: "bg-red-500/20 text-red-400",
      refunded: "bg-purple-500/20 text-purple-400",
      canceled: "bg-gray-500/20 text-gray-400",
    };
    return (
      statusColorMap[status.toLowerCase()] || "bg-gray-500/20 text-gray-400"
    );
  };

  // Group transactions by date
  const groupedTransactions: GroupedTransactions = filteredTransactions.reduce(
    (acc: GroupedTransactions, transaction: Transaction) => {
      // Format date for grouping (e.g., "June 3, 2025")
      const date = new Date(transaction.date);
      const dateKey = date.toLocaleDateString("en-US", {
        month: "long",
        day: "numeric",
        year: "numeric",
      });

      if (!acc[dateKey]) {
        acc[dateKey] = [];
      }
      acc[dateKey].push(transaction);
      return acc;
    },
    {}
  );

  // const handleDownloadInvoice = (
  //   invoiceUrl?: string,
  //   transactionId?: string
  // ): void => {
  //   if (invoiceUrl) {
  //     // Fetch the image as a blob
  //     fetch(invoiceUrl)
  //       .then((response) => response.blob())
  //       .then((blob) => {
  //         // Create a temporary link to download the blob
  //         const link = document.createElement("a");
  //         const url = URL.createObjectURL(blob);
  //         link.href = url;
  //         link.download = `invoice-${transactionId}.pdf`;
  //         document.body.appendChild(link);
  //         link.click();
  //         document.body.removeChild(link);
  //         URL.revokeObjectURL(url); // Clean up the object URL
  //       })
  //       .catch((error) => {
  //         console.error("Error fetching image:", error);
  //         alert("Failed to download the image.");
  //       });
  //   } else {
  //     console.log("No generated image to download");
  //     alert("No generated image available to download.");
  //   }
  // };

  // const handleDownloadInvoice = async (transaction: Transaction) => {
  //   if (!transaction.invoicePdf && !transaction.hostedInvoiceUrl) {
  //     toast.error("Invoice not available for this transaction");
  //     return;
  //   }

  //   try {
  //     toast.loading("Generating invoice...");
  //     // Call the API to get the invoice download URL
  //     const invoiceUrl = await PaymentService.downloadInvoice(transaction.id);

  //     // Open the invoice in a new tab
  //     window.open(invoiceUrl, "_blank");
  //     toast.dismiss();
  //     toast.success("Invoice downloaded successfully");
  //   } catch (error) {
  //     toast.dismiss();
  //     toast.error("Failed to download invoice");
  //     console.error("Error downloading invoice:", error);
  //   }
  // };

  return (
    <div className="min-h-screen text-white p-3">
      <div className="mx-auto">
        {/* Header */}
        <div className="mb-8 bg-primary rounded-xl py-3 px-4">
          <h1 className="text-2xl font-semibold mb-2">Transactions</h1>
          <p className="text-gray-400 text-sm">
            Take a look over your transaction details.
          </p>
        </div>

        {/* Search and Actions Bar */}
        <div className="flex items-center justify-between mb-8 gap-4">
          {/* Search Input */}
          <div className="relative flex-1 max-w-sm">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <input
              type="text"
              placeholder="Search by name, amount, or transaction ID"
              value={searchTerm}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                setSearchTerm(e.target.value)
              }
              className="w-full bg-white/10 border border-gray-700 rounded-lg py-2 pl-10 pr-4 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
            />
          </div>
        </div>

        {/* Transaction Groups */}
        <div className="space-y-8 relative border border-gray-800 py-2 px-6 rounded-md min-h-[300px]">
          {/* Action Buttons */}
          <div className="flex gap-3 absolute top-2 right-2">
            <button className="flex items-center gap-2 px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white hover:bg-gray-700 transition-colors">
              <Filter className="w-4 h-4" />
              Filter
            </button>
            <ShadowButton
              className="flex items-center gap-2 px-4"
              onClick={() => setIsShowDepositModal(true)}
            >
              <Download className="w-4 h-4" />
              Deposit
            </ShadowButton>
          </div>

          {loading ? (
            <div className="flex justify-center items-center h-64">
              <div className="flex flex-col items-center">
                <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-purple-500 mb-4"></div>
                <p className="text-gray-400">Loading your transactions...</p>
              </div>
            </div>
          ) : error ? (
            <div className="flex justify-center items-center h-64">
              <div className="text-center">
                <p className="text-red-400 mb-2">{error}</p>
                <button
                  onClick={() => window.location.reload()}
                  className="text-purple-500 underline hover:text-purple-300"
                >
                  Try again
                </button>
              </div>
            </div>
          ) : Object.keys(groupedTransactions).length === 0 ? (
            <div className="flex justify-center items-center h-64">
              <div className="text-center">
                <p className="text-gray-400 mb-2">No transactions found</p>
                {searchTerm && (
                  <p className="text-sm text-gray-500">
                    Try adjusting your search or clear the filter
                  </p>
                )}
              </div>
            </div>
          ) : (
            <div className="pt-3">
              {Object.entries(groupedTransactions).map(
                ([dateGroup, transactions]: [string, Transaction[]], i) => (
                  <div key={dateGroup} className={`${i !== 0 ? "mt-3" : ""}`}>
                    {/* Date Header */}
                    <h2 className="text-lg font-medium text-gray-300">
                      {dateGroup}
                    </h2>

                    {/* Transaction List */}
                    <div className="space-y-3 mt-6">
                      {transactions.map((transaction: Transaction) => (
                        <div
                          key={transaction.id}
                          className="flex items-center justify-between p-2 hover:bg-gray-800/40 rounded-md cursor-pointer transition-colors"
                          onClick={() => {
                            setSelectedTransaction(transaction);
                            setIsTransactionModalOpen(true);
                          }}
                        >
                          {/* Left Side - Avatar and Details */}
                          <div className="flex items-center gap-3">
                            {/* Avatar */}
                            {/* <div className="w-12 h-12 font-inter bg-gradient-to-br from-teal-400 to-blue-500 rounded-full flex items-center justify-center text-white font-medium text-sm">
                              {transaction.avatar}
                            </div> */}
                            <Avatar>
                              <AvatarImage
                                src={`${import.meta.env.VITE_UPLOAD_URL}${
                                  transaction.avatar
                                }`}
                                alt="@shadcn"
                              />
                              <AvatarFallback>
                                {transaction.avatar}
                              </AvatarFallback>
                            </Avatar>

                            {/* Name and Date */}
                            <div>
                              <div className="font-medium text-white font-inter">
                                {transaction.name}
                              </div>
                              <div className="text-sm text-gray-400 font-inter">
                                {transaction.date} - {transaction.time}
                              </div>
                              <div className="text-xs text-gray-500 font-inter mt-1">
                                ID: {transaction.transactionId.substring(0, 12)}
                                ...
                              </div>
                            </div>
                          </div>

                          {/* Right Side - Amount and Status */}
                          <div className="flex items-center gap-20">
                            <div className="text-white font-medium">
                              {transaction.amount}
                            </div>
                            <div className="flex flex-col items-end">
                              <span
                                className={`text-xs px-2 py-1 rounded-full ${getStatusColor(
                                  transaction.status
                                )}`}
                              >
                                {formatStatus(transaction.status)}
                              </span>
                              <Link
                                target="_blank"
                                to={`${transaction.invoicePdf}`}
                              >
                                <button
                                  className="text-purple-500 underline hover:text-purple-300 transition-colors text-sm font-medium mt-1 cursor-pointer"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                  }}
                                >
                                  Get Invoice
                                </button>
                              </Link>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )
              )}
            </div>
          )}
        </div>
      </div>
      <DepositModal
        isOpen={isShowDepositModal}
        onClose={() => setIsShowDepositModal(false)}
      />
      <TransactionDetailsModal
        isOpen={isTransactionModalOpen}
        onClose={() => setIsTransactionModalOpen(false)}
        transaction={selectedTransaction}
      />
    </div>
  );
};

export default TransactionHistoryPage;
