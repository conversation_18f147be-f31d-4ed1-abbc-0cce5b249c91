import Section from "@/components/layout/Section";

import { Text } from "@/components/ui/text";

const Hero = () => {
  return (
    <Section className="py-[106px] z-[999]">
      <div className="flex gap-16 justify-end items-center ">
        {/* <div className="flex-1">
          <img loading="lazy" className="max-w-[250px]" src="/png/about_logo.png" />
        </div> */}

        <div className="flex flex-col gap-6 justify-end items-end max-w-[860px]">
          <Text
            font="Inter"
            variant={"page_title"}
            className="bg-clip-text text-right text-transparent bg-gradient-to-l from-white via-white to-gray-500"
          >
            MiragicAI Resources
          </Text>
          <Text font="Inter" variant={"sub_title"} className="text-right">
            Explore how MiragicAI empowers customers to cut production costs,
            save time and boost engagement and accessibility Generative AI
          </Text>
        </div>
      </div>
    </Section>
  );
};

export default Hero;
