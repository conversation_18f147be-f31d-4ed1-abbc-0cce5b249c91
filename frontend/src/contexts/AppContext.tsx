import React, {
  createContext,
  useEffect,
  useState,
  type ReactNode,
} from "react";
import type { User, AuthResponse } from "@/services/auth.service";
import {
  checkAuth,
  login,
  logout,
  register,
  updateUser,
  refreshUserData,
} from "./AppContextHelpers";
import { type Dispatch, type SetStateAction } from "react";
import type { CreditBalance } from "@/services/credit.service";

// Context types are now imported from auth.service.ts

interface AppContextType {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (email: string, password: string) => Promise<AuthResponse>;
  logout: () => Promise<boolean>;
  register: (
    firstName: string,
    lastName: string,
    email: string,
    password: string
  ) => Promise<AuthResponse>;
  setUser: Dispatch<SetStateAction<User | null>>;
  updateUser: (userData: Partial<User>) => Promise<User | null>;
  refreshUserData: () => Promise<User>;
  userCredits: CreditBalance | null;
  setUserCredits: Dispatch<SetStateAction<CreditBalance | null>>;
}

// Create the context with default values
const AppContext = createContext<AppContextType>({
  user: null,
  isAuthenticated: false,
  isLoading: true,
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  login: async (_email: string, _password: string) => {
    throw new Error("Not implemented");
  },
  logout: async () => {
    throw new Error("Not implemented");
    return false;
  },
  register: async () =>
    // _firstName: string,
    // _lastName: string,
    // _email: string,
    // _password: string
    {
      throw new Error("Not implemented");
    },
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  updateUser: async (_userData: Partial<User>) => {
    throw new Error("Not implemented");
  },
  setUser: () => {
    throw new Error("Not implemented");
  },
  refreshUserData: async () => {
    throw new Error("Not implemented");
  },
  userCredits: null,
  setUserCredits: () => {},
});

interface AppProviderProps {
  children: ReactNode;
}

// API calls are now handled by AuthService

export const AppProvider: React.FC<AppProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [userCredits, setUserCredits] = useState<CreditBalance | null>(null);

  // Check for existing session on mount
  useEffect(() => {
    checkAuth(setUser, setIsLoading);
  }, []);

  // Create wrapped versions of the helper functions that include the state setters
  const handleLogin = async (email: string, password: string) => {
    return login(email, password, setUser, setIsLoading);
  };

  const handleLogout = async () => {
    const success = await logout(setUser);
    if (success) {
      window.location.href = "/";
    }
    return success;
  };

  const handleRegister = async (
    firstName: string,
    lastName: string,
    email: string,
    password: string
  ) => {
    return register(firstName, lastName, email, password, setIsLoading);
  };

  const handleUpdateUser = async (userData: Partial<User>) => {
    return updateUser(user, userData, setUser, setIsLoading);
  };

  const handleRefreshUserData = async () => {
    return refreshUserData(setUser, setIsLoading);
  };

  return (
    <AppContext.Provider
      value={{
        user,
        isAuthenticated: !!user,
        isLoading,
        login: handleLogin,
        logout: handleLogout,
        register: handleRegister,
        updateUser: handleUpdateUser,
        refreshUserData: handleRefreshUserData,
        userCredits,
        setUser,
        setUserCredits, // Now matches the correct type
      }}
    >
      {children}
    </AppContext.Provider>
  );
};

// Export the context for use in the hook file
export { AppContext };
