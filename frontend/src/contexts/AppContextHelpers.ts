import { toast } from "sonner";
import AuthService from "@/services/auth.service";
import type { User, AuthResponse } from "@/services/auth.service";
import Cookies from "js-cookie";

// These are pure functions that don't use React hooks
// They will be used by the actual hook in AppContext.tsx

export const checkAuth = async (
  setUser: (user: User | null) => void,
  setIsLoading: (loading: boolean) => void
): Promise<User | null> => {
  try {
    // Check if there's a token in localStorage
    const token = localStorage.getItem("token");

    if (token) {
      // Validate the token with the backend
      const userData = await AuthService.getCurrentUser();
      setUser(userData);
      return userData;
    }
    return null;
  } catch (error) {
    console.error("Authentication error:", error);
    localStorage.removeItem("token");
    localStorage.removeItem("user");
    Cookies.remove("refreshToken");
    return null;
  } finally {
    setIsLoading(false);
  }
};

export const login = async (
  email: string,
  password: string,
  setUser: (user: User | null) => void,
  setIsLoading: (loading: boolean) => void
): Promise<AuthResponse> => {
  try {
    setIsLoading(true);
    const response = await AuthService.login({ email, password });
    // Save user data and token
    setUser(response.user);
    localStorage.setItem("user", JSON.stringify(response.user));
    localStorage.setItem("token", response.tokens.accessToken);
    Cookies.set("refreshToken", response.tokens.refreshToken, {
      expires: 7,
      path: "/",
      secure: true,
      sameSite: "strict",
      // httpOnly: true,
    });
    toast.success("Logged in successfully");
    return response;
  } catch (error) {
    toast.error("Login failed. Please check your credentials.");
    throw error;
  } finally {
    setIsLoading(false);
  }
};

// export const logout = async (
//   setUser: (user: User | null) => void
// ): Promise<boolean> => {
//   try {
//     const refreshToken = Cookies.get("refreshToken");
//     await AuthService.logout(refreshToken || "");
//     setUser(null);
//     localStorage.removeItem("token");
//     localStorage.removeItem("user");
//     Cookies.remove("refreshToken");
//     toast.success("Logged out successfully");
//     return true;
//   } catch (error) {
//     console.error("Logout error:", error);
//     setUser(null);
//     localStorage.removeItem("token");
//     localStorage.removeItem("user");
//     Cookies.remove("refreshToken");
//     return true; // Still consider it successful for redirect purposes
//   }
// };

export const logout = async (
  setUser: (user: User | null) => void
): Promise<boolean> => {
  try {
    // Get refresh token from cookies
    const refreshToken = Cookies.get("refreshToken");

    // Send refresh token to logout API
    await AuthService.logout(refreshToken || "");
    setUser(null);
    localStorage.removeItem("token");
    localStorage.removeItem("user");
    Cookies.remove("refreshToken");
    toast.success("Logged out successfully");
    return true;
  } catch (error) {
    console.error("Logout error:", error);
    // Still clear local data even if the API call fails
    setUser(null);
    localStorage.removeItem("token");
    localStorage.removeItem("user");
    return false;
  }
};

export const register = async (
  firstName: string,
  lastName: string,
  email: string,
  password: string,
  // setUser: (user: User | null) => void,
  setIsLoading: (loading: boolean) => void
): Promise<AuthResponse> => {
  try {
    setIsLoading(true);
    const response = await AuthService.register({
      firstName,
      lastName,
      email,
      password,
    });

    // Save user data and token
    // setUser(response.user);
    // localStorage.setItem("user", JSON.stringify(response.user));
    // localStorage.setItem("token", response.tokens.accessToken);
    // Cookies.set("refreshToken", response.tokens.refreshToken, {
    //   expires: 7,
    //   path: "/",
    //   secure: true,
    //   sameSite: "strict",
    // });
    toast.success("Registered successfully");
    return response;
  } catch (error) {
    toast.error("Registration failed. Please try again.");
    throw error;
  } finally {
    setIsLoading(false);
  }
};

export const updateUser = async (
  currentUser: User | null,
  userData: Partial<User>,
  setUser: (user: User | null) => void,
  setIsLoading: (loading: boolean) => void
): Promise<User | null> => {
  try {
    if (currentUser) {
      setIsLoading(true);
      const updatedUser = await AuthService.updateProfile(userData);
      setUser(updatedUser);
      localStorage.setItem("user", JSON.stringify(updatedUser));
      toast.success("Profile updated successfully");
      return updatedUser;
    }
    return null;
  } catch (error) {
    console.error("Profile update error:", error);
    toast.error("Failed to update profile");
    throw error;
  } finally {
    setIsLoading(false);
  }
};

export const refreshUserData = async (
  setUser: (user: User | null) => void,
  setIsLoading: (loading: boolean) => void
) => {
  try {
    setIsLoading(true);
    const userData = await AuthService.getCurrentUser();
    setUser(userData);
    localStorage.setItem("user", JSON.stringify(userData));
    return userData;
  } catch (error) {
    console.error("Error refreshing user data:", error);
    toast.error("Failed to refresh user data");
    throw error;
  } finally {
    setIsLoading(false);
  }
};
