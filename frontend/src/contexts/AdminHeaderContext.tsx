import { createContext, useContext, useState, useCallback, type ReactNode } from "react";

interface AdminHeaderContextType {
  setHeaderContent: (config: {
    title?: string;
    description?: string;
    content?: ReactNode;
  }) => void;
}

const AdminHeaderContext = createContext<AdminHeaderContextType | undefined>(
  undefined
);

export const HeaderConfigContext = createContext<{
  title?: string;
  description?: string;
  content?: ReactNode;
}>({});

export const HeaderConfigProvider = ({ children }: { children: ReactNode }) => {
  const [headerConfig, setHeaderConfig] = useState<{
    title?: string;
    description?: string;
    content?: ReactNode;
  }>({});

  const setHeaderContent = useCallback((config: {
    title?: string;
    description?: string;
    content?: ReactNode;
  }) => {
    setHeaderConfig(config);
  }, []);

  return (
    <HeaderConfigContext.Provider value={headerConfig}>
      <AdminHeaderContext.Provider value={{ setHeaderContent }}>
        {children}
      </AdminHeaderContext.Provider>
    </HeaderConfigContext.Provider>
  );
};

export const useAdminHeader = () => {
  const context = useContext(AdminHeaderContext);
  if (!context) {
    throw new Error(
      "useAdminHeader must be used within an AdminHeaderProvider"
    );
  }
  return context;
};
