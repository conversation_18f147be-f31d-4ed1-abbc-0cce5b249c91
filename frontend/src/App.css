.no-break {
  white-space: nowrap;
}

.swiper-button-prev::after,
.swiper-button-next::after {
  display: none;
}

.before-after-slider__first-photo-container{
  background-position: center;
  background-size: cover;
  background-repeat: no-repeat;
  background-image: url('/png/transparent_bg.png');
}

.review-swipper .swiper-wrapper {
  gap: 5rem;
}



/* Scrollbar */

/* Global scrollbar styling */
::-webkit-scrollbar {
  width: 8px; /* Width of the scrollbar */
}

::-webkit-scrollbar-track {
  background: #1a1a1a; /* Background of the track, matching your app's dark theme */
}

::-webkit-scrollbar-thumb {
  background: #4b5563; /* Color of the scrollbar thumb (gray-600) */
  border-radius: 4px; /* Rounded corners for the thumb */
}

::-webkit-scrollbar-thumb:hover {
  background: #6b7280; /* Lighter gray (gray-500) on hover */
}

/* Firefox scrollbar styling */
* {
  scrollbar-width: thin; /* Makes the scrollbar thinner */
  scrollbar-color: #4b5563 #1a1a1a; /* thumb color, track color */
}

/* Scrollbar End */


/* .before-after-slider__first-photo-container img {
  object-fit: cover;
}

.before-after-slider__second-photo-container img {
  object-fit: cover;
}  */

@keyframes gradientAnimation {
  0% {
    background-position: 0% 50%;
  }
  25% {
    background-position: 50% 100%;
  }
  50% {
    background-position: 100% 50%;
  }
  75% {
    background-position: 50% 0%;
  }
  100% {
    background-position: 0% 50%;
  }
}

@keyframes shineAnimation {
  0% {
    left: -100px;
  }
  50% {
    left: 100%;
  }
  to {
    left: -100%;
  }
}