export interface UserSidebarProps {
  activeItem: string;
  setActiveItem: (item: string) => void;
  isMobileMenuOpen: boolean;
  setIsMobileMenuOpen: (isOpen: boolean) => void;
  isExpanded?: boolean;
  isMobile?: boolean;
  toggleSidebar: () => void;
}

export interface NavItem {
  name: string;
  href: string;
  icon: React.ComponentType<{ color: string }>;
}

export interface NavSectionProps {
  title: string;
  items: NavItem[];
  activeItem: string;
  isExpanded: boolean;
  isMobile: boolean;
  onItemClick: (name: string, href: string) => void;
}