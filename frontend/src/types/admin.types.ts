// Unified type definitions for admin panel

export interface UserProfile {
  firstName: string;
  lastName: string;
  fullName: string;
  avatarUrl: string;
}

export interface UserCredit {
  balance: number;
  spent: number;
}

export interface UserSubscription {
  id: string;
  planId: string;
  status: string;
  startDate: string;
  endDate?: string;
  currentPeriodEnd: string;
  plan: {
    id: string;
    name: string;
    displayName: string;
    price: number;
    currency: string;
    interval: string;
    creditsAmount: number;
  };
}

export interface User {
  id: string;
  email: string;
  role: "USER" | "ADMIN";
  emailVerified: boolean;
  profile?: UserProfile;
  name: string;
  subscription?: UserSubscription | null;
  plan: string;
  credit: UserCredit;
  status: "active" | "inactive";
  createdAt: string;
  lastLogin?: string | null;
  joinDate: string;
  lastActive?: string | null;
  totalSpent: number;
  // Legacy fields for backward compatibility
  credits?: number;
  avatar?: string;
}

export interface UserListParams {
  page?: string;
  limit?: string;
  search?: string;
  status?: "active" | "inactive" | "all";
  role?: "USER" | "ADMIN" | "all";
  sortBy?: "createdAt" | "lastLogin" | "email" | "name";
  sortOrder?: "asc" | "desc";
}

export interface Pagination {
  total: number;
  page: number;
  limit: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPrevPage: boolean;
}

export interface UserListResponse {
  users: User[];
  pagination: Pagination;
  filters: {
    search?: string;
    status: string;
    role: string;
    sortBy: string;
    sortOrder: string;
  };
}

export interface UserUpdateData {
  role?: "USER" | "ADMIN";
  emailVerified?: boolean;
  profile?: {
    firstName?: string;
    lastName?: string;
    avatarUrl?: string;
  };
  credit?: {
    balance?: number;
  };
}

// Credit Package types
export interface CreditPackage {
  id: string;
  name: string;
  description: string | null;
  creditsAmount: number;
  price: number;
  currency: string;
  stripePriceId?: string;
  paypalPlanId?: string;
  isActive: boolean;
  createdAt?: string;
  updatedAt?: string;
}

// Subscription Plan types
export interface SubscriptionPlan {
  id: string;
  name: string;
  displayName: string;
  description?: string;
  price: number;
  currency: string;
  interval: string;
  creditsAmount: number;
  features: Record<string, boolean | number | string>;
  featureHighlights: string[];
  colorScheme?: string;
  isFeatured: boolean;
  sortOrder: number;
  stripePriceId?: string;
  paypalPlanId?: string;
  isActive: boolean;
  createdAt?: string;
  updatedAt?: string;
}

// Dashboard stats types
export interface AdminStats {
  totalUsers: number;
  activeUsers: number;
  totalRevenue: number;
  monthlyRevenue?: number;
  totalJobs: number;
  recentSignups: number;
  activeSubscriptions: number;
  totalCredits?: number;
  creditsUsed?: number;
  videoGenerations?: number;
  imageGenerations?: number;
  backgroundRemovals?: number;
  activeJobs?: number;
  completedJobs?: number;
  blogPosts?: number;
  conversionRate?: number;
  backgroundRemoval?: number;
  imageGeneration?: number;
}

export interface RecentUser {
  id: string;
  name: string;
  email: string;
  role: string;
  plan: string;
  joinDate: string;
  status: string;
}

// Blog types
export interface BlogPost {
  id: string;
  title: string;
  slug: string;
  content: string;
  excerpt?: string;
  featuredImageUrl?: string;
  status: "DRAFT" | "PUBLISHED" | "SCHEDULED";
  publishedAt?: string;
  createdAt: string;
  updatedAt: string;
  author: {
    id: string;
    email: string;
    profile?: {
      firstName?: string;
      lastName?: string;
    };
  };
  categories: Array<{
    category: {
      id: string;
      name: string;
      slug: string;
    };
  }>;
  tags: Array<{
    tag: {
      id: string;
      name: string;
    };
  }>;
}

export interface BlogCategory {
  id: string;
  name: string;
  slug: string;
  description?: string;
  createdAt: string;
  updatedAt: string;
}

export interface BlogTag {
  id: string;
  name: string;
  createdAt: string;
  updatedAt: string;
}

// API Response wrapper
export interface ApiResponse<T> {
  success: boolean;
  message?: string;
  data: T;
  error?: {
    code: string;
    message: string;
    details?: Record<string, unknown>;
  };
}

// Form validation types
export interface FormErrors {
  [key: string]: string | undefined;
}

export interface LoadingState {
  [key: string]: boolean;
}

// Dialog/Modal state types
export interface DialogState<T = unknown> {
  open: boolean;
  mode: "view" | "edit" | "create" | "delete";
  data: T | null;
}

// Filter and sort types
export type SortDirection = "asc" | "desc";
export type UserStatus = "active" | "inactive" | "all";
export type UserRole = "USER" | "ADMIN" | "all";

// Table column types
export interface TableColumn {
  key: string;
  label: string;
  sortable?: boolean;
  width?: string;
  align?: "left" | "center" | "right";
}

// Bulk operation types
export interface BulkOperation {
  type: "delete" | "activate" | "deactivate" | "changeRole";
  userIds: string[];
  data?: Record<string, unknown>;
}
