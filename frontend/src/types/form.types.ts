/**
 * Common form types for the application
 */
import * as z from 'zod';

// Subscription plan schema
export const subscriptionPlanSchema = z.object({
  name: z.string().min(2, "Name must be at least 2 characters").max(50),
  displayName: z.string().min(2, "Display name must be at least 2 characters").max(100),
  description: z.string().optional().nullable(),
  price: z.coerce.number().positive("Price must be positive"),
  currency: z.enum(["USD", "EUR", "GBP"]),
  interval: z.enum(["month", "year"]),
  features: z.object({
    videoGenerationQuota: z.coerce.number().int().min(0),
    imageGenerationQuota: z.coerce.number().int().min(0),
    backgroundRemovalQuota: z.coerce.number().int().min(0),
  }),
  creditsAmount: z.coerce.number().int().min(0),
  featureHighlights: z.array(z.string()).default([]),
  stripePriceId: z.string().optional().nullable(),
  paypalPlanId: z.string().optional().nullable(),
  isActive: z.boolean(),
});

// Infer the type from the schema
export type SubscriptionPlanFormValues = z.infer<typeof subscriptionPlanSchema>;
