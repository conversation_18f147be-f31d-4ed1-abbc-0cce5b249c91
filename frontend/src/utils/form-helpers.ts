/**
 * Form helper utilities to resolve TypeScript issues with react-hook-form
 */
import type { ZodSchema } from 'zod';
import type { 
  <PERSON><PERSON><PERSON><PERSON>, 
  SubmitErrorHandler,
  UseFormHandleSubmit
} from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';

/**
 * Type-safe wrapper for zodResolver to avoid type conflicts
 * @param schema Zod schema
 * @returns A resolver that works with react-hook-form
 */
/**
 * This is a workaround for the type conflicts between different versions of react-hook-form types.
 * The issue is that there are multiple versions of the same types in the project,
 * and they are not compatible with each other.
 */
// eslint-disable-next-line @typescript-eslint/no-explicit-any
type ResolverFunction = (data: Record<string, any>, context: object, options: object) => Promise<object>;

export function createZodResolver<T extends FieldValues>(schema: ZodSchema<T>) {
  // Using type assertion to bypass the type conflicts between different versions of the same types
  return zodResolver(schema) as unknown as ResolverFunction;
}

/**
 * Type-safe wrapper for form.handleSubmit to avoid type conflicts
 * @param handleSubmit The handleSubmit function from useForm
 * @param onValid The callback for valid form data
 * @param onInvalid Optional callback for invalid form data
 * @returns A function that can be used as the onSubmit handler for a form
 */
export function createSubmitHandler<T extends FieldValues>(
  handleSubmit: UseFormHandleSubmit<T>,
  onValid: (data: T) => void | Promise<void>,
  onInvalid?: SubmitErrorHandler<T>
) {
  return handleSubmit(
    (data) => onValid(data as T),
    onInvalid
  );
}
