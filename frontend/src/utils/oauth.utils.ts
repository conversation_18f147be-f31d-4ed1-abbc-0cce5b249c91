// OAuth utility functions for frontend

export interface OAuthConfig {
  google: {
    clientId: string;
    redirectUri: string;
  };
  apple: {
    clientId: string;
    redirectUri: string;
  };
}

// OAuth configuration - these should be set via environment variables
export const oauthConfig: OAuthConfig = {
  google: {
    clientId: import.meta.env.VITE_GOOGLE_CLIENT_ID || "",
    redirectUri: `${
      import.meta.env.VITE_API_URL || "http://localhost:5000/api/v1"
    }/auth/google/callback`,
  },
  apple: {
    clientId: import.meta.env.VITE_APPLE_CLIENT_ID || "",
    redirectUri: `${
      import.meta.env.VITE_API_URL || "http://localhost:5000/api/v1"
    }/auth/apple/callback`,
  },
};

// Generate OAuth URLs
export const generateOAuthUrl = (provider: "google" | "apple"): string => {
  const baseUrl =
    import.meta.env.VITE_API_URL || "http://localhost:5000/api/v1";
  return `${baseUrl}/auth/${provider}`;
};

// Handle OAuth redirect
export const handleOAuthRedirect = (): {
  accessToken?: string;
  refreshToken?: string;
  provider?: string;
  error?: string;
} => {
  const urlParams = new URLSearchParams(window.location.search);

  const accessToken = urlParams.get("access_token");
  const refreshToken = urlParams.get("refresh_token");
  const provider = urlParams.get("provider");
  const error = urlParams.get("error") || urlParams.get("message");

  // Clean up URL
  if (accessToken || refreshToken || error) {
    window.history.replaceState({}, document.title, window.location.pathname);
  }

  return {
    accessToken: accessToken || undefined,
    refreshToken: refreshToken || undefined,
    provider: provider || undefined,
    error: error || undefined,
  };
};

// Initiate OAuth login
export const initiateOAuthLogin = (provider: "google" | "apple"): void => {
  const oauthUrl = generateOAuthUrl(provider);
  console.log("oauthUrl", oauthUrl);
  console.log("oauthConfig", oauthConfig);

  // Store current location for redirect after login
  const currentPath = window.location.pathname + window.location.search;
  localStorage.setItem("oauth_redirect_path", currentPath);

  // Redirect to OAuth provider
  window.location.href = oauthUrl;
};

// Get stored redirect path
export const getOAuthRedirectPath = (): string => {
  const path = localStorage.getItem("oauth_redirect_path");
  localStorage.removeItem("oauth_redirect_path");
  return path || "/";
};

// OAuth error messages
export const getOAuthErrorMessage = (error: string): string => {
  const errorMessages: Record<string, string> = {
    oauth_error: "Authentication failed. Please try again.",
    oauth_failed: "OAuth authentication was not completed.",
    callback_error: "There was an error processing your authentication.",
    access_denied: "Access was denied. Please try again.",
    invalid_request: "Invalid authentication request.",
    server_error: "Server error during authentication.",
  };

  return (
    errorMessages[error] ||
    "An unexpected error occurred during authentication."
  );
};

// Check if OAuth is configured
export const isOAuthConfigured = (provider: "google" | "apple"): boolean => {
  return !!oauthConfig[provider].clientId;
};

// Validate OAuth tokens
export const validateOAuthTokens = (
  accessToken?: string,
  refreshToken?: string
): boolean => {
  return !!(accessToken && refreshToken);
};
