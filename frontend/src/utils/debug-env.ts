// Debug utility to check environment variables
export const debugEnv = () => {
  console.log('Environment Variables:');
  console.log('VITE_STRIPE_PUBLISHABLE_KEY:', import.meta.env.VITE_STRIPE_PUBLISHABLE_KEY || 'Not set');
  
  // Check if the key is in the correct format (starts with pk_)
  const stripeKey = import.meta.env.VITE_STRIPE_PUBLISHABLE_KEY;
  if (stripeKey) {
    if (typeof stripeKey !== 'string') {
      console.error('VITE_STRIPE_PUBLISHABLE_KEY is not a string:', typeof stripeKey);
    } else if (!stripeKey?.startsWith('pk_')) {
      console.error('VITE_STRIPE_PUBLISHABLE_KEY does not start with pk_:', stripeKey.substring(0, 5) + '...');
    } else {
      console.log('VITE_STRIPE_PUBLISHABLE_KEY format looks correct (starts with pk_)');
    }
  }
};
