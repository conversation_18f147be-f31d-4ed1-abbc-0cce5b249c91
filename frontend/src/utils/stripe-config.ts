// Stripe configuration utility
// This provides a fallback mechanism when the environment variable is not set

// A properly formatted test key (this is not a real key, just a placeholder with correct format)
const FALLBACK_STRIPE_KEY = 'pk_test_51MzKLTEVOLBDlZCX0000000000000000000000000000';

export const getStripeKey = (): string => {
  const envKey = import.meta.env.VITE_STRIPE_PUBLISHABLE_KEY;
  
  if (!envKey) {
    console.warn('VITE_STRIPE_PUBLISHABLE_KEY not found in environment variables. Using fallback key.');
    return FALLBACK_STRIPE_KEY;
  }
  
  return envKey;
};

// Initialize and export the Stripe key
export const STRIPE_PUBLISHABLE_KEY = getStripeKey();
