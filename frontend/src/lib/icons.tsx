export const AiToolIcon = () => (
  <svg
    width="22"
    height="23"
    viewBox="0 0 22 23"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M18 0.5L16.74 3.25L14 4.5L16.74 5.76L18 8.5L19.25 5.76L22 4.5L19.25 3.25M8 3.5L5.5 9L0 11.5L5.5 14L8 19.5L10.5 14L16 11.5L10.5 9M18 14.5L16.74 17.24L14 18.5L16.74 19.75L18 22.5L19.25 19.75L22 18.5L19.25 17.24"
      fill="#D1C4E7"
    />
  </svg>
);

export const FacebookIcon = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="32"
    height="32"
    viewBox="0 0 32 32"
    fill="none"
  >
    <path
      d="M16 0C7.16342 0 0 7.16342 0 16C0 24.8366 7.16342 32 16 32C24.8366 32 32 24.8366 32 16C32 7.16342 24.8366 0 16 0ZM22.1041 8.14813L19.8911 8.14911C18.1557 8.14911 17.8202 8.97375 17.8202 10.1837V12.8517H21.9584L21.9564 17.0305H17.8205V27.7544H13.5042V17.0305H9.89594V12.8517H13.5042V9.7699C13.5042 6.19343 15.6893 4.24561 18.8795 4.24561L22.1044 4.25052V8.14813H22.1041Z"
      fill="#F5F5F7"
    />
  </svg>
);

export const GenerationAILogo = () => (
  <svg
    width="27"
    height="27"
    viewBox="0 0 35 35"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      opacity="0.5"
      d="M9.375 14.25C9.375 11.9522 9.375 10.8034 10.09 10.09C10.8018 9.375 11.9506 9.375 14.25 9.375H20.75C23.0478 9.375 24.1966 9.375 24.91 10.09C25.625 10.8034 25.625 11.9522 25.625 14.25V20.75C25.625 23.0478 25.625 24.1966 24.91 24.91C24.1966 25.625 23.0478 25.625 20.75 25.625H14.25C11.9522 25.625 10.8034 25.625 10.09 24.91C9.375 24.1982 9.375 23.0494 9.375 20.75V14.25Z"
      stroke="#BEACDB"
      strokeWidth="2"
    />
    <path
      d="M4.5 17.5C4.5 11.3721 4.5 8.30737 6.4045 6.4045C8.309 4.50162 11.3721 4.5 17.5 4.5C23.6279 4.5 26.6926 4.5 28.5955 6.4045C30.4984 8.309 30.5 11.3721 30.5 17.5C30.5 23.6279 30.5 26.6926 28.5955 28.5955C26.691 30.4984 23.6279 30.5 17.5 30.5C11.3721 30.5 8.30737 30.5 6.4045 28.5955C4.50162 26.691 4.5 23.6279 4.5 17.5Z"
      stroke="#BEACDB"
      strokeWidth="2"
    />
    <path
      opacity="0.5"
      d="M4.5 17.5H1.25M33.75 17.5H30.5M4.5 12.625H1.25M33.75 12.625H30.5M4.5 22.375H1.25M33.75 22.375H30.5M17.5 30.5V33.75M17.5 1.25V4.5M12.625 30.5V33.75M12.625 1.25V4.5M22.375 30.5V33.75M22.375 1.25V4.5"
      stroke="#BEACDB"
      strokeWidth="2"
      strokeLinecap="round"
    />
  </svg>
);

export const SeperatorLineIcon = () => (
  <svg
    width="1239"
    height="2"
    viewBox="0 0 1239 2"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <line
      y1="1"
      x2="1239"
      y2="1"
      stroke="url(#paint0_linear_188_3718)"
      strokeWidth="2"
    />
    <defs>
      <linearGradient
        id="paint0_linear_188_3718"
        x1="0"
        y1="2.5"
        x2="1239"
        y2="2.5"
        gradientUnits="userSpaceOnUse"
      >
        <stop stopColor="#2C1250" stopOpacity="0" />
        <stop offset="0.509615" stopColor="#6528B6" />
        <stop offset="0.966346" stopColor="#311358" stopOpacity="0" />
      </linearGradient>
    </defs>
  </svg>
);

export const MyStarIcon = ({ color = "#9855FF" }) => (
  <svg
    width="30"
    height="30"
    viewBox="0 0 38 38"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M11.598 29.9615L13.5613 21.5492L7.0332 15.8936L15.6323 15.1494L19 7.21533L22.3678 15.1478L30.9653 15.892L24.4372 21.5477L26.4021 29.9599L19 25.4949L11.598 29.9615Z"
      fill={color}
    />
  </svg>
);

export const XTwitterIcon = ({ size = "24" }) => (
  <svg
    width={size}
    height={size}
    viewBox="0 0 24 22"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M18.9 0.125H22.5806L14.5406 9.33757L24 21.8759H16.5943L10.7897 14.273L4.15543 21.8759H0.471429L9.07029 12.0187L0 0.126714H7.59429L12.8331 7.07471L18.9 0.125ZM17.6057 19.6679H19.6457L6.48 2.21814H4.29257L17.6057 19.6679Z"
      fill="#868688"
    />
  </svg>
);

export const MusicIcon = () => (
  <svg
    width="19"
    height="23"
    viewBox="0 0 19 23"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M15.0583 4.0325C14.2325 3.08943 13.7773 1.87852 13.7774 0.625H10.0437V15.6083C10.0155 16.4194 9.67336 17.1877 9.08948 17.7513C8.5056 18.3149 7.72562 18.6297 6.91411 18.6292C5.19828 18.6292 3.77244 17.2275 3.77244 15.4875C3.77244 13.4092 5.77828 11.8504 7.84453 12.4908V8.6725C3.67578 8.11667 0.0266113 11.355 0.0266113 15.4875C0.0266113 19.5113 3.36161 22.375 6.90203 22.375C10.6962 22.375 13.7774 19.2938 13.7774 15.4875V7.88708C15.2915 8.9744 17.1093 9.55778 18.9733 9.55458V5.82083C18.9733 5.82083 16.7016 5.92958 15.0583 4.0325Z"
      fill="#868688"
    />
  </svg>
);

export const LinkedInIcon = ({ size = "23" }) => (
  <svg
    width={size}
    height={size}
    viewBox="0 0 23 23"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M19.9583 0.625C20.5993 0.625 21.214 0.879612 21.6672 1.33283C22.1204 1.78604 22.375 2.40073 22.375 3.04167V19.9583C22.375 20.5993 22.1204 21.214 21.6672 21.6672C21.214 22.1204 20.5993 22.375 19.9583 22.375H3.04167C2.40073 22.375 1.78604 22.1204 1.33283 21.6672C0.879612 21.214 0.625 20.5993 0.625 19.9583V3.04167C0.625 2.40073 0.879612 1.78604 1.33283 1.33283C1.78604 0.879612 2.40073 0.625 3.04167 0.625H19.9583ZM19.3542 19.3542V12.95C19.3542 11.9053 18.9391 10.9033 18.2004 10.1646C17.4617 9.42585 16.4597 9.01083 15.415 9.01083C14.3879 9.01083 13.1917 9.63917 12.6117 10.5817V9.24042H9.24042V19.3542H12.6117V13.3971C12.6117 12.4667 13.3608 11.7054 14.2913 11.7054C14.7399 11.7054 15.1702 11.8836 15.4874 12.2009C15.8047 12.5181 15.9829 12.9484 15.9829 13.3971V19.3542H19.3542ZM5.31333 7.34333C5.85172 7.34333 6.36806 7.12946 6.74876 6.74876C7.12946 6.36806 7.34333 5.85172 7.34333 5.31333C7.34333 4.18958 6.43708 3.27125 5.31333 3.27125C4.77174 3.27125 4.25233 3.4864 3.86936 3.86936C3.4864 4.25233 3.27125 4.77174 3.27125 5.31333C3.27125 6.43708 4.18958 7.34333 5.31333 7.34333ZM6.99292 19.3542V9.24042H3.64583V19.3542H6.99292Z"
      fill="#868688"
    />
  </svg>
);

export const InstagramIcon = ({ size = "24" }) => (
  <svg
    width={size}
    height={size}
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M7.09992 0.333008H16.8999C20.6333 0.333008 23.6666 3.36634 23.6666 7.09968V16.8997C23.6666 18.6943 22.9537 20.4154 21.6847 21.6844C20.4157 22.9534 18.6946 23.6663 16.8999 23.6663H7.09992C3.36659 23.6663 0.333252 20.633 0.333252 16.8997V7.09968C0.333252 5.30504 1.04617 3.58391 2.31516 2.31492C3.58416 1.04592 5.30529 0.333008 7.09992 0.333008ZM6.86658 2.66634C5.75268 2.66634 4.68439 3.10884 3.89674 3.89649C3.10908 4.68415 2.66659 5.75243 2.66659 6.86634V17.133C2.66659 19.4547 4.54492 21.333 6.86658 21.333H17.1333C18.2472 21.333 19.3154 20.8905 20.1031 20.1029C20.8908 19.3152 21.3333 18.2469 21.3333 17.133V6.86634C21.3333 4.54467 19.4549 2.66634 17.1333 2.66634H6.86658ZM18.1249 4.41634C18.5117 4.41634 18.8826 4.56999 19.1561 4.84348C19.4296 5.11697 19.5833 5.4879 19.5833 5.87467C19.5833 6.26145 19.4296 6.63238 19.1561 6.90587C18.8826 7.17936 18.5117 7.33301 18.1249 7.33301C17.7381 7.33301 17.3672 7.17936 17.0937 6.90587C16.8202 6.63238 16.6666 6.26145 16.6666 5.87467C16.6666 5.4879 16.8202 5.11697 17.0937 4.84348C17.3672 4.56999 17.7381 4.41634 18.1249 4.41634ZM11.9999 6.16634C13.547 6.16634 15.0307 6.78092 16.1247 7.87489C17.2187 8.96885 17.8333 10.4526 17.8333 11.9997C17.8333 13.5468 17.2187 15.0305 16.1247 16.1245C15.0307 17.2184 13.547 17.833 11.9999 17.833C10.4528 17.833 8.96909 17.2184 7.87513 16.1245C6.78117 15.0305 6.16658 13.5468 6.16658 11.9997C6.16658 10.4526 6.78117 8.96885 7.87513 7.87489C8.96909 6.78092 10.4528 6.16634 11.9999 6.16634ZM11.9999 8.49967C11.0717 8.49967 10.1814 8.86842 9.52504 9.5248C8.86867 10.1812 8.49992 11.0714 8.49992 11.9997C8.49992 12.9279 8.86867 13.8182 9.52504 14.4745C10.1814 15.1309 11.0717 15.4997 11.9999 15.4997C12.9282 15.4997 13.8184 15.1309 14.4748 14.4745C15.1312 13.8182 15.4999 12.9279 15.4999 11.9997C15.4999 11.0714 15.1312 10.1812 14.4748 9.5248C13.8184 8.86842 12.9282 8.49967 11.9999 8.49967Z"
      fill="#868688"
    />
  </svg>
);

export const GoogleIcon = () => (
  <svg
    width="21"
    height="21"
    viewBox="0 0 21 21"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clip-path="url(#clip0_378_1659)">
      <path
        d="M10.7041 8.68182V12.5546H16.1958C15.9546 13.8 15.231 14.8546 14.1456 15.5637L17.4573 18.0819C19.3868 16.3365 20.5 13.7728 20.5 10.7274C20.5 10.0183 20.4351 9.33642 20.3145 8.68193L10.7041 8.68182Z"
        fill="#4285F4"
      />
      <path
        d="M4.98536 12.4034L4.23845 12.9637L1.5946 14.9819C3.27364 18.2455 6.71497 20.5001 10.7038 20.5001C13.4589 20.5001 15.7687 19.6092 17.4571 18.0819L14.1454 15.5637C13.2363 16.1637 12.0767 16.5274 10.7038 16.5274C8.05077 16.5274 5.79664 14.7729 4.98953 12.4092L4.98536 12.4034Z"
        fill="#34A853"
      />
      <path
        d="M1.59467 6.01822C0.898969 7.36363 0.500122 8.88184 0.500122 10.5C0.500122 12.1182 0.898969 13.6364 1.59467 14.9818C1.59467 14.9908 4.98992 12.4 4.98992 12.4C4.78584 11.8 4.66521 11.1636 4.66521 10.4999C4.66521 9.83617 4.78584 9.19984 4.98992 8.59984L1.59467 6.01822Z"
        fill="#FBBC05"
      />
      <path
        d="M10.704 4.48184C12.2068 4.48184 13.5426 4.99092 14.6094 5.97275L17.5315 3.10914C15.7597 1.49098 13.4592 0.5 10.704 0.5C6.71518 0.5 3.27364 2.74546 1.5946 6.01822L4.98975 8.60005C5.79676 6.23638 8.05097 4.48184 10.704 4.48184Z"
        fill="#EA4335"
      />
    </g>
    <defs>
      <clipPath id="clip0_378_1659">
        <rect
          width="20"
          height="20"
          fill="white"
          transform="translate(0.5 0.5)"
        />
      </clipPath>
    </defs>
  </svg>
);

export const AppleIcon = () => (
  <svg
    width="21"
    height="25"
    viewBox="0 0 21 25"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M19.4144 8.6816C19.2752 8.7896 16.8176 10.1744 16.8176 13.2536C16.8176 16.8152 19.9448 18.0752 20.0384 18.1064C20.024 18.1832 19.5416 19.832 18.3896 21.512C17.3624 22.9904 16.2896 24.4664 14.6576 24.4664C13.0256 24.4664 12.6056 23.5184 10.7216 23.5184C8.8856 23.5184 8.2328 24.4976 6.74 24.4976C5.2472 24.4976 4.2056 23.1296 3.008 21.4496C1.6208 19.4768 0.5 16.412 0.5 13.5032C0.5 8.8376 3.5336 6.3632 6.5192 6.3632C8.1056 6.3632 9.428 7.4048 10.424 7.4048C11.372 7.4048 12.8504 6.3008 14.6552 6.3008C15.3392 6.3008 17.7968 6.3632 19.4144 8.6816ZM13.7984 4.3256C14.5448 3.44 15.0728 2.2112 15.0728 0.9824C15.0728 0.812 15.0584 0.6392 15.0272 0.5C13.8128 0.5456 12.368 1.3088 11.4968 2.3192C10.8128 3.0968 10.1744 4.3256 10.1744 5.5712C10.1744 5.7584 10.2056 5.9456 10.22 6.0056C10.2968 6.02 10.4216 6.0368 10.5464 6.0368C11.636 6.0368 13.0064 5.3072 13.7984 4.3256Z"
      fill="white"
    />
  </svg>
);

export const WebinarIconDashboard = () => (
  <svg
    width="39"
    height="39"
    viewBox="0 0 39 39"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clip-path="url(#clip0_79_1295)">
      <path
        d="M19.5 0C30.2705 0 39 8.7295 39 19.5V35.75C39 37.5326 37.5326 39 35.75 39H23.5625L23.5609 38.7254C23.4812 34.0226 21.372 30.7596 17.5338 29.0404L39 19.5L38.48 19.3229C33.8358 17.7 29.9466 15.3216 26.8125 12.1875C23.6784 9.05342 21.3 5.16371 19.6771 0.518375L19.5 0ZM19.5 0L6.5 29.25C6.5 30.3333 6.77083 31.1458 7.3125 31.6875C7.85525 32.2292 8.66775 32.5 9.75 32.5L10.0539 32.5016C16.1817 32.5666 18.6875 34.606 18.6875 39H3.25C1.46738 39 0 37.5326 0 35.75V3.25C0 1.46738 1.46738 0 3.25 0H19.5ZM31.2114 7.78863C29.9423 6.5195 28.4001 6.006 27.7648 6.63975C27.131 7.2735 27.6445 8.81725 28.9136 10.0864C30.1827 11.3555 31.7265 11.8706 32.3602 11.2353C32.9956 10.6015 32.4805 9.05775 31.2114 7.78863Z"
        fill="#FDCCBA"
      />
    </g>
    <defs>
      <clipPath id="clip0_79_1295">
        <rect width="39" height="39" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const UserIconDashboard = () => (
  <svg
    width="30"
    height="30"
    viewBox="0 0 30 30"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M15 15C12.9834 15 11.257 14.2819 9.82087 12.8458C8.38476 11.4097 7.66671 9.68331 7.66671 7.66665C7.66671 5.64998 8.38476 3.92359 9.82087 2.48748C11.257 1.05137 12.9834 0.333313 15 0.333313C17.0167 0.333313 18.7431 1.05137 20.1792 2.48748C21.6153 3.92359 22.3334 5.64998 22.3334 7.66665C22.3334 9.68331 21.6153 11.4097 20.1792 12.8458C18.7431 14.2819 17.0167 15 15 15ZM0.333374 29.6666V24.5333C0.333374 23.4944 0.601041 22.5399 1.13637 21.6696C1.67171 20.7994 2.38182 20.1345 3.26671 19.675C5.16115 18.7278 7.08615 18.0176 9.04171 17.5446C10.9973 17.0716 12.9834 16.8345 15 16.8333C17.0167 16.8321 19.0028 17.0692 20.9584 17.5446C22.9139 18.0201 24.8389 18.7302 26.7334 19.675C27.6195 20.1333 28.3302 20.7982 28.8655 21.6696C29.4009 22.5411 29.6679 23.4956 29.6667 24.5333V29.6666H0.333374ZM4.00004 26H26V24.5333C26 24.1972 25.9163 23.8916 25.7489 23.6166C25.5814 23.3416 25.3596 23.1278 25.0834 22.975C23.4334 22.15 21.7681 21.5315 20.0875 21.1196C18.407 20.7078 16.7112 20.5012 15 20.5C13.2889 20.4988 11.5931 20.7053 9.91254 21.1196C8.23199 21.534 6.56671 22.1524 4.91671 22.975C4.64171 23.1278 4.41987 23.3416 4.25121 23.6166C4.08254 23.8916 3.99882 24.1972 4.00004 24.5333V26ZM15 11.3333C16.0084 11.3333 16.8719 10.9746 17.5905 10.2571C18.3092 9.5397 18.6679 8.6762 18.6667 7.66665C18.6655 6.65709 18.3068 5.7942 17.5905 5.07798C16.8743 4.36176 16.0108 4.00242 15 3.99998C13.9893 3.99753 13.1264 4.35687 12.4114 5.07798C11.6964 5.79909 11.337 6.66198 11.3334 7.66665C11.3297 8.67131 11.689 9.53481 12.4114 10.2571C13.1337 10.9795 13.9966 11.3382 15 11.3333Z"
      fill="#8DD7BA"
    />
  </svg>
);

export const SolarCardIconDashboard = () => (
  <svg
    width="41"
    height="41"
    viewBox="0 0 41 41"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <mask
      id="mask0_79_1299"
      maskUnits="userSpaceOnUse"
      x="3"
      y="6"
      width="35"
      height="29"
    >
      <path
        d="M23.9167 6.83331H17.0834C10.6412 6.83331 7.41931 6.83331 5.41885 8.83548C3.97702 10.2756 3.57385 12.3495 3.4611 15.8021H37.5389C37.4262 12.3495 37.023 10.2756 35.5812 8.83548C33.5807 6.83331 30.3588 6.83331 23.9167 6.83331ZM17.0834 34.1666H23.9167C30.3588 34.1666 33.5807 34.1666 35.5812 32.1645C37.5816 30.1623 37.5834 26.9421 37.5834 20.5C37.5834 19.746 37.5822 19.0342 37.5799 18.3646H3.4201C3.41669 19.0342 3.41555 19.746 3.41669 20.5C3.41669 26.9421 3.41669 30.164 5.41885 32.1645C7.42102 34.1649 10.6412 34.1666 17.0834 34.1666Z"
        fill="white"
      />
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M8.96875 27.3333C8.96875 26.9935 9.10374 26.6676 9.34402 26.4273C9.5843 26.1871 9.91019 26.0521 10.25 26.0521H17.0833C17.4231 26.0521 17.749 26.1871 17.9893 26.4273C18.2296 26.6676 18.3646 26.9935 18.3646 27.3333C18.3646 27.6731 18.2296 27.999 17.9893 28.2393C17.749 28.4796 17.4231 28.6146 17.0833 28.6146H10.25C9.91019 28.6146 9.5843 28.4796 9.34402 28.2393C9.10374 27.999 8.96875 27.6731 8.96875 27.3333ZM20.0729 27.3333C20.0729 26.9935 20.2079 26.6676 20.4482 26.4273C20.6885 26.1871 21.0144 26.0521 21.3542 26.0521H23.9167C24.2565 26.0521 24.5824 26.1871 24.8226 26.4273C25.0629 26.6676 25.1979 26.9935 25.1979 27.3333C25.1979 27.6731 25.0629 27.999 24.8226 28.2393C24.5824 28.4796 24.2565 28.6146 23.9167 28.6146H21.3542C21.0144 28.6146 20.6885 28.4796 20.4482 28.2393C20.2079 27.999 20.0729 27.6731 20.0729 27.3333Z"
        fill="black"
      />
    </mask>
    <g mask="url(#mask0_79_1299)">
      <path d="M0 0H41V41H0V0Z" fill="#C0B5FF" />
    </g>
  </svg>
);

export const SingleUserInScript = () => (
  <svg
    width="21"
    height="17"
    viewBox="0 0 21 17"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M0 0.5V2C0 6.15 2.21 9.78 5.5 11.8V16.5H20.5V14.5C20.5 11.84 15.17 10.5 12.5 10.5H12.25C7.5 10.5 3.5 6.5 3.5 2V0.5M12.5 0.5C11.4391 0.5 10.4217 0.921427 9.67157 1.67157C8.92143 2.42172 8.5 3.43913 8.5 4.5C8.5 5.56087 8.92143 6.57828 9.67157 7.32843C10.4217 8.07857 11.4391 8.5 12.5 8.5C13.5609 8.5 14.5783 8.07857 15.3284 7.32843C16.0786 6.57828 16.5 5.56087 16.5 4.5C16.5 3.43913 16.0786 2.42172 15.3284 1.67157C14.5783 0.921427 13.5609 0.5 12.5 0.5Z"
      fill="white"
    />
  </svg>
);

export const DoubleUserInScript = () => (
  <svg
    width="15"
    height="17"
    viewBox="0 0 15 17"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M14.8126 10.1876C14.814 9.43051 14.6233 8.68541 14.2585 8.02199C14.3054 7.95262 14.3453 7.87874 14.3783 7.80037C14.462 7.60029 14.4919 7.3818 14.465 7.16661C14.4381 6.95142 14.3553 6.74702 14.225 6.57369C14.0947 6.40036 13.9212 6.26414 13.722 6.17857C13.5227 6.09299 13.3045 6.06103 13.0891 6.08587C13.1836 5.89537 13.2586 5.69662 13.3141 5.48962L13.727 3.95568C13.9518 3.24119 13.9057 2.46894 13.5975 1.78626C13.2893 1.10358 12.7406 0.558247 12.056 0.25428C11.3714 -0.0496876 10.5989 -0.091011 9.88583 0.138192C9.17273 0.367396 8.56897 0.851084 8.18971 1.49699C7.8424 1.01823 7.33574 0.678999 6.76077 0.540255C6.1858 0.401511 5.58019 0.472344 5.05275 0.740026C4.52532 1.00771 4.11062 1.4547 3.88316 2.00069C3.6557 2.54668 3.63039 3.15589 3.81178 3.71887H2.52309C0.296714 3.71887 -0.577411 6.60618 1.2749 7.84087L2.36615 8.56874L1.60509 11.5545C1.50995 11.9148 1.49954 12.2922 1.57469 12.6572C1.64983 13.0222 1.80849 13.3648 2.03821 13.6582C2.33839 14.0452 2.74801 14.3329 3.21384 14.484V16.3751C3.21384 16.5243 3.2731 16.6674 3.37859 16.7729C3.48408 16.8784 3.62715 16.9376 3.77634 16.9376H14.2501C14.3993 16.9376 14.5423 16.8784 14.6478 16.7729C14.7533 16.6674 14.8126 16.5243 14.8126 16.3751V10.1876ZM6.93759 14.6032H8.46759C8.79342 14.6033 9.10626 14.4754 9.33875 14.2471C9.57125 14.0188 9.70482 13.7084 9.71071 13.3826C9.71072 13.2159 9.67779 13.0509 9.61384 12.897C9.54988 12.7431 9.45615 12.6033 9.33803 12.4857C9.21991 12.3681 9.07973 12.275 8.92553 12.2117C8.77132 12.1484 8.60614 12.1163 8.43946 12.117H6.93759V10.5651C6.9376 10.4725 6.91476 10.3813 6.8711 10.2997C6.82743 10.218 6.76429 10.1484 6.68728 10.0971L1.89871 6.90487C0.972839 6.28724 1.4099 4.84387 2.52309 4.84387H4.11946L4.25446 5.34449C4.38317 5.82341 4.64481 6.25612 5.00914 6.59258C5.37346 6.92903 5.82558 7.15548 6.31321 7.24574C6.63028 7.30123 6.95539 7.2907 7.2682 7.21482C7.58101 7.13894 7.87481 6.99934 8.13121 6.80474C8.16234 6.80999 8.19421 6.81262 8.22684 6.81262H10.3126C10.5282 6.81299 10.7382 6.83249 10.9426 6.87112L9.53071 7.41449C9.26088 7.51931 9.03609 7.71501 8.89512 7.96784C8.75414 8.22066 8.70581 8.51476 8.75848 8.79941C8.81114 9.08405 8.96149 9.3414 9.18359 9.52705C9.40569 9.7127 9.68562 9.81502 9.97509 9.81637C10.1292 9.81637 10.2817 9.78768 10.4251 9.73199L13.3045 8.62387C13.5492 9.09074 13.6876 9.62343 13.6876 10.1876V15.8126H6.93759V14.6032ZM8.9654 5.68762C8.98678 5.63212 9.00646 5.57587 9.02446 5.51887C9.17183 5.04576 9.18178 4.54053 9.05315 4.06199L8.80115 3.15018L8.93615 2.6473L8.93953 2.6338C8.99432 2.379 9.10031 2.138 9.25107 1.9254C9.40183 1.71281 9.59422 1.53307 9.81655 1.39709C10.0389 1.26111 10.2865 1.17172 10.5445 1.13435C10.8024 1.09698 11.0652 1.11242 11.317 1.17971C11.5688 1.24701 11.8043 1.36476 12.0092 1.52583C12.2141 1.6869 12.3841 1.88792 12.509 2.11669C12.6338 2.34547 12.7109 2.59722 12.7355 2.85668C12.76 3.11614 12.7317 3.3779 12.652 3.62605L12.6481 3.63843L12.2279 5.19937C12.1536 5.47493 12.0234 5.73226 11.8454 5.95537C11.3539 5.77779 10.8352 5.68718 10.3126 5.68762H8.9654Z"
      fill="white"
    />
  </svg>
);

export const HandInScript = () => (
  <svg
    width="22"
    height="22"
    viewBox="0 0 22 22"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M8.81692 3.1399C8.94466 3.01226 9.09631 2.91107 9.26321 2.84212C9.43011 2.77318 9.60897 2.73783 9.78954 2.73811C9.97012 2.73839 10.1489 2.77429 10.3156 2.84375C10.4822 2.91321 10.6336 3.01486 10.7609 3.1429L16.0149 8.4249L15.5699 7.3299C15.0099 5.9499 16.5759 4.6799 17.8099 5.5109C17.9999 5.6379 18.1569 5.8059 18.2709 6.0009L20.6509 10.0769C21.4519 11.4489 21.7759 13.0475 21.5724 14.6231C21.3689 16.1987 20.6492 17.6625 19.5259 18.7859L18.6819 19.6299C17.3335 20.9782 15.5048 21.7357 13.5979 21.7357C11.6911 21.7357 9.8623 20.9782 8.51392 19.6299L3.47992 14.5949C3.22485 14.3365 3.08232 13.9877 3.08344 13.6247C3.08456 13.2616 3.22924 12.9137 3.48591 12.6569C3.74258 12.4001 4.0904 12.2552 4.45348 12.2539C4.81655 12.2526 5.16541 12.395 5.42392 12.6499L7.02792 14.2539L7.55892 13.7229L3.86892 10.0329C3.61385 9.77452 3.47132 9.42573 3.47244 9.06266C3.47356 8.69958 3.61824 8.35169 3.87491 8.09488C4.13158 7.83808 4.4794 7.69322 4.84248 7.69192C5.20555 7.69061 5.55441 7.83295 5.81292 8.0879L9.50292 11.7779L10.0329 11.2479L4.97692 6.1909C4.84233 6.06491 4.73444 5.91315 4.65969 5.74462C4.58493 5.5761 4.54482 5.39427 4.54175 5.20993C4.53869 5.0256 4.57272 4.84253 4.64183 4.67162C4.71094 4.5007 4.81371 4.34543 4.94404 4.21504C5.07437 4.08464 5.22959 3.98179 5.40047 3.91259C5.57135 3.84339 5.7544 3.80926 5.93873 3.81224C6.12307 3.81521 6.30492 3.85522 6.47348 3.92989C6.64204 4.00456 6.79387 4.11237 6.91992 4.2469L11.9769 9.3039L12.5069 8.7739L8.81592 5.0819C8.68838 4.9544 8.58721 4.80302 8.51819 4.63642C8.44916 4.46981 8.41364 4.29124 8.41364 4.1109C8.41364 3.93056 8.44916 3.75199 8.51819 3.58538C8.58721 3.41877 8.68838 3.2674 8.81592 3.1399M16.4799 0.0458984L17.3029 0.612898C18.5093 1.44591 19.5543 2.49123 20.3869 3.6979L20.9539 4.5209L19.3079 5.6559L18.7399 4.8319C18.0453 3.82598 17.1738 2.95452 16.1679 2.2599L15.3439 1.6919L16.4799 0.0458984ZM1.69092 15.3439L2.25892 16.1679C2.95384 17.1739 3.82564 18.0454 4.83192 18.7399L5.65492 19.3079L4.51992 20.9539L3.69692 20.3869C2.49025 19.5543 1.44493 18.5093 0.611922 17.3029L0.0449219 16.4789L1.69092 15.3439Z"
      fill="#F8F8F8"
    />
  </svg>
);

export const LikeIconInScript = () => (
  <svg
    width="21"
    height="21"
    viewBox="0 0 21 21"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M0.875 11.1875V18.0625C0.875 19.1996 1.80038 20.125 2.9375 20.125H5V9.12501H2.9375C1.80038 9.12501 0.875 10.0504 0.875 11.1875ZM19.5351 8.87063C19.281 8.52143 18.9475 8.23769 18.5621 8.04284C18.1767 7.84798 17.7505 7.74761 17.3186 7.75001H12.5983L13.2363 4.56001C13.3258 4.11307 13.315 3.65182 13.2047 3.20956C13.0943 2.76729 12.8872 2.35503 12.5983 2.00251C12.3101 1.64938 11.9468 1.36494 11.5348 1.1699C11.1228 0.974862 10.6726 0.874119 10.2168 0.875006C9.60625 0.875006 9.06313 1.28476 8.90913 1.82788L8.27113 3.59613C7.77281 4.97411 7.13808 6.29885 6.37638 7.55063V20.125H15.1723C15.7589 20.1271 16.3307 19.9407 16.8034 19.5931C17.276 19.2455 17.6244 18.7552 17.7971 18.1945L19.9449 11.3195C20.0756 10.9081 20.1067 10.4714 20.0354 10.0456C19.9642 9.61984 19.7927 9.21709 19.5351 8.87063Z"
      fill="#F0F0F0"
    />
  </svg>
);

export const HandDropIconInScript = () => (
  <svg
    width="25"
    height="25"
    viewBox="0 0 25 25"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M4.5 14.5001H6.895C7.189 14.5001 7.479 14.5661 7.742 14.6941L9.784 15.6821C10.047 15.8091 10.337 15.8751 10.632 15.8751H11.674C12.682 15.8751 13.5 16.6661 13.5 17.6421C13.5 17.6821 13.473 17.7161 13.434 17.7271L10.893 18.4301C10.4371 18.5561 9.95087 18.512 9.525 18.3061L7.342 17.2501M9.5 8.04711C9.5 6.06011 11.185 4.09611 12.368 2.96211C12.6706 2.66648 13.0769 2.50098 13.5 2.50098C13.9231 2.50098 14.3294 2.66648 14.632 2.96211C15.815 4.09611 17.5 6.06011 17.5 8.04711C17.5 9.99611 15.985 12.0001 13.5 12.0001C11.015 12.0001 9.5 9.99611 9.5 8.04711Z"
      stroke="#F0F0F0"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M13.5 17L18.093 15.589C18.4929 15.4663 18.9213 15.4731 19.317 15.6083C19.7128 15.7435 20.0558 16.0003 20.297 16.342C20.666 16.852 20.516 17.584 19.978 17.894L12.463 22.231C12.228 22.367 11.9678 22.4537 11.6982 22.4858C11.4287 22.518 11.1554 22.4949 10.895 22.418L4.5 20.52"
      stroke="#F0F0F0"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const PublishIconImageGenerator = () => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M3.69211 7.8969L5.10211 8.3669C6.03411 8.6769 6.49911 8.8329 6.83311 9.1669C7.16711 9.5009 7.32311 9.9669 7.63311 10.8969L8.10311 12.3069C8.88711 14.6609 9.27911 15.8369 10.0001 15.8369C10.7201 15.8369 11.1131 14.6609 11.8971 12.3069L14.7351 3.7949C15.2871 2.1389 15.5631 1.3109 15.1261 0.8739C14.6891 0.4369 13.8611 0.7129 12.2061 1.2639L3.69111 4.1039C1.34011 4.8869 0.162109 5.2789 0.162109 5.9999C0.162109 6.7209 1.33911 7.1129 3.69211 7.8969Z"
      fill="white"
      fill-opacity="0.25"
    />
  </svg>
);

// Sidebars
export const HomeIconSidebar = ({ color = "white" }) => (
  <svg
    width="18"
    height="20"
    viewBox="0 0 18 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M0.25 18.7498V7.8848C0.25 7.5648 0.321667 7.26189 0.465 6.97605C0.608333 6.69022 0.805833 6.4548 1.0575 6.2698L7.78875 1.17231C8.14125 0.903139 8.54375 0.768555 8.99625 0.768555C9.44875 0.768555 9.85375 0.903139 10.2113 1.17231L16.9425 6.26855C17.195 6.45355 17.3925 6.68939 17.535 6.97605C17.6783 7.26189 17.75 7.5648 17.75 7.8848V18.7498C17.75 19.0848 17.6254 19.3769 17.3763 19.6261C17.1271 19.8752 16.835 19.9998 16.5 19.9998H12.27C11.9833 19.9998 11.7433 19.9031 11.55 19.7098C11.3567 19.5156 11.26 19.2756 11.26 18.9898V13.0286C11.26 12.7427 11.1633 12.5031 10.97 12.3098C10.7758 12.1156 10.5358 12.0186 10.25 12.0186H7.75C7.46417 12.0186 7.22458 12.1156 7.03125 12.3098C6.83708 12.5031 6.74 12.7427 6.74 13.0286V18.9911C6.74 19.2769 6.64333 19.5165 6.45 19.7098C6.25667 19.9031 6.01708 19.9998 5.73125 19.9998H1.5C1.165 19.9998 0.872916 19.8752 0.62375 19.6261C0.374583 19.3769 0.25 19.0848 0.25 18.7498Z"
      fill={color}
    />
  </svg>
);

export const UserGroupIconSidebar = ({ color = "white" }) => (
  <svg
    width="28"
    height="26"
    viewBox="0 0 28 26"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M9.31249 5.5625C9.31249 4.3193 9.80635 3.12701 10.6854 2.24794C11.5645 1.36886 12.7568 0.875 14 0.875C15.2432 0.875 16.4355 1.36886 17.3146 2.24794C18.1936 3.12701 18.6875 4.3193 18.6875 5.5625C18.6875 6.8057 18.1936 7.99799 17.3146 8.87706C16.4355 9.75614 15.2432 10.25 14 10.25C12.7568 10.25 11.5645 9.75614 10.6854 8.87706C9.80635 7.99799 9.31249 6.8057 9.31249 5.5625ZM11.1875 12.125C10.4416 12.125 9.7262 12.4213 9.19875 12.9488C8.6713 13.4762 8.37499 14.1916 8.37499 14.9375V19.625C8.37499 21.1168 8.96762 22.5476 10.0225 23.6025C11.0774 24.6574 12.5081 25.25 14 25.25C15.4918 25.25 16.9226 24.6574 17.9775 23.6025C19.0324 22.5476 19.625 21.1168 19.625 19.625V14.9375C19.625 14.1916 19.3287 13.4762 18.8012 12.9488C18.2738 12.4213 17.5584 12.125 16.8125 12.125H11.1875ZM6.60499 13.9438C6.53468 14.2703 6.49948 14.6035 6.49999 14.9375V19.625C6.4998 20.6285 6.701 21.6218 7.09167 22.5462C7.48234 23.4705 8.05453 24.3071 8.77436 25.0063L8.58686 25.0588C7.14626 25.4442 5.61155 25.2418 4.32013 24.496C3.0287 23.7503 2.08626 22.5223 1.69999 21.0819L0.970614 18.3631C0.875065 18.0063 0.850729 17.6342 0.898995 17.268C0.94726 16.9018 1.06718 16.5487 1.25192 16.2289C1.43665 15.9091 1.68257 15.6287 1.97564 15.4039C2.26872 15.1791 2.6032 15.0143 2.95999 14.9188L6.60499 13.9438ZM19.2237 25.0063C19.9439 24.3072 20.5164 23.4707 20.9074 22.5464C21.2984 21.6221 21.4999 20.6286 21.5 19.625V14.9375C21.4987 14.595 21.4637 14.2638 21.395 13.9438L25.0381 14.9188C25.3951 15.0143 25.7298 15.1792 26.023 15.4041C26.3162 15.629 26.5622 15.9096 26.747 16.2296C26.9317 16.5497 27.0516 16.903 27.0997 17.2694C27.1479 17.6358 27.1233 18.0081 27.0275 18.365L26.3 21.0819C26.1046 21.811 25.7644 22.4934 25.2998 23.0884C24.8351 23.6833 24.2554 24.1786 23.5953 24.5448C22.9352 24.911 22.2081 25.1406 21.4574 25.2199C20.7067 25.2991 19.9458 25.2265 19.2237 25.0063ZM0.874989 8.375C0.874989 7.38044 1.27008 6.42661 1.97334 5.72335C2.6766 5.02009 3.63043 4.625 4.62499 4.625C5.61955 4.625 6.57338 5.02009 7.27664 5.72335C7.9799 6.42661 8.37499 7.38044 8.37499 8.375C8.37499 9.36956 7.9799 10.3234 7.27664 11.0267C6.57338 11.7299 5.61955 12.125 4.62499 12.125C3.63043 12.125 2.6766 11.7299 1.97334 11.0267C1.27008 10.3234 0.874989 9.36956 0.874989 8.375ZM19.625 8.375C19.625 7.38044 20.0201 6.42661 20.7233 5.72335C21.4266 5.02009 22.3804 4.625 23.375 4.625C24.3696 4.625 25.3234 5.02009 26.0266 5.72335C26.7299 6.42661 27.125 7.38044 27.125 8.375C27.125 9.36956 26.7299 10.3234 26.0266 11.0267C25.3234 11.7299 24.3696 12.125 23.375 12.125C22.3804 12.125 21.4266 11.7299 20.7233 11.0267C20.0201 10.3234 19.625 9.36956 19.625 8.375Z"
      fill={color}
    />
  </svg>
);

export const StreamingAvatarIcon = ({ color = "white" }) => (
  <svg
    width="28"
    height="18"
    viewBox="0 0 28 18"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M3.9625 17.1899C4.15 17.3774 4.3875 17.4649 4.625 17.4649C4.8625 17.4649 5.1 17.3774 5.2875 17.1899C5.65 16.8274 5.65 16.2274 5.2875 15.8649C3.4 13.9774 2.4375 11.6774 2.4375 9.0274C2.4375 6.3774 3.4 4.0774 5.2875 2.1899C5.65 1.8274 5.65 1.2274 5.2875 0.8649C5.11108 0.69055 4.87304 0.592773 4.625 0.592773C4.37696 0.592773 4.13892 0.69055 3.9625 0.8649C1.7375 3.0899 0.5625 5.9149 0.5625 9.0274C0.5625 12.1399 1.7375 14.9649 3.9625 17.1899ZM8.375 14.9649C8.1375 14.9649 7.9 14.8774 7.7125 14.6899C6.35 13.3274 5.5625 11.2649 5.5625 9.0274C5.5625 6.7899 6.35 4.7274 7.7125 3.3649C8.075 3.0024 8.675 3.0024 9.0375 3.3649C9.4 3.7274 9.4 4.3274 9.0375 4.6899C8.025 5.7024 7.4375 7.2899 7.4375 9.0274C7.4375 10.7649 8.025 12.3524 9.0375 13.3649C9.4 13.7274 9.4 14.3274 9.0375 14.6899C8.85 14.8774 8.6125 14.9649 8.375 14.9649ZM14 12.4649C12.1 12.4649 10.5625 10.9274 10.5625 9.0274C10.5625 7.1274 12.1 5.5899 14 5.5899C15.9 5.5899 17.4375 7.1274 17.4375 9.0274C17.4375 10.9274 15.9 12.4649 14 12.4649ZM14 7.4649C13.7947 7.4649 13.5915 7.50533 13.4018 7.58389C13.2122 7.66244 13.0399 7.77758 12.8947 7.92273C12.7496 8.06788 12.6344 8.2402 12.5559 8.42984C12.4773 8.61949 12.4369 8.82275 12.4369 9.02803C12.4369 9.2333 12.4773 9.43656 12.5559 9.62621C12.6344 9.81585 12.7496 9.98817 12.8947 10.1333C13.0399 10.2785 13.2122 10.3936 13.4018 10.4722C13.5915 10.5507 13.7947 10.5912 14 10.5912C14.4144 10.5912 14.8118 10.4265 15.1049 10.1335C15.3979 9.84048 15.5625 9.44305 15.5625 9.02865C15.5625 8.61425 15.3979 8.21682 15.1049 7.9238C14.8118 7.63077 14.4144 7.46615 14 7.46615M19.6213 14.9662C19.3837 14.9662 19.1462 14.8787 18.9587 14.6912C18.7847 14.5146 18.6871 14.2766 18.6871 14.0287C18.6871 13.7807 18.7847 13.5427 18.9587 13.3661C19.9713 12.3536 20.5588 10.7662 20.5588 9.02865C20.5588 7.29115 19.9713 5.70365 18.9587 4.69115C18.7847 4.51457 18.6871 4.27659 18.6871 4.02865C18.6871 3.78071 18.7847 3.54273 18.9587 3.36615C19.3213 3.00365 19.9213 3.00365 20.2838 3.36615C21.6462 4.72865 22.4338 6.79115 22.4338 9.02865C22.4338 11.2662 21.6462 13.3287 20.2838 14.6912C20.0963 14.8787 19.8587 14.9662 19.6213 14.9662ZM22.7087 17.1912C22.8962 17.3787 23.1337 17.4662 23.3713 17.4662C23.6087 17.4662 23.8463 17.3787 24.0338 17.1912C26.2588 14.9662 27.4338 12.1412 27.4338 9.02865C27.4338 5.91615 26.2588 3.09115 24.0338 0.866151C23.8573 0.6918 23.6193 0.594023 23.3713 0.594023C23.1232 0.594023 22.8852 0.6918 22.7087 0.866151C22.3462 1.22865 22.3462 1.82865 22.7087 2.19115C24.5962 4.07865 25.5588 6.37865 25.5588 9.02865C25.5588 11.6786 24.5962 13.9786 22.7087 15.8661C22.3462 16.2286 22.3462 16.8287 22.7087 17.1912Z"
      fill={color}
    />
  </svg>
);

export const TalkingAvatarIcon = ({ color = "white" }) => (
  <svg
    width="28"
    height="25"
    viewBox="0 0 28 25"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M10.25 4.25C11.5761 4.25 12.8479 4.77678 13.7855 5.71447C14.7232 6.65215 15.25 7.92392 15.25 9.25C15.25 10.5761 14.7232 11.8479 13.7855 12.7855C12.8479 13.7232 11.5761 14.25 10.25 14.25C8.92392 14.25 7.65215 13.7232 6.71447 12.7855C5.77678 11.8479 5.25 10.5761 5.25 9.25C5.25 7.92392 5.77678 6.65215 6.71447 5.71447C7.65215 4.77678 8.92392 4.25 10.25 4.25ZM10.25 16.75C13.5875 16.75 20.25 18.425 20.25 21.75V24.25H0.25V21.75C0.25 18.425 6.9125 16.75 10.25 16.75ZM19.95 4.7C22.475 7.45 22.475 11.2625 19.95 13.7875L17.85 11.675C18.9 10.2 18.9 8.2875 17.85 6.8125L19.95 4.7ZM24.0875 0.5C29 5.5625 28.9625 13.1375 24.0875 18L22.05 15.9625C25.5125 11.9875 25.5125 6.3125 22.05 2.5375L24.0875 0.5Z"
      fill={color}
    />
  </svg>
);

export const VideoTranslationIcon = ({ color = "white" }) => (
  <svg
    width="27"
    height="28"
    viewBox="0 0 27 28"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M26 11.5C24.8419 5.79438 19.7975 1.5 13.75 1.5C7.7025 1.5 2.65813 5.79438 1.5 11.5L5.25 10.25M1.5 16.5C2.65813 22.2056 7.7025 26.5 13.75 26.5C19.7975 26.5 24.8419 22.2056 26 16.5L22.75 17.75"
      stroke={color}
      stroke-width="2"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
  </svg>
);

export const FaceSwapIcon = ({ color = "white" }) => (
  <svg
    width="26"
    height="26"
    viewBox="0 0 26 26"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M9.25 15.8125C8.8125 15.8125 8.4425 15.6613 8.14 15.3588C7.8375 15.0563 7.68667 14.6867 7.6875 14.25C7.68833 13.8133 7.83958 13.4433 8.14125 13.14C8.44292 12.8367 8.8125 12.6858 9.25 12.6875C9.6875 12.6892 10.0575 12.8404 10.36 13.1413C10.6625 13.4421 10.8133 13.8117 10.8125 14.25C10.8117 14.6883 10.6604 15.0583 10.3588 15.36C10.0571 15.6617 9.6875 15.8125 9.25 15.8125ZM16.75 15.8125C16.3125 15.8125 15.9425 15.6613 15.64 15.3588C15.3375 15.0563 15.1867 14.6867 15.1875 14.25C15.1883 13.8133 15.3396 13.4433 15.6413 13.14C15.9429 12.8367 16.3125 12.6858 16.75 12.6875C17.1875 12.6892 17.5575 12.8404 17.86 13.1413C18.1625 13.4421 18.3133 13.8117 18.3125 14.25C18.3117 14.6883 18.1604 15.0583 17.8588 15.36C17.5571 15.6617 17.1875 15.8125 16.75 15.8125ZM13 23C15.7917 23 18.1562 22.0313 20.0938 20.0938C22.0312 18.1563 23 15.7917 23 13C23 12.5 22.9688 12.0158 22.9063 11.5475C22.8438 11.0792 22.7292 10.6258 22.5625 10.1875C22.125 10.2917 21.6875 10.37 21.25 10.4225C20.8125 10.475 20.3542 10.5008 19.875 10.5C17.9792 10.5 16.1875 10.0938 14.5 9.28126C12.8125 8.46876 11.375 7.33334 10.1875 5.87501C9.52083 7.50001 8.56792 8.91167 7.32875 10.11C6.08958 11.3083 4.64667 12.2092 3 12.8125V13C3 15.7917 3.96875 18.1563 5.90625 20.0938C7.84375 22.0313 10.2083 23 13 23ZM13 25.5C11.2708 25.5 9.64584 25.1721 8.125 24.5163C6.60417 23.8604 5.28125 22.9696 4.15625 21.8438C3.03125 20.7179 2.14084 19.395 1.485 17.875C0.829168 16.355 0.500835 14.73 0.500002 13C0.499168 11.27 0.827502 9.64501 1.485 8.12501C2.1425 6.60501 3.03292 5.28209 4.15625 4.15626C5.27958 3.03042 6.6025 2.14001 8.125 1.48501C9.6475 0.830006 11.2725 0.501673 13 0.500006C14.7275 0.49834 16.3525 0.826673 17.875 1.48501C19.3975 2.14334 20.7204 3.03376 21.8438 4.15626C22.9671 5.27876 23.8579 6.60167 24.5163 8.12501C25.1746 9.64834 25.5025 11.2733 25.5 13C25.4975 14.7267 25.1692 16.3517 24.515 17.875C23.8608 19.3983 22.9704 20.7213 21.8438 21.8438C20.7171 22.9663 19.3942 23.8571 17.875 24.5163C16.3558 25.1754 14.7308 25.5033 13 25.5Z"
      fill={color}
    />
  </svg>
);

export const ImageGeneratorIcon = ({ color = "white" }) => (
  <svg
    width="30"
    height="30"
    viewBox="0 0 30 30"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M27.4625 11.8377C27.4613 12.1171 27.3727 12.3891 27.209 12.6155C27.0454 12.8419 26.8149 13.0114 26.55 13.1002L24.2 13.8752C23.4622 14.1219 22.7921 14.5374 22.2431 15.0886C21.6941 15.6397 21.2813 16.3114 21.0375 17.0502L20.225 19.3877C20.1317 19.6485 19.9618 19.8751 19.7375 20.0377C19.5675 20.1601 19.371 20.2405 19.1639 20.2722C18.9569 20.304 18.7453 20.2862 18.5464 20.2203C18.3476 20.1544 18.1672 20.0424 18.0201 19.8933C17.873 19.7442 17.7633 19.5624 17.7 19.3627L16.9125 17.0127C16.6585 16.2717 16.2298 15.6028 15.6625 15.0627C15.114 14.4997 14.4421 14.0718 13.7 13.8127L11.35 13.0252C11.0839 12.9414 10.8514 12.7751 10.6863 12.5502C10.5243 12.3219 10.4372 12.0489 10.4372 11.769C10.4372 11.489 10.5243 11.216 10.6863 10.9877C10.8524 10.7563 11.0893 10.5851 11.3613 10.5002L13.6988 9.7127C14.4511 9.4597 15.1324 9.03122 15.6863 8.4627C16.2488 7.90895 16.6775 7.2327 16.9363 6.4877L17.7113 4.1752C17.7845 3.91099 17.9427 3.67821 18.1613 3.5127C18.3812 3.33625 18.6543 3.23934 18.9363 3.2377C19.2155 3.23097 19.4902 3.30945 19.7238 3.4627C19.9612 3.61172 20.1417 3.83624 20.2363 4.1002L21.0238 6.4877C21.2825 7.2327 21.7113 7.90895 22.2738 8.4627C22.8241 9.02955 23.5009 9.45792 24.2488 9.7127L26.5863 10.5377C26.8494 10.62 27.0779 10.787 27.2363 11.0127C27.3988 11.2552 27.48 11.544 27.4625 11.8377ZM15.125 20.5002C15.1244 20.7551 15.0459 21.0037 14.9 21.2127C14.7489 21.4159 14.5397 21.5685 14.3 21.6502L12.6125 22.2127C12.1397 22.3794 11.7119 22.6532 11.3625 23.0127C11.0064 23.3649 10.7331 23.7918 10.5625 24.2627L9.975 25.9377C9.89331 26.1774 9.74075 26.3866 9.5375 26.5377C9.32702 26.6856 9.07602 26.765 8.81875 26.765C8.56149 26.765 8.31049 26.6856 8.1 26.5377C7.89676 26.3866 7.74419 26.1774 7.6625 25.9377L7.1125 24.2627C6.94152 23.7917 6.66781 23.3647 6.31125 23.0127C5.95911 22.6566 5.53212 22.3833 5.06125 22.2127L3.37375 21.6627C3.13241 21.5777 2.92298 21.4206 2.77375 21.2127C2.62342 21.0052 2.54051 20.7564 2.53625 20.5002C2.54076 20.2424 2.62486 19.9924 2.77702 19.7842C2.92919 19.5761 3.14199 19.4202 3.38625 19.3377L5.06125 18.7877C5.5304 18.6137 5.95644 18.3405 6.31025 17.9867C6.66406 17.6329 6.93729 17.2068 7.11125 16.7377L7.67375 15.0877C7.74867 14.8512 7.89226 14.6423 8.08625 14.4877C8.28906 14.3332 8.53426 14.2444 8.78897 14.2332C9.04367 14.2221 9.2957 14.2891 9.51125 14.4252C9.725 14.5727 9.89 14.7815 9.98625 15.0252L10.5488 16.7377C10.7227 17.2068 10.996 17.6329 11.3498 17.9867C11.7036 18.3405 12.1296 18.6137 12.5988 18.7877L14.2738 19.3752C14.5129 19.4514 14.7197 19.6054 14.8613 19.8127C15.0201 20.0079 15.1121 20.2488 15.1238 20.5002"
      fill={color}
    />
  </svg>
);

export const BackgroundRemoverIcon = ({ color = "white" }) => (
  <svg
    width="25"
    height="25"
    viewBox="0 0 25 25"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M8.59375 11.7188H16.4062C16.6135 11.7188 16.8122 11.8011 16.9587 11.9476C17.1052 12.0941 17.1875 12.2928 17.1875 12.5C17.1875 12.7072 17.1052 12.9059 16.9587 13.0524C16.8122 13.1989 16.6135 13.2812 16.4062 13.2812H8.59375C8.38655 13.2812 8.18784 13.1989 8.04132 13.0524C7.89481 12.9059 7.8125 12.7072 7.8125 12.5C7.8125 12.2928 7.89481 12.0941 8.04132 11.9476C8.18784 11.8011 8.38655 11.7188 8.59375 11.7188Z"
      fill={color}
    />
    <path
      d="M12.5 21.875C13.7311 21.875 14.9502 21.6325 16.0877 21.1614C17.2251 20.6902 18.2586 19.9997 19.1291 19.1291C19.9997 18.2586 20.6902 17.2251 21.1614 16.0877C21.6325 14.9502 21.875 13.7311 21.875 12.5C21.875 11.2689 21.6325 10.0498 21.1614 8.91234C20.6902 7.77492 19.9997 6.74142 19.1291 5.87087C18.2586 5.00032 17.2251 4.30977 16.0877 3.83863C14.9502 3.36749 13.7311 3.125 12.5 3.125C10.0136 3.125 7.62903 4.11272 5.87087 5.87087C4.11272 7.62903 3.125 10.0136 3.125 12.5C3.125 14.9864 4.11272 17.371 5.87087 19.1291C7.62903 20.8873 10.0136 21.875 12.5 21.875ZM12.5 23.4375C9.59919 23.4375 6.8172 22.2852 4.76602 20.234C2.71484 18.1828 1.5625 15.4008 1.5625 12.5C1.5625 9.59919 2.71484 6.8172 4.76602 4.76602C6.8172 2.71484 9.59919 1.5625 12.5 1.5625C15.4008 1.5625 18.1828 2.71484 20.234 4.76602C22.2852 6.8172 23.4375 9.59919 23.4375 12.5C23.4375 15.4008 22.2852 18.1828 20.234 20.234C18.1828 22.2852 15.4008 23.4375 12.5 23.4375Z"
      fill={color}
    />
  </svg>
);

export const VirtualTryOnIcon = ({ color = "white" }) => (
  <svg
    width="26"
    height="20"
    viewBox="0 0 26 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M13 0C11.8397 0 10.7269 0.460936 9.90641 1.28141C9.08594 2.10188 8.625 3.21468 8.625 4.375H11.125C11.125 3.87772 11.3225 3.40081 11.6742 3.04917C12.0258 2.69754 12.5027 2.5 13 2.5C13.4973 2.5 13.9742 2.69754 14.3258 3.04917C14.6775 3.40081 14.875 3.87772 14.875 4.375C14.875 4.87228 14.6775 5.34919 14.3258 5.70083C13.9742 6.05246 13.4973 6.25 13 6.25C12.3125 6.25 11.75 6.8125 11.75 7.5V9.6875L1 17.75C0.790118 17.9074 0.635083 18.1269 0.556858 18.3773C0.478633 18.6277 0.481183 18.8964 0.564146 19.1453C0.647109 19.3942 0.80628 19.6107 1.01911 19.7641C1.23194 19.9175 1.48765 20 1.75 20H24.25C24.5124 20 24.7681 19.9175 24.9809 19.7641C25.1937 19.6107 25.3529 19.3942 25.4359 19.1453C25.5188 18.8964 25.5214 18.6277 25.4431 18.3773C25.3649 18.1269 25.2099 17.9074 25 17.75L14.25 9.6875V8.5625C15.152 8.29356 15.9433 7.74087 16.5063 6.98649C17.0692 6.23211 17.3739 5.31628 17.375 4.375C17.375 3.21468 16.9141 2.10188 16.0936 1.28141C15.2731 0.460936 14.1603 0 13 0ZM13 11.875L20.5 17.5H5.5L13 11.875Z"
      fill={color}
    />
  </svg>
);

export const SpeedpaintingIcon = ({ color = "white" }) => (
  <svg
    width="26"
    height="20"
    viewBox="0 0 26 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M11.0625 14.375C11.5833 14.8958 12.2396 15.1408 13.0312 15.11C13.8229 15.0792 14.3958 14.7925 14.75 14.25L20.0312 6.34375C20.2188 6.05208 20.1929 5.78625 19.9538 5.54625C19.7146 5.30625 19.4487 5.28042 19.1562 5.46875L11.25 10.75C10.7083 11.125 10.4117 11.6929 10.36 12.4538C10.3083 13.2146 10.5425 13.855 11.0625 14.375ZM4.375 20C3.91667 20 3.495 19.9008 3.11 19.7025C2.725 19.5042 2.4175 19.2075 2.1875 18.8125C1.64583 17.8333 1.22917 16.8179 0.9375 15.7663C0.645833 14.7146 0.5 13.6258 0.5 12.5C0.5 10.7708 0.828333 9.14583 1.485 7.625C2.14167 6.10417 3.03208 4.78125 4.15625 3.65625C5.28042 2.53125 6.60333 1.64083 8.125 0.985C9.64667 0.329167 11.2717 0.000833333 13 0C14.7083 0 16.3125 0.322917 17.8125 0.96875C19.3125 1.61458 20.625 2.495 21.75 3.61C22.875 4.725 23.7708 6.02708 24.4375 7.51625C25.1042 9.00542 25.4479 10.6042 25.4688 12.3125C25.4896 13.4583 25.3596 14.5783 25.0788 15.6725C24.7979 16.7667 24.3654 17.8133 23.7812 18.8125C23.5521 19.2083 23.245 19.5054 22.86 19.7038C22.475 19.9021 22.0525 20.0008 21.5925 20H4.375Z"
      fill={color}
    />
  </svg>
);

export const ResultLibraryIcon = ({ color = "white" }) => (
  <svg
    width="28"
    height="28"
    viewBox="0 0 28 28"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M2.28125 0.875C1.90829 0.875 1.5506 1.02316 1.28688 1.28688C1.02316 1.5506 0.875 1.90829 0.875 2.28125C0.875 2.65421 1.02316 3.0119 1.28688 3.27562C1.5506 3.53934 1.90829 3.6875 2.28125 3.6875H18.2188C18.5917 3.6875 18.9494 3.53934 19.2131 3.27562C19.4768 3.0119 19.625 2.65421 19.625 2.28125C19.625 1.90829 19.4768 1.5506 19.2131 1.28688C18.9494 1.02316 18.5917 0.875 18.2188 0.875H2.28125ZM0.875 7.90625C0.875 7.53329 1.02316 7.1756 1.28688 6.91188C1.5506 6.64816 1.90829 6.5 2.28125 6.5H12.125C12.498 6.5 12.8556 6.64816 13.1194 6.91188C13.3831 7.1756 13.5312 7.53329 13.5312 7.90625C13.5312 8.27921 13.3831 8.6369 13.1194 8.90062C12.8556 9.16434 12.498 9.3125 12.125 9.3125H2.28125C1.90829 9.3125 1.5506 9.16434 1.28688 8.90062C1.02316 8.6369 0.875 8.27921 0.875 7.90625ZM17.75 22.4375C18.9932 22.4375 20.1855 21.9436 21.0646 21.0646C21.9436 20.1855 22.4375 18.9932 22.4375 17.75C22.4375 16.5068 21.9436 15.3145 21.0646 14.4354C20.1855 13.5564 18.9932 13.0625 17.75 13.0625C16.5068 13.0625 15.3145 13.5564 14.4354 14.4354C13.5564 15.3145 13.0625 16.5068 13.0625 17.75C13.0625 18.9932 13.5564 20.1855 14.4354 21.0646C15.3145 21.9436 16.5068 22.4375 17.75 22.4375ZM17.75 25.25C19.3137 25.25 20.7631 24.7719 21.965 23.9544L24.725 26.7125C24.8537 26.8507 25.009 26.9615 25.1815 27.0383C25.354 27.1152 25.5402 27.1565 25.729 27.1599C25.9178 27.1632 26.1054 27.1285 26.2805 27.0577C26.4556 26.987 26.6147 26.8817 26.7482 26.7482C26.8817 26.6147 26.987 26.4556 27.0577 26.2805C27.1285 26.1054 27.1632 25.9178 27.1599 25.729C27.1565 25.5402 27.1152 25.354 27.0383 25.1815C26.9615 25.009 26.8507 24.8537 26.7125 24.725L23.9544 21.965C24.8954 20.58 25.3459 18.9199 25.2341 17.2491C25.1223 15.5784 24.4546 13.9931 23.3373 12.7459C22.2201 11.4986 20.7176 10.6611 19.0692 10.3668C17.4208 10.0724 15.7213 10.3382 14.2414 11.1217C12.7616 11.9053 11.5865 13.1615 10.9034 14.6903C10.2202 16.2191 10.0684 17.9325 10.4719 19.5576C10.8755 21.1827 11.8113 22.6261 13.1303 23.6577C14.4493 24.6893 16.0755 25.2498 17.75 25.25ZM2.28125 12.125C1.90829 12.125 1.5506 12.2732 1.28688 12.5369C1.02316 12.8006 0.875 13.1583 0.875 13.5312C0.875 13.9042 1.02316 14.2619 1.28688 14.5256C1.5506 14.7893 1.90829 14.9375 2.28125 14.9375H6.5C6.87296 14.9375 7.23065 14.7893 7.49437 14.5256C7.75809 14.2619 7.90625 13.9042 7.90625 13.5312C7.90625 13.1583 7.75809 12.8006 7.49437 12.5369C7.23065 12.2732 6.87296 12.125 6.5 12.125H2.28125Z"
      fill={color}
    />
  </svg>
);

export const CreditIcon = ({ color = "white" }) => (
  <svg
    width="30"
    height="30"
    viewBox="0 0 30 30"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M12.5 20H14.375M18.125 20H22.5M2.5 11.25H27.5M2.5 15C2.5 10.5787 2.5 8.3675 3.81625 6.89125C4.02708 6.65458 4.25792 6.43708 4.50875 6.23875C6.0775 5 8.42625 5 13.125 5H16.875C21.5738 5 23.9225 5 25.49 6.2375C25.7417 6.4375 25.9729 6.65542 26.1838 6.89125C27.5 8.36625 27.5 10.5787 27.5 15C27.5 19.4212 27.5 21.6325 26.1838 23.1087C25.9718 23.3459 25.7397 23.5642 25.49 23.7612C23.9225 25 21.575 25 16.875 25H13.125C8.42625 25 6.0775 25 4.50875 23.7625C4.25945 23.565 4.02775 23.3463 3.81625 23.1087C2.5 21.6337 2.5 19.4212 2.5 15Z"
      stroke={color}
      stroke-width="1.5"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
  </svg>
);

export const ProfileIcon = ({ color = "white" }) => (
  <svg
    width="30"
    height="30"
    viewBox="0 0 30 30"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M20 11.25C20 12.5761 19.4732 13.8479 18.5355 14.7855C17.5979 15.7232 16.3261 16.25 15 16.25C13.6739 16.25 12.4021 15.7232 11.4645 14.7855C10.5268 13.8479 10 12.5761 10 11.25C10 9.92392 10.5268 8.65215 11.4645 7.71447C12.4021 6.77678 13.6739 6.25 15 6.25C16.3261 6.25 17.5979 6.77678 18.5355 7.71447C19.4732 8.65215 20 9.92392 20 11.25ZM17.5 11.25C17.5 11.913 17.2366 12.5489 16.7678 13.0178C16.2989 13.4866 15.663 13.75 15 13.75C14.337 13.75 13.7011 13.4866 13.2322 13.0178C12.7634 12.5489 12.5 11.913 12.5 11.25C12.5 10.587 12.7634 9.95107 13.2322 9.48223C13.7011 9.01339 14.337 8.75 15 8.75C15.663 8.75 16.2989 9.01339 16.7678 9.48223C17.2366 9.95107 17.5 10.587 17.5 11.25Z"
      fill={color}
    />
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M15 1.25C7.40625 1.25 1.25 7.40625 1.25 15C1.25 22.5937 7.40625 28.75 15 28.75C22.5937 28.75 28.75 22.5937 28.75 15C28.75 7.40625 22.5937 1.25 15 1.25ZM3.75 15C3.75 17.6125 4.64125 20.0175 6.135 21.9275C7.1843 20.5502 8.53769 19.4339 10.0895 18.6659C11.6414 17.8979 13.3497 17.4989 15.0812 17.5C16.7905 17.498 18.4776 17.8864 20.0139 18.6355C21.5502 19.3846 22.8952 20.4746 23.9462 21.8225C25.0293 20.402 25.7585 18.744 26.0736 16.9857C26.3887 15.2275 26.2805 13.4194 25.7581 11.7112C25.2357 10.003 24.314 8.44381 23.0694 7.16255C21.8247 5.88128 20.2928 4.91482 18.6005 4.34314C16.9081 3.77145 15.104 3.61096 13.3373 3.87497C11.5707 4.13897 9.89224 4.81986 8.44096 5.86132C6.98968 6.90277 5.80726 8.27484 4.99154 9.86399C4.17581 11.4532 3.75023 13.2137 3.75 15ZM15 26.25C12.4174 26.2542 9.91268 25.3657 7.91 23.735C8.71601 22.5808 9.78899 21.6384 11.0376 20.9881C12.2862 20.3378 13.6734 19.9988 15.0812 20C16.4715 19.9988 17.8419 20.3293 19.0787 20.9641C20.3155 21.599 21.383 22.5197 22.1925 23.65C20.1743 25.3338 17.6283 26.2541 15 26.25Z"
      fill={color}
    />
  </svg>
);

export const PaymentIcon = ({ color = "white" }) => (
  <svg
    width="25"
    height="26"
    viewBox="0 0 25 26"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M10.7559 0.5L19.8784 3.35C20.1328 3.42943 20.3552 3.5881 20.513 3.80286C20.6709 4.01762 20.756 4.27721 20.7559 4.54375V6.75H23.2559C23.5874 6.75 23.9053 6.8817 24.1398 7.11612C24.3742 7.35054 24.5059 7.66848 24.5059 8V18C24.5059 18.3315 24.3742 18.6495 24.1398 18.8839C23.9053 19.1183 23.5874 19.25 23.2559 19.25L19.2309 19.2512C18.7471 19.8887 18.1596 20.4512 17.4809 20.9137L10.7559 25.5L4.03086 20.915C3.02151 20.2268 2.19555 19.3025 1.62478 18.2224C1.05402 17.1423 0.755733 15.9391 0.755859 14.7175V4.54375C0.75601 4.27743 0.841218 4.01811 0.999062 3.80361C1.15691 3.5891 1.37914 3.43061 1.63336 3.35125L10.7559 0.5ZM10.7559 3.1175L3.25586 5.4625V14.7175C3.25568 15.4828 3.43117 16.2379 3.76882 16.9247C4.10646 17.6114 4.59724 18.2115 5.20336 18.6787L5.43961 18.8487L10.7559 22.4737L15.4834 19.25H9.50586C9.17434 19.25 8.8564 19.1183 8.62198 18.8839C8.38756 18.6495 8.25586 18.3315 8.25586 18V8C8.25586 7.66848 8.38756 7.35054 8.62198 7.11612C8.8564 6.8817 9.17434 6.75 9.50586 6.75H18.2559V5.4625L10.7559 3.1175ZM10.7559 13V16.75H22.0059V13H10.7559ZM10.7559 10.5H22.0059V9.25H10.7559V10.5Z"
      fill={color}
    />
  </svg>
);

// Sidebars

// Admin Dashboard
export const AnalyticsIcon = () => (
  <svg
    width="35"
    height="35"
    viewBox="0 0 35 35"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M9.47913 25.5208V21.1458M16.7708 25.5208V12.3958M24.0625 25.5208V19.6874M31.3541 8.02075C31.3541 9.18107 30.8932 10.2939 30.0727 11.1143C29.2522 11.9348 28.1394 12.3958 26.9791 12.3958C25.8188 12.3958 24.706 11.9348 23.8855 11.1143C23.0651 10.2939 22.6041 9.18107 22.6041 8.02075C22.6041 6.86043 23.0651 5.74763 23.8855 4.92716C24.706 4.10669 25.8188 3.64575 26.9791 3.64575C28.1394 3.64575 29.2522 4.10669 30.0727 4.92716C30.8932 5.74763 31.3541 6.86043 31.3541 8.02075Z"
      stroke="#787878"
      stroke-width="1.5"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
    <path
      d="M31.3483 16.0416C31.3483 16.0416 31.3541 16.5374 31.3541 17.4999C31.3541 24.0303 31.3541 27.297 29.3256 29.3255C27.2971 31.3541 24.0319 31.3541 17.5 31.3541C10.9696 31.3541 7.7029 31.3541 5.67435 29.3255C3.64581 27.297 3.64581 24.0318 3.64581 17.4999C3.64581 10.9695 3.64581 7.70429 5.67435 5.67429C7.7029 3.64575 10.9681 3.64575 17.5 3.64575H18.9583"
      stroke="#787878"
      stroke-width="1.5"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
  </svg>
);

// export const Name = () => ()
// export const Name = () => ()
// export const Name = () => ()
// export const Name = () => ()
// export const Name = () => ()
// export const Name = () => ()
// export const Name = () => ()
