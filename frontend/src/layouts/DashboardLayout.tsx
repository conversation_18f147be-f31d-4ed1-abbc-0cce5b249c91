import { useState } from "react";
import {
  Home,
  Users,
  Zap,
  Image,
  Palette,
  Wand2,
  Sparkles,
  Settings,
  HelpCircle,
  CreditCard,
  User,
  Menu,
  X,
  Bell,
  Search,
} from "lucide-react";

const DashboardLayout = () => {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [activeItem, setActiveItem] = useState("Home");

  // Mock user data
  const user = {
    name: "<PERSON>",
    email: "<EMAIL>",
    avatar: "JD",
    plan: "Pro",
  };

  const navigationItems = [
    { name: "Home", icon: Home, href: "/" },
    { name: "Community", icon: Users, href: "/community" },
    { name: "AI Tools", icon: Zap, href: "/ai-tools" },
    { name: "Streaming Avatar", icon: User, href: "/streaming-avatar" },
    { name: "Talking Avatar", icon: Users, href: "/talking-avatar" },
    { name: "Voice Translation", icon: Spark<PERSON>, href: "/voice-translation" },
    { name: "Face Swap", icon: Palette, href: "/face-swap" },
    { name: "Image Generating", icon: Image, href: "/image-generating" },
    { name: "Background Removal", icon: Wand2, href: "/background-removal" },
    { name: "Image Enhancer", icon: Sparkles, href: "/image-enhancer" },
    { name: "Video Enhancer", icon: Zap, href: "/video-enhancer" },
    { name: "Sound Enhancer", icon: Wand2, href: "/sound-enhancer" },
  ];

  const bottomItems = [
    { name: "Settings", icon: Settings, href: "/settings" },
    { name: "Help", icon: HelpCircle, href: "/help" },
  ];

  const handleItemClick = (itemName: string) => {
    setActiveItem(itemName);
    setIsMobileMenuOpen(false);
  };

  return (
    <div className="flex h-screen bg-gray-900 text-white">
      {/* Sidebar - Desktop */}
      <aside className="hidden lg:flex flex-col w-64 bg-gray-800 border-r border-gray-700">
        {/* Logo */}
        <div className="p-6 border-b border-gray-700">
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-lg">M</span>
            </div>
            <span className="text-xl font-bold text-white">MIRAGIC</span>
          </div>
        </div>

        {/* Navigation */}
        <div className="flex-1 py-4 overflow-y-auto">
          <nav className="px-3 space-y-1">
            {navigationItems.map((item) => {
              const Icon = item.icon;
              const isActive = activeItem === item.name;
              return (
                <button
                  key={item.name}
                  onClick={() => handleItemClick(item.name)}
                  className={`w-full flex items-center px-3 py-2.5 text-sm font-medium rounded-lg transition-all duration-200 ${
                    isActive
                      ? "bg-blue-600 text-white shadow-lg"
                      : "text-gray-300 hover:bg-gray-700 hover:text-white"
                  }`}
                >
                  <Icon className="w-5 h-5 mr-3" />
                  {item.name}
                </button>
              );
            })}
          </nav>
        </div>

        {/* Bottom Navigation */}
        <div className="p-3 border-t border-gray-700">
          <nav className="space-y-1">
            {bottomItems.map((item) => {
              const Icon = item.icon;
              const isActive = activeItem === item.name;
              return (
                <button
                  key={item.name}
                  onClick={() => handleItemClick(item.name)}
                  className={`w-full flex items-center px-3 py-2.5 text-sm font-medium rounded-lg transition-all duration-200 ${
                    isActive
                      ? "bg-blue-600 text-white shadow-lg"
                      : "text-gray-300 hover:bg-gray-700 hover:text-white"
                  }`}
                >
                  <Icon className="w-5 h-5 mr-3" />
                  {item.name}
                </button>
              );
            })}
          </nav>
        </div>

        {/* User Profile */}
        <div className="p-4 border-t border-gray-700">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white font-bold">
              {user.avatar}
            </div>
            <div className="flex-1 min-w-0">
              <p className="text-sm font-medium text-white truncate">
                {user.name}
              </p>
              <p className="text-xs text-gray-400 truncate">{user.email}</p>
            </div>
          </div>
        </div>
      </aside>

      {/* Mobile Menu Overlay */}
      {isMobileMenuOpen && (
        <div
          className="lg:hidden fixed inset-0 z-50 bg-black bg-opacity-50"
          onClick={() => setIsMobileMenuOpen(false)}
        >
          <div
            className="fixed inset-y-0 left-0 w-64 bg-gray-800 border-r border-gray-700"
            onClick={(e) => e.stopPropagation()}
          >
            {/* Mobile Logo */}
            <div className="p-6 border-b border-gray-700 flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                  <span className="text-white font-bold text-lg">M</span>
                </div>
                <span className="text-xl font-bold text-white">MIRAGIC</span>
              </div>
              <button
                onClick={() => setIsMobileMenuOpen(false)}
                className="p-2 text-gray-400 hover:text-white"
              >
                <X className="w-5 h-5" />
              </button>
            </div>

            {/* Mobile Navigation */}
            <div className="flex-1 py-4 overflow-y-auto">
              <nav className="px-3 space-y-1">
                {navigationItems.map((item) => {
                  const Icon = item.icon;
                  const isActive = activeItem === item.name;
                  return (
                    <button
                      key={item.name}
                      onClick={() => handleItemClick(item.name)}
                      className={`w-full flex items-center px-3 py-2.5 text-sm font-medium rounded-lg transition-all duration-200 ${
                        isActive
                          ? "bg-blue-600 text-white shadow-lg"
                          : "text-gray-300 hover:bg-gray-700 hover:text-white"
                      }`}
                    >
                      <Icon className="w-5 h-5 mr-3" />
                      {item.name}
                    </button>
                  );
                })}
              </nav>
            </div>

            {/* Mobile Bottom Navigation */}
            <div className="p-3 border-t border-gray-700">
              <nav className="space-y-1">
                {bottomItems.map((item) => {
                  const Icon = item.icon;
                  const isActive = activeItem === item.name;
                  return (
                    <button
                      key={item.name}
                      onClick={() => handleItemClick(item.name)}
                      className={`w-full flex items-center px-3 py-2.5 text-sm font-medium rounded-lg transition-all duration-200 ${
                        isActive
                          ? "bg-blue-600 text-white shadow-lg"
                          : "text-gray-300 hover:bg-gray-700 hover:text-white"
                      }`}
                    >
                      <Icon className="w-5 h-5 mr-3" />
                      {item.name}
                    </button>
                  );
                })}
              </nav>
            </div>

            {/* Mobile User Profile */}
            <div className="p-4 border-t border-gray-700">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white font-bold">
                  {user.avatar}
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-white truncate">
                    {user.name}
                  </p>
                  <p className="text-xs text-gray-400 truncate">{user.email}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Main Content Area */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Header */}
        <header className="bg-gray-800 border-b border-gray-700 px-4 lg:px-6 py-4">
          <div className="flex items-center justify-between">
            {/* Mobile Menu Button */}
            <div className="flex items-center space-x-4">
              <button
                onClick={() => setIsMobileMenuOpen(true)}
                className="lg:hidden p-2 text-gray-400 hover:text-white"
              >
                <Menu className="w-6 h-6" />
              </button>

              {/* Page Title */}
              <h1 className="text-xl lg:text-2xl font-bold text-white">
                {activeItem}
              </h1>
            </div>

            {/* Header Actions */}
            <div className="flex items-center space-x-4">
              {/* Search Bar - Hidden on small screens */}
              <div className="hidden md:flex relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <input
                  type="text"
                  placeholder="Search..."
                  className="bg-gray-700 text-white placeholder-gray-400 pl-10 pr-4 py-2 rounded-lg border border-gray-600 focus:border-blue-500 focus:outline-none w-64"
                />
              </div>

              {/* Notifications */}
              <button className="p-2 text-gray-400 hover:text-white relative">
                <Bell className="w-5 h-5" />
                <span className="absolute top-1 right-1 w-2 h-2 bg-red-500 rounded-full"></span>
              </button>

              {/* Credits */}
              <div className="hidden sm:flex items-center space-x-2 bg-gray-700 px-3 py-2 rounded-lg">
                <CreditCard className="w-4 h-4 text-blue-400" />
                <span className="text-sm text-white">Credits: 1</span>
              </div>

              {/* User Avatar - Desktop */}
              <div className="hidden lg:flex items-center space-x-2">
                <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white font-bold text-sm">
                  {user.avatar}
                </div>
              </div>
            </div>
          </div>
        </header>

        {/* Main Content */}
        <main className="flex-1 overflow-y-auto bg-gray-900 p-4 lg:p-6">
          <div className="max-w-7xl mx-auto">
            {/* Welcome Section */}
            <div className="mb-8">
              <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl p-6 text-white">
                <h2 className="text-2xl lg:text-3xl font-bold mb-2">
                  Welcome back, {user.name}!
                </h2>
                <p className="text-blue-100 mb-4">
                  Ready to create something amazing with AI?
                </p>
                <div className="flex flex-wrap gap-4">
                  <div className="bg-white/20 backdrop-blur-sm rounded-lg p-4 flex-1 min-w-48">
                    <h3 className="font-semibold mb-1">Credits Available</h3>
                    <p className="text-2xl font-bold">1</p>
                  </div>
                  <div className="bg-white/20 backdrop-blur-sm rounded-lg p-4 flex-1 min-w-48">
                    <h3 className="font-semibold mb-1">Current Plan</h3>
                    <p className="text-2xl font-bold">{user.plan}</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Quick Actions */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
              <div className="bg-gray-800 rounded-xl p-6 border border-gray-700 hover:border-blue-500 transition-colors cursor-pointer">
                <div className="w-12 h-12 bg-orange-500 rounded-lg flex items-center justify-center mb-4">
                  <Image className="w-6 h-6 text-white" />
                </div>
                <h3 className="text-lg font-semibold text-white mb-2">
                  Generate Image
                </h3>
                <p className="text-gray-400 text-sm">
                  Create stunning images with AI
                </p>
              </div>

              <div className="bg-gray-800 rounded-xl p-6 border border-gray-700 hover:border-green-500 transition-colors cursor-pointer">
                <div className="w-12 h-12 bg-green-500 rounded-lg flex items-center justify-center mb-4">
                  <Users className="w-6 h-6 text-white" />
                </div>
                <h3 className="text-lg font-semibold text-white mb-2">
                  Talking Avatar
                </h3>
                <p className="text-gray-400 text-sm">
                  Create AI-powered avatars
                </p>
              </div>

              <div className="bg-gray-800 rounded-xl p-6 border border-gray-700 hover:border-purple-500 transition-colors cursor-pointer">
                <div className="w-12 h-12 bg-purple-500 rounded-lg flex items-center justify-center mb-4">
                  <Sparkles className="w-6 h-6 text-white" />
                </div>
                <h3 className="text-lg font-semibold text-white mb-2">
                  Voice Translation
                </h3>
                <p className="text-gray-400 text-sm">
                  Translate voice in real-time
                </p>
              </div>

              <div className="bg-gray-800 rounded-xl p-6 border border-gray-700 hover:border-blue-500 transition-colors cursor-pointer">
                <div className="w-12 h-12 bg-blue-500 rounded-lg flex items-center justify-center mb-4">
                  <Wand2 className="w-6 h-6 text-white" />
                </div>
                <h3 className="text-lg font-semibold text-white mb-2">
                  Face Swap
                </h3>
                <p className="text-gray-400 text-sm">
                  Swap faces in images seamlessly
                </p>
              </div>
            </div>

            {/* Recent Activity or Additional Content */}
            <div className="bg-gray-800 rounded-xl p-6 border border-gray-700">
              <h3 className="text-xl font-bold text-white mb-4">
                Recent Activity
              </h3>
              <div className="space-y-4">
                <div className="flex items-center space-x-4 p-4 bg-gray-700 rounded-lg">
                  <div className="w-10 h-10 bg-blue-500 rounded-lg flex items-center justify-center">
                    <Image className="w-5 h-5 text-white" />
                  </div>
                  <div className="flex-1">
                    <p className="text-white font-medium">
                      Generated new image
                    </p>
                    <p className="text-gray-400 text-sm">2 hours ago</p>
                  </div>
                </div>
                <div className="flex items-center space-x-4 p-4 bg-gray-700 rounded-lg">
                  <div className="w-10 h-10 bg-green-500 rounded-lg flex items-center justify-center">
                    <Users className="w-5 h-5 text-white" />
                  </div>
                  <div className="flex-1">
                    <p className="text-white font-medium">
                      Created talking avatar
                    </p>
                    <p className="text-gray-400 text-sm">5 hours ago</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </main>
      </div>
    </div>
  );
};

export default DashboardLayout;

//
//
//
// import { useState } from "react";
// import { Outlet, Link, useNavigate } from "react-router-dom";
// import { Button } from "@/components/ui/button";
// import AdminNav from "@/components/admin/AdminNav";
// import DashboardNav from "@/components/dashboard/DashboardNav";
// import { useApp } from "@/contexts/useApp";
// import { Spinner } from "@/components/ui/spinner";

// interface DashboardLayoutProps {
//   isAdmin?: boolean;
// }

// const DashboardLayout = ({ isAdmin = false }: DashboardLayoutProps) => {
//   const navigate = useNavigate();
//   const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
//   let { user, logout, isLoading } = useApp();
//   console.log("user", user);
//   // If user data is loading, show a loading spinner
//   if (isLoading) {
//     return (
//       <div className="flex items-center justify-center h-screen">
//         <Spinner size="lg" />
//         <span className="ml-2">Loading...</span>
//       </div>
//     );
//   }

//   // If no user is found, redirect to login
//   if (!user) {
//     // navigate("/auth/login");
//     // return null;
//     user = {
//       email: "<EMAIL>",
//       name: "azhar",
//       plan: "Free",
//       role: "USER",
//       credits: 1,
//       id: "1",
//       profile: {
//         firstName: "Azh",
//         lastName: "ar",
//         avatarUrl: "n",
//         id: "123",
//         userId: "123",
//       },
//       createdAt: new Date().toISOString(),
//       updatedAt: new Date().toISOString(),
//     };
//   }

//   // The isAdmin prop is used to determine which navigation to show

//   // Path checking is now handled by the DashboardNav component

//   // Handle logout
//   const handleLogout = () => {
//     logout();
//     navigate("/");
//   };

//   return (
//     <div className="flex h-screen bg-background">
//       {/* Sidebar - Desktop */}
//       <aside className="hidden md:flex flex-col w-64 border-r">
//         {/* Logo */}
//         <div className="p-6 border-b">
//           <Link to="/" className="flex items-center space-x-2">
//             <span className="text-xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 text-transparent bg-clip-text">
//               Miragic-AI
//             </span>
//           </Link>
//         </div>

//         {/* Navigation */}
//         <div className="flex-1 py-6 px-4 overflow-y-auto">
//           {isAdmin ? <AdminNav /> : <DashboardNav />}
//         </div>

//         {/* User Profile */}
//         <div className="p-4 border-t">
//           <div className="flex items-center space-x-3 p-2">
//             <div className="h-8 w-8 rounded-full bg-primary/10 flex items-center justify-center text-primary font-bold">
//               {user?.name?.charAt(0).toUpperCase()}
//             </div>
//             <div className="flex-1 min-w-0">
//               <p className="text-sm font-medium truncate">{user?.name}</p>
//               <p className="text-xs text-muted-foreground truncate">
//                 {user?.email}
//               </p>
//               <p className="text-xs bg-primary/10 text-primary px-1.5 py-0.5 rounded-full inline-block mt-1">
//                 {user.plan} Plan
//               </p>
//             </div>
//             <button
//               onClick={handleLogout}
//               className="text-muted-foreground hover:text-foreground"
//             >
//               <svg
//                 xmlns="http://www.w3.org/2000/svg"
//                 fill="none"
//                 viewBox="0 0 24 24"
//                 strokeWidth={1.5}
//                 stroke="currentColor"
//                 className="w-5 h-5"
//               >
//                 <path
//                   strokeLinecap="round"
//                   strokeLinejoin="round"
//                   d="M15.75 9V5.25A2.25 2.25 0 0013.5 3h-6a2.25 2.25 0 00-2.25 2.25v13.5A2.25 2.25 0 007.5 21h6a2.25 2.25 0 002.25-2.25V15m3 0l3-3m0 0l-3-3m3 3H9"
//                 />
//               </svg>
//             </button>
//           </div>
//         </div>
//       </aside>

//       {/* Mobile Header */}
//       <div className="md:hidden fixed top-0 left-0 right-0 z-50 bg-background border-b">
//         <div className="flex items-center justify-between p-4">
//           <Link to="/" className="flex items-center space-x-2">
//             <span className="text-xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 text-transparent bg-clip-text">
//               Miragic-AI
//             </span>
//           </Link>

//           <button
//             onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
//             className="p-2"
//           >
//             <svg
//               xmlns="http://www.w3.org/2000/svg"
//               fill="none"
//               viewBox="0 0 24 24"
//               strokeWidth={1.5}
//               stroke="currentColor"
//               className="w-6 h-6"
//             >
//               {isMobileMenuOpen ? (
//                 <path
//                   strokeLinecap="round"
//                   strokeLinejoin="round"
//                   d="M6 18L18 6M6 6l12 12"
//                 />
//               ) : (
//                 <path
//                   strokeLinecap="round"
//                   strokeLinejoin="round"
//                   d="M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5"
//                 />
//               )}
//             </svg>
//           </button>
//         </div>

//         {/* Mobile Menu */}
//         {isMobileMenuOpen && (
//           <div className="bg-background border-t p-4">
//             {isAdmin ? (
//               <div onClick={() => setIsMobileMenuOpen(false)}>
//                 <AdminNav />
//               </div>
//             ) : (
//               <div onClick={() => setIsMobileMenuOpen(false)}>
//                 <DashboardNav />
//               </div>
//             )}

//             <div className="mt-4 pt-4 border-t">
//               <div className="flex items-center space-x-3 p-2">
//                 <div className="h-8 w-8 rounded-full bg-primary/10 flex items-center justify-center text-primary font-bold">
//                   {user.name.charAt(0).toUpperCase()}
//                 </div>
//                 <div className="flex-1 min-w-0">
//                   <p className="text-sm font-medium truncate">{user.name}</p>
//                   <p className="text-xs text-muted-foreground truncate">
//                     {user.email}
//                   </p>
//                   <p className="text-xs bg-primary/10 text-primary px-1.5 py-0.5 rounded-full inline-block mt-1">
//                     {user.plan} Plan
//                   </p>
//                 </div>
//               </div>
//               <Button
//                 variant="outline"
//                 className="w-full mt-2"
//                 onClick={handleLogout}
//               >
//                 Log out
//               </Button>
//             </div>
//           </div>
//         )}
//       </div>

//       {/* Main Content */}
//       <main className="flex-1 overflow-y-auto">
//         <div className="container mx-auto px-4 py-6 md:py-8 mt-14 md:mt-0">
//           <Outlet />
//         </div>
//       </main>
//     </div>
//   );
// };

// export default DashboardLayout;
