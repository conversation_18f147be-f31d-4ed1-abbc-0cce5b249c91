import GradientText from "@/components/common/GradientText";
import BottomFooter from "@/components/footer/BottomFooter";
import UpdatesSection from "@/components/footer/UpdatesSection";
import { Text } from "@/components/ui/text";
import { Link } from "react-router-dom";

const menuSections = [
  {
    title: "Products",
    links: [
      {
        name: "Background Remover",
        link: "/ai-tool/background-remover",
      },
      {
        name: "Virtual Try On",
        link: "/ai-tool/virtual-try-on",
      },
      {
        name: "Speed Painting",
        link: "/ai-tool/speedpainting",
      },
      {
        name: "Image to video",
        link: "/coming-soon",
      },
      {
        name: "Image generator",
        link: "/coming-soon",
      },
    ],
  },
  {
    title: "Resources",
    links: [
      {
        name: "API",
        link: "/docs-api",
      },
      {
        name: "Case Studies",
        link: "/use-cases",
      },
      {
        name: "Use Cases",
        link: "/use-cases",
      },
    ],
  },
  {
    title: "Company",
    links: [
      {
        name: "About",
        link: "/about",
      },
      {
        name: "Blog",
        link: "/blog",
      },
      {
        name: "Pricing",
        link: "/pricing",
      },
      {
        name: "Research",
        link: "/coming-soon",
      },
      {
        name: "Affiliate program",
        link: "/coming-soon",
      },
    ],
  },
];

const NewFooter = () => {
  return (
    <footer className="bg-[#1A1A1A] text-white pt-10 px-4 md:px-20">
      <div className="max-w-7xl mx-auto">
        <div className="grid grid-cols-1 md:grid-cols-5 gap-8 mb-5">
          <div className="md:col-span-2 flex flex-col items-start justify-between space-y-6">
            <div className="">
              <img
                loading="lazy"
                src="/png/new_miragic_logo.png"
                className="w-[170px]"
              />
              <UpdatesSection />
            </div>
          </div>

          {menuSections.map((section, i) => (
            <div key={i} className="flex flex-col space-y-3">
              <Text
                variant={"card_list_title"}
                className="text-lg font-semibold mb-2"
              >
                <GradientText>{section.title}</GradientText>
              </Text>
              {section.links.map((link, i) => (
                <Link
                  key={link.link + i}
                  to={link.link}
                  className="text-gray-300 hover:text-[#00A3FF] transition text-sm"
                >
                  {link.name}
                </Link>
              ))}
            </div>
          ))}
        </div>
        <div className="border-t-2 border-white/15 flex justify-between gap-5 items-center md:flex-row flex-col py-6">
          <BottomFooter />
        </div>
      </div>
    </footer>
  );
};

export default NewFooter;
