import { Outlet } from "react-router-dom";

const AuthLayout = () => {
  return <Outlet />;
  // return (
  //   <div className="min-h-screen flex flex-col bg-muted/30">
  //     {/* Header */}
  //     <header className="py-6 px-4 sm:px-6 lg:px-8">
  //       <div className="container mx-auto">
  //         <Link to="/" className="flex items-center space-x-2">
  //           <span className="text-xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 text-transparent bg-clip-text">
  //             Miragic-AI
  //           </span>
  //         </Link>
  //       </div>
  //     </header>

  //     {/* Main Content */}
  //     <main className="flex-1 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
  //       <div className="w-full max-w-md space-y-8">
  //         <Outlet />
  //       </div>
  //     </main>

  //     {/* Footer */}
  //     <footer className="py-6 px-4 sm:px-6 lg:px-8">
  //       <div className="container mx-auto text-center text-sm text-muted-foreground">
  //         <p>© {new Date().getFullYear()} Miragic-AI. All rights reserved.</p>
  //       </div>
  //     </footer>
  //   </div>
  // );
};

export default AuthLayout;
