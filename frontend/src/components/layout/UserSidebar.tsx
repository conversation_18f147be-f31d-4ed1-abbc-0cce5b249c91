import { useEffect } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import {
  BackgroundRemoverIcon,
  CreditIcon,
  HomeIconSidebar,
  PaymentIcon,
  ProfileIcon,
  SpeedpaintingIcon,
  VirtualTryOnIcon,
} from "@/lib/icons";
import type { NavItem, UserSidebarProps } from "@/types/layouts";
import { NavSection } from "./NavSection";
import { LogoSection } from "./LogoSection";

const generalItems: NavItem[] = [
  {
    name: "Dashboard",
    href: "/dashboard",
    icon: HomeIconSidebar,
  },
];

const aiToolsItems: NavItem[] = [
  {
    name: "Background Remover",
    href: "/ai-tool/background-remover",
    icon: BackgroundRemoverIcon,
  },
  {
    name: "Virtual Try On",
    href: "/ai-tool/virtual-try-on",
    icon: VirtualTryOnIcon,
  },
  {
    name: "Speed Painting",
    href: "/ai-tool/speedpainting",
    icon: SpeedpaintingIcon,
  },
];

const accountItems: NavItem[] = [
  {
    name: "Credit",
    href: "/account/credit",
    icon: CreditIcon,
  },
  {
    name: "Profile",
    href: "/dashboard/profile",
    icon: ProfileIcon,
  },
  {
    name: "Payments",
    href: "/account/transaction",
    icon: PaymentIcon,
  },
];

// Main Component
const UserSidebar = ({
  activeItem,
  setActiveItem,
  setIsMobileMenuOpen,
  isExpanded = false,
  isMobile = false,
  toggleSidebar,
}: UserSidebarProps) => {
  const navigate = useNavigate();
  const location = useLocation();

  useEffect(() => {
    const currentPath = location.pathname;
    const allItems = [...generalItems, ...aiToolsItems, ...accountItems];
    const matchingItem = allItems.find((item) => currentPath === item.href);
    if (matchingItem) {
      setActiveItem(matchingItem.name);
    }
  }, [location.pathname, setActiveItem]);

  const handleItemClick = (itemName: string, href: string) => {
    setActiveItem(itemName);
    if (setIsMobileMenuOpen && isMobile) {
      setIsMobileMenuOpen(false);
    }
    navigate(href);
  };

  return (
    <aside className="flex flex-col bg-[#1A1A1A] h-full border-r border-white/15">
      <LogoSection
        isExpanded={isExpanded}
        isMobile={isMobile}
        toggleSidebar={toggleSidebar}
      />

      <div className="flex-1 overflow-y-auto overflow-x-hidden bg-[#1A1A1A] ">
        <NavSection
          title="GENERAL"
          items={generalItems}
          activeItem={activeItem}
          isExpanded={isExpanded}
          isMobile={isMobile}
          onItemClick={handleItemClick}
        />

        <NavSection
          title="AI TOOLS"
          items={aiToolsItems}
          activeItem={activeItem}
          isExpanded={isExpanded}
          isMobile={isMobile}
          onItemClick={handleItemClick}
        />

        <NavSection
          title="ACCOUNT"
          items={accountItems}
          activeItem={activeItem}
          isExpanded={isExpanded}
          isMobile={isMobile}
          onItemClick={handleItemClick}
        />

        <div className="h-6" />
      </div>
    </aside>
  );
};

export default UserSidebar;
