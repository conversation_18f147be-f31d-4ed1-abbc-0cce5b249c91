import { PanelRightClose, PanelRightOpen } from "lucide-react";
import { Link } from "react-router-dom";

export const LogoSection = ({
  isExpanded,
  isMobile,
  toggleSidebar,
}: {
  isExpanded: boolean;
  isMobile: boolean;
  toggleSidebar: () => void;
}) => (
  <div className="flex justify-center bg-[#1A1A1A] ">
    <div className="flex items-center gap-3 mt-6">
      {!isMobile && !isExpanded ? (
        <div className="flex flex-col items-center gap-4">
          <Link to="/">
            <img
              loading="lazy"
              src="/png/about_logo.png"
              className="w-[40px] h-[40px]"
              alt="Logo"
            />
          </Link>
          <button
            onClick={toggleSidebar}
            className="hidden lg:flex items-center justify-center p-2 text-gray-400 hover:text-white rounded-lg hover:bg-gray-700 transition-colors"
            title={isExpanded ? "Collapse sidebar" : "Expand sidebar"}
          >
            <PanelRightClose className="w-5 h-5" />
          </button>
        </div>
      ) : (
        <>
          <Link to="/">
            <img
              loading="lazy"
              src="/png/new_miragic_logo.png"
              className="w-[180px] h-[40px] pl-3"
              alt="Logo"
            />
          </Link>
          <button
            onClick={toggleSidebar}
            className="hidden lg:flex items-center justify-center p-2 text-gray-400 hover:text-white rounded-lg hover:bg-gray-700 transition-colors"
            title={isExpanded ? "Collapse sidebar" : "Expand sidebar"}
          >
            <PanelRightOpen className="w-5 h-5" />
          </button>
        </>
      )}
    </div>
  </div>
);
