import { cn } from "@/lib/utils";
import React from "react";

const Section = ({
  children,
  className,
  style,
  containerClassName,
}: {
  className?: string;
  containerClassName?: string;
  children: React.ReactNode;
  style?: React.CSSProperties;
}) => {
  return (
    <div
      style={style}
      className={cn(
        "mx-auto flex flex-col items-center justify-center",
        className
      )}
    >
      <div className={cn("px-5 max-w-[1088px] w-full", containerClassName)}>
        {children}
      </div>
    </div>
  );
};

export default Section;
