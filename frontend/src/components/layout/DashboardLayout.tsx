import { useState } from "react";
import { X } from "lucide-react";
import UserSidebar from "./UserSidebar";
import UserHeader from "./UserHeader";
import { Outlet } from "react-router-dom";

const DashboardLayout = () => {
  const [activeItem, setActiveItem] = useState("Home");
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isSidebarExpanded, setIsSidebarExpanded] = useState(() => {
    const stored = localStorage.getItem("sidebar-expanded");
    return stored === "true";
  });

  // const toggleSidebar = () => {
  //   setIsSidebarExpanded(!isSidebarExpanded);
  // };

  const toggleSidebar = () => {
    setIsSidebarExpanded((prev) => {
      const next = !prev;
      localStorage.setItem("sidebar-expanded", String(next));
      return next;
    });
  };

  return (
    <div className="flex h-screen bg-[#1A1A1A]">
      {/* Sidebar Component - Takes actual space */}
      <div
        className={`hidden lg:flex transition-all duration-300 ${
          isSidebarExpanded ? "w-64" : "w-20"
        }`}
      >
        <UserSidebar
          activeItem={activeItem}
          setActiveItem={setActiveItem}
          isMobileMenuOpen={isMobileMenuOpen}
          setIsMobileMenuOpen={setIsMobileMenuOpen}
          isExpanded={isSidebarExpanded}
          toggleSidebar={toggleSidebar}
        />
      </div>

      {/* Main Content Area */}
      <div
        className={`flex-1 flex flex-col overflow-hidden ${
          isSidebarExpanded ? "ml-[6px]" : ""
        }`}
      >
        {/* Header Component with Toggle Button */}
        <UserHeader
          setIsMobileMenuOpen={setIsMobileMenuOpen}
          // toggleSidebar={toggleSidebar}
          // isSidebarExpanded={isSidebarExpanded}
        />

        {/* Main Content */}
        <main className="flex-1 overflow-auto px-4 bg-[#1A1A1A]">
          <Outlet />
        </main>
      </div>

      {/* Mobile Menu Overlay - Only for mobile */}
      {isMobileMenuOpen && (
        <div
          className="fixed inset-0 z-50 lg:hidden bg-black bg-opacity-50"
          onClick={() => setIsMobileMenuOpen(false)}
        >
          <div
            className="w-64 h-full bg-[#1A1A1A] shadow-xl"
            onClick={(e) => e.stopPropagation()}
          >
            {/* Mobile Close Button */}
            <div className="flex justify-end p-4">
              <button
                onClick={() => setIsMobileMenuOpen(false)}
                className="text-gray-400 hover:text-white rounded-lg"
              >
                <X className="w-6 h-6" />
              </button>
            </div>

            <UserSidebar
              activeItem={activeItem}
              setActiveItem={setActiveItem}
              isMobileMenuOpen={isMobileMenuOpen}
              setIsMobileMenuOpen={setIsMobileMenuOpen}
              isMobile={true}
              isExpanded={isSidebarExpanded}
              toggleSidebar={toggleSidebar}
            />
          </div>
        </div>
      )}
    </div>
  );
};

export default DashboardLayout;

// import { useState } from "react";
// import { X } from "lucide-react";
// import UserSidebar from "./UserSidebar";
// import UserHeader from "./UserHeader";
// import { Outlet } from "react-router-dom";

// const DashboardLayout = () => {
//   const [activeItem, setActiveItem] = useState("Home");
//   const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
//   const [isSidebarExpanded, setIsSidebarExpanded] = useState(false);

//   const toggleSidebar = () => {
//     setIsSidebarExpanded(!isSidebarExpanded);
//   };

//   return (
//     <div className="flex h-screen bg-[#1A1A1A]">
//       {/* Sidebar Component - Takes actual space */}
//       <div
//         className={`hidden lg:flex transition-all duration-300 ${
//           isSidebarExpanded ? "w-64" : "w-20"
//         }`}
//       >
//         <UserSidebar
//           activeItem={activeItem}
//           setActiveItem={setActiveItem}
//           isMobileMenuOpen={isMobileMenuOpen}
//           setIsMobileMenuOpen={setIsMobileMenuOpen}
//           isExpanded={isSidebarExpanded}
//           toggleSidebar={toggleSidebar}
//         />
//       </div>

//       {/* Main Content Area */}
//       <div className={`flex-1 flex flex-col overflow-hidden ${isSidebarExpanded ? "ml-[6px]": ""}`}>
//         {/* Header Component with Toggle Button */}
//         <UserHeader
//           setIsMobileMenuOpen={setIsMobileMenuOpen}
//           // toggleSidebar={toggleSidebar}
//           // isSidebarExpanded={isSidebarExpanded}
//         />

//         {/* Main Content */}
//         <main className="flex-1 overflow-auto px-4 bg-[#1A1A1A]">
//           <Outlet />
//         </main>
//       </div>

//       {/* Mobile Menu Overlay - Only for mobile */}
//       {isMobileMenuOpen && (
//         <div
//           className="fixed inset-0 z-50 lg:hidden bg-black bg-opacity-50"
//           onClick={() => setIsMobileMenuOpen(false)}
//         >
//           <div
//             className="w-64 h-full bg-[#1A1A1A] shadow-xl"
//             onClick={(e) => e.stopPropagation()}
//           >
//             {/* Mobile Close Button */}
//             <div className="flex justify-end p-4">
//               <button
//                 onClick={() => setIsMobileMenuOpen(false)}
//                 className="text-gray-400 hover:text-white rounded-lg"
//               >
//                 <X className="w-6 h-6" />
//               </button>
//             </div>

//             <UserSidebar
//               activeItem={activeItem}
//               setActiveItem={setActiveItem}
//               isMobileMenuOpen={isMobileMenuOpen}
//               setIsMobileMenuOpen={setIsMobileMenuOpen}
//               isMobile={true}
//               isExpanded={isSidebarExpanded}
//               toggleSidebar={toggleSidebar}
//             />
//           </div>
//         </div>
//       )}
//     </div>
//   );
// };

// export default DashboardLayout;
