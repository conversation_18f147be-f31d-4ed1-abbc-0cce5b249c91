import type { NavItem } from "@/types/layouts";

export const NavButton = ({ item, isActive, isExpanded, isMobile, onClick }: {
  item: NavItem;
  isActive: boolean;
  isExpanded: boolean;
  isMobile: boolean;
  onClick: () => void;
}) => {
  const activeColor = "#9855FF";
  const IconComponent = item.icon;

  return (
    <button
      onClick={onClick}
      className={`group relative w-full text-left px-3 py-2 text-sm font-medium transition-all duration-500 rounded-lg cursor-pointer flex items-center ${
        isActive ? `text-[${activeColor}]` : "text-gray-300 hover:text-gray-100 hover:bg-gray-700/50"
      }`}
      title={!isMobile && !isExpanded ? item.name : undefined}
    >
      <div className="relative z-10">
        <IconComponent color={isActive ? activeColor : "white"} />
      </div>
      <div className={`
        absolute left-[44px] overflow-hidden transition-all duration-500 ease-in-out
        ${!isMobile && !isExpanded ? 'max-w-0' : 'max-w-[220px]'}
      `}>
        <span className={`
          inline-block whitespace-nowrap transform transition-all duration-500 ease-in-out
          ${!isMobile && !isExpanded ? 'translate-x-[200%]' : 'translate-x-0'}
        `}>
          {item.name}
        </span>
      </div>
      {!isMobile && !isExpanded && (
        <div className="absolute left-full ml-2 px-2 py-1 text-white text-xs rounded transition-all duration-500 pointer-events-none whitespace-nowrap z-50">
          {item.name}
        </div>
      )}
    </button>
  );
};
