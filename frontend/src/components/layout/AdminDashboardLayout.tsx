import { useState } from "react";
import { Outlet } from "react-router-dom";
import AdminSidebar from "./AdminSidebar";
import AdminHeader from "./AdminHeader";
import { HeaderConfigProvider } from "@/contexts/AdminHeaderContext";

const AdminDashboardLayout = () => {
  const [activeItem, setActiveItem] = useState("Home");
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  return (
    <HeaderConfigProvider>
      <div className="flex h-screen bg-gray-900 text-white">
        {/* Sidebar Component */}
        <AdminSidebar
          activeItem={activeItem}
          setActiveItem={setActiveItem}
          isMobileMenuOpen={isMobileMenuOpen}
          setIsMobileMenuOpen={setIsMobileMenuOpen}
        />

        {/* Main Content Area */}
        <div className="flex-1 flex flex-col overflow-hidden bg-[#1E1E1E]">
          {/* Header Component */}
          <AdminHeader setIsMobileMenuOpen={setIsMobileMenuOpen} />

          {/* Main Content */}
          <main className="flex-1 overflow-y-auto p-4 lg:p-6">
            <Outlet />
          </main>
        </div>
      </div>
    </HeaderConfigProvider>
  );
};

export default AdminDashboardLayout;
