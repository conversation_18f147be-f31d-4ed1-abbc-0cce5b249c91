import type { NavSectionProps } from "@/types/layouts";
import { NavButton } from "./NavButton";

export const NavSection = ({ title, items, activeItem, isExpanded, isMobile, onItemClick }: NavSectionProps) => (
  <div className={`px-3 pt-3 mb-4`}>
    {(isMobile || isExpanded) && (
      <h3 className="text-xs font-medium text-gray-500 uppercase tracking-wider mb-2">
        {title}
      </h3>
    )}
    <nav className={`space-y-1 border-b-2 border-white/15 ${!isExpanded && !isMobile ? "pb-6" : ""}`}>
      {items.map((item) => (
        <NavButton
          key={item.name}
          item={item}
          isActive={activeItem === item.name}
          isExpanded={isExpanded}
          isMobile={isMobile}
          onClick={() => onItemClick(item.name, item.href)}
        />
      ))}
    </nav>
  </div>
);
