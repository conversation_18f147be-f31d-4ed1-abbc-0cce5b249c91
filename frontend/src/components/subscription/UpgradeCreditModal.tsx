import React, { useState } from "react";
import { X } from "lucide-react";

// Define props interface
interface UpgradeCreditModalProps {
  isOpen?: boolean;
  onClose?: () => void;
}

// Define sub-item type (string or object)
type SubItem = string | { name: string; credit: string; minimumCost: string };

// Define pricing item interface
interface PricingItem {
  product: string;
  subItems: SubItem[];
  credit?: string;
  minimumCost?: string;
  isHeader: boolean;
}

// Define active tab type
type ActiveTab = "Credit cost" | "Credit usage";

const UpgradeCreditModal: React.FC<UpgradeCreditModalProps> = ({
  isOpen = true,
  onClose = () => {},
}) => {
  const [activeTab, setActiveTab] = useState<ActiveTab>("Credit cost");

  if (!isOpen) return null;

  const pricingData: PricingItem[] = [
    {
      product: "Products",
      subItems: ["-"],
      credit: "check API pricing",
      minimumCost: "free on web",
      isHeader: true,
    },
    {
      product: "Face Swap",
      subItems: [
        { name: "Image", credit: "4 credits", minimumCost: "-" },
        {
          name: "Video",
          credit: "min cost 10 credits",
          minimumCost: "min cost 10 credits",
        },
      ],
      isHeader: false,
    },
    {
      product: "Live Face Swap",
      subItems: ["-"],
      credit: "min cost 10 credits",
      minimumCost: "min cost 10 credits",
      isHeader: false,
    },
  ];

  return (
    <div className="fixed inset-0 bg-black/90 flex items-center justify-center z-50 p-4">
      <div className="bg-gray-800 rounded-2xl p-6 w-full max-w-6xl max-h-[90vh] flex flex-col">
        {/* Header */}
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-2xl font-semibold text-white">Credits</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-white transition-colors"
            aria-label="Close modal"
          >
            <X size={24} />
          </button>
        </div>

        {/* Credits Calculation Box */}
        <div className="bg-gray-700 rounded-xl p-6 mb-6">
          <div className="grid grid-cols-5 gap-4 items-center">
            <div className="text-center">
              <div className="text-sm text-gray-400 mb-2">
                Remaining credits
              </div>
              <div className="text-4xl font-bold text-white">76</div>
            </div>

            <div className="text-center">
              <div className="text-4xl font-bold text-white">=</div>
            </div>

            <div className="text-center">
              <div className="text-sm text-gray-400 mb-2">Paid</div>
              <div className="text-4xl font-bold text-white">0</div>
            </div>

            <div className="text-center">
              <div className="text-4xl font-bold text-white">+</div>
            </div>

            <div className="text-center">
              <div className="text-sm text-gray-400 mb-2">Gift</div>
              <div className="text-4xl font-bold text-white">76</div>
            </div>
          </div>
        </div>

        {/* Tabs */}
        <div className="flex border-b border-gray-600 mb-6">
          <button
            onClick={() => setActiveTab("Credit cost")}
            className={`px-4 py-2 text-sm font-medium border-b-2 transition-colors ${
              activeTab === "Credit cost"
                ? "border-white text-white"
                : "border-transparent text-gray-400 hover:text-white"
            }`}
          >
            Credit cost
          </button>
          <button
            onClick={() => setActiveTab("Credit usage")}
            className={`px-4 py-2 text-sm font-medium border-b-2 transition-colors ${
              activeTab === "Credit usage"
                ? "border-white text-white"
                : "border-transparent text-gray-400 hover:text-white"
            }`}
          >
            Credit usage
          </button>
        </div>

        {/* Pricing Table */}
        <div className="flex-1 overflow-auto mb-6">
          <div className="bg-gray-700 rounded-xl overflow-hidden border border-gray-600">
            {activeTab === "Credit cost" ? (
              <>
                {/* Table Header */}
                <div className="grid grid-cols-3 gap-4 p-4 bg-gray-600 border-b border-gray-500">
                  <div className="text-sm font-medium text-white text-center">
                    Products
                  </div>
                  <div className="text-sm font-medium text-white text-center">
                    Credit
                  </div>
                  <div className="text-sm font-medium text-white text-center">
                    Minimum Cost
                  </div>
                </div>

                {/* Table Body */}
                <div className="divide-y divide-gray-600">
                  {pricingData.map((item, index) => (
                    <div key={index}>
                      {item.isHeader ? (
                        <div className="grid grid-cols-3 gap-4 p-4">
                          <div className="text-sm text-white font-medium">
                            {item.product}
                          </div>
                          <div className="text-sm text-center">
                            <span className="text-purple-400 underline cursor-pointer">
                              {item.credit}
                            </span>
                          </div>
                          <div className="text-sm text-white text-center">
                            {item.minimumCost}
                          </div>
                        </div>
                      ) : (
                        <>
                          {item.subItems[0] === "-" ? (
                            // Single row for products without sub-items
                            <div className="grid grid-cols-3 gap-4 p-4">
                              <div className="text-sm text-white font-medium">
                                {item.product}
                              </div>
                              <div className="text-sm text-white text-center">
                                {item.credit}
                              </div>
                              <div className="text-sm text-white text-center">
                                {item.minimumCost}
                              </div>
                            </div>
                          ) : (
                            // Multiple rows for products with sub-items (merged cell effect)
                            <div className="flex">
                              {/* Product name column - spans multiple rows */}
                              <div className="flex-1 flex items-center justify-start p-4 border-r border-gray-600">
                                <div className="text-sm text-white font-medium">
                                  {item.product}
                                </div>
                              </div>

                              {/* Sub-items columns */}
                              <div className="flex-[2] divide-y divide-gray-600">
                                {item.subItems.map((subItem, subIndex) => (
                                  <div
                                    key={subIndex}
                                    className="grid grid-cols-2 gap-4 p-4"
                                  >
                                    <div className="text-sm text-gray-300 pl-4">
                                      {typeof subItem === "string"
                                        ? subItem
                                        : subItem.name}
                                    </div>
                                    <div className="grid grid-cols-2 gap-4">
                                      <div className="text-sm text-white text-center">
                                        {typeof subItem === "string"
                                          ? "-"
                                          : subItem.credit}
                                      </div>
                                      <div className="text-sm text-white text-center">
                                        {typeof subItem === "string"
                                          ? "-"
                                          : subItem.minimumCost}
                                      </div>
                                    </div>
                                  </div>
                                ))}
                              </div>
                            </div>
                          )}
                        </>
                      )}
                    </div>
                  ))}
                </div>
              </>
            ) : (
              <>
                {/* Credit Usage Table Header */}
                <div className="grid grid-cols-4 gap-4 p-4 bg-gray-600 border-b border-gray-500">
                  <div className="text-sm font-medium text-white text-center">
                    Date
                  </div>
                  <div className="text-sm font-medium text-white text-center">
                    User
                  </div>
                  <div className="text-sm font-medium text-white text-center">
                    Type
                  </div>
                  <div className="text-sm font-medium text-white text-center">
                    Credit change
                  </div>
                </div>

                {/* Credit Usage Table Body */}
                <div className="divide-y divide-gray-600">
                  {[
                    {
                      date: "21/05/2025 12:26:53",
                      user: "Hidilson Doho",
                      type: "Image Generate",
                      credit: "-8",
                    },
                    {
                      date: "21/05/2025 12:26:53",
                      user: "Hidilson Doho",
                      type: "Image Generate",
                      credit: "-8",
                    },
                    {
                      date: "21/05/2025 12:26:53",
                      user: "Hidilson Doho",
                      type: "Image Generate",
                      credit: "-8",
                    },
                  ].map((item, index) => (
                    <div key={index} className="grid grid-cols-4 gap-4 p-4">
                      <div className="text-sm text-white text-center">
                        {item.date}
                      </div>
                      <div className="text-sm text-white text-center">
                        {item.user}
                      </div>
                      <div className="text-sm text-white text-center">
                        {item.type}
                      </div>
                      <div className="text-sm text-white text-center">
                        {item.credit}
                      </div>
                    </div>
                  ))}
                </div>

                {/* Pagination */}
                <div className="flex justify-between items-center p-4 bg-gray-700 border-t border-gray-600">
                  <div className="text-sm text-gray-400">
                    A total of <span className="text-purple-400">3</span>
                  </div>

                  <div className="flex items-center gap-2">
                    <button className="p-2 text-gray-400 hover:text-white transition-colors">
                      <svg
                        width="16"
                        height="16"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                      >
                        <path d="M15 18l-6-6 6-6" />
                      </svg>
                    </button>

                    <div className="flex items-center gap-1">
                      <button className="w-8 h-8 bg-white text-black rounded text-sm font-medium">
                        1
                      </button>
                    </div>

                    <button className="p-2 text-gray-400 hover:text-white transition-colors">
                      <svg
                        width="16"
                        height="16"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                      >
                        <path d="M9 18l6-6-6-6" />
                      </svg>
                    </button>
                  </div>

                  <div className="flex items-center gap-2">
                    <span className="text-sm text-gray-400">1</span>
                    <span className="text-sm text-gray-400">/</span>
                    <span className="text-sm text-gray-400">1</span>
                  </div>
                </div>
              </>
            )}
          </div>
        </div>

        {/* Footer Buttons */}
        <div className="flex justify-end gap-4">
          <button
            onClick={onClose}
            className="px-6 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-500 transition-colors"
          >
            Cancel
          </button>
          <button className="px-6 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors">
            Upgrade
          </button>
        </div>
      </div>
    </div>
  );
};

export default UpgradeCreditModal;