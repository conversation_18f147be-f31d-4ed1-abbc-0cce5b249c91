import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Loader2, AlertCircle, ArrowUpRight, ArrowDownRight, Calendar } from "lucide-react";
import { toast } from "sonner";
import { useApp } from "@/contexts/useApp";
import SubscriptionService from "@/services/subscription.service";
import type { 
  SubscriptionPlan, 
  UserSubscription, 
  ProrationDetails,
  ScheduledPlanChange
} from "@/services/subscription.service";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";

interface SubscriptionPlanChangeProps {
  currentSubscription: UserSubscription | null;
  targetPlan: SubscriptionPlan;
  onSuccess: () => void;
  onCancel: () => void;
}

export const SubscriptionPlanChange = ({
  currentSubscription,
  targetPlan,
  onSuccess,
  onCancel,
}: SubscriptionPlanChangeProps) => {
  const { user } = useApp();
  const [loading, setLoading] = useState(false);
  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);
  const [prorationDetails, setProrationDetails] = useState<ProrationDetails | null>(null);
  const [isUpgrade, setIsUpgrade] = useState(false);
  const [scheduledChange, setScheduledChange] = useState<ScheduledPlanChange | null>(null);

  useEffect(() => {
    if (currentSubscription && targetPlan) {
      // Determine if this is an upgrade or downgrade
      setIsUpgrade(targetPlan.price > currentSubscription.plan.price);
    }
  }, [currentSubscription, targetPlan]);

  const handlePlanChange = async () => {
    if (!user || !currentSubscription) {
      toast.error("You must be logged in with an active subscription");
      return;
    }

    setLoading(true);
    try {
      if (isUpgrade) {
        // Handle immediate upgrade with proration
        const result = await SubscriptionService.upgradeSubscription(
          currentSubscription.id,
          targetPlan.id
        );
        
        toast.success("Subscription upgraded successfully!");
        setProrationDetails(result.prorationDetails);
        onSuccess();
      } else {
        // Handle scheduled downgrade
        const result = await SubscriptionService.downgradeSubscription(
          currentSubscription.id,
          targetPlan.id
        );
        
        setScheduledChange(result.scheduledDowngrade);
        toast.success(result.message);
        onSuccess();
      }
    } catch (error) {
      console.error("Error changing subscription plan:", error);
      toast.error("Failed to change subscription plan. Please try again.");
    } finally {
      setLoading(false);
      setConfirmDialogOpen(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  if (!currentSubscription || !targetPlan) {
    return null;
  }

  return (
    <>
      <Button
        variant={isUpgrade ? "default" : "outline"}
        className={isUpgrade ? "bg-green-600 hover:bg-green-700" : "border-amber-500 text-amber-500 hover:bg-amber-500/10"}
        onClick={() => setConfirmDialogOpen(true)}
        disabled={loading || currentSubscription.plan.id === targetPlan.id}
      >
        {currentSubscription.plan.id === targetPlan.id ? (
          "Current Plan"
        ) : isUpgrade ? (
          <span className="flex items-center">
            <ArrowUpRight className="mr-2 h-4 w-4" />
            Upgrade
          </span>
        ) : (
          <span className="flex items-center">
            <ArrowDownRight className="mr-2 h-4 w-4" />
            Downgrade
          </span>
        )}
      </Button>

      {/* Confirmation Dialog */}
      <AlertDialog open={confirmDialogOpen} onOpenChange={setConfirmDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>
              {isUpgrade ? "Upgrade Subscription" : "Downgrade Subscription"}
            </AlertDialogTitle>
            <AlertDialogDescription>
              {isUpgrade ? (
                <div className="space-y-4">
                  <p>
                    You are upgrading from <strong>{currentSubscription.plan.displayName}</strong> to <strong>{targetPlan.displayName}</strong>.
                  </p>
                  <p>
                    Your upgrade will take effect immediately. You'll be charged the prorated difference for the remainder of your billing period.
                  </p>
                  <div className="bg-green-50 p-4 rounded-md border border-green-200">
                    <p className="font-medium text-green-800">Benefits of upgrading:</p>
                    <ul className="list-disc list-inside text-green-700 mt-2 space-y-1">
                      {targetPlan.featureHighlights.slice(0, 3).map((highlight, index) => (
                        <li key={index}>{highlight.title}</li>
                      ))}
                    </ul>
                  </div>
                </div>
              ) : (
                <div className="space-y-4">
                  <p>
                    You are downgrading from <strong>{currentSubscription.plan.displayName}</strong> to <strong>{targetPlan.displayName}</strong>.
                  </p>
                  <p>
                    Your downgrade will take effect at the end of your current billing period on <strong>{formatDate(currentSubscription.currentPeriodEnd)}</strong>. 
                    Until then, you'll continue to enjoy your current plan benefits.
                  </p>
                  <div className="bg-amber-50 p-4 rounded-md border border-amber-200 flex items-start">
                    <Calendar className="text-amber-500 mr-2 mt-0.5" />
                    <p className="text-amber-800">
                      You can cancel this scheduled downgrade anytime before it takes effect.
                    </p>
                  </div>
                </div>
              )}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={onCancel}>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handlePlanChange}
              className={isUpgrade ? "bg-green-600 hover:bg-green-700" : "bg-amber-600 hover:bg-amber-700"}
            >
              {loading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Processing...
                </>
              ) : isUpgrade ? (
                "Confirm Upgrade"
              ) : (
                "Schedule Downgrade"
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Success Dialog for Upgrade with Proration Details */}
      {prorationDetails && (
        <Dialog open={!!prorationDetails} onOpenChange={() => setProrationDetails(null)}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Subscription Upgraded Successfully</DialogTitle>
              <DialogDescription>
                Your subscription has been upgraded to {targetPlan.displayName}.
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4 py-4">
              <div className="bg-gray-50 p-4 rounded-md border">
                <h4 className="font-medium mb-2">Proration Details</h4>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span>Remaining days in billing period:</span>
                    <span>{prorationDetails.remainingDays} days</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Unused amount from previous plan:</span>
                    <span>${(prorationDetails.unusedAmount / 100).toFixed(2)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Prorated charge for new plan:</span>
                    <span>${(prorationDetails.upgradeAmount / 100).toFixed(2)}</span>
                  </div>
                </div>
              </div>
              <p className="text-sm text-gray-600">
                Your next regular billing date remains unchanged.
              </p>
            </div>
            <DialogFooter>
              <Button onClick={() => setProrationDetails(null)}>Close</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}

      {/* Success Dialog for Downgrade with Schedule Details */}
      {scheduledChange && (
        <Dialog open={!!scheduledChange} onOpenChange={() => setScheduledChange(null)}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Downgrade Scheduled</DialogTitle>
              <DialogDescription>
                Your subscription downgrade has been scheduled.
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4 py-4">
              <div className="bg-gray-50 p-4 rounded-md border">
                <h4 className="font-medium mb-2">Downgrade Details</h4>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span>Current plan:</span>
                    <span>{currentSubscription.plan.displayName}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>New plan:</span>
                    <span>{targetPlan.displayName}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Effective date:</span>
                    <span>{formatDate(scheduledChange.effectiveDate)}</span>
                  </div>
                </div>
              </div>
              <div className="flex items-start bg-blue-50 p-4 rounded-md border border-blue-200">
                <AlertCircle className="text-blue-500 mr-2 mt-0.5" />
                <p className="text-sm text-blue-800">
                  You can cancel this scheduled downgrade anytime from your subscription settings before it takes effect.
                </p>
              </div>
            </div>
            <DialogFooter>
              <Button onClick={() => setScheduledChange(null)}>Close</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}
    </>
  );
};

export default SubscriptionPlanChange;
