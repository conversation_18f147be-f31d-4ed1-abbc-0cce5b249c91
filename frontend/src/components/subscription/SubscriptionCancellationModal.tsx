import React, { useState } from 'react';
import {
  <PERSON>alog,
  DialogContent,
  Di<PERSON>Header,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Loader2, Calendar, AlertCircle, RefreshCw } from 'lucide-react';
import { toast } from 'sonner';
import SubscriptionService from '@/services/subscription.service';
import { format } from 'date-fns';
import type { UserSubscription } from '@/services/subscription.service';
import { Badge } from '@/components/ui/badge';

interface SubscriptionCancellationModalProps {
  isOpen: boolean;
  onClose: () => void;
  subscription: UserSubscription;
  onSuccess: () => void;
  onRefundRequest: () => void;
}

const SubscriptionCancellationModal: React.FC<SubscriptionCancellationModalProps> = ({
  isOpen,
  onClose,
  subscription,
  onSuccess,
  onRefundRequest,
}) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  // Calculate if the subscription is eligible for refund (within 14 days)
  const subscriptionStartDate = new Date(subscription.startDate);
  const currentDate = new Date();
  const daysSinceSubscription = Math.floor(
    (currentDate.getTime() - subscriptionStartDate.getTime()) / (1000 * 60 * 60 * 24)
  );
  const isEligibleForRefund = daysSinceSubscription <= 14;
  
  // Format the end date for display
  const formattedEndDate = subscription.currentPeriodEnd 
    ? format(new Date(subscription.currentPeriodEnd), 'MMMM dd, yyyy')
    : 'Unknown';

  const handleCancelSubscription = async () => {
    setIsSubmitting(true);
    try {
      await SubscriptionService.cancelSubscription(subscription.id);
      
      toast.success('Subscription canceled successfully', {
        description: `You'll still have access until ${formattedEndDate}.`,
      });
      
      onSuccess();
      onClose();
    } catch (error) {
      console.error('Error canceling subscription:', error);
      toast.error('Failed to cancel subscription', {
        description: 'Please try again or contact support.',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="sm:max-w-[550px]">
        <DialogHeader>
          <DialogTitle>Cancel Your Subscription</DialogTitle>
          <DialogDescription>
            You are about to cancel your {subscription.plan.displayName} subscription.
          </DialogDescription>
        </DialogHeader>

        <div className="py-4 space-y-6">
          <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
            <div className="flex items-start">
              <Calendar className="h-5 w-5 text-blue-600 mt-0.5 mr-2" />
              <div>
                <h4 className="font-medium text-blue-800">Access Until End of Billing Period</h4>
                <p className="text-sm text-blue-700 mt-1">
                  You'll continue to have access to all subscription benefits until <strong>{formattedEndDate}</strong>.
                </p>
              </div>
            </div>
          </div>
          
          <div className="bg-amber-50 border border-amber-200 rounded-md p-4">
            <div className="flex items-start">
              <AlertCircle className="h-5 w-5 text-amber-600 mt-0.5 mr-2" />
              <div>
                <h4 className="font-medium text-amber-800">What Happens After Cancellation</h4>
                <ul className="text-sm text-amber-700 list-disc list-inside mt-1 space-y-1">
                  <li>Your subscription will not renew after {formattedEndDate}</li>
                  <li>Any unused credits will expire after 2 years from purchase date</li>
                  <li>You can resubscribe at any time</li>
                </ul>
              </div>
            </div>
          </div>
          
          {isEligibleForRefund && (
            <div className="bg-green-50 border border-green-200 rounded-md p-4">
              <div className="flex items-start">
                <RefreshCw className="h-5 w-5 text-green-600 mt-0.5 mr-2" />
                <div>
                  <div className="flex items-center">
                    <h4 className="font-medium text-green-800">Eligible for Refund</h4>
                    <Badge className="ml-2 bg-green-600">14-Day Guarantee</Badge>
                  </div>
                  <p className="text-sm text-green-700 mt-1">
                    Your subscription is within our 14-day money-back guarantee period.
                    You can request a full refund instead of just canceling.
                  </p>
                  <Button 
                    variant="outline" 
                    className="mt-2 border-green-600 text-green-700 hover:bg-green-100"
                    onClick={() => {
                      onClose();
                      onRefundRequest();
                    }}
                  >
                    Request Refund Instead
                  </Button>
                </div>
              </div>
            </div>
          )}
        </div>

        <DialogFooter className="flex flex-col sm:flex-row gap-2">
          <Button variant="outline" onClick={onClose} disabled={isSubmitting} className="sm:flex-1">
            Keep My Subscription
          </Button>
          <Button 
            variant="destructive" 
            onClick={handleCancelSubscription}
            disabled={isSubmitting}
            className="sm:flex-1"
          >
            {isSubmitting ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Processing...
              </>
            ) : (
              'Confirm Cancellation'
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default SubscriptionCancellationModal;
