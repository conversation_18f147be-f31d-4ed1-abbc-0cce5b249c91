import { useState, useEffect, useCallback } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Loader2, Calendar, XCircle } from "lucide-react";
import { Text } from "@/components/ui/text";
import { toast } from "sonner";
import { useApp } from "@/contexts/useApp";
import SubscriptionService from "@/services/subscription.service";
import type { ScheduledPlanChange } from "@/services/subscription.service";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";

interface ScheduledChangesManagerProps {
  onUpdate?: () => void;
}

export const ScheduledChangesManager = ({ onUpdate }: ScheduledChangesManagerProps) => {
  const { user } = useApp();
  const [loading, setLoading] = useState(false);
  const [scheduledChanges, setScheduledChanges] = useState<ScheduledPlanChange[]>([]);
  const [selectedChangeId, setSelectedChangeId] = useState<string | null>(null);
  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);

  const fetchScheduledChanges = useCallback(async () => {
    if (!user) return;

    setLoading(true);
    try {
      const changes = await SubscriptionService.getScheduledPlanChanges();
      setScheduledChanges(changes);
    } catch (error) {
      console.error("Error fetching scheduled plan changes:", error);
      toast.error("Failed to load scheduled plan changes");
    } finally {
      setLoading(false);
    }
  }, [user]);

  useEffect(() => {
    fetchScheduledChanges();
  }, [fetchScheduledChanges]);

  const handleCancelChange = async () => {
    if (!selectedChangeId) return;

    setLoading(true);
    try {
      await SubscriptionService.cancelScheduledPlanChange(selectedChangeId);
      toast.success("Scheduled plan change cancelled successfully");
      fetchScheduledChanges();
      if (onUpdate) onUpdate();
    } catch (error) {
      console.error("Error cancelling scheduled plan change:", error);
      toast.error("Failed to cancel scheduled plan change");
    } finally {
      setLoading(false);
      setConfirmDialogOpen(false);
      setSelectedChangeId(null);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  if (loading && scheduledChanges.length === 0) {
    return (
      <div className="flex justify-center items-center p-8">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  if (scheduledChanges.length === 0) {
    return null;
  }

  return (
    <div className="space-y-4">
      <div className="border rounded-lg overflow-hidden">
        <div className="bg-amber-50 px-4 py-3 border-b border-amber-200">
          <Text className="font-medium text-amber-800 flex items-center">
            <Calendar className="h-4 w-4 mr-2" />
            Scheduled Plan Changes
          </Text>
        </div>
        <div className="p-4 space-y-4">
          {scheduledChanges.map((change) => (
            <div key={change.id} className="bg-white p-4 rounded-md border shadow-sm">
              <div className="flex justify-between items-start">
                <div>
                  <h4 className="font-medium text-gray-900">
                    {change.changeType === "UPGRADE" ? "Upgrade to " : "Downgrade to "}
                    <span className="text-primary">{change.newPlan.displayName}</span>
                  </h4>
                  <p className="text-sm text-gray-600 mt-1">
                    Effective on: <span className="font-medium">{formatDate(change.effectiveDate)}</span>
                  </p>
                  <div className="mt-2 text-sm text-gray-500">
                    From: {change.currentSubscription.plan.displayName} (${change.currentSubscription.plan.price}/
                    {change.currentSubscription.plan.interval})
                    <br />
                    To: {change.newPlan.displayName} (${change.newPlan.price}/{change.newPlan.interval})
                  </div>
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  className="text-red-500 border-red-200 hover:bg-red-50"
                  onClick={() => {
                    setSelectedChangeId(change.id);
                    setConfirmDialogOpen(true);
                  }}
                >
                  <XCircle className="h-4 w-4 mr-1" />
                  Cancel
                </Button>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Confirmation Dialog */}
      <AlertDialog open={confirmDialogOpen} onOpenChange={setConfirmDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Cancel Scheduled Plan Change</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to cancel this scheduled plan change? Your subscription will remain on your current plan with no changes.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={() => setSelectedChangeId(null)}>
              No, Keep It
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={handleCancelChange}
              className="bg-red-600 hover:bg-red-700"
            >
              {loading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Processing...
                </>
              ) : (
                "Yes, Cancel Change"
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};

export default ScheduledChangesManager;
