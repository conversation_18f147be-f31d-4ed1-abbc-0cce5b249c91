import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  <PERSON>alogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Loader2 } from 'lucide-react';
import { toast } from 'sonner';
import SubscriptionService from '@/services/subscription.service';

interface RefundRequestModalProps {
  isOpen: boolean;
  onClose: () => void;
  subscriptionId: string;
  subscriptionName: string;
  onSuccess: () => void;
}

const RefundRequestModal: React.FC<RefundRequestModalProps> = ({
  isOpen,
  onClose,
  subscriptionId,
  subscriptionName,
  onSuccess,
}) => {
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleRequestRefund = async () => {
    setIsSubmitting(true);
    try {
      const response = await SubscriptionService.requestRefund(subscriptionId);
      
      if (response.success) {
        toast.success('Refund request submitted', {
          description: 'Your subscription has been canceled and refund request is being processed.',
        });
        onSuccess();
        onClose();
      } else {
        toast.error('Failed to request refund', {
          description: response.message || 'Please try again or contact support.',
        });
      }
    } catch (error) {
      console.error('Error requesting refund:', error);
      toast.error('Error requesting refund', {
        description: 'An unexpected error occurred. Please try again or contact support.',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Request Subscription Refund</DialogTitle>
          <DialogDescription>
            You are about to request a refund for your {subscriptionName} subscription.
            This will immediately cancel your subscription and initiate the refund process.
          </DialogDescription>
        </DialogHeader>

        <div className="py-4">
          <div className="bg-amber-50 border border-amber-200 rounded-md p-4 mb-4">
            <h4 className="font-medium text-amber-800">Important Information</h4>
            <ul className="text-sm text-amber-700 list-disc list-inside mt-2 space-y-1">
              <li>Refunds are only available within 14 days of subscription purchase</li>
              <li>Your subscription will be canceled immediately</li>
              <li>Refunds typically process within 5-7 business days</li>
              <li>Any unused credits will be removed from your account</li>
            </ul>
          </div>
          
          <p className="text-sm text-gray-600">
            Are you sure you want to request a refund for your subscription?
          </p>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose} disabled={isSubmitting}>
            Cancel
          </Button>
          <Button 
            variant="destructive" 
            onClick={handleRequestRefund}
            disabled={isSubmitting}
          >
            {isSubmitting ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Processing...
              </>
            ) : (
              'Request Refund'
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default RefundRequestModal;
