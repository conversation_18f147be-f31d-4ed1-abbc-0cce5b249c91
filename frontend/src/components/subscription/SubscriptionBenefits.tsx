import React from 'react';
import { Shield, ArrowUpDown, Coins } from 'lucide-react';

interface SubscriptionBenefitsProps {
  className?: string;
}

const SubscriptionBenefits: React.FC<SubscriptionBenefitsProps> = ({ className = '' }) => {
  return (
    <div className={`space-y-6 ${className}`}>
      <h3 className="text-lg font-semibold">Subscription Benefits</h3>
      
      <div className="space-y-4">
        <div className="flex items-start">
          <Shield className="h-5 w-5 mr-3 text-green-500 mt-0.5" />
          <div>
            <h4 className="font-medium">Risk Free</h4>
            <p className="text-sm text-gray-600">
              14-day money-back guarantee. If you're not satisfied, request a full refund within 14 days.
            </p>
          </div>
        </div>
        
        <div className="flex items-start">
          <ArrowUpDown className="h-5 w-5 mr-3 text-blue-500 mt-0.5" />
          <div>
            <h4 className="font-medium">Flexible</h4>
            <p className="text-sm text-gray-600">
              Downgrade, upgrade, or cancel your subscription at any time. No long-term commitments.
            </p>
          </div>
        </div>
        
        <div className="flex items-start">
          <Coins className="h-5 w-5 mr-3 text-amber-500 mt-0.5" />
          <div>
            <h4 className="font-medium">Fair</h4>
            <p className="text-sm text-gray-600">
              Unused credits roll over as long as you're subscribed. Your credits won't expire while your subscription is active.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SubscriptionBenefits;
