import type { DropdownItem } from "./DropdownMenu";

export interface FaqItemProps {
  id: number;
  question: string;
  answer: string;
}
export const faqData: FaqItemProps[] = [
  {
    id: 1,
    question: "What is Miragic?",
    answer:
      "Miragic is a cutting-edge platform for face-swapping and generative AI-powered media tools.",
  },
  {
    id: 2,
    question: "How do I access Miragic through an API?",
    answer:
      "You can access Miragic's API by registering on the platform and generating an API key.",
  },
  {
    id: 3,
    question:
      "Is it permissible for me to utilize the videos for the purpose of advertising services to my clients?",
    answer:
      "Yes, Miragic permits the use of generated media for client advertising under specific licensing terms.",
  },
  {
    id: 4,
    question: "What file formats are supported for uploads?",
    answer:
      "Miragic supports most common video and image formats including MP4, MOV, JPG, PNG, and WEBP. Files should not exceed 500MB in size.",
  },
  {
    id: 5,
    question: "How long does it take to process a video?",
    answer:
      "Processing time varies depending on video length and complexity. Most videos are processed within 5-15 minutes, while longer or more complex videos may take up to 30 minutes.",
  },
  {
    id: 6,
    question: "What are the payment plans available?",
    answer:
      "We offer flexible plans including pay-per-use, monthly subscriptions, and enterprise solutions. Each plan comes with different features and processing quotas to suit your needs.",
  },
  {
    id: 7,
    question: "Is my content kept private and secure?",
    answer:
      "Yes, we take privacy seriously. All uploaded content is encrypted, processed securely, and automatically deleted after processing unless specified otherwise. We never share your content with third parties.",
  },
  {
    id: 8,
    question: "Do you offer customer support?",
    answer:
      "Yes, we provide 24/7 customer support through email and live chat. Enterprise customers also get access to dedicated support representatives.",
  },
];

export const companyItems: DropdownItem[] = [
  {
    name: "About Us",
    description: "Miragic empowers creativity with AI-driven solutions",
    href: "/about",
  },
  {
    name: "Blog",
    description: "Insights, tips, and updates on AI and technology innovations",
    href: "/blog",
  },
  {
    name: "Contact Us",
    description: "Reach out to Miragic for support and inquiries",
    href: "/about",
  },
];

export const programItems: DropdownItem[] = [
  {
    name: "Referral Program",
    description: "Refer friends and earn rewards",
    href: "/referral",
  },
];

export const products: DropdownItem[] = [
  {
    name: "Background Remover",
    description: "Remove image backgrounds instantly with AI",
    color: "bg-orange-500",
    href: "/ai-tool/background-remover",
  },
  {
    name: "Virtual Try On",
    description: "Try products on virtually in real-time with AI",
    color: "bg-purple-500",
    href: "/ai-tool/virtual-try-on",
  },
  {
    name: "Speed Painting",
    description: "Create stunning digital art quickly with AI",
    color: "bg-green-500",
    href: "/ai-tool/speedpainting",
  },
];

export const resources: DropdownItem[] = [
  {
    name: "Community",
    description: "Connect, share, and grow with the Miragic",
    color: "bg-orange-500",
    href: "/community",
  },
  {
    name: "Use Cases",
    description: "Explore diverse Miragic use-cases driving innovation",
    color: "bg-purple-500",
    href: "/use-cases",
  },
];

export const navLinks = [
  { to: "/docs-api", label: "API" },
  { to: "/pricing", label: "Pricing" },
  { to: "/coming-soon", label: "Contact" },
];

export const mobileNavLinks = [
  {
    to: "",
    label: "Products",
    subItem: [
      {
        to: "/background-remover",
        label: "Background Remover",
      },
      {
        to: "/virtual-try-on",
        label: "Virtual Try On",
      },
      {
        to: "/speedpainting",
        label: "Speed Painting",
      },
    ],
  },
  {
    to: "",
    label: "Resources",
    subItem: [
      {
        to: "/community",
        label: "Community",
      },
      {
        to: "/use-cases",
        label: "Use-Cases",
      },
    ],
  },

  {
    to: "",
    label: "Company",
    subItem: [
      {
        to: "/referral",
        label: "Referral Program",
      },
      {
        to: "/about",
        label: "About Us",
      },
      {
        to: "/blog",
        label: "Blog",
      },
      {
        to: "/about",
        label: "Contact Us",
      },
    ],
  },
  { to: "/docs-api", label: "API" },
  { to: "/pricing", label: "Pricing" },
  { to: "/coming-soon", label: "Contact" },
];
