import { Button } from "@/components/ui/button";
import { Text } from "@/components/ui/text";

const FutureSection = () => {
  return (
    <div className=" text-white xl:pb-32 pb-16 px-6">
      <div className="mx-auto text-center">
        <Text
          variant={"body"}
          className="text-white xl:text-5xl text-4xl sm:text-4xl font-semibold text-center mb-4"
        >
          Ready to Step into the Future?
        </Text>
        <Text
          variant={"body"}
          className="text-[#888] xl:text-xl text-base font-normal text-center mb-10"
        >
          AI-powered personalized marketing that captivates, inspires, and
          delights!
        </Text>
        <p className="text-lg sm:text-xl text-gray-300 mb-10"></p>

        <div className="flex flex-col sm:flex-row justify-center items-center space-y-4 sm:space-y-0 sm:space-x-6">
          <Button
            outline={false}
            variant={"animeGradient"}
            className="bg-radial-[at_5%_5%] from-[#2C9BF7] to-[#8054F3] rounded-full border border-white/15 px-7 py-3 h-[50px]"
          >
            Get Started
          </Button>
          <Button
            outline={false}
            variant={"animeShine"}
            className="rounded-full bg-black px-7 py-3 hover:bg-black h-[50px]"
          >
            API Docs
          </Button>
        </div>
      </div>
    </div>
  );
};

export default FutureSection;
