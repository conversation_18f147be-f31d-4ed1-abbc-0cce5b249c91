import React from "react";
import { Text } from "../ui/text";
import { Button } from "../ui/button";

interface FloatingImage {
  src: string;
  alt?: string;
  className?: string;
}

interface BannerProps {
  title: string;
  subtitle?: string;
  buttonText: string;
  bannerClassName?: string;
  leftContentClassName?: string;
  rightContentClassName?: string;
  onClick?: () => void;
  floatingImages?: FloatingImage[];
}

export const Banner: React.FC<BannerProps> = ({
  title,
  subtitle,
  buttonText,
  bannerClassName = "bg-gradient-to-b from-[#0B1016] to-[#2A1E5C]",
  leftContentClassName,
  rightContentClassName,
  onClick,
  floatingImages = [],
}) => (
  <div
    className={`relative w-full rounded-xl ${bannerClassName} overflow-hidden`}
  >
    <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center">
      <div
        className={`space-y-4 lg:space-y-6 relative p-14 ${leftContentClassName}`}
      >
        <Text className="text-3xl lg:text-4xl text-white font-semibold">
          {title}
        </Text>
        {subtitle && (
          <Text className="text-sm md:text-base lg:text-lg text-white/66 leading-relaxed max-w-xl">
            {subtitle}
          </Text>
        )}
        {buttonText && (
          <Button
            variant="animeGradient"
            onClick={onClick}
            className="bg-radial-[at_5%_5%] from-[#2C9BF7] to-[#8054F3] rounded-full border border-white/15 !px-7 h-[50px] lg:text-lg sm:text-base text-sm"
          >
            {buttonText}
          </Button>
        )}
      </div>

      <div className={`relative h-full ${rightContentClassName}`}>
        {floatingImages.map((img, idx) => (
          <img
            loading="lazy"
            key={idx}
            src={img.src}
            alt={img.alt || ""}
            className={img.className}
          />
        ))}
      </div>
    </div>
  </div>
);
