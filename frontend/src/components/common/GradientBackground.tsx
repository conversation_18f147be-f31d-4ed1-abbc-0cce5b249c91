interface GradientBackgroundProps {
  imageUrl?: string;
  imageAlt?: string;
  startColor?: string;
  endColor?: string;
  direction?: "to top" | "to bottom" | "to left" | "to right";
  className?: string;
  withMask?: boolean;
}

export const GradientBackground = ({
  imageUrl,
  imageAlt = "Background Image",
  startColor,
  endColor,
  direction,
  className = "absolute top-0 right-0 left-0 bottom-[30%]",
  withMask = true,
}: GradientBackgroundProps) => {
  return (
    <div className={`${className} overflow-hidden`}>
      <img
        fetchPriority="high"
        loading="eager"
        className="absolute w-full h-full object-cover border-0"
        src={imageUrl}
        alt={imageAlt}
        style={{
          ...(withMask && {
            maskImage: `linear-gradient(to bottom, 
                      rgba(0, 0, 0, 0.7) 0%, 
                      rgba(0, 0, 0, 0) 100%
                    )`,
            WebkitMaskImage: `linear-gradient(to bottom, 
                       rgba(0, 0, 0, 0.7) 0%, 
                      rgba(0, 0, 0, 0) 100%
                    )`,
          }),
        }}
      />
      <div
        className="absolute w-full h-full"
        style={{
          background: `linear-gradient(${direction}, ${startColor} 0%, ${endColor} 100%)`,
          mixBlendMode: "multiply",
        }}
      ></div>
    </div>
  );
};
