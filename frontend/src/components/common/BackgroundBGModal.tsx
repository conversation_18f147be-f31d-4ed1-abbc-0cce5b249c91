import { useEffect, useState } from "react";
import { X } from "lucide-react";
import { Button } from "../ui/button";
import { Text } from "../ui/text";

const PRESET_BACKGROUNDS = [
  "/bg/bg_1.svg",
  "/bg/bg_2.svg",
  "/bg/bg_3.svg",
  "/bg/bg_4.svg",
  "/bg/bg_5.svg",
  "/bg/bg_6.svg",
  "/bg/bg_7.svg",
  "/bg/bg_8.svg",
  // ...add more
];

const PRESET_COLORS = [
  "#FFFFFF",
  "#F44336",
  "#E91E63",
  "#9C27B0",
  "#673AB7",
  "#3F51B5",
  "#3596F3",
  "#3BA9F4",
  "#3EBCD4",
  "#2E9688",
];

export const BackgroundBGModal = ({
  isOpen,
  onClose,
  onColorSelect,
  onBackgroundSelect,
}: {
  isOpen: boolean;
  onClose: () => void;
  onColorSelect: (color: string) => void;
  onBackgroundSelect: (background: string) => void;
}) => {
  const [mode, setMode] = useState<"color" | "photo">("color");
  const [selectedColor, setSelectedColor] = useState("#ffffff");
  const [selectedBackground, setSelectedBackground] = useState("");
  const [customBackground, setCustomBackground] = useState("");

  // Reset selections when modal opens
  useEffect(() => {
    if (isOpen) {
      setMode("color");
      setSelectedColor("#ffffff");
      setSelectedBackground("");
      setCustomBackground("");
    }
  }, [isOpen]);

  const handleClose = () => {
    onClose();
    setMode("color");
    setSelectedColor("#ffffff");
    setSelectedBackground("");
    setCustomBackground("");
  };

  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        const result = e.target?.result as string;
        setCustomBackground(result);
        setSelectedBackground(result);
      };
      reader.readAsDataURL(file);
    }
  };

  return (
    <div className="fixed inset-0 bg-black/40 flex items-center justify-center z-50">
      <div className="bg-[#020103] lg:p-12 p-8 rounded-lg relative border border-white/15">
        <button
          onClick={handleClose}
          className="absolute top-2 right-2 text-gray-400 hover:text-white"
        >
          <X size={20} />
        </button>

        {/* Toggle Button Group */}
        <div className="flex justify-center items-center gap-2 bg-gray-100 rounded-full p-1 w-fit mx-auto mb-4">
          <button
            className={`px-4 py-1 rounded-full text-sm font-medium transition ${
              mode === "photo" ? "bg-white shadow text-black" : "text-gray-500"
            }`}
            onClick={() => setMode("photo")}
          >
            Photo
          </button>
          <button
            className={`px-4 py-1 rounded-full text-sm font-medium transition ${
              mode === "color" ? "bg-white shadow text-black" : "text-gray-500"
            }`}
            onClick={() => setMode("color")}
          >
            Color
          </button>
        </div>

        {/* Conditional Content */}
        {mode === "color" ? (
          <>
            <Text className="text-white text-lg mb-4">
              Choose Background Color
            </Text>
            <div className="grid grid-cols-3 gap-3 mb-4">
              <input
                type="color"
                value={selectedColor}
                onChange={(e) => setSelectedColor(e.target.value)}
                className="w-[70px] h-[70px] rounded-2xl border cursor-pointer"
              />
              {PRESET_COLORS.map((color) => (
                <button
                  key={color}
                  className={`w-16 h-16 rounded-3xl border cursor-pointer border-white/25 ${
                    selectedColor === color
                      ? "border-blue-500"
                      : "border-transparent"
                  }`}
                  style={{ backgroundColor: color }}
                  onClick={() => setSelectedColor(color)}
                />
              ))}
            </div>

            <Button
              variant="animeGradient"
              onClick={() => {
                onColorSelect(selectedColor);
                handleClose();
              }}
              className="w-full h-11"
            >
              Apply Color
            </Button>
          </>
        ) : (
          <>
            <Text className="text-white text-lg mb-4">Choose Background</Text>
            <div className="grid grid-cols-3 gap-3 mb-4">
              {PRESET_BACKGROUNDS.map((bg, index) => (
                <button
                  key={index}
                  className={`w-16 h-16 rounded-3xl border cursor-pointer border-white/25 overflow-hidden ${
                    selectedBackground === bg
                      ? "border-blue-500"
                      : "border-transparent"
                  }`}
                  onClick={() => {
                    setSelectedBackground(bg);
                    setCustomBackground("");
                  }}
                >
                  <img
                    loading="lazy"
                    src={bg}
                    alt={`Background ${index + 1}`}
                    className="w-full h-full object-cover"
                  />
                </button>
              ))}
            </div>
            <div className="mb-4">
              <Text className="text-white/70 text-sm mb-2">
                Or upload your own background
              </Text>
              <div className="flex items-center gap-3">
                <input
                  type="file"
                  id="background-upload"
                  accept="image/*"
                  onChange={handleFileUpload}
                  className="hidden"
                />
                <label
                  htmlFor="background-upload"
                  className="text-white flex items-center gap-2 px-4 py-2 border border-white/25 rounded-lg cursor-pointer hover:bg-white/5"
                >
                  Upload Image
                </label>
                {customBackground && (
                  <div className="w-16 h-16 rounded-3xl border border-blue-500 overflow-hidden">
                    <img
                      loading="lazy"
                      src={customBackground}
                      alt="Custom background"
                      className="w-full h-full object-cover"
                    />
                  </div>
                )}
              </div>
            </div>
            <Button
              variant="animeGradient"
              onClick={() => {
                onBackgroundSelect(selectedBackground);
                handleClose();
              }}
              className="w-full h-11"
              disabled={!selectedBackground}
            >
              Apply Background
            </Button>
          </>
        )}
      </div>
    </div>
  );
};
