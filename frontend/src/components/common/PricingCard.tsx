import { useState } from "react";
import SubscriptionService from "@/services/subscription.service";
import CreditPurchaseModal from "@/components/payment/CreditPurchaseModal";
import { toast } from "sonner";
import { useApp } from "@/contexts/useApp";
import SubscriptionCancellationModal from "@/components/subscription/SubscriptionCancellationModal";
import RefundRequestModal from "@/components/subscription/RefundRequestModal";
import { useUserSubscription } from "@/hooks/useUserSubscription";
import { usePricingPlans } from "@/hooks/usePricingPlans";
import { useCreditPackages } from "@/hooks/useCreditPackages";
import { usePurchasing } from "@/hooks/usePurchasing";
import { FreePlan } from "../pricing/FreePlan";
import { SubscriptionPlan } from "../pricing/SubscriptionPlan";
import { TopUpCredit } from "../pricing/TopUpCredit";
import useCredits from "@/hooks/useCredits";

const commonClass =
  "hover:transform hover:-translate-y-1 transition-all duration-300 ease-in-out rounded-[8px] hover:z-40 border border-white/15 bg-[#16243D] overflow-hidden flex flex-col gap-8 justify-between shadow-2xl hover:shadow-3xl";

export const PricingCard = () => {
  const { user } = useApp();

  const {
    userSubscription,
    loading: subscriptionLoading,
    refreshUserSubscription,
  } = useUserSubscription(user);

  const {
    subscriptionPlans,
    selectedSubscriptionId,
    setSelectedSubscriptionId,
    loading: plansLoading,
    error: plansError,
  } = usePricingPlans(userSubscription);

  const {
    creditPackages,
    selectedPackageId,
    setSelectedPackageId,
    loading: packagesLoading,
    error: packagesError,
  } = useCreditPackages();

  const {
    loading: purchaseLoading,
    handlePurchaseCredits,
    setLoading,
  } = usePurchasing();
  const [zIndexPosition, setZIndexPosition] = useState({
    free: "z-10",
    subscription: "z-20",
    topup: "z-10",
  });

  // State for modals
  const [isPurchaseModalOpen, setIsPurchaseModalOpen] = useState(false);
  const [isCancellationModalOpen, setIsCancellationModalOpen] = useState(false);
  const [isRefundModalOpen, setIsRefundModalOpen] = useState(false);
  const { refreshCredits } = useCredits();
  const [selectedPackageDetails, setSelectedPackageDetails] = useState<{
    id: string;
    name: string;
  } | null>(null);

  // Handle subscription checkout
  const handleSubscribe = async () => {
    if (!user) {
      toast.error("Please sign in to subscribe");
      return;
    }

    if (!selectedSubscriptionId) {
      toast.error("Please select a subscription plan");
      return;
    }

    try {
      const response = await SubscriptionService.createCheckoutSession({
        planId: selectedSubscriptionId,
        successUrl: `${window.location.origin}/dashboard/subscription`,
        cancelUrl: `${window.location.origin}/pricing?subscription=canceled`,
      });

      // Redirect to Stripe checkout
      if (response && response.url) {
        window.location.href = response.url;
      } else {
        throw new Error("Invalid checkout response");
      }
    } catch (err) {
      console.error("Error creating checkout session:", err);
      toast.error("Failed to create checkout session. Please try again.");
    }
  };

  // Handle opening the subscription cancellation modal
  const handleOpenCancellationModal = () => {
    if (!user || !userSubscription) {
      toast.error("No active subscription to cancel");
      return;
    }
    setIsCancellationModalOpen(true);
  };

  // Handle subscription cancellation success
  const handleCancellationSuccess = async () => {
    // Refresh subscription status
    refreshUserSubscription();
  };

  // Handle successful purchase
  const handlePurchaseSuccess = async () => {
    toast("Purchase successful", {
      description: "Your credits have been added to your account",
      // variant: "default",
    });
    // Optionally refresh user credit balance or redirect to dashboard
    await refreshCredits();
    setLoading(false);
  };

  const handleCreditPackageChange = (value: string) => {
    setSelectedPackageId(value);
  };

  const handleZIndex = (
    cardType: "free" | "subscription" | "topup",
    id: string
  ) => {
    if (cardType === "subscription") {
      setSelectedSubscriptionId(id);
      setZIndexPosition({
        free: "z-10",
        subscription: "z-30",
        topup: "z-10",
      });
    } else if (cardType === "topup") {
      handleCreditPackageChange(id);
      setZIndexPosition({
        free: "z-10",
        subscription: "z-20",
        topup: "z-30",
      });
    }
  };

  return (
    <>
      {/* Credit Purchase Modal */}
      {selectedPackageDetails && (
        <CreditPurchaseModal
          isOpen={isPurchaseModalOpen}
          onClose={() => setIsPurchaseModalOpen(false)}
          packageId={selectedPackageDetails?.id || ""}
          packageName={selectedPackageDetails?.name || ""}
          onSuccess={handlePurchaseSuccess}
        />
      )}

      {userSubscription && (
        <SubscriptionCancellationModal
          isOpen={isCancellationModalOpen}
          onClose={() => setIsCancellationModalOpen(false)}
          subscription={userSubscription}
          onSuccess={handleCancellationSuccess}
          onRefundRequest={() => {
            setIsCancellationModalOpen(false);
            setIsRefundModalOpen(true);
          }}
        />
      )}

      {userSubscription && (
        <RefundRequestModal
          isOpen={isRefundModalOpen}
          onClose={() => setIsRefundModalOpen(false)}
          subscriptionId={userSubscription.id}
          subscriptionName={userSubscription.plan.displayName}
          onSuccess={refreshUserSubscription}
        />
      )}

      <div className="xl:max-w-[1440px] mx-auto">
        <div className="relative lg:pt-20 pt-12 lg:pb-14 pb-8">
          <h1 className="text-white font-semibold xl:text-5xl xl:leading-16 text-3xl text-center 2xl:px-[15%] lg:px-[10%] px-6">
            Select the right plan to boost your business productivity
          </h1>
        </div>

        <div className="grid items-center px-4 sm:px-6 lg:px-8 justify-center grid-cols-1 space-y-8 xl:grid-cols-10 w-full">
          <div
            className={`xl:col-span-3 xl:-mr-7 ${commonClass} ${zIndexPosition.free}`}
          >
            <FreePlan user={user} />
          </div>

          <div
            className={`xl:col-span-4 relative ${commonClass} ${zIndexPosition.subscription}`}
          >
            <SubscriptionPlan
              plansLoading={plansLoading}
              plansError={plansError}
              subscriptionPlans={subscriptionPlans}
              selectedSubscriptionId={selectedSubscriptionId}
              setSelectedSubscriptionId={setSelectedSubscriptionId}
              userSubscription={userSubscription}
              subscriptionLoading={subscriptionLoading}
              handleSubscribe={handleSubscribe}
              handleOpenCancellationModal={handleOpenCancellationModal}
              handleZIndex={handleZIndex}
              refreshUserSubscription={refreshUserSubscription}
            />
          </div>

          <div
            className={`xl:col-span-3 xl:-ml-5 ${commonClass} ${zIndexPosition.topup}`}
          >
            <TopUpCredit
              creditPackages={creditPackages}
              selectedPackageId={selectedPackageId}
              packagesLoading={packagesLoading}
              packagesError={packagesError}
              purchaseLoading={purchaseLoading}
              handleCreditPackageChange={handleCreditPackageChange}
              handlePurchaseCredits={() =>
                handlePurchaseCredits({
                  user,
                  selectedPackageId,
                  creditPackages,
                  onSuccess: () => {
                    setSelectedPackageDetails({
                      id: selectedPackageId,
                      name: `${
                        creditPackages.find(
                          (pkg) => pkg.id === selectedPackageId
                        )?.creditsAmount
                      } Credits`,
                    });
                    setIsPurchaseModalOpen(true);
                  },
                })
              }
              handleZIndex={handleZIndex}
            />
          </div>
        </div>
      </div>
    </>
  );
};
