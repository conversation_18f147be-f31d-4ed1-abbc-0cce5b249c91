import { Button } from "@/components/ui/button";
import { Text } from "@/components/ui/text";
import { useState } from "react";
import { FaqItem } from "./FaqItem";
import { faqData } from "@/components/common/variable";
import GradientText from "./GradientText";

interface FaqSectionProps {
  initialCount?: number;
}

const FaqSection = ({ initialCount }: FaqSectionProps) => {
  const [openItemId, setOpenItemId] = useState<number | null>(null);
  const [showAll, setShowAll] = useState(false);

  const handleToggle = (id: number): void => {
    setOpenItemId(openItemId === id ? null : id);
  };

  const shouldSplit = initialCount !== undefined;
  const initialFaqs = shouldSplit ? faqData.slice(0, initialCount) : faqData;
  const additionalFaqs = shouldSplit ? faqData.slice(initialCount) : [];

  return (
    <div className="px-4 sm:px-6 lg:px-8 font-inter">
      <div className="mx-auto">
        <Text
          variant={"body"}
          className="text-white xl:text-5xl text-4xl sm:text-4xl font-semibold text-center mb-10"
        >
          FAQ
        </Text>

        <div className="space-y-4">
          {initialFaqs.map((item) => (
            <FaqItem
              key={item.id}
              question={item.question}
              answer={item.answer}
              isOpen={openItemId === item.id}
              onToggle={() => handleToggle(item.id)}
            />
          ))}

          {shouldSplit && (
            <div
              className={`
                overflow-hidden transition-all duration-700 ease-in-out
                ${showAll ? "max-h-[1000px] opacity-100" : "max-h-0 opacity-0"}
              `}
            >
              <div className="pt-4 space-y-4">
                {additionalFaqs.map((item) => (
                  <FaqItem
                    key={item.id}
                    question={item.question}
                    answer={item.answer}
                    isOpen={openItemId === item.id}
                    onToggle={() => handleToggle(item.id)}
                  />
                ))}
              </div>
            </div>
          )}
        </div>

        {shouldSplit && additionalFaqs.length > 0 && (
          <div className="text-center mt-10">
            <Button
              outline={true}
              variant={"animeShine"}
              onClick={() => setShowAll(!showAll)}
              className="px-9 py-3 text-base border border-white/15 rounded-full cursor-pointer bg-transparent hover:bg-transparent h-[50px]"
            >
              <GradientText>
                {showAll ? "Show Less" : "See more FAQ"}
              </GradientText>
            </Button>
          </div>
        )}
      </div>
    </div>
  );
};

export default FaqSection;
