interface FaqItemProps {
  question: string;
  answer: string;
  isOpen: boolean;
  onToggle: () => void;
}

export const FaqItem = ({
  question,
  answer,
  isOpen,
  onToggle,
}: FaqItemProps) => {
  return (
    <div className="border-b border-white/15 py-4">
      <button
        className="bg-transparent hover:bg-transparent cursor-pointer flex justify-between items-center w-full text-left text-white text-lg font-medium"
        onClick={onToggle}
        aria-expanded={isOpen}
      >
        <span>{question}</span>
        <span className="text-purple-400 transition-transform duration-300">
          <img
            loading="lazy"
            src={isOpen ? "/icons/up_arrow.svg" : "/icons/down_arrow.svg"}
            alt={isOpen ? "Arrow Up" : "Arrow Down"}
            className={`w-6 h-6 transform transition-transform duration-500 ${
              isOpen ? "rotate-0" : "rotate-180"
            }`}
          />
        </span>
      </button>
      <p
        className={`
          mt-2 text-gray-400 text-base leading-relaxed
          transition-all duration-300 ease-in-out overflow-hidden
          ${isOpen ? "max-h-screen py-2" : "max-h-0 py-0"}
        `}
      >
        {answer}
      </p>
    </div>
  );
};
