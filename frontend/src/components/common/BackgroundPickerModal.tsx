import { useState } from "react";
import { X, Upload, Palette } from "lucide-react";
import { Button } from "../ui/button";
import { Text } from "../ui/text";

interface BackgroundPickerModalProps {
  isOpen: boolean;
  onClose: () => void;
  onBackgroundSelect: (background: string) => void;
  onSwitchToColor: () => void;
}

// Preset background images - replace these URLs with your actual background images
const PRESET_BACKGROUNDS = [
  "/backgrounds/bg1.jpg",
  "/backgrounds/bg2.jpg",
  "/backgrounds/bg3.jpg",
  "/backgrounds/bg4.jpg",
  "/backgrounds/bg5.jpg",
  "/backgrounds/bg6.jpg",
  "/backgrounds/bg7.jpg",
  "/backgrounds/bg8.jpg",
  "/backgrounds/bg9.jpg",
  "/backgrounds/bg10.jpg",
];

export const BackgroundPickerModal = ({
  isOpen,
  onClose,
  onBackgroundSelect,
  onSwitchToColor,
}: BackgroundPickerModalProps) => {
  const [selectedBackground, setSelectedBackground] = useState<string>("");
  const [customBackground, setCustomBackground] = useState<string>("");

  if (!isOpen) return null;

  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        const result = e.target?.result as string;
        setCustomBackground(result);
        setSelectedBackground(result);
      };
      reader.readAsDataURL(file);
    }
  };

  return (
    <div className="fixed inset-0 bg-black/40 flex items-center justify-center z-50">
      <div className="bg-[#020103] p-6 rounded-lg relative border border-white/15 w-[90%] max-w-2xl">
        <button
          onClick={onClose}
          className="absolute top-2 right-2 text-gray-400 hover:text-white"
        >
          <X size={20} />
        </button>

        <div className="flex justify-between items-center mb-4">
          <Text className="text-white text-lg">Choose Background</Text>
          <Button
            onClick={onSwitchToColor}
            className="flex items-center gap-2 text-white/70 hover:text-white"
          >
            <Palette size={16} />
            <Text className="text-sm">Switch to Colors</Text>
          </Button>
        </div>

        {/* Preset Backgrounds */}
        <div className="grid grid-cols-5 gap-3 mb-4">
          {PRESET_BACKGROUNDS.map((bg, index) => (
            <button
              key={index}
              className={`w-16 h-16 rounded-3xl border cursor-pointer border-white/25 overflow-hidden ${
                selectedBackground === bg
                  ? "border-blue-500"
                  : "border-transparent"
              }`}
              onClick={() => {
                setSelectedBackground(bg);
                setCustomBackground("");
              }}
            >
              <img
                loading="lazy"
                src={bg}
                alt={`Background ${index + 1}`}
                className="w-full h-full object-cover"
              />
            </button>
          ))}
        </div>

        {/* Custom Background Upload */}
        <div className="mb-4">
          <Text className="text-white/70 text-sm mb-2">
            Or upload your own background
          </Text>
          <div className="flex items-center gap-3">
            <input
              type="file"
              id="background-upload"
              accept="image/*"
              onChange={handleFileUpload}
              className="hidden"
            />
            <label
              htmlFor="background-upload"
              className="flex items-center gap-2 px-4 py-2 border border-white/25 rounded-lg cursor-pointer hover:bg-white/5"
            >
              <Upload size={16} className="text-white/70" />
              <Text className="text-white/70">Upload Image</Text>
            </label>
            {customBackground && (
              <div className="w-16 h-16 rounded-3xl border border-blue-500 overflow-hidden">
                <img
                  loading="lazy"
                  src={customBackground}
                  alt="Custom background"
                  className="w-full h-full object-cover"
                />
              </div>
            )}
          </div>
        </div>

        <Button
          variant="animeGradient"
          onClick={() => onBackgroundSelect(selectedBackground)}
          className="w-full"
          disabled={!selectedBackground}
        >
          Apply Background
        </Button>
      </div>
    </div>
  );
};
