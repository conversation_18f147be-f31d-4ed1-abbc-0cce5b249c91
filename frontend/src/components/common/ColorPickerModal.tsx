// src/components/ui/color-picker-modal.tsx
import { useState } from "react";
import { Image, X } from "lucide-react";
import { Button } from "../ui/button";
import { Text } from "../ui/text";

interface ColorPickerModalProps {
  isOpen: boolean;
  onClose: () => void;
  onColorSelect: (color: string) => void;
  onSwitchToBackground: () => void;
}

export const ColorPickerModal = ({
  isOpen,
  onClose,
  onColorSelect,
  onSwitchToBackground,
}: ColorPickerModalProps) => {
  const [selectedColor, setSelectedColor] = useState("#ffffff");

  if (!isOpen) return null;

  const presetColors = [
    "#ffffff",
    "#000000",
    "#ff0000",
    "#00ff00",
    "#0000ff",
    "#ffff00",
    "#ff00ff",
    "#00ffff",
    "#808080",
    "#800000",
  ];

  return (
    <div className="fixed inset-0 bg-black/40 flex items-center justify-center z-50">
      <div className="bg-[#020103] p-6 rounded-lg relative border border-white/15">
        <button
          onClick={onClose}
          className="absolute top-2 right-2 text-gray-400 hover:text-white"
        >
          <X size={20} />
        </button>

        <Text className="text-white text-lg mb-4">Choose Background Color</Text>
        <Button
          onClick={onSwitchToBackground}
          className="flex items-center gap-2 text-white/70 hover:text-white"
        >
          <Image size={16} />
          <Text className="text-sm">Switch to Images</Text>
        </Button>
        <div className="grid grid-cols-5 gap-3 mb-4">
          {presetColors.map((color) => (
            <button
              key={color}
              className={`w-16 h-16 rounded-3xl border cursor-pointer border-white/25 ${
                selectedColor === color
                  ? "border-blue-500"
                  : "border-transparent"
              }`}
              style={{ backgroundColor: color }}
              onClick={() => setSelectedColor(color)}
            />
          ))}
        </div>

        <input
          type="color"
          value={selectedColor}
          onChange={(e) => setSelectedColor(e.target.value)}
          className="w-full h-11 mb-4 rounded-2xl"
        />

        <Button
          variant="animeGradient"
          onClick={() => onColorSelect(selectedColor)}
          className="w-full"
        >
          Apply Color
        </Button>
      </div>
    </div>
  );
};
