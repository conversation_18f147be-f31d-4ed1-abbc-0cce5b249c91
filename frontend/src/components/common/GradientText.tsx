import { cn } from "@/lib/utils";

interface GradientTextProps {
  children: React.ReactNode;
  className?: string;
  variant?: "primary" | "secondary" | "custom";
  customGradient?: string;
}

const GradientText = ({
  children,
  className,
  variant = "primary",
  customGradient
}: GradientTextProps) => {
  const gradientVariants = {
    primary: "from-[#2C9BF7] via-[#6176F7] to-[#8054F3]",
    secondary: "from-[#8054F3] via-[#6176F7] to-[#2C9BF7]",
    custom: customGradient
  };

  return (
    <span
      className={cn(
        "bg-gradient-to-r bg-clip-text text-transparent inline-block",
        gradientVariants[variant],
        className
      )}
    >
      {children}
    </span>
  );
};

export default GradientText; 