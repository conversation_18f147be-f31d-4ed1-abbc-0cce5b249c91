import { cn } from "@/lib/utils";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { Swiper as SwiperType } from "swiper";

const SliderBtnArrow = ({
  className,

  isEnd,
  isBeginning,
  swiperRef,
}: {
  className?: string;
  isEnd?: boolean;
  isBeginning?: boolean;
  swiperRef: React.RefObject<SwiperType | null>;
}) => {
  return (
    <div className={cn("z-10 flex items-center gap-5", className)}>
      <button
        onClick={() => swiperRef.current?.slidePrev()}
        className={cn(
          "rounded-full cursor-pointer w-12 h-12 border border-gray-500 text-white flex items-center justify-center text-2xl hover:bg-gray-600 transition",
          isBeginning && "opacity-50 cursor-not-allowed"
        )}
      >
        <ChevronLeft />
      </button>
      <button
        onClick={() => swiperRef.current?.slideNext()}
        className={cn(
          "rounded-full cursor-pointer w-12 h-12 border border-gray-500 text-white flex items-center justify-center text-2xl hover:bg-gray-600 transition",
          isEnd && "opacity-50 cursor-not-allowed"
        )}
      >
        <ChevronRight />
      </button>
    </div>
  );
};

export default SliderBtnArrow;
