import React from "react";
import { ChevronDown } from "lucide-react";

// Define the type for a generic dropdown item
export interface DropdownItem {
  name: string;
  description: string;
  href: string;
  // Optional property for products, which have a color
  color?: string;
}

export interface DropdownMenuProps {
  title: string;
  // type?: "products" | "resources";
  items: DropdownItem[];
  isOpen: boolean;
  onMouseEnter: () => void;
  onMouseLeave: () => void;
  onItemClick: (href: string) => void;
  renderItem?: (
    item: DropdownItem,
    index: number,
    onItemClick: (href: string) => void
  ) => React.ReactNode;
}

const DropdownMenu: React.FC<DropdownMenuProps> = ({
  title,
  // type,
  items,
  isOpen,
  onMouseEnter,
  onMouseLeave,
  onItemClick,
  renderItem,
}) => {
  const defaultRenderItem = (
    item: DropdownItem,
    index: number,
    clickHandler: (href: string) => void
  ) => (
    <div
      key={index}
      onClick={() => clickHandler(item.href)} // Call the item click handler
      className="flex items-center gap-3 px-7 py-3 bg-gray-800/40 hover:bg-gray-800/50 rounded-4xl border border-gray-600 cursor-pointer transition-colors duration-150"
    >
      <div className="flex-1 min-w-0">
        <h4 className="text-white font-medium text-sm mb-1">{item.name}</h4>
        <p className="text-[#F5F5F7] text-xs leading-relaxed">
          {item.description}
        </p>
      </div>
    </div>
  );

  return (
    <div className="relative">
      <button
        className="text-[#F5F5F7] hover:text-white transition flex items-center gap-1 cursor-pointer"
        onMouseEnter={onMouseEnter}
      >
        {title}
        <ChevronDown
          className={`w-4 h-4 transition-transform duration-200 ${
            isOpen ? "rotate-180" : ""
          }`}
        />
      </button>

      {isOpen && (
        <div
          className="absolute top-11 -left-6 mt-2 px-7 py-5 w-[450px] bg-primary/90 border border-gray-700 rounded-xl shadow-2xl z-50 overflow-hidden"
          onMouseLeave={onMouseLeave}
        >
          <div className="mb-3">
            <h3 className="text-white font-medium">
              Our {title.toLowerCase()}
            </h3>
          </div>

          <div className="py-2 flex flex-col gap-3">
            {items.map((item, index) =>
              renderItem
                ? renderItem(item, index, onItemClick)
                : defaultRenderItem(item, index, onItemClick)
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default DropdownMenu;
