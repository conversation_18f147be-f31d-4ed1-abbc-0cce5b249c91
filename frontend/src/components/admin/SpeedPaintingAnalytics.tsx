import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON>, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { <PERSON><PERSON><PERSON>, <PERSON>, XAxis, <PERSON>Axis, CartesianGrid, Tooltip, Legend, ResponsiveContainer, PieChart, Pie, Cell } from 'recharts';
import { Loader2 } from 'lucide-react';
import ApiService from '@/services/api.service';

interface SpeedPaintingStats {
  totalJobs: number;
  completedJobs: number;
  failedJobs: number;
  successRate: number;
  averageProcessingTime: number;
  totalCreditsUsed: number;
  period: {
    start: string;
    end: string;
  };
}

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8'];

const SpeedPaintingAnalytics: React.FC = () => {
  const [stats, setStats] = useState<SpeedPaintingStats | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [timeRange, setTimeRange] = useState<'week' | 'month' | 'year'>('month');

  useEffect(() => {
    const fetchAnalytics = async () => {
      setLoading(true);
      setError(null);

      try {
        // Calculate date range based on selected time range
        const endDate = new Date();
        const startDate = new Date();
        
        if (timeRange === 'week') {
          startDate.setDate(endDate.getDate() - 7);
        } else if (timeRange === 'month') {
          startDate.setDate(endDate.getDate() - 30);
        } else if (timeRange === 'year') {
          startDate.setFullYear(endDate.getFullYear() - 1);
        }

        const response = await ApiService.get<{
          success: boolean;
          data: SpeedPaintingStats;
          error?: { message: string };
        }>(
          `/api/v1/admin/analytics/speed-painting?startDate=${startDate.toISOString()}&endDate=${endDate.toISOString()}`
        );

        if (response.data.success) {
          setStats(response.data.data);
        } else {
          setError(response.data.error?.message || 'Failed to fetch analytics data');
        }
      } catch (error) {
        console.error('Error fetching speed painting analytics:', error);
        setError('Failed to fetch analytics data. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchAnalytics();
  }, [timeRange]);

  // Prepare data for pie chart
  const pieData = stats
    ? [
        { name: 'Completed', value: stats.completedJobs },
        { name: 'Failed', value: stats.failedJobs },
      ]
    : [];

  // Prepare data for bar chart
  const barData = stats
    ? [
        {
          name: 'Jobs',
          Total: stats.totalJobs,
          Completed: stats.completedJobs,
          Failed: stats.failedJobs,
        },
        {
          name: 'Credits',
          Used: stats.totalCreditsUsed,
        },
      ]
    : [];

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Speed Painting Analytics</CardTitle>
        <CardDescription>
          View statistics and usage patterns for the speed painting feature
        </CardDescription>
        <Tabs defaultValue="month" className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="week" onClick={() => setTimeRange('week')}>
              Last Week
            </TabsTrigger>
            <TabsTrigger value="month" onClick={() => setTimeRange('month')}>
              Last Month
            </TabsTrigger>
            <TabsTrigger value="year" onClick={() => setTimeRange('year')}>
              Last Year
            </TabsTrigger>
          </TabsList>
        </Tabs>
      </CardHeader>
      <CardContent>
        {loading ? (
          <div className="flex justify-center items-center h-64">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
          </div>
        ) : error ? (
          <div className="text-center text-red-500 h-64 flex items-center justify-center">
            <p>{error}</p>
          </div>
        ) : stats ? (
          <div className="space-y-8">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">Total Jobs</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{stats.totalJobs}</div>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">Success Rate</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{stats.successRate.toFixed(2)}%</div>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">Credits Used</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{stats.totalCreditsUsed}</div>
                </CardContent>
              </Card>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <div>
                <h3 className="text-lg font-semibold mb-4">Job Status Distribution</h3>
                <div className="h-64">
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie
                        data={pieData}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="value"
                      >
                        {pieData.map((_, index) => (
                          <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                        ))}
                      </Pie>
                      <Tooltip />
                      <Legend />
                    </PieChart>
                  </ResponsiveContainer>
                </div>
              </div>

              <div>
                <h3 className="text-lg font-semibold mb-4">Job Metrics</h3>
                <div className="h-64">
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart data={barData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="name" />
                      <YAxis />
                      <Tooltip />
                      <Legend />
                      <Bar dataKey="Total" fill="#8884d8" />
                      <Bar dataKey="Completed" fill="#82ca9d" />
                      <Bar dataKey="Failed" fill="#ff8042" />
                      <Bar dataKey="Used" fill="#0088FE" />
                    </BarChart>
                  </ResponsiveContainer>
                </div>
              </div>
            </div>

            <div>
              <h3 className="text-lg font-semibold mb-4">Performance Metrics</h3>
              <p>
                <span className="font-medium">Average Processing Time:</span>{' '}
                {(stats.averageProcessingTime / 1000).toFixed(2)} seconds
              </p>
              <p>
                <span className="font-medium">Period:</span>{' '}
                {new Date(stats.period.start).toLocaleDateString()} to{' '}
                {new Date(stats.period.end).toLocaleDateString()}
              </p>
            </div>
          </div>
        ) : (
          <div className="text-center text-gray-500 h-64 flex items-center justify-center">
            <p>No data available</p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default SpeedPaintingAnalytics;
