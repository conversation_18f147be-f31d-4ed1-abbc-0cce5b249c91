import ShadowButton from "@/components/ui/shadowButton";
import { useState } from "react";

const AdminBlogSettings = () => {
    const [blogTitle, setBlogTitle] = useState('Miragic AI Blog');
    const [blogDescription, setBlogDescription] = useState('Latest updates and insights from the Miragic AI team');
    const [postsPerPage, setPostsPerPage] = useState('10');
    const [enableComments, setEnableComments] = useState(true);
    const [moderateComments, setModerateComments] = useState(true);
    const [enableRss, setEnableRss] = useState(true);
    const [seoEnabled, setSeoEnabled] = useState(true);
  
    return (
      <div className="bg-white/5 rounded-lg p-6">
        <h2 className="text-xl font-semibold mb-2">Blog Settings</h2>
        <p className="text-gray-400 mb-6">Configure blog settings and content management</p>
  
        <div className="space-y-6">
          <div>
            <label className="block text-sm font-medium mb-2">Blog Title</label>
            <input
              type="text"
              value={blogTitle}
              onChange={(e) => setBlogTitle(e.target.value)}
              className="w-full bg-white/5 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
            />
          </div>
  
          <div>
            <label className="block text-sm font-medium mb-2">Blog Description</label>
            <textarea
              value={blogDescription}
              onChange={(e) => setBlogDescription(e.target.value)}
              rows={3}
              className="w-full bg-white/5 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
            />
          </div>
  
          <div>
            <label className="block text-sm font-medium mb-2">Posts Per Page</label>
            <input
              type="number"
              value={postsPerPage}
              onChange={(e) => setPostsPerPage(e.target.value)}
              className="w-full bg-white/5 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
            />
          </div>
  
          <div className="bg-white/5 rounded-lg p-4">
            <div className="flex items-center justify-between mb-4">
              <div>
                <h3 className="font-medium">Enable Comments</h3>
                <p className="text-sm text-gray-400">Allow users to comment on blog posts</p>
              </div>
              <button
                onClick={() => setEnableComments(!enableComments)}
                className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                  enableComments ? 'bg-purple-600' : 'bg-gray-600'
                }`}
              >
                <span
                  className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                    enableComments ? 'translate-x-6' : 'translate-x-1'
                  }`}
                />
              </button>
            </div>
          </div>
  
          <div className="bg-white/5 rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="font-medium">Moderate Comments</h3>
                <p className="text-sm text-gray-400">Require approval before comments are published</p>
              </div>
              <button
                onClick={() => setModerateComments(!moderateComments)}
                className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                  moderateComments ? 'bg-purple-600' : 'bg-gray-600'
                }`}
              >
                <span
                  className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                    moderateComments ? 'translate-x-6' : 'translate-x-1'
                  }`}
                />
              </button>
            </div>
          </div>
  
          <div className="bg-white/5 rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="font-medium">Enable RSS Feed</h3>
                <p className="text-sm text-gray-400">Generate RSS feed for blog posts</p>
              </div>
              <button
                onClick={() => setEnableRss(!enableRss)}
                className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                  enableRss ? 'bg-purple-600' : 'bg-gray-600'
                }`}
              >
                <span
                  className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                    enableRss ? 'translate-x-6' : 'translate-x-1'
                  }`}
                />
              </button>
            </div>
          </div>
  
          <div className="bg-white/5 rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="font-medium">SEO Optimization</h3>
                <p className="text-sm text-gray-400">Enable search engine optimization features</p>
              </div>
              <button
                onClick={() => setSeoEnabled(!seoEnabled)}
                className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                  seoEnabled ? 'bg-purple-600' : 'bg-gray-600'
                }`}
              >
                <span
                  className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                    seoEnabled ? 'translate-x-6' : 'translate-x-1'
                  }`}
                />
              </button>
            </div>
          </div>
  
          <ShadowButton className="bg-purple-600 hover:bg-purple-700 text-white px-6 py-2 rounded-lg transition-colors">
            Save Changes
          </ShadowButton>
        </div>
      </div>
    );
  };

  

  export default AdminBlogSettings