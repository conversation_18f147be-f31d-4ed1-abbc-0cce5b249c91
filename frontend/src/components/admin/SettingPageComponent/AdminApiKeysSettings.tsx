import ShadowButton from "@/components/ui/shadowButton";
import { useState } from "react";

const AdminApiKeysSettings = () => {
  const [synthesiaKey, setSynthesiaKey] = useState("*".repeat(12));
  const [synthesiaEndpoint, setSynthesiaEndpoint] = useState(
    "https://api.synthesia.io/v1"
  );
  const [stabilityKey, setStabilityKey] = useState("*".repeat(12));
  const [stabilityEndpoint, setStabilityEndpoint] = useState(
    "https://api.stability.io/v1"
  );
  const [removalKey, setRemovalKey] = useState("*".repeat(12));
  const [removalEndpoint, setRemovalEndpoint] = useState(
    "https://api.removal.io/v1"
  );

  return (
    <div className="bg-white/5 rounded-lg p-6">
      <h2 className="text-xl font-semibold mb-2">API Configuration</h2>
      <p className="text-gray-400 mb-6">
        Configure API keys and endpoints for third-party services
      </p>

      <div className="space-y-8">
        {/* Video Generation (Synthesia) */}
        <div>
          <h3 className="text-lg font-medium mb-4">
            Video Generation (Synthesia)
          </h3>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium mb-2">API Key</label>
              <input
                type="password"
                value={synthesiaKey}
                onChange={(e) => setSynthesiaKey(e.target.value)}
                className="w-full bg-white/5 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-2">
                API Endpoint
              </label>
              <input
                type="url"
                value={synthesiaEndpoint}
                onChange={(e) => setSynthesiaEndpoint(e.target.value)}
                className="w-full bg-white/5 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
              />
            </div>
          </div>
        </div>

        {/* Video Generation (Stability AI) */}
        <div>
          <h3 className="text-lg font-medium mb-4">
            Video Generation (Stability AI)
          </h3>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium mb-2">API Key</label>
              <input
                type="password"
                value={stabilityKey}
                onChange={(e) => setStabilityKey(e.target.value)}
                className="w-full bg-white/5 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-2">
                API Endpoint
              </label>
              <input
                type="url"
                value={stabilityEndpoint}
                onChange={(e) => setStabilityEndpoint(e.target.value)}
                className="w-full bg-white/5 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
              />
            </div>
          </div>
        </div>

        {/* Background Removal */}
        <div>
          <h3 className="text-lg font-medium mb-4">
            Background Removal (Removal AI)
          </h3>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium mb-2">API Key</label>
              <input
                type="password"
                value={removalKey}
                onChange={(e) => setRemovalKey(e.target.value)}
                className="w-full bg-white/5 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-2">
                API Endpoint
              </label>
              <input
                type="url"
                value={removalEndpoint}
                onChange={(e) => setRemovalEndpoint(e.target.value)}
                className="w-full bg-white/5 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
              />
            </div>
          </div>
        </div>

        <ShadowButton className="bg-purple-600 hover:bg-purple-700 text-white px-6 py-2 rounded-lg transition-colors">
          Save Changes
        </ShadowButton>
      </div>
    </div>
  );
};

// add comment
export default AdminApiKeysSettings; 
