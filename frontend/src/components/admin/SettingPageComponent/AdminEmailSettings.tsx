import ShadowButton from "@/components/ui/shadowButton";
import { useState } from "react";

const AdminEmailSettings = () => {
  const [smtpServer, setSmtpServer] = useState("smtp.resend.com");
  const [smtpPort, setSmtpPort] = useState("587");
  const [smtpUsername, setSmtpUsername] = useState("apikey");
  const [smtpPassword, setSmtpPassword] = useState("*".repeat(10));
  const [senderEmail, setSenderEmail] = useState("<EMAIL>");
  const [senderName, setSenderName] = useState("Miragic AI");

  return (
    <div className="bg-white/5 rounded-lg p-6">
      <h2 className="text-xl font-semibold mb-2">Email Configuration</h2>
      <p className="text-gray-400 mb-6">
        Configure email settings for notifications and user communications
      </p>

      <div className="space-y-6">
        <div>
          <label className="block text-sm font-medium mb-2">SMTP Server</label>
          <input
            type="text"
            value={smtpServer}
            onChange={(e) => setSmtpServer(e.target.value)}
            className="w-full bg-white/5 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
          />
        </div>

        <div>
          <label className="block text-sm font-medium mb-2">SMTP Port</label>
          <input
            type="number"
            value={smtpPort}
            onChange={(e) => setSmtpPort(e.target.value)}
            className="w-full bg-white/5 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
          />
        </div>

        <div>
          <label className="block text-sm font-medium mb-2">
            SMTP Username
          </label>
          <input
            type="text"
            value={smtpUsername}
            onChange={(e) => setSmtpUsername(e.target.value)}
            className="w-full bg-white/5 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
          />
        </div>

        <div>
          <label className="block text-sm font-medium mb-2">
            SMTP Password
          </label>
          <input
            type="password"
            value={smtpPassword}
            onChange={(e) => setSmtpPassword(e.target.value)}
            className="w-full bg-white/5 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
          />
        </div>

        <div>
          <label className="block text-sm font-medium mb-2">Sender Email</label>
          <input
            type="email"
            value={senderEmail}
            onChange={(e) => setSenderEmail(e.target.value)}
            className="w-full bg-white/5 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
          />
        </div>

        <div>
          <label className="block text-sm font-medium mb-2">Sender Name</label>
          <input
            type="text"
            value={senderName}
            onChange={(e) => setSenderName(e.target.value)}
            className="w-full bg-white/5 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
          />
        </div>

        <ShadowButton className="bg-purple-600 hover:bg-purple-700 text-white px-6 py-2 rounded-lg transition-colors">
          Save Changes
        </ShadowButton>
      </div>
    </div>
  );
};

export default AdminEmailSettings;