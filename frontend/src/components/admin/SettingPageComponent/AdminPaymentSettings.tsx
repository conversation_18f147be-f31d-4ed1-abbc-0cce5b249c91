import ShadowButton from "@/components/ui/shadowButton";
import { useState } from "react";

const AdminPaymentSettings = () => {
  const [enableStripe, setEnableStripe] = useState(true);
  const [stripePublicKey, setStripePublicKey] = useState("*".repeat(12));
  const [stripeSecretKey, setStripeSecretKey] = useState("*".repeat(14));
  const [enablePaypal, setEnablePaypal] = useState(true);
  const [paypalClientId, setPaypalClientId] = useState("*".repeat(12));
  const [paypalSecret, setPaypalSecret] = useState("*".repeat(14));
  const [testMode, setTestMode] = useState(true);

  return (
    <div className="bg-white/5 rounded-lg p-6">
      <h2 className="text-xl font-semibold mb-2">Payment Configuration</h2>
      <p className="text-gray-400 mb-6">
        Configure payment gateways and settings
      </p>

      <div className="space-y-8">
        {/* Stripe */}
        <div>
          <div className="flex items-center justify-between mb-4 border border-gray-500 rounded-xl bg-white/10 py-3 px-5">
            <h3 className="text-lg font-medium">Stripe</h3>
            <button
              onClick={() => setEnableStripe(!enableStripe)}
              className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                enableStripe ? "bg-purple-600" : "bg-gray-600"
              }`}
            >
              <span
                className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                  enableStripe ? "translate-x-6" : "translate-x-1"
                }`}
              />
            </button>
          </div>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium mb-2">
                Stripe Public Key
              </label>
              <input
                type="password"
                value={stripePublicKey}
                onChange={(e) => setStripePublicKey(e.target.value)}
                className="w-full bg-white/5 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-2">
                Stripe Secret Key
              </label>
              <input
                type="password"
                value={stripeSecretKey}
                onChange={(e) => setStripeSecretKey(e.target.value)}
                className="w-full bg-white/5 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
              />
            </div>
          </div>
        </div>

        {/* PayPal */}
        <div>
          <div className="flex items-center justify-between mb-4 border border-gray-500 rounded-xl bg-white/10 py-3 px-5">
            <h3 className="text-lg font-medium">PayPal</h3>
            <button
              onClick={() => setEnablePaypal(!enablePaypal)}
              className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                enablePaypal ? "bg-purple-600" : "bg-gray-600"
              }`}
            >
              <span
                className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                  enablePaypal ? "translate-x-6" : "translate-x-1"
                }`}
              />
            </button>
          </div>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium mb-2">
                PayPal Client ID
              </label>
              <input
                type="password"
                value={paypalClientId}
                onChange={(e) => setPaypalClientId(e.target.value)}
                className="w-full bg-white/5 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-2">
                PayPal Secret
              </label>
              <input
                type="password"
                value={paypalSecret}
                onChange={(e) => setPaypalSecret(e.target.value)}
                className="w-full bg-white/5 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
              />
            </div>
          </div>
        </div>

        {/* Test Mode */}
        <div className="bg-white/5 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="font-medium">Test Mode</h3>
              <p className="text-sm text-gray-400">
                When enabled, payments will be processed in test/sandbox mode
              </p>
            </div>
            <button
              onClick={() => setTestMode(!testMode)}
              className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                testMode ? "bg-purple-600" : "bg-gray-600"
              }`}
            >
              <span
                className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                  testMode ? "translate-x-6" : "translate-x-1"
                }`}
              />
            </button>
          </div>
        </div>

        <ShadowButton className="bg-purple-600 hover:bg-purple-700 text-white px-6 py-2 rounded-lg transition-colors">
          Save Changes
        </ShadowButton>
      </div>
    </div>
  );
};

export default AdminPaymentSettings;
