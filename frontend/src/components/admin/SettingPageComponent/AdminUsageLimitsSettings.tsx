import ShadowButton from "@/components/ui/shadowButton";
import { useState } from "react";

const AdminUsageLimitsSettings = () => {
  const [freePlan, setFreePlan] = useState({
    videoGeneration: "1",
    imageGeneration: "10",
    backgroundRemoval: "20",
  });
  const [starterPlan, setStarterPlan] = useState({
    videoGeneration: "30",
    imageGeneration: "30",
    backgroundRemoval: "30",
  });
  const [proPlan, setProPlan] = useState({
    videoGeneration: "50",
    imageGeneration: "50",
    backgroundRemoval: "50",
  });
  const [businessPlan, setBusinessPlan] = useState({
    videoGeneration: "Unlimited",
    imageGeneration: "Unlimited",
    backgroundRemoval: "Unlimited",
  });

  return (
    <div className="bg-white/5 rounded-lg p-6">
      <h2 className="text-xl font-semibold mb-2">Usage Limits</h2>
      <p className="text-gray-400 mb-6">
        Configure usage limits for different subscription plans
      </p>

      <div className="space-y-8">
        {/* Free Plan */}
        <div>
          <h3 className="text-lg font-medium mb-4">Free plan</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium mb-2">
                Video Generation
              </label>
              <input
                type="number"
                value={freePlan.videoGeneration}
                onChange={(e) =>
                  setFreePlan({ ...freePlan, videoGeneration: e.target.value })
                }
                className="w-full bg-white/5 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-2">
                Image Generation
              </label>
              <input
                type="number"
                value={freePlan.imageGeneration}
                onChange={(e) =>
                  setFreePlan({ ...freePlan, imageGeneration: e.target.value })
                }
                className="w-full bg-white/5 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-2">
                Background Removal
              </label>
              <input
                type="number"
                value={freePlan.backgroundRemoval}
                onChange={(e) =>
                  setFreePlan({
                    ...freePlan,
                    backgroundRemoval: e.target.value,
                  })
                }
                className="w-full bg-white/5 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
              />
            </div>
          </div>
        </div>

        {/* Starter Plan */}
        <div>
          <h3 className="text-lg font-medium mb-4">Starter plan</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium mb-2">
                Video Generation
              </label>
              <input
                type="number"
                value={starterPlan.videoGeneration}
                onChange={(e) =>
                  setStarterPlan({
                    ...starterPlan,
                    videoGeneration: e.target.value,
                  })
                }
                className="w-full bg-white/5 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-2">
                Image Generation
              </label>
              <input
                type="number"
                value={starterPlan.imageGeneration}
                onChange={(e) =>
                  setStarterPlan({
                    ...starterPlan,
                    imageGeneration: e.target.value,
                  })
                }
                className="w-full bg-white/5 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-2">
                Background Removal
              </label>
              <input
                type="number"
                value={starterPlan.backgroundRemoval}
                onChange={(e) =>
                  setStarterPlan({
                    ...starterPlan,
                    backgroundRemoval: e.target.value,
                  })
                }
                className="w-full bg-white/5 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
              />
            </div>
          </div>
        </div>

        {/* Pro Plan */}
        <div>
          <h3 className="text-lg font-medium mb-4">Pro plan</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium mb-2">
                Video Generation
              </label>
              <input
                type="number"
                value={proPlan.videoGeneration}
                onChange={(e) =>
                  setProPlan({ ...proPlan, videoGeneration: e.target.value })
                }
                className="w-full bg-white/5 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-2">
                Image Generation
              </label>
              <input
                type="number"
                value={proPlan.imageGeneration}
                onChange={(e) =>
                  setProPlan({ ...proPlan, imageGeneration: e.target.value })
                }
                className="w-full bg-white/5 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-2">
                Background Removal
              </label>
              <input
                type="number"
                value={proPlan.backgroundRemoval}
                onChange={(e) =>
                  setProPlan({ ...proPlan, backgroundRemoval: e.target.value })
                }
                className="w-full bg-white/5 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
              />
            </div>
          </div>
        </div>

        {/* Business Plan */}
        <div>
          <h3 className="text-lg font-medium mb-4">Business plan</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium mb-2">
                Video Generation
              </label>
              <input
                type="text"
                value={businessPlan.videoGeneration}
                onChange={(e) =>
                  setBusinessPlan({
                    ...businessPlan,
                    videoGeneration: e.target.value,
                  })
                }
                className="w-full bg-white/5 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-2">
                Image Generation
              </label>
              <input
                type="text"
                value={businessPlan.imageGeneration}
                onChange={(e) =>
                  setBusinessPlan({
                    ...businessPlan,
                    imageGeneration: e.target.value,
                  })
                }
                className="w-full bg-white/5 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-2">
                Background Removal
              </label>
              <input
                type="text"
                value={businessPlan.backgroundRemoval}
                onChange={(e) =>
                  setBusinessPlan({
                    ...businessPlan,
                    backgroundRemoval: e.target.value,
                  })
                }
                className="w-full bg-white/5 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
              />
            </div>
          </div>
        </div>

        <ShadowButton className="bg-purple-600 hover:bg-purple-700 text-white px-6 py-2 rounded-lg transition-colors">
          Save Changes
        </ShadowButton>
      </div>
    </div>
  );
};

export default AdminUsageLimitsSettings;
