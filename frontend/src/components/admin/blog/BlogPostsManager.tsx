import React, { useEffect, useState } from "react";
import {
  getAllBlogPostsAdmin,
  deleteBlogPostAdmin,
  type BlogPost,
  type PaginatedBlogPosts,
} from "../../../services/adminBlog.service";
import { Button } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { toast } from "sonner";
import { Edit, Trash2 } from "lucide-react";
// import { useNavigate } from "react-router-dom"; // We'll add this later for navigation

const BlogPostsManager: React.FC = () => {
  const [posts, setPosts] = useState<BlogPost[]>([]);
  const [pagination, setPagination] = useState<
    PaginatedBlogPosts["pagination"] | null
  >(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  // const navigate = useNavigate(); // For navigation to create/edit pages

  const fetchPosts = async (page = 1, limit = 10) => {
    setLoading(true);
    setError(null);
    try {
      const response = await getAllBlogPostsAdmin(page, limit);
      setPosts(response.data);
      setPagination(response.pagination);
    } catch (err) {
      setError("Failed to fetch blog posts. Please try again.");
      console.error("Fetch Posts Error:", err);
      toast.error("Failed to fetch blog posts.");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchPosts();
  }, []);

  const handleDeletePost = async (postId: string) => {
    if (!window.confirm("Are you sure you want to delete this post?")) {
      return;
    }
    try {
      await deleteBlogPostAdmin(postId);
      toast.success("Blog post deleted successfully!");
      fetchPosts(pagination?.currentPage || 1); // Refetch posts from current page
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
    } catch (apiError: any) {
      console.error("Failed to delete blog post:", apiError);
      toast.error(
        `Error deleting post: ${
          apiError?.response?.data?.message ||
          apiError.message ||
          "Unknown error"
        }`
      );
    }
  };

  const handleEditPost = (postId: string) => {
    console.log(`Navigate to edit post page for ID: ${postId}`); // Placeholder
    // navigate(`/admin/blog/posts/edit/${postId}`); // Example navigation
    toast.info(
      `Edit post functionality for ID ${postId} will be implemented here.`
    );
  };

  if (loading) {
    return <div className="text-center py-10">Loading blog posts...</div>;
  }

  if (error) {
    return <div className="text-center py-10 text-red-500">{error}</div>;
  }

  return (
    <div className="container mx-auto py-8 px-4 md:px-6">
      {/* <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-semibold text-white">Blog Posts</h1>
        <Button
          onClick={handleCreateNewPost}
          className="bg-purple-600 hover:bg-purple-700 text-white"
        >
          <PlusCircle className="mr-2 h-4 w-4" /> Create New Post
        </Button>
      </div> */}

      {posts.length === 0 ? (
        <div className="text-center py-10 text-gray-400">
          No blog posts found.
        </div>
      ) : (
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="text-white">Title</TableHead>
              <TableHead className="text-white">Status</TableHead>
              <TableHead className="text-white">Author ID</TableHead>
              <TableHead className="text-white">Created At</TableHead>
              <TableHead className="text-white">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {posts.map((post) => (
              <TableRow key={post.id}>
                <TableCell className="font-medium text-gray-300">
                  {post.title}
                </TableCell>
                <TableCell className="text-gray-400">{post.status}</TableCell>
                <TableCell className="text-gray-400">{post.authorId}</TableCell>
                <TableCell className="text-gray-400">
                  {new Date(post.createdAt).toLocaleDateString()}
                </TableCell>
                <TableCell>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleEditPost(post.id)}
                    className="mr-2 border-blue-500 text-blue-500 hover:bg-blue-600 hover:text-white focus:ring-blue-500 transition-colors duration-150"
                  >
                    <Edit className="mr-1 h-3 w-3" /> Edit
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleDeletePost(post.id)}
                    className="border-red-500 text-red-500 hover:bg-red-600 hover:text-white focus:ring-red-500 transition-colors duration-150"
                  >
                    <Trash2 className="mr-1 h-3 w-3" /> Delete
                  </Button>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      )}
      {/* Basic Pagination (can be improved) */}
      {pagination && pagination.totalPages > 1 && (
        <div className="flex justify-center items-center space-x-2 mt-6">
          <Button
            onClick={() => fetchPosts(pagination.currentPage - 1)}
            disabled={pagination.currentPage === 1}
            variant="outline"
          >
            Previous
          </Button>
          <span className="text-white">
            Page {pagination.currentPage} of {pagination.totalPages}
          </span>
          <Button
            onClick={() => fetchPosts(pagination.currentPage + 1)}
            disabled={pagination.currentPage === pagination.totalPages}
            variant="outline"
          >
            Next
          </Button>
        </div>
      )}
    </div>
  );
};

export default BlogPostsManager;
