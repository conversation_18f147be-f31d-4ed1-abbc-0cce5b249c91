import { But<PERSON> } from "@/components/ui/button";
import { Text } from "@/components/ui/text";
import { AiToolIcon } from "@/lib/icons";
import { ChevronRight, Loader2 } from "lucide-react";
import type { FileHandlerState } from "../../../hooks/useFileHandler";

interface LeftSectionProps {
  fileHandler: {
    state: FileHandlerState;
    handleProcessImage: () => Promise<void>;
  };
}

export const LeftSection = ({ fileHandler }: LeftSectionProps) => {
  const { state, handleProcessImage } = fileHandler;
  const { uploadedFile, selectedModel, isProcessing } = state;

  return (
    <div className="flex flex-col justify-center items-center lg:items-start gap-6 lg:gap-8 h-full lg:w-1/2 text-center lg:text-left">
      {!uploadedFile && !selectedModel && (
        <div className="flex justify-center lg:justify-start">
          <Button className="flex items-center !px-7 text-white text-base rounded-full bg-gradient-to-r from-[rgba(44,155,247,0.10)] to-[rgba(128,84,243,0.10)] hover:from-[rgba(44,155,247,0.15)] hover:to-[rgba(128,84,243,0.15)] shadow-[0_0_10px_rgba(129,90,219,0.6)] border border-white/15 h-[50px]">
            <AiToolIcon />
            <span>AI Tool</span>
          </Button>
        </div>
      )}

      <div>
        <Text className="text-3xl lg:text-4xl text-white font-semibold mb-4 lg:mb-6">
          Background Remover
        </Text>
        <Text className="text-sm md:text-base lg:text-lg text-white/66 leading-relaxed max-w-xl mx-auto lg:mx-0">
          No matter if you want to make a background transparent (PNG), add a white background to a photo, 
          extract or isolate the subject, or get the cutout of a photo - you can do all this and more with 
          miragic.ai, the AI background remover for professionals.
        </Text>
      </div>

      {(uploadedFile || selectedModel) && (
        <div className="mt-2">
          <Button
            disabled={isProcessing}
            variant="animeGradient"
            onClick={handleProcessImage}
            className="bg-radial-[at_5%_5%] from-[#2C9BF7] to-[#8054F3] rounded-full border border-white/15 !px-7 h-[50px]"
          >
            {isProcessing ? (
              <Loader2 className="animate-spin" size={18} />
            ) : (
              <>
                <span>Generate</span>
                <ChevronRight />
              </>
            )}
          </Button>
        </div>
      )}
    </div>
  );
}; 