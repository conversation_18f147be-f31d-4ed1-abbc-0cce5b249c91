import { Swiper, SwiperSlide } from "swiper/react";
import { Autoplay, Navigation, EffectFade } from "swiper/modules";
import type { Review } from '../type';
import { Swiper as SwiperType } from "swiper";
import { ReviewCard } from "./ReviewCard";

export const SwiperColumn = ({
  reviews,
  delay,
  reverseDirection = false,
  translateY = "",
  onSwiper,
  className,
}: {
  reviews: Review[];
  delay: number;
  reverseDirection?: boolean;
  translateY?: string;
  onSwiper: (swiper: SwiperType) => void;
  className?: string;
}) => (
  <div className={`review-swipper relative h-[860px] ${translateY} ${className}`}>
    <Swiper
      modules={[Autoplay, Navigation, EffectFade]}
      direction="vertical"
      autoplay={{
        delay,
        disableOnInteraction: false,
        reverseDirection,
      }}
      onSwiper={onSwiper}
      spaceBetween={32}
      slidesPerView={3}
      loop={true}
      navigation={false}
      speed={6000}
      effect="slide"
      className="h-full [&_.swiper-wrapper]:transition-all [&_.swiper-wrapper]:duration-700 [&_.swiper-wrapper]:ease-out"
    >
      {reviews.map((review, idx) => (
        <SwiperSlide
          key={idx}
          className="transition-all duration-700 ease-out"
        >
          <ReviewCard review={review} />
        </SwiperSlide>
      ))}
    </Swiper>
  </div>
);