import { But<PERSON> } from "@/components/ui/button";
import { Text } from "@/components/ui/text";
import { AlertCircle, Plus, RefreshCw, X } from "lucide-react";
import type { FileHandlerState } from "../../../hooks/useFileHandler";
import { useState, type DragEvent } from "react";
import ReactBeforeSliderComponent from "react-before-after-slider-component";
import "react-before-after-slider-component/dist/build.css";
import { ModelPreview } from "../../ui/model-preview";
import { ACCEPTED_FILE_TYPES, MODEL_IMAGES } from "@/lib/utils";
import { StarOverlay } from "../../ui/star-overlay";
import { BackgroundBGModal } from "@/components/common/BackgroundBGModal";

interface RightSectionProps {
  fileHandler: {
    state: FileHandlerState;
    resetState: () => void;
    handleFile: (file: File) => void;
    handleModelSelect: (modelPath: string) => void;
    handleDownload: () => Promise<void>;
    handleBackgroundColor: (color: string) => void;
  };
}

const ErrorDisplay = ({ message }: { message: string }) => (
  <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 w-11/12">
    <div className="p-3 bg-red-900/30 border border-red-700 rounded-lg flex items-center gap-2">
      <AlertCircle className="text-red-500" size={18} />
      <p className="text-red-400 text-sm">{message}</p>
    </div>
  </div>
);

export const RightSection = ({ fileHandler }: RightSectionProps) => {
  const {
    state,
    resetState,
    handleFile,
    handleModelSelect,
    handleDownload,
    handleBackgroundColor,
  } = fileHandler;
  const {
    previewUrl,
    uploadedFile,
    processedImage,
    error,
    selectedModel,
    isProcessing,
  } = state;

  const [isBackgroundModalOpen, setIsBackgroundModalOpen] =
    useState<boolean>(false);

  const hasFile = !!uploadedFile || !!selectedModel;

  const handleDrop = (e: DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    if (processedImage && previewUrl) {
      return;
    }
    const files = e.dataTransfer.files;
    if (files?.[0]) handleFile(files[0]);
  };

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (processedImage && previewUrl) {
      e.target.value = '';
      return;
    }
    const files = e.target.files;
    if (files?.[0]) handleFile(files[0]);
  };

  const handleDragOver = (e: DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    if (!processedImage || !previewUrl) {
      e.dataTransfer.dropEffect = 'copy';
    } else {
      e.dataTransfer.dropEffect = 'none';
    }
  };

  return (
    <div className="flex flex-col gap-6 lg:w-1/2 w-full">
      <div
        className={`bg-gradient-to-r from-[rgba(44,155,247,0.10)] to-[rgba(128,84,243,0.10)] border border-white/15 rounded-lg flex items-center justify-center w-full min-h-[300px] max-h-[400px] relative ${
          hasFile ? "" : "p-8"
        } ${processedImage && previewUrl ? 'cursor-default' : 'cursor-pointer'}`}
        onDragOver={handleDragOver}
        onDrop={handleDrop}
      >
        <input
          type="file"
          id="file-upload"
          accept={ACCEPTED_FILE_TYPES.join(",")}
          onChange={handleFileSelect}
          className="hidden"
          disabled={!!(processedImage && previewUrl)}
        />

        <label
          htmlFor="file-upload"
          className={`relative w-full h-full flex items-center justify-center ${
            processedImage && previewUrl ? 'cursor-default' : 'cursor-pointer'
          }`}
          onClick={(e) => {
            if (processedImage && previewUrl) {
              e.preventDefault();
              e.stopPropagation();
              return false;
            }
          }}
        >
          {(uploadedFile || selectedModel) && previewUrl ? (
            <div className="relative w-full h-full z-50">
              {isProcessing && <StarOverlay />}
              <Button
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  resetState();
                }}
                className="border-none absolute -top-12 right-2 bg-red-500 text-white rounded-full cursor-pointer hover:bg-red-600 transition"
              >
                <X className="w-4 h-4" />
              </Button>

              {/* Show before/after slider if both images are available, otherwise show single image */}
              {processedImage && previewUrl ? (
                <div className="relative w-full h-full max-h-[350px] overflow-hidden rounded-lg">
                  <div className="w-full h-full max-h-[350px]">
                    <ReactBeforeSliderComponent
                      firstImage={{
                        imageUrl: processedImage,
                        alt: "Processed Image",
                      }}
                      secondImage={{
                        imageUrl: previewUrl,
                        alt: "Original Image",
                      }}
                      currentPercentPosition={50}
                      className="w-full h-full max-h-[350px]"
                      withResizeFeel={true}
                      delimiterIconStyles={{
                        backgroundColor: "#2C9BF7",
                        borderColor: "#2C9BF7",
                      }}
                      delimiterColor="#2C9BF7"
                    />
                  </div>
                  <div className="absolute top-2 left-2 bg-black/50 text-white text-xs px-2 py-1 rounded z-10">
                    Before
                  </div>
                  <div className="absolute top-2 right-8 bg-black/50 text-white text-xs px-2 py-1 rounded z-10">
                    After
                  </div>
                </div>
              ) : (
                <img
                  loading="lazy"
                  src={previewUrl}
                  alt="Selected preview"
                  className="w-full h-full max-h-[350px] object-contain rounded-lg"
                />
              )}
            </div>
          ) : (
            <div className="flex flex-col items-center justify-center h-full space-y-5">
              <Button
                variant="animeGradient"
                onClick={() => document.getElementById("file-upload")?.click()}
                className="bg-radial-[at_5%_5%] from-[#2C9BF7] to-[#8054F3] rounded-full border border-white/15 !px-7 h-[50px]"
              >
                <Plus />
                <span>Upload a file</span>
              </Button>
              <Text className="text-xl text-white max-w-[300px] text-center">
                or drag an image
              </Text>
              <Text className="text-sm text-gray-500 max-w-[300px] text-center">
                Original image must be .png, .jpg, .jpeg or webp format and 30mb
                max size.
              </Text>
            </div>
          )}
        </label>

        {error && <ErrorDisplay message={error} />}
      </div>
      {processedImage && (
        <>
          {" "}
          {isBackgroundModalOpen && (
            <BackgroundBGModal
              isOpen={isBackgroundModalOpen}
              onClose={() => setIsBackgroundModalOpen(false)}
              onColorSelect={handleBackgroundColor}
              onBackgroundSelect={handleBackgroundColor}
            />
          )}
          <div className="relative px-4 lg:px-5 min-h-16 lg:min-h-20 flex items-center justify-center gap-3 lg:gap-5 transform -translate-y-3 lg:-translate-y-4 -translate-x-1/2 bottom-0 left-1/2 rounded-[10px]">
            <Button
              variant="animeShine"
              onClick={resetState}
              className="rounded-full bg-black px-7 hover:bg-black h-[40px] text-sm"
            >
              <RefreshCw className="text-gray-400 w-4 h-4" />
            </Button>
            <Button
              outline={false}
              onClick={() => setIsBackgroundModalOpen(true)}
              variant={"animeShine"}
              className="rounded-full bg-black px-7 hover:bg-black h-[40px] text-sm"
            >
              Background
            </Button>
            <Button
              variant="animeGradient"
              onClick={handleDownload}
              className="bg-radial-[at_5%_5%] from-[#2C9BF7] to-[#8054F3] rounded-full border border-white/15 !px-7 h-[40px] text-sm"
            >
              Download
            </Button>
          </div>
        </>
      )}

      <div className="flex gap-4 items-center">
        <div className="text-center lg:text-left">
          <Text className="text-base text-white/65 mb-2">
            No Image <br />
            Try one of these:
          </Text>
        </div>

        <div className="grid grid-cols-5 gap-3 max-w-[420px] mx-auto lg:mx-0">
          {MODEL_IMAGES.slice(0, 5).map((modelPath, index) => (
            <ModelPreview
              key={index}
              modelPath={modelPath}
              isSelected={selectedModel === modelPath}
              onSelect={() => handleModelSelect(modelPath)}
            />
          ))}
        </div>
      </div>
    </div>
  );
};
