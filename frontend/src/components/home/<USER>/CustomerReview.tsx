import { Text } from "../../ui/text";
import GradientText from "../../common/GradientText";
import { SeperatorLineIcon } from "@/lib/icons";
import { Swiper as SwiperType } from "swiper";
import "swiper/css";
import "swiper/css/navigation";
import { useRef } from "react";
import type { Review } from "../type";
import { SwiperColumn } from "./SwiperColumn";

const reviews1st: Review[] = [
  {
    name: "<PERSON>",
    avatar: "/icons/user_8.svg",
    date: "Sep 27, 2025",
    stars: 5,
    text: "Miragic's image background removal tool is a game changer for our e-commerce store. It's fast, accurate, and saves us hours of manual editing every week.",
  },
  {
    name: "<PERSON><PERSON>",
    avatar: "/icons/user_9.svg",
    date: "Sep 27, 2025",
    stars: 5,
    text: "<PERSON><PERSON>’s image generator is amazing for creating promotional visuals. The results are high-quality and require almost no touch-up.",
  },
  {
    name: "<PERSON>",
    avatar: "/icons/user_3.svg",
    date: "Sep 27, 2025",
    stars: 5,
    text: "Speedpainting in <PERSON><PERSON> feels like having a creative assistant. I’ve never been more productive in my storyboarding work.",
  },
  {
    name: "<PERSON> <PERSON>tosh",
    avatar: "/icons/user_8.svg",
    date: "Sep 27, 2025",
    stars: 5,
    text: "Miragic's image background removal tool is a game changer for our e-commerce store. It's fast, accurate, and saves us hours of manual editing every week.",
  },
  {
    name: "Ashlynn Philips",
    avatar: "/icons/user_9.svg",
    date: "Sep 27, 2025",
    stars: 5,
    text: "Miragic’s image generator is amazing for creating promotional visuals. The results are high-quality and require almost no touch-up.",
  },
  {
    name: "Jocelyn Kenter",
    avatar: "/icons/user_3.svg",
    date: "Sep 27, 2025",
    stars: 5,
    text: "Speedpainting in Miragic feels like having a creative assistant. I’ve never been more productive in my storyboarding work.",
  },
];

const reviews2nd: Review[] = [
  {
    name: "Cooper Mango",
    avatar: "/icons/user_10.svg",
    date: "Sep 27, 2025",
    stars: 5,
    text: "The speedpainting feature in Miragic’s suite lets me create dynamic concept art in record time. It’s intuitive and fun to use!",
  },
  {
    name: "Zain Philips",
    avatar: "/icons/user_4.svg",
    date: "Sep 27, 2025",
    stars: 5,
    text: "Our sales team swears by Miragic’s Sales Pilot tool. It provides smart lead insights and has noticeably improved our conversion rate.",
  },
  {
    name: "Anika Curtis",
    avatar: "/icons/user_11.svg",
    date: "Sep 27, 2025",
    stars: 5,
    text: "Face swap features can be gimmicky, but Miragic nails the realism. We use it for both fun and professional content.",
  },
  {
    name: "Cooper Mango",
    avatar: "/icons/user_10.svg",
    date: "Sep 27, 2025",
    stars: 5,
    text: "The speedpainting feature in Miragic’s suite lets me create dynamic concept art in record time. It’s intuitive and fun to use!",
  },
  {
    name: "Zain Philips",
    avatar: "/icons/user_4.svg",
    date: "Sep 27, 2025",
    stars: 5,
    text: "Our sales team swears by Miragic’s Sales Pilot tool. It provides smart lead insights and has noticeably improved our conversion rate.",
  },
  {
    name: "Anika Curtis",
    avatar: "/icons/user_11.svg",
    date: "Sep 27, 2025",
    stars: 5,
    text: "Face swap features can be gimmicky, but Miragic nails the realism. We use it for both fun and professional content.",
  },
];

const reviews3rd: Review[] = [
  {
    name: "Hidilson",
    avatar: "/icons/user_2.svg",
    date: "Sep 27, 2025",
    stars: 5,
    text: "We integrated Miragic’s virtual-try-on for our online eyewear shop, and customer engagement shot up. Shoppers love trying before buying!",
  },
  {
    name: "Corey Dokidis",
    avatar: "/icons/user_7.svg",
    date: "Sep 27, 2025",
    stars: 5,
    text: "I’ve tried several background removal tools, but Miragic is by far the fastest and most precise. Great for batch processing too!",
  },
  {
    name: "Davis Donin",
    avatar: "/icons/user_6.svg",
    date: "Sep 26, 2025",
    stars: 5,
    text: "Miragic’s Sales Pilot integrates seamlessly with our CRM. The AI suggestions are smart and actionable.",
  },
  {
    name: "Hidilson",
    avatar: "/icons/user_2.svg",
    date: "Sep 27, 2025",
    stars: 5,
    text: "We integrated Miragic’s virtual-try-on for our online eyewear shop, and customer engagement shot up. Shoppers love trying before buying!",
  },
  {
    name: "Corey Dokidis",
    avatar: "/icons/user_7.svg",
    date: "Sep 27, 2025",
    stars: 5,
    text: "I’ve tried several background removal tools, but Miragic is by far the fastest and most precise. Great for batch processing too!",
  },
  {
    name: "Davis Donin",
    avatar: "/icons/user_6.svg",
    date: "Sep 26, 2025",
    stars: 5,
    text: "Miragic’s Sales Pilot integrates seamlessly with our CRM. The AI suggestions are smart and actionable.",
  },
];

const reviews4th: Review[] = [
  {
    name: "Craig",
    avatar: "/icons/user_12.svg",
    date: "Sep 27, 2025",
    stars: 5,
    text: "The face swap feature is incredibly realistic. We use it for social media campaigns, and it drives a ton of user-generated content.",
  },
  {
    name: "Brandon Saris",
    avatar: "/icons/user_5.svg",
    date: "Sep 27, 2025",
    stars: 5,
    text: "The virtual-try-on tool is helping our beauty brand reduce returns. Customers get a true sense of how a shade or product looks on them.",
  },
  {
    name: "Rayna Vaccaro",
    avatar: "/icons/user_13.svg",
    date: "Sep 27, 2025",
    stars: 5,
    text: "From image generation to background removal, Miragic offers a complete toolkit for content creators. Absolutely essential!",
  },
  {
    name: "Craig",
    avatar: "/icons/user_12.svg",
    date: "Sep 27, 2025",
    stars: 5,
    text: "The face swap feature is incredibly realistic. We use it for social media campaigns, and it drives a ton of user-generated content.",
  },
  {
    name: "Brandon Saris",
    avatar: "/icons/user_5.svg",
    date: "Sep 27, 2025",
    stars: 5,
    text: "The virtual-try-on tool is helping our beauty brand reduce returns. Customers get a true sense of how a shade or product looks on them.",
  },
  {
    name: "Rayna Vaccaro",
    avatar: "/icons/user_13.svg",
    date: "Sep 27, 2025",
    stars: 5,
    text: "From image generation to background removal, Miragic offers a complete toolkit for content creators. Absolutely essential!",
  },
];

const reviewColumn = [
  {
    reviews: reviews1st,
    delay: 0,
    reverseDirection: false,
    translateY: "",
    className: "",
  },
  {
    reviews: reviews2nd,
    delay: 0,
    reverseDirection: true,
    translateY: "translate-y-14",
    className: "",
  },
  {
    reviews: reviews3rd,
    delay: 0,
    reverseDirection: false,
    translateY: "",
    className: "",
  },
  {
    reviews: reviews4th,
    delay: 0,
    reverseDirection: true,
    translateY: "translate-y-14",
    className: "",
  },
];

export default function CustomerReview() {
  const swiperRefs = [
    useRef<SwiperType | null>(null),
    useRef<SwiperType | null>(null),
    useRef<SwiperType | null>(null),
    useRef<SwiperType | null>(null),
  ];

  return (
    <div className="px-4 sm:px-6 lg:px-8 2xl:px-[120px] w-full max-w-[1440px] mx-auto">
      <div className="mb-16 mx-auto">
        <SeperatorLineIcon />
      </div>
      <Text className="text-white text-lg md:text-2xl font-medium text-center xl:leading-10 mb-16 xl:mb-24">
        Miragic is becoming an industry standard for marketing and advertising
        creators and innovators with <GradientText>millions</GradientText> of
        assets generated
      </Text>

      {/* 4-Column Vertical Carousel Grid */}
      <div
        className="mx-auto px-2 relative"
        style={{
          maskImage: `linear-gradient(to bottom,
            rgba(18, 24, 40, 0.1) 0%,
            rgba(18, 24, 40, 0.7) 30%,
            rgba(18, 24, 40, 0.7) 80%,
            rgba(18, 24, 40, 0.1) 100%)`,
          WebkitMaskImage: `linear-gradient(to bottom,
            rgba(18, 24, 40, 0.1) 0%,
            rgba(18, 24, 40, 0.7) 30%,
            rgba(18, 24, 40, 0.7) 80%,
            rgba(18, 24, 40, 0.1) 100%)`,
        }}
      >
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 lg:gap-8">
          {reviewColumn.map((config, index) => (
            <SwiperColumn
              key={index}
              reviews={config.reviews}
              delay={config.delay}
              reverseDirection={config.reverseDirection}
              translateY={config.translateY}
              onSwiper={(swiper: SwiperType) => {
                swiperRefs[index].current = swiper;
              }}
            />
          ))}
        </div>
      </div>
    </div>
  );
}
