import { useState } from "react";
import { useFileHandler } from "../../../hooks/useFileHandler";
import { LeftSection } from "./LeftSection";
import AiCustomBgSelectionModal from "@/components/aiTools/AiCustomBgSelectionModal";
import { RightSection } from "./RightSection";

const BackgroundRemover = () => {
  const [isOpenBgSelectModal, setIsOpenBgSelectModal] = useState(false);
  const fileHandler = useFileHandler();

  return (
    <div className="flex flex-col lg:flex-row justify-between items-start mx-auto font-sans gap-8 lg:gap-12 w-full">
      <LeftSection fileHandler={fileHandler} />

      <RightSection fileHandler={fileHandler} />

      <AiCustomBgSelectionModal
        isOpen={isOpenBgSelectModal}
        onClose={() => setIsOpenBgSelectModal(false)}
        onSelect={() => {}}
      />
    </div>
  );
};

export default BackgroundRemover;
