import { Text } from "@/components/ui/text";
import { Star } from "lucide-react";
import type { Review } from "../type";

export const ReviewCard = ({ review }: { review: Review }) => (
  <div className="bg-gradient-to-br from-[#23243a] to-[#1a1b2b] border border-white/10 rounded-2xl p-6 flex flex-col min-h-[344px] shadow-lg transition-transform">
    <div className="flex items-center gap-3 mb-3">
      <img
        loading="lazy"
        src={review.avatar}
        alt={review.name}
        className="w-12 h-12 rounded-full object-cover border-2 border-white/15"
      />
      <div>
        <Text className="font-semibold text-white text-base">
          {review.name}
        </Text>
      </div>
    </div>
    <div className="flex items-center mb-2">
      {[...Array(review.stars)].map((_, i) => (
        <Star key={i} className="w-4 h-4 text-[#FFD600] fill-[#FFD600] mr-1" />
      ))}
    </div>
    <Text className="text-white/90 text-base">{review.text}</Text>
    <Text className="text-xs text-white/60 mt-6">{review.date}</Text>
  </div>
);
