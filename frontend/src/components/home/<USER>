import { useRef } from "react";
import { Swiper, SwiperSlide } from "swiper/react";
import { Navigation } from "swiper/modules";
import { Swiper as SwiperType } from "swiper";
import "swiper/css";
import "swiper/css/navigation";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { Text } from "../ui/text";

const TESTIMONIALS = [
  {
    text: "Working with this AI platform has transformed our business operations completely. The accuracy and speed of their machine learning models have helped us reduce decision-making time by 60%. Their innovative approach to problem-solving and dedicated support team make them stand out in the industry.",
    name: "<PERSON>",
    title: "CTO at DataFlow Solutions",
    avatar: "/icons/user_1.svg",
    icon: "/icons/quote.svg",
  },
  {
    text: "As an early adopter of their AI solutions, I’ve witnessed firsthand the evolution of their technology. Their natural language processing capabilities have revolutionized how we handle customer interactions. The ROI we’ve seen in just six months has exceeded our expectations.",
    name: "<PERSON>",
    title: "Head of Innovation at TechVista",
    avatar: "/icons/user_2.svg",
    icon: "/icons/quote.svg",
  },
  {
    text: "The implementation of their AI-powered analytics suite was seamless. We’ve seen a 40% improvement in predictive accuracy and significant cost savings in our operations. Their commitment to continuous improvement and customer success is truly remarkable.",
    name: "Dr. <PERSON>",
    title: "Director in FutureScale",
    avatar: "/icons/user_14.png",
    icon: "/icons/quote.svg",
  },
  {
    text: "Working with this AI platform has transformed our business operations completely. The accuracy and speed of their machine learning models have helped us reduce decision-making time by 60%. Their innovative approach to problem-solving and dedicated support team make them stand out in the industry.",
    name: "Sarah Chen",
    title: "CTO at DataFlow Solutions",
    avatar: "/icons/user_2.svg",
    icon: "/icons/quote.svg",
  },
  {
    text: "As an early adopter of their AI solutions, I’ve witnessed firsthand the evolution of their technology. Their natural language processing capabilities have revolutionized how we handle customer interactions. The ROI we’ve seen in just six months has exceeded our expectations.",
    name: "Marcus Rodriguez",
    title: "Head of Innovation at TechVista",
    avatar: "/icons/user_1.svg",
    icon: "/icons/quote.svg",
  },
];
export default function LoveByUser() {
  const swiperRef = useRef<SwiperType | null>(null);

  return (
    <div className="relative ">
      <div className="relative z-[5] lg:space-y-16 space-y-10 px-4 sm:px-6 lg:px-8 2xl:px-[120px] w-full max-w-[1440px] mx-auto">
        <Text
          variant={"body"}
          className="text-[#f5f5f7] text-3xl lg:text-4xl font-semibold leading-tight text-center max-w-[1020px] w-full mx-auto mb-10"
        >
          They love us. You will too.
        </Text>
        <div className="relative max-w-6xl mx-auto pb-20">
          <Swiper
            modules={[Navigation]}
            onSwiper={(swiper: SwiperType) => {
              swiperRef.current = swiper;
            }}
            spaceBetween={32}
            slidesPerView={1}
            breakpoints={{
              768: { slidesPerView: 2 },
              1024: { slidesPerView: 3 },
            }}
            className="!h-[420px]" // Ensures Swiper itself has a fixed height
          >
            {TESTIMONIALS.map((t, idx) => (
              <SwiperSlide key={idx} className="h-full grid grid-cols-1">
                <div className="bg-gradient-to-br from-[#23243a] to-[#1a1b2b] border border-white/10 rounded-2xl p-8 flex flex-col h-full w-full shadow-lg min-w-[340px] max-w-[370px]">
                  <img
                    loading="lazy"
                    src={t.icon}
                    alt="quote"
                    className="w-7 h-7 mb-4"
                  />
                  <Text className="text-[#f5f5f7] text-base flex-1">
                    “{t.text}”
                  </Text>
                  <div className="flex items-center gap-3 mt-4">
                    <img
                      loading="lazy"
                      src={t.avatar}
                      alt={t.name}
                      className="w-10 h-10 rounded-full object-cover border-2 border-white/20"
                    />
                    <div>
                      <Text className="font-semibold text-white text-base">
                        {t.name}
                      </Text>
                      <Text className="text-xs text-white/60">{t.title}</Text>
                    </div>
                  </div>
                </div>
              </SwiperSlide>
            ))}
          </Swiper>
          <div className="absolute bottom-4 right-0 flex gap-2 z-10">
            <button
              onClick={() => swiperRef.current?.slidePrev()}
              className="rounded-full w-10 h-10 border border-white/10 text-white/55 hover:text-white flex items-center justify-center text-xl bg-[#121828] hover:bg-white/5  transition cursor-pointer"
            >
              <ChevronLeft />
            </button>
            <button
              onClick={() => swiperRef.current?.slideNext()}
              className="rounded-full w-10 h-10 border border-white/10 text-white/55 hover:text-white flex items-center justify-center text-xl bg-[#121828] hover:bg-white/5  transition cursor-pointer"
            >
              <ChevronRight />
            </button>
          </div>
        </div>{" "}
      </div>
      {/* <div className="absolute top-20 -right-[6%] w-[320px] h-[320px] z-0">
        <img
          loading="lazy"
          src="/icons/right_elipse.svg"
          alt="lighting effect"
          className="w-full h-full object-cover rounded-full"
        />
      </div> */}
      <img
        loading="lazy"
        src="/png/referrel_right_shadow.png"
        alt="Background light"
        className="w-full h-full absolute -top-[30%] -right-[50%] z-0"
      />
    </div>
  );
}
