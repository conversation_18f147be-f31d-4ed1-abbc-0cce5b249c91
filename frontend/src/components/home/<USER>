import { Text } from "@/components/ui/text";
import { useState } from "react";
import { TabSelect } from "../ui/tab-select";
import VirtualTryOn from "./VirtualTryOn";
import SpeedPainting from "./Speedpainting";
import BackgroundRemover from "./BackgroundRemover/BackgroundRemover";

const Tabs = [
  "Virtual Try On",
  "Speed Painting",
  "Background Remover",
  // "Image Generator",
  // "Sales Pilot",
  // "Face Swap",
] as const;

const VisibilityAI = () => {
  const [activeTool, setActiveTool] =
    useState<(typeof Tabs)[number]>("Virtual Try On");
  return (
    <div className="relative w-full">
      <div className="relative z-10 lg:space-y-16 space-y-10 px-4 sm:px-6 lg:px-8 2xl:px-[120px] w-full max-w-[1440px] mx-auto">
        <Text
          variant={"body"}
          className="text-[#f5f5f7] text-4xl lg:text-5xl font-semibold leading-tight text-center lg:max-w-[820px] w-full mx-auto"
        >
          Customize your content with AI-powered tools from Miragic
        </Text>

        <div className="flex flex-wrap items-center justify-center w-full lg:max-w-[820px] mx-auto">
          {Tabs.map((tool) => (
            <TabSelect
              key={tool}
              name={tool}
              isActive={tool === activeTool}
              onClick={() => setActiveTool(tool)}
              className="cursor-pointer lg:text-lg text-sm"
              activeClassName="bg-gradient-to-r bg-clip-text text-transparent inline-block from-[#2C9BF7] via-[#6176F7] to-[#8054F3] relative pb-1 lg:m-0 m-4 before:absolute before:left-0 before:-bottom-1 before:w-full before:h-[3px] before:bg-gradient-to-r before:from-[#2C9BF7] before:via-[#6176F7] before:to-[#8054F3] after:absolute after:inset-0 after:-bottom-1 after:bg-gradient-to-r after:from-[#2C9BF7] after:via-[#6176F7] after:to-[#8054F3] after:bg-clip-text after:text-transparent"
              inactiveClassName="text-[#f5f5f7] border-b-2 border-[#f5f5f7] relative before:absolute before:bottom-0 before:left-0 before:w-full before:h-[2px] before:bg-[#2C9BF7] before:origin-right before:scale-x-0 before:transition-transform before:duration-500"
            />
          ))}
        </div>

        <div className="flex justify-center items-center w-full">
          {activeTool === "Background Remover" ? (
            <BackgroundRemover />
          ) : activeTool === "Virtual Try On" ? (
            <VirtualTryOn />
          ) : activeTool === "Speed Painting" ? (
            <SpeedPainting />
          ) : (
            <BackgroundRemover />
          )}
        </div>
      </div>
      <img
        loading="lazy"
        src="/png/about_hero_shadow.png"
        alt="Background light"
        className="w-full h-full absolute top-0 -left-[45%] z-0"
      />
    </div>
  );
};

export default VisibilityAI;
