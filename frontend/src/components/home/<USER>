import { But<PERSON> } from "../ui/button";
import { AiToolIcon } from "@/lib/icons";
import { Link, useNavigate } from "react-router-dom";
import VideoPlayer from "../common/VideoPlayer";
import { FaYoutube } from "react-icons/fa";
import { useApp } from "@/contexts/useApp";
import { Text } from "../ui/text";

const VirtualTryOn = () => {
  const { isAuthenticated } = useApp();
  const navigate = useNavigate();

  return (
    <div className="flex flex-col lg:flex-row justify-between items-start mx-auto font-sans gap-8 lg:gap-12 w-full">
      <div className="flex flex-col justify-center gap-8 h-full lg:w-1/2">
        <div className="flex justify-center lg:justify-start">
          <Button className="flex items-center !px-7 text-white text-base rounded-full bg-gradient-to-r from-[rgba(44,155,247,0.10)] to-[rgba(128,84,243,0.10)] hover:from-[rgba(44,155,247,0.15)] hover:to-[rgba(128,84,243,0.15)] shadow-[0_0_10px_rgba(129,90,219,0.6)] border border-white/15 h-[50px]">
            <AiToolIcon />
            <span>AI Tool</span>
          </Button>
        </div>

        {/* Heading */}
        <div>
          <Text className="text-3xl lg:text-4xl text-white font-semibold mb-4 lg:mb-6">
            Virtual Try-On
          </Text>
          <Text className="text-sm md:text-base lg:text-lg text-white/66 leading-relaxed max-w-xl mx-auto lg:mx-0">
            <span className="text-white/65">
              Create realistic images of your clothes, worn by anyone.
              This tool changes the clothing in any model photo using a
              reference images whether it's from someone wearing the
              outfit or a product photo.
              <br />
              <br />
              <span className="text-white/70">
                Whether you're showcasing casual wear or high-fashion pieces, we
                are delivering exceptional, professional-quality photos without
                the need for a commercial photoshoot.
              </span>
            </span>
          </Text>
        </div>

        <div className="mt-2">
          <Button
            outline={false}
            onClick={() =>
              isAuthenticated
                ? navigate("/ai-tool/speedpainting")
                : navigate("/auth/login")
            }
            variant={"animeGradient"}
            className="bg-radial-[at_5%_5%] from-[#2C9BF7] to-[#8054F3] rounded-full border border-white/15 px-7 py-3 h-[50px]"
          >
            Try Now
          </Button>
        </div>
      </div>

      <div className="flex items-center justify-center lg:w-1/2 w-full">
        <div className="relative w-full max-w-[500px] aspect-video rounded-2xl overflow-hidden shadow-[0_0_20px_rgba(129,90,219,0.3)] group">
          <VideoPlayer
            src="/video/virtual_try_on.mp4"
            ariaLabel="Virtual Try Video with Miragic AI"
          />
          <Link
            to="https://www.youtube.com/watch?v=c-n0OsUJsrw"
            target="_blank"
            rel="noopener noreferrer"
            className="absolute top-3 right-3 flex items-center gap-2 px-3 py-1.5 bg-black/80 text-white rounded-lg shadow-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300 z-10"
          >
            <FaYoutube className="w-5 h-5 text-red-500" />
            <span className="font-medium text-xs">Watch on YouTube</span>
          </Link>
        </div>
      </div>
    </div>
  );
};

export default VirtualTryOn;
