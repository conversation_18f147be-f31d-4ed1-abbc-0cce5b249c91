// No imports needed

import GradientText from "../common/GradientText";
import { Text } from "../ui/text";
import { useSlideUpAnimation } from "@/hooks/useSlideUpAnimation";

const TrustedTeam = () => {
  const { elementRef, animationStyle } = useSlideUpAnimation({ 
    duration: 1000, 
    delay: 200,
    distance: 60 
  });

  const TRUSTED_TEAM = [
    "/png/calestial.png",
    "/png/apex.png",
    "/png/quantum.png",
    "/png/acme_corp.png",
    "/png/pulsew.png",
  ];
  
  return (
    <div 
      ref={elementRef}
      style={animationStyle}
      className="flex flex-col gap-10 justify-center w-full items-center"
    >
      <Text className="text-white font-inter xl:text-2xl text-xl font-light tracking-wide text-center">
        <GradientText>150M+</GradientText> Asset Creations Trusted by Fortune{" "}
        <GradientText>500</GradientText> Companies
      </Text>
      <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:gap-16 gap-10 justify-center">
        {TRUSTED_TEAM?.map((src) => (
          <img
            loading="lazy"
            className="max-h-10 max-w-fit opacity-80 hover:opacity-100 transition-opacity duration-300"
            key={src}
            src={src}
            alt="Trusted company logo"
          />
        ))}
      </div>
    </div>
  );
};

export default TrustedTeam;
