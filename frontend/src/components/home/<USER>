import { useState } from "react";
import { TabSelect } from "../ui/tab-select";
import { Text } from "../ui/text";

const Tabs = ["Advertising", "Education", "Inspiration", "E-Commerce"] as const;

export default function PersonalBrand() {
  const [activeTool, setActiveTool] =
    useState<(typeof Tabs)[number]>("Advertising");
  return (
    <div className="lg:space-y-16 space-y-10 px-4 sm:px-6 lg:px-8 2xl:px-[120px] w-full max-w-[1440px] mx-auto">
      <Text
        variant={"body"}
        className="text-[#f5f5f7] text-3xl lg:text-4xl font-semibold leading-tight text-center max-w-[1020px] w-full mx-auto"
      >
        Enable Personal and Immersive Brand Experiences Through The Power of
        Generative AI
      </Text>
      <div className="flex flex-wrap items-center justify-center w-full lg:max-w-[820px] mx-auto">
        {Tabs.map((tool) => (
          <TabSelect
            key={tool}
            name={tool}
            isActive={tool === activeTool}
            onClick={() => setActiveTool(tool)}
            className="cursor-pointer px-12 py-4 text-lg"
            activeClassName="bg-gradient-to-br from-[#2C9BF7] to-[#8054F3] text-white hover:text-white rounded-full after:scale-x-0"
            inactiveClassName="text-[#888888] hover:text-white"
          />
        ))}
      </div>
      <div className="flex justify-center items-center w-full">
        {activeTool === "Advertising" ? (
          <img
            loading="lazy"
            src="/webp/horse.webp"
            alt="Advertising Miragic AI"
            className="w-full h-full object-cover rounded-4xl border border-white/25"
          />
        ) : activeTool === "Education" ? (
          <img
            loading="lazy"
            src="/webp/study.webp"
            alt="Education Miragic AI"
            className="w-full h-full object-cover rounded-4xl border border-white/25"
          />
        ) : activeTool === "Inspiration" ? (
          <img
            loading="lazy"
            src="/webp/inspiration.webp"
            alt="Inspiration Miragic AI"
            className="w-full h-full object-cover rounded-4xl border border-white/25"
          />
        ) : activeTool === "E-Commerce" ? (
          <img
            loading="lazy"
            src="/webp/ecommerce.webp"
            alt="E-Commerce Miragic AI"
            className="w-full h-full object-cover rounded-4xl border border-white/25"
          />
        ) : (
          <img
            loading="lazy"
            src="/webp/horse.webp"
            alt="Miragic AI"
            className="w-full h-full object-cover rounded-4xl border border-white/25"
          />
        )}
      </div>
    </div>
  );
}
