import { SeperatorLineIcon } from "@/lib/icons";
import { useRef } from "react";
import { Swiper, SwiperSlide } from "swiper/react";
import { Navigation } from "swiper/modules";
import { Swiper as SwiperType } from "swiper";
import { ChevronLeft, ChevronRight } from "lucide-react";
import "swiper/css";
import "swiper/css/navigation";
import { Text } from "../ui/text";
import { useNavigate } from "react-router-dom";
import { useBlogPosts } from "@/hooks/useBlogPosts";

export default function RelatedArticle() {
  const swiperRef = useRef<SwiperType | null>(null);
  const navigate = useNavigate();
  const { blogData, loading, error } = useBlogPosts({ limit: 10 });

  if (loading) {
    return (
      <div className="relative">
        <div className="px-4 sm:px-6 lg:px-8 2xl:px-[120px] w-full max-w-[1440px] mx-auto relative z-[5]">
          <div className="mb-16 mx-auto">
            <SeperatorLineIcon />
          </div>
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-white text-3xl font-semibold">
              Related Articles
            </h2>
          </div>
          <div className="text-white text-center py-8">Loading...</div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="relative">
        <div className="px-4 sm:px-6 lg:px-8 2xl:px-[120px] w-full max-w-[1440px] mx-auto relative z-[5]">
          <div className="mb-16 mx-auto">
            <SeperatorLineIcon />
          </div>
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-white text-3xl font-semibold">
              Related Articles
            </h2>
          </div>
          <div className="text-red-400 text-center py-8">{error}</div>
        </div>
      </div>
    );
  }

  return (
    <div className="relative">
      <div className="px-4 sm:px-6 lg:px-8 2xl:px-[120px] w-full max-w-[1440px] mx-auto relative z-[5]">
        <div className="mb-16 mx-auto">
          <SeperatorLineIcon />
        </div>
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-white text-3xl font-semibold">
            Related Articles
          </h2>
          <div className="flex gap-2">
            <button
              onClick={() => swiperRef.current?.slidePrev()}
              className="rounded-full w-10 h-10 text-white flex items-center justify-center text-xl hover:bg-white/25 bg-white/10 transition"
            >
              <ChevronLeft />
            </button>
            <button
              onClick={() => swiperRef.current?.slideNext()}
              className="rounded-full w-10 h-10 text-white flex items-center justify-center text-xl hover:bg-white/25 bg-white/10 transition"
            >
              <ChevronRight />
            </button>
          </div>
        </div>
        <Swiper
          modules={[Navigation]}
          onSwiper={(swiper: SwiperType) => {
            swiperRef.current = swiper;
          }}
          spaceBetween={24}
          slidesPerView={1}
          breakpoints={{
            640: { slidesPerView: 1.2 },
            768: { slidesPerView: 2 },
            1024: { slidesPerView: 3 },
          }}
          className="pb-8"
        >
          {blogData.map((post) => (
            <SwiperSlide key={post.id} className="h-auto releted-blog">
              <div
                onClick={() => navigate(`/blog/${post.slug}`)}
                className="bg-white/5 border border-white/20 rounded-2xl p-4 flex flex-col h-full min-h-[340px] max-w-[370px] mx-auto cursor-pointer overflow-hidden"
              >
                {post.featuredImageUrl && (
                  <img
                    loading="lazy"
                    src={post.featuredImageUrl}
                    alt={post.title}
                    className="w-full h-[140px] object-cover rounded-lg mb-4 hover:transform hover:scale-125 transition-all duration-500"
                  />
                )}
                <Text className="text-xs text-gray-400 mb-2">
                  {new Date(
                    post.publishedAt || post.createdAt || ""
                  ).toLocaleDateString("en-GB", {
                    day: "2-digit",
                    month: "long",
                    year: "numeric",
                  })}
                </Text>
                <Text className="font-semibold text-white text-base mb-2 line-clamp-2">
                  {post.title}
                </Text>
                <Text className="text-gray-400 text-sm line-clamp-3 mb-2">
                  {post.excerpt || post.content}
                </Text>
              </div>
            </SwiperSlide>
          ))}
        </Swiper>
      </div>
      <img
        loading="lazy"
        src="/png/about_hero_shadow.png"
        alt="Background light"
        className="w-full h-full absolute top-0 -left-[45%] z-0"
      />
    </div>
  );
}
