import { useRef } from "react";
import { Swiper, SwiperSlide } from "swiper/react";
import { Navigation } from "swiper/modules";
import { Swiper as SwiperType } from "swiper";
import { ChevronLeft, ChevronRight } from "lucide-react";

import "swiper/css";
import "swiper/css/navigation";

const MySlider = () => {
  const swiperRef = useRef<SwiperType | null>(null);

  const SLIDER_DATA = [
    {
      title: (
        <>
          Working with this AI platform has transformed our business operations
          completely.{" "}
          <span className="text-[#AE84EF]">
            The accuracy and speed of their machine learning models have helped
            us reduce decision-making time by 60%.
          </span>{" "}
          Their innovative approach to problem-solving and dedicated support
          team make them stand out in the industry.
        </>
      ),
      personName: "<PERSON>",
      designation: "CTO at DataFlow Solutions",
      avatar: "/png/avatar1.png",
    },
    {
      title: (
        <>
          As an early adopter of their AI solutions, I've witnessed firsthand
          the evolution of their technology.{" "}
          <span className="text-[#AE84EF]">
            Their natural language processing capabilities have revolutionized
            how we handle customer interactions.
          </span>{" "}
          The ROI we've seen in just six months has exceeded our expectations.
        </>
      ),
      personName: "<PERSON>",
      designation: "Head of Innovation at TechVista",
      avatar: "/png/avatar2.png",
    },
    {
      title: (
        <>
          The implementation of their AI-powered analytics suite was seamless.{" "}
          <span className="text-[#AE84EF]">
            We've seen a 40% improvement in predictive accuracy and significant
            cost savings in our operations.
          </span>{" "}
          Their commitment to continuous improvement and customer success is
          truly remarkable.
        </>
      ),
      personName: "Dr. Emily Watson",
      designation: "Director of AI Research at FutureScale",
      avatar: "/png/avatar3.png",
    },
  ];

  return (
    <article className="relative w-full mx-auto pl-10">
      {/* Custom navigation buttons */}
      <div className="absolute top-0 right-0 z-10 flex space-x-4 mr-16">
        <button
          onClick={() => swiperRef.current?.slidePrev()}
          className="rounded-full w-12 h-12 border border-gray-500 text-white flex items-center justify-center text-2xl hover:bg-gray-600 transition"
        >
          <ChevronLeft />
        </button>
        <button
          onClick={() => swiperRef.current?.slideNext()}
          className="rounded-full w-12 h-12 border border-gray-500 text-white flex items-center justify-center text-2xl hover:bg-gray-600 transition"
        >
          <ChevronRight />
        </button>
      </div>

      <div className="pt-20">
        <Swiper
          modules={[Navigation]}
          onSwiper={(swiper: SwiperType) => {
            swiperRef.current = swiper;
          }}
          spaceBetween={20}
          slidesPerView={1.3}
          centeredSlides={false}
        >
          {SLIDER_DATA.map(
            ({ title, designation, personName, avatar }, index) => (
              <SwiperSlide key={index}>
                <div className="flex gap-3 bg-white/5 border border-white/25 text-white p-6 rounded-xl shadow-md mr-5 min-h-[280px]">
                  <div className="w-8 h-8 bg-[#AE84EF] text-4xl flex justify-center items-baseline rounded-full flex-shrink-0">
                    "
                  </div>
                  <div className="w-[90%] flex flex-col">
                    <div className="flex-1 overflow-hidden">
                      <p className="text-lg line-clamp-6 overflow-hidden">
                        {title}
                      </p>
                    </div>
                    <div className="mt-8 flex items-center space-x-3 flex-shrink-0">
                      <img
                        loading="lazy"
                        src={avatar}
                        className="w-10 h-10 rounded-full"
                        alt="User"
                      />
                      <div className="overflow-hidden">
                        <p className="font-semibold text-white text-lg truncate">
                          {personName}
                        </p>
                        <p className="text-xs text-gray-400 truncate">
                          {designation}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </SwiperSlide>
            )
          )}
        </Swiper>
      </div>

      <style>{`
        .line-clamp-6 {
          display: -webkit-box;
          -webkit-line-clamp: 6;
          -webkit-box-orient: vertical;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      `}</style>
    </article>
  );
};

export default MySlider;

// import { useRef } from "react";
// import { Swiper, SwiperSlide } from "swiper/react";
// import { Navigation } from "swiper/modules";
// import { Swiper as SwiperType } from "swiper"; // 🧠 Type import
// import { ChevronLeft, ChevronRight } from "lucide-react";

// import "swiper/css";
// import "swiper/css/navigation";

// const MySlider = () => {
//   const swiperRef = useRef<SwiperType | null>(null); // ✅ Typed ref

//   const SLIDER_DATA = [
//     {
//       title: (
//         <>
//           Working with this AI platform has transformed our business operations completely.{" "}
//           <span className="text-[#AE84EF]">
//             The accuracy and speed of their machine learning models have helped us reduce decision-making time by 60%.
//           </span>{" "}
//           Their innovative approach to problem-solving and dedicated support team make them stand out in the industry.
//         </>
//       ),
//       personName: "Sarah Chen",
//       designation: "CTO at DataFlow Solutions",
//       avatar: "/png/avatar1.png",
//     },
//     {
//       title: (
//         <>
//           As an early adopter of their AI solutions, I've witnessed firsthand the evolution of their technology.{" "}
//           <span className="text-[#AE84EF]">
//             Their natural language processing capabilities have revolutionized how we handle customer interactions.
//           </span>{" "}
//           The ROI we've seen in just six months has exceeded our expectations.
//         </>
//       ),
//       personName: "Marcus Rodriguez",
//       designation: "Head of Innovation at TechVista",
//       avatar: "/png/avatar2.png",
//     },
//     {
//       title: (
//         <>
//           The implementation of their AI-powered analytics suite was seamless.{" "}
//           <span className="text-[#AE84EF]">
//             We've seen a 40% improvement in predictive accuracy and significant cost savings in our operations.
//           </span>{" "}
//           Their commitment to continuous improvement and customer success is truly remarkable.
//         </>
//       ),
//       personName: "Dr. Emily Watson",
//       designation: "Director of AI Research at FutureScale",
//       avatar: "/png/avatar3.png",
//     },
//   ];

//   return (
//     <article className="relative w-full mx-auto pl-10">
//       {/* Custom navigation buttons */}
//       <div className="absolute top-0 right-0 z-10 flex space-x-4 mr-16">
//         <button
//           onClick={() => swiperRef.current?.slidePrev()}
//           className="rounded-full w-12 h-12 border border-gray-500 text-white flex items-center justify-center text-2xl hover:bg-gray-600 transition"
//         >
//           <ChevronLeft />
//         </button>
//         <button
//           onClick={() => swiperRef.current?.slideNext()}
//           className="rounded-full w-12 h-12 border border-gray-500 text-white flex items-center justify-center text-2xl hover:bg-gray-600 transition"
//         >
//           <ChevronRight />
//         </button>
//       </div>

//       <div className="pt-20">
//         <Swiper
//           modules={[Navigation]}
//           onSwiper={(swiper: SwiperType) => {
//             swiperRef.current = swiper;
//           }}
//           spaceBetween={20}
//           slidesPerView={1.3}
//           centeredSlides={false}
//         >
//           {SLIDER_DATA.map(({ title, designation, personName, avatar }, index) => (
//             <SwiperSlide key={index}>
//               <div className="flex gap-3 bg-white/5 border border-white/25 text-white p-6 rounded-xl shadow-md mr-5">
//                 <div className="w-8 h-8 bg-[#AE84EF] text-4xl flex justify-center items-baseline rounded-full">
//                   "
//                 </div>
//                 <div className="w-[90%]">
//                   <p className="text-lg">{title}</p>
//                   <div className="mt-8 flex items-center space-x-3">
//                     <img loading="lazy"
//                       src={avatar}
//                       className="w-10 h-10 rounded-full"
//                       alt="User"
//                     />
//                     <div>
//                       <p className="font-semibold text-white text-lg">
//                         {personName}
//                       </p>
//                       <p className="text-xs text-gray-400">{designation}</p>
//                     </div>
//                   </div>
//                 </div>
//               </div>
//             </SwiperSlide>
//           ))}
//         </Swiper>
//       </div>
//     </article>
//   );
// };

// export default MySlider;
