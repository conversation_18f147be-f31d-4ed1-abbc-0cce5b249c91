import * as Accordion from "@radix-ui/react-accordion";
import { ChevronDown } from "lucide-react";
import { twMerge } from "tailwind-merge";

const FAQ_DATA = [
  {
    question: "What is Miragic?",
    answer:
      "Miragic is a cutting-edge platform for virtual try on and generative AI-powered media tools.",
  },
  {
    question: "How do I access Miragic through an API?",
    answer:
      "You can access Miragic's API by registering on the platform and generating an API key.",
  },
  {
    question: "Is it permissible to use videos for advertising services to my clients?",
    answer:
      "Yes, Miragic permits the use of generated media for client advertising under specific licensing terms.",
  },
  {
    question: "What file formats are supported for uploads?",
    answer:
      "Miragic supports most common video and image formats including MP4, MOV, JPG, PNG, and WEBP. Files should not exceed 500MB in size.",
  },
  {
    question: "How long does it take to process a video?",
    answer:
      "Processing time varies depending on video length and complexity. Most videos are processed within 5-15 minutes, while longer or more complex videos may take up to 30 minutes.",
  },
  {
    question: "What are the payment plans available?",
    answer:
      "We offer flexible plans including pay-per-use, monthly subscriptions, and enterprise solutions. Each plan comes with different features and processing quotas to suit your needs.",
  },
  {
    question: "Is my content kept private and secure?",
    answer:
      "Yes, we take privacy seriously. All uploaded content is encrypted, processed securely, and automatically deleted after processing unless specified otherwise. We never share your content with third parties.",
  },
  {
    question: "Do you offer customer support?",
    answer:
      "Yes, we provide 24/7 customer support through email and live chat. Enterprise customers also get access to dedicated support representatives.",
  }
];

const LandingPageFAQ = () => {
  return (
    <div className="w-full z-10 mx-auto px-4">
      <h2 className="text-center  z-[1000] text-white text-5xl font-semibold mb-10">
        FAQ
      </h2>
      <Accordion.Root type="multiple" className="space-y-6">
        {FAQ_DATA.map((item, index) => (
          <Accordion.Item key={index} value={`item-${index}`}>
            <Accordion.Trigger
              className={twMerge(
                "w-full cursor-pointer flex justify-between items-center text-left text-white text-3xl group", // ← add `group` here
                "transition-colors duration-300 hover:text-gray-300 focus:outline-none"
              )}
            >
              <span className="w-[85%]">{item.question}</span>
              <div className="w-8 h-8 rounded-full flex justify-center items-center bg-gray-500">
                <ChevronDown className="transition-transform duration-300 group-data-[state=open]:rotate-180" />
              </div>
            </Accordion.Trigger>

            <Accordion.Content className="mt-2 text-sm text-gray-400 leading-relaxed pr-4">
              {item.answer}
            </Accordion.Content>

            {/* Gradient separator line */}
            <div className="mt-6 h-[2px] bg-gradient-to-r from-transparent via-gray-500 to-transparent" />
          </Accordion.Item>
        ))}
      </Accordion.Root>
    </div>
  );
};

export default LandingPageFAQ;
