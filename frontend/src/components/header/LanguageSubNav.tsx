import { Globe } from "lucide-react";
import { useRef, useState } from "react";
import { ListWithIcon } from "../ui/list-with-icon";
import useOutsideClick from "@/hooks/useOutsideClick";

const languagesData = [
  {
    id: "english",
    name: "English",
    flag: "/icons/flags/usa.svg",
  },
  {
    id: "bangla",
    name: "বাংলা",
    flag: "/icons/flags/bangladesh.svg",
  },
  {
    id: "finland",
    name: "Finland",
    flag: "/icons/flags/finland.svg",
  },
  {
    id: "german",
    name: "German",
    flag: "/icons/flags/german.svg",
  },
  {
    id: "france",
    name: "France",
    flag: "/icons/flags/france.svg",
  },
];

export const LanguageSubNav = () => {
  const [selectedLanguage, setSelectedLanguage] = useState("bangla");
  const [isOpen, setIsOpen] = useState(false);

  const dropdownsContainerRef = useRef<HTMLDivElement>(null);
  useOutsideClick(dropdownsContainerRef, () => {
    setIsOpen(false);
  });

  const handleMouseEnter = () => {
    setIsOpen(true);
  };

  const handleMouseLeave = () => {
    setIsOpen(false);
  };

  const handleLanguageSelection = (langId: string) => {
    setSelectedLanguage(langId);
    setIsOpen(false);
  };

  return (
    <div className="relative" ref={dropdownsContainerRef}>
      <button
        className="text-[#F5F5F7] hover:white transition flex items-center gap-1 cursor-pointer"
        onMouseEnter={handleMouseEnter}
      >
        <Globe />
      </button>
      {isOpen && (
        <div
          className="absolute top-11 lg:-left-6 lg:right-auto -right-6 left-auto mt-2 p-5 bg-primary/90 backdrop-blur-sm border border-gray-700 rounded-2xl shadow-2xl z-50 cursor-pointer"
          onMouseLeave={handleMouseLeave}
        >
          <div className="flex flex-col rounded-xl shadow-lg w-full mx-auto">
            {languagesData.map((lang) => (
              <ListWithIcon
                key={lang.id}
                text={lang.name}
                icon={lang.flag}
                isActive={selectedLanguage === lang.id}
                onClick={() => handleLanguageSelection(lang.id)}
              />
            ))}
          </div>
        </div>
      )}
    </div>
  );
};
