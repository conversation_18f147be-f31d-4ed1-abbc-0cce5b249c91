import { useState } from "react";

export default function TopBanner() {
  const [isBannerVisible, setIsBannerVisible] = useState(true);
  return (
    <>
      {isBannerVisible && (
        <div className="bg-gradient-to-r from-[#12091E] via-[#4F2884] to-[#190C29] text-white px-4 py-3 relative">
          <div className="container mx-auto flex items-center justify-center text-center">
            <div className="flex items-center gap-2 text-sm">
              <span className="hidden sm:inline font-inter font-semibold">
                As Featured in Tech Crunch: How MiragicAI is Building a Gen AI
                Unicorn
              </span>
              <span className="sm:hidden">
                Featured in Tech Crunch: MiragicAI Gen AI Unicorn
              </span>
              <span className="text-purple-300">👉</span>
              <a
                href="/coming-soon"
                className="text-purple-300 underline cursor-pointer hover:text-white transition"
              >
                Read the Full Story
              </a>
            </div>
            <button
              onClick={() => setIsBannerVisible(false)}
              className="absolute right-4 top-1/2 transform -translate-y-1/2 text-purple-200 hover:text-white transition-colors p-1"
              aria-label="Close banner"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                strokeWidth={1.5}
                stroke="currentColor"
                className="w-5 h-5"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </button>
          </div>
        </div>
      )}
    </>
  );
}
