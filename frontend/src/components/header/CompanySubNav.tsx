import { ChevronDown } from "lucide-react";
import React from "react"
import { companyItems, programItems } from "../common/variable";

export interface CompanySubNavProps {
  isOpen: boolean;
  onMouseEnter: () => void;
  onMouseLeave: () => void;
  onItemClick: (href: string) => void;
}

const CompanySubNav: React.FC<CompanySubNavProps> = ({
  isOpen,
  onMouseEnter,
  onMouseLeave,
  onItemClick,
}) => {
  return (
    <div className="relative">
      <button
        className=" text-[#F5F5F7] hover:text-white transition flex items-center gap-1 cursor-pointer"
        onMouseEnter={onMouseEnter}
      >
        Company
        <ChevronDown
          className={`w-4 h-4 transition-transform duration-200 ${
            isOpen ? "rotate-180" : ""
          }`}
        />
      </button>

      {isOpen && (
        <div
          className="absolute top-11 -left-6 mt-2 p-6 w-[450px] bg-primary/90 backdrop-blur-sm border border-gray-700 rounded-2xl shadow-2xl z-50 cursor-pointer"
          onMouseLeave={onMouseLeave}
        >
          <div className="mb-6">
            <h3 className="text-gray-300 font-medium text-xs mb-4 uppercase tracking-wider">
              Programs
            </h3>
            <div className="grid grid-cols-2 gap-3">
              {programItems.map((program, index) => (
                <div
                  key={index}
                  onClick={() => onItemClick(program.href)}
                  className="bg-gray-800/40 border border-gray-700 rounded-xl p-4 cursor-pointer hover:bg-gray-700/50 hover:border-gray-600 transition-all duration-200"
                >
                  <h4 className="text-white font-medium text-sm mb-2">
                    {program.name}
                  </h4>
                  <p className="text-[#F5F5F7] text-xs leading-relaxed">
                    {program.description}
                  </p>
                </div>
              ))}
            </div>
          </div>

          <div>
            <h3 className="text-gray-300 font-medium text-xs mb-4 uppercase tracking-wider">
              Information
            </h3>
            <div className="flex flex-col gap-2">
              {companyItems.map((item, index) => (
                <div
                  key={index}
                  onClick={() => onItemClick(item.href)}
                  className="bg-gray-800/20 border border-gray-700/50 rounded-lg p-3 cursor-pointer hover:bg-gray-700/30 hover:border-gray-600 transition-all duration-200"
                >
                  <h4 className="text-white font-medium text-sm mb-1">
                    {item.name}
                  </h4>
                  <p className="text-[#F5F5F7] text-xs">{item.description}</p>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default CompanySubNav;
