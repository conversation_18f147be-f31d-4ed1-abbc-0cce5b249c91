import { Text } from "@/components/ui/text";
import type { BlogPost } from "@/services/blog.service";
import { useNavigate } from "react-router-dom";

interface BlogCardProps {
  blog: BlogPost;
}

const BlogCard: React.FC<BlogCardProps> = ({ blog }) => {
  const navigate = useNavigate();

  // Format the published date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      day: "numeric",
      month: "long",
      year: "numeric",
    });
  };

  // Default image fallback
  const DEFAULT_IMAGE =
    "https://images.unsplash.com/photo-1626544827763-d516dce335e2?w=800&auto=format&fit=crop&q=60&ixlib=rb-4.0.3";

  return (
    <div
      onClick={() => navigate(`/blog/${blog.slug}`)}
      style={{
        background:
          "linear-gradient(#1f2937, #1f2937) padding-box, linear-gradient(to bottom, #A166FC, #999999) border-box",
        border: "1px solid transparent",
      }}
      className="flex flex-col h-[500px] z-10 relative overflow-hidden rounded-xl bg-white/5 border border-white/25 text-white shadow-md w-full cursor-pointer hover:transform hover:scale-105 transition-all duration-300"
    >
      {/* Category Badge */}
      <div className="absolute bg-[#D9D9D9]/36 top-4 right-6 px-3 py-1 rounded-sm z-10">
        <Text variant={"card_body"} className="text-white font-medium text-xs">
          {blog.categories?.map((category) => category.name).join(", ") ||
            "Uncategorized"}
        </Text>
      </div>

      {/* Blog Image - Fixed Height */}
      <div className="h-[240px] w-full overflow-hidden">
        <img
          loading="lazy"
          className="w-full h-full object-cover"
          src={blog.featuredImageUrl || DEFAULT_IMAGE}
          alt={blog.title}
          onError={(e) => {
            (e.target as HTMLImageElement).src = DEFAULT_IMAGE;
          }}
        />
      </div>

      {/* Blog Content - Flexible Height with Spacing */}
      <div className="flex flex-1 flex-col justify-between gap-4 px-6 py-5">
        {/* Top Content */}
        <div className="flex flex-col gap-3">
          {/* Date */}
          <Text variant={"card_body"} className="text-gray-400 text-sm">
            {blog.publishedAt ? formatDate(blog.publishedAt) : "Draft"}
          </Text>

          {/* Title - Fixed Height */}
          <div className="h-[48px] flex items-start">
            <Text
              variant={"card_body"}
              className="text-white font-semibold leading-tight line-clamp-2"
              style={{
                display: "-webkit-box",
                WebkitBoxOrient: "vertical",
                WebkitLineClamp: 2,
                overflow: "hidden",
              }}
            >
              {blog.title}
            </Text>
          </div>

          {/* Excerpt - Fixed Height */}
          <div className="h-[60px] flex items-start">
            <Text
              variant={"card_body"}
              className="text-gray-300 leading-relaxed text-sm line-clamp-3"
              style={{
                display: "-webkit-box",
                WebkitBoxOrient: "vertical",
                WebkitLineClamp: 3,
                overflow: "hidden",
              }}
            >
              {blog.excerpt}
            </Text>
          </div>
        </div>

        {/* Bottom Content - Author */}
        <div className="mt-3">
          <Text variant={"card_body"} className="text-gray-400 text-sm">
            By Admin
            {/* :{" "}
            <span className="font-semibold text-white">{getAuthorName()}</span> */}
          </Text>
        </div>
      </div>
    </div>
  );
};

export default BlogCard;
