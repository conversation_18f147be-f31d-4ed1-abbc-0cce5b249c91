import * as React from "react";
import { Slot } from "@radix-ui/react-slot";
import { cva, type VariantProps } from "class-variance-authority";

import { cn } from "@/lib/utils";

const whiteShine =
  "before:bg-[image:linear-gradient(125deg,rgba(255,255,255,0)_20%,rgba(255,255,255,0.2),rgba(255,255,255,0)_50%)] before:opacity-50";
const baseAnimationClasses =
  "inline-flex cursor-pointer min-w-16 border text-[18px] px-6 py-4 justify-center items-center gap-3.5 rounded-xl hover:transform hover:scale-101 hover:shadow-[0px_2px_5px_rgba(0,0,0,0.2)] hover:-translate-y-1 hover:shadow-xl";

const shineEffectClasses =
  "relative overflow-hidden before:content-[''] before:absolute before:w-[300px] before:h-full before:top-0 before:left-[-100px] before:animate-[shineAnimation_3s_ease-out_infinite] before:delay-[var(--shine-delay,0s)]";

const buttonVariants = cva(
  "group inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive active:transform active:scale-95 active:shadow-[0px_2px_5px_rgba(0,0,0,0.2)] duration-300",
  {
    variants: {
      variant: {
        primary:
          "inline-flex hover:text-white hover:bg-secondary cursor-pointer min-w-16 border text-[18px] border-secondary text-secondary min-h-14 px-6 py-4 justify-center items-center gap-3.5 rounded-xl",
        default:
          "bg-primary text-primary-foreground shadow-xs hover:bg-primary/90 cursor-pointer",
        destructive:
          "bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 cursor-pointer dark:bg-destructive/60",
        outline:
          "border bg-background shadow-xs hover:bg-accent text-accent-foreground hover:text-accent-foreground dark:bg-input/30 dark:border-input cursor-pointer dark:hover:bg-input/50",
        secondary:
          "bg-secondary text-secondary-foreground shadow-xs cursor-pointer hover:bg-secondary/80",
        ghost:
          "hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",
        link: "text-primary underline-offset-4 cursor-pointer hover:underline",
        gradient:
          "text-white rounded-3 cursor-pointer bg-gradient-to-br from-[#4e1e95] to-[#3d0d6a] shadow-[0_0_10px_rgba(129,90,219,0.6)] border border-purple-500 hover:shadow-[0_0_15px_rgba(129,90,219,0.9)] transition",
        animeShine: `${baseAnimationClasses} ${shineEffectClasses} ${whiteShine} border-secondary cursor-pointer text-secondary`,

        animeGradient: `${baseAnimationClasses} ${shineEffectClasses} ${whiteShine} bg-gradient-to-br from-[#2C9BF7] to-[#8054F3] bg-[length:400%_400%] animate-[gradientAnimation_2s_linear_infinite] cursor-pointer border-secondary text-white`,
      },
      outline: {
        true: "",
        false: "border bg-secondary text-white",
      },
      size: {
        default: "h-9 px-4 py-2 has-[>svg]:px-3",
        sm: "h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",
        lg: "h-10 rounded-md px-6 has-[>svg]:px-4",
        icon: "size-9",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
);

function Button({
  className,
  variant,
  size,
  outline,
  asChild = false,
  maxShineDelayMs = 1500,
  ...props
}: React.ComponentProps<"button"> &
  VariantProps<typeof buttonVariants> & {
    asChild?: boolean;
    maxShineDelayMs?: number;
  }) {
  const Comp = asChild ? Slot : "button";

  const shineDelay = React.useMemo(() => {
    if (
      maxShineDelayMs > 0 &&
      (variant === "animeShine" || variant === "animeGradient")
    ) {
      return `${Math.random() * maxShineDelayMs}ms`;
    }
    return "0s";
  }, [maxShineDelayMs, variant]);

  return (
    <Comp
      data-slot="button"
      className={cn(buttonVariants({ variant, outline, size, className }))}
      style={{ "--shine-delay": shineDelay }}
      {...props}
    />
  );
}

// eslint-disable-next-line react-refresh/only-export-components
export { Button, buttonVariants };
