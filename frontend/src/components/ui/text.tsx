import { cn } from "@/lib/utils";
import { cva, type VariantProps } from "class-variance-authority";
import { forwardRef, type ComponentPropsWithoutRef } from "react";

// eslint-disable-next-line react-refresh/only-export-components
export const textVariants = cva("", {
  variants: {
    variant: {
      page_title: "text-[42px] sm:text-[64px] font-medium font-[Inter]",
      section_title: "text-[36px] font-medium font-[Inter]",
      sub_title: "text-[24px] font-normal font-[Inter]",
      body: "text-[20px] font-normal",
      card_title_large: "text-[25px] font-normal",
      card_title_small: "text-[22px] font-normal",
      card_body: "text-[15px] font-normal",
      card_title: "text-[32px] font-semibold",
      card_list_title: "text-base font-normal",
      small_title: "text-sm font-normal",
      h1: "text-[42px] sm:text-[64px] font-medium font-[Inter]",
      h2: "text-[36px] font-medium font-[Inter]",
      h3: "text-[24px] font-normal font-[Inter]",
      h4: "text-[20px] font-normal",
      h5: "text-[18px] font-normal",
      h6: "text-[16px] font-normal",
      p: "text-[16px] font-normal",
      span: "text-[16px] font-normal",
      div: "text-[16px] font-normal",
    },
    font: {
      Inter: "font-[Inter]",
      roboto: "font-[roboto]",
      poppins: "font-[poppins]",
    },
    align: {
      left: "text-left",
      center: "text-center",
      right: "text-right",
    },
  },
  defaultVariants: {
    variant: "body",
    font: "poppins",
    align: "left",
  },
});

type TextElement =
  | "h1"
  | "h2"
  | "h3"
  | "h4"
  | "h5"
  | "h6"
  | "p"
  | "span"
  | "div";

export interface TextProps
  extends ComponentPropsWithoutRef<"p">,
    VariantProps<typeof textVariants> {
  as?: TextElement;
}

export const Text = forwardRef<HTMLParagraphElement, TextProps>(
  (
    { className = "", variant, font, align, as: Component = "p", ...props },
    ref
  ) => {
    return (
      <Component
        ref={ref}
        className={cn(
          "text-gray-400 ",
          textVariants({ variant, font, align }),
          className,
          " "
        )}
        {...props}
      />
    );
  }
);

Text.displayName = "Text";
