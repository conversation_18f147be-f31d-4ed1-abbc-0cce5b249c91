import { cn } from "@/lib/utils";

interface TabSelectProps {
  name: string;
  isActive?: boolean;
  onClick?: () => void;
  className?: string;
  activeClassName?: string;
  inactiveClassName?: string;
}

export const TabSelect = ({
  name,
  isActive,
  onClick,
  className,
  activeClassName = "text-[#2C9BF7] relative before:absolute before:bottom-0 before:left-0 before:w-full before:h-[2px] before:bg-[#2C9BF7] before:origin-left before:scale-x-100",
  inactiveClassName = "text-[#f5f5f7] relative before:absolute before:bottom-0 before:left-0 before:w-full before:h-[2px] before:bg-[#2C9BF7] before:origin-right before:scale-x-0"
}: TabSelectProps) => (
  <button
    onClick={onClick}
    className={cn(
      "relative px-4 py-2",
      "after:absolute after:bottom-[-2px] after:left-0 after:w-full after:h-[4px] after:bg-[#2C9BF7]/30 after:origin-left",
      isActive 
        ? "after:scale-x-100" 
        : "after:scale-x-0",
      "hover:text-[#2C9BF7]/80",
      isActive ? activeClassName : inactiveClassName,
      className
    )}
  >
    {name}
  </button>
);
