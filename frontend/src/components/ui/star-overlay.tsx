export const StarOverlay = () => (
  <div className="absolute inset-0 pointer-events-none z-20">
    {[...Array(16)].map((_, i) => (
      <img
        loading="lazy"
        src="/icons/sparcle.png"
        alt="sparcle"
        key={i}
        className={`absolute animate-ping`}
        style={{
          top: `${Math.random() * 90}%`,
          left: `${Math.random() * 90}%`,
          width: `${8 + Math.random() * 24}px`,
          height: `${8 + Math.random() * 24}px`,
          opacity: 0.7 + Math.random() * 0.7,
          animationDelay: `${Math.random() * 2}s`,
        }}
      />
    ))}
    <div className="absolute inset-0 bg-black/30 rounded-lg" />
  </div>
);
