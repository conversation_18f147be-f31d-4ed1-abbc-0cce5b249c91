export const ModelPreview = ({
  modelPath,
  isSelected,
  onSelect,
}: {
  modelPath: string;
  isSelected: boolean;
  onSelect: () => void;
}) => (
  <div
    onClick={onSelect}
    className={`relative aspect-square rounded-lg overflow-hidden border-2 cursor-pointer transition-all hover:scale-105 ${
      isSelected
        ? "border-purple-500 shadow-[0_0_10px_rgba(129,90,219,0.6)]"
        : "border-gray-500 hover:border-purple-400"
    }`}
  >
    <img
      loading="lazy"
      src={modelPath}
      alt={`Model ${modelPath}`}
      className="w-full h-full object-cover"
      onError={(e) => {
        e.currentTarget.src =
          "https://via.placeholder.com/100x100/333333/ffffff?text=Model";
      }}
    />
    {isSelected && (
      <div className="absolute inset-0 bg-purple-500/20 flex items-center justify-center">
        <div className="w-6 h-6 bg-purple-500 rounded-full flex items-center justify-center">
          <svg
            className="w-4 h-4 text-white"
            fill="currentColor"
            viewBox="0 0 20 20"
          >
            <path
              fillRule="evenodd"
              d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
              clipRule="evenodd"
            />
          </svg>
        </div>
      </div>
    )}
  </div>
);
