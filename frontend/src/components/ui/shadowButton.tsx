import React from "react";

interface ShadowButtonProps {
  children: React.ReactNode;
  className?: string;
  disabled?: boolean;
  onClick?: () => void;
}

const ShadowButton: React.FC<ShadowButtonProps> = ({
  children,
  className = "",
  disabled = false,
  onClick,
}) => {
  return (
    <button
      onClick={onClick}
      disabled={disabled}
      className={`p-2 cursor-pointer border border-gray-500 rounded-md transition-all hover:border-gray-400 bg-purple-600 hover:bg-purple-700 !text-white flex justify-center items-center ${className}`}
      style={{
        boxShadow: "inset 0 0 10px rgba(255, 255, 255, 0.6)", // Strong white inner shadow
      }}
    >
      {children}
    </button>
  );
};

export default ShadowButton;
