import React from "react";
import { textVariants } from "./text";
import { cn } from "@/lib/utils";

interface RadioButtonProps {
  name: string;
  value: string;
  checked: boolean;
  onChange: (value: string) => void;
  label?: string;
  labelClass?: string;
}

const RadioButton: React.FC<RadioButtonProps> = ({
  name,
  value,
  checked,
  onChange,
  label,
  labelClass,
}) => {
  return (
    <label className="inline-flex items-center gap-2 cursor-pointer">
      <input
        type="radio"
        name={name}
        value={value}
        checked={checked}
        onChange={() => onChange(value)}
        className="sr-only peer"
      />
      <div
        className={`w-[18px] h-[18px] rounded-full border-[2px] 
            ${checked ? "border-blue-600" : "border-gray-300"} 
            flex items-center justify-center transition-all duration-200`}
      >
        <div
          className={`w-[8px] h-[8px] rounded-full bg-blue-600 
              transition-transform duration-200 
              ${checked ? "scale-100" : "scale-0"}`}
        ></div>
      </div>
      {label && (
        <span
          className={cn(
            "text-sm text-white select-none",
            textVariants({
              variant: "card_body",
            }),
            labelClass
          )}
        >
          {label}
        </span>
      )}
    </label>
  );
};

export default RadioButton;
