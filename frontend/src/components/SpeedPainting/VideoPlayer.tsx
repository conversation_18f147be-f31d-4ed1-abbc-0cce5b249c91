import { useEffect, useRef, useState } from "react";

interface VideoPlayerProps {
  videoUrl: string | null;
  onError?: (error: string) => void;
  className?: string;
}

const VideoPlayer = ({
  videoUrl,
  onError,
  className = "",
}: VideoPlayerProps) => {
  const videoRef = useRef<HTMLVideoElement>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [useFallback, setUseFallback] = useState(false);
  
  console.log("VideoPlayer rendering with URL:", videoUrl);
  
  // Test if the video URL is directly accessible
  useEffect(() => {
    if (!videoUrl) return;
    
    // Test if the URL is directly accessible
    const testDirectAccess = async () => {
      try {
        const response = await fetch(videoUrl, { method: 'HEAD' });
        const isWorking = response.ok;
        console.log(`Direct URL access test: ${isWorking ? 'SUCCESS' : 'FAILED'}`, {
          status: response.status,
          statusText: response.statusText,
          headers: Object.fromEntries([...response.headers.entries()])
        });
        
        if (!isWorking) {
          console.log("Using fallback approach for video playback");
          setUseFallback(true);
        }
      } catch (err) {
        console.error('Error testing direct URL access:', err);
        setUseFallback(true);
      }
    };
    
    testDirectAccess();
  }, [videoUrl]);
  
  useEffect(() => {
    if (!videoUrl) return;

    setIsLoading(true);
    setError(null);

    // Reset video element when URL changes
    if (videoRef.current && !useFallback) {
      videoRef.current.load();
    }
  }, [videoUrl, useFallback]);

  const handleLoadedData = () => {
    setIsLoading(false);
  };

  const handleError = () => {
    const errorMessage = "Failed to load video. Please try again later.";
    setError(errorMessage);
    setIsLoading(false);
    if (onError) onError(errorMessage);

    // Log error details for debugging
    console.error("Video loading error:", {
      videoUrl,
      videoElement: videoRef.current,
      error: videoRef.current?.error,
    });
  };

  return (
    <div className={`relative ${className}`}>
      {isLoading && videoUrl && !useFallback && (
        <div className="absolute inset-0 flex items-center justify-center bg-gray-800/50 rounded-lg">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
        </div>
      )}

      {error && !useFallback && (
        <div className="absolute inset-0 flex flex-col items-center justify-center bg-gray-800/50 rounded-lg text-red-500 p-4 text-center">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-10 w-10 mb-2"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
            />
          </svg>
          <p>{error}</p>
          <div className="flex gap-3 mt-4">
            <button
              className="px-4 py-2 bg-primary text-white rounded hover:bg-primary/80 transition-colors"
              onClick={() => {
                if (videoRef.current) {
                  setIsLoading(true);
                  setError(null);
                  videoRef.current.load();
                }
              }}
            >
              Retry
            </button>
            <button
              className="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-500 transition-colors"
              onClick={() => {
                setUseFallback(true);
                setError(null);
              }}
            >
              Try Alternate Player
            </button>
          </div>
        </div>
      )}

      {!useFallback ? (
        // Standard HTML5 video player
        <video
          ref={videoRef}
          controls
          className={`w-full h-80 object-contain mx-auto rounded-lg ${
            error ? "opacity-0" : "bg-gray-800/50"
          }`}
          onLoadedData={handleLoadedData}
          onError={handleError}
        >
          {videoUrl && <source src={videoUrl} type="video/mp4" />}
          Your browser does not support the video tag.
        </video>
      ) : (
        // Fallback approach using iframe
        <div className="w-full h-80 bg-gray-800/50 rounded-lg overflow-hidden flex flex-col">
          {videoUrl && (
            <>
              <iframe
                src={videoUrl}
                className="w-full h-full"
                allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                allowFullScreen
                title="Speed Painting Video"
                onLoad={() => setIsLoading(false)}
                loading="lazy"
              />
              <div className="bg-gray-700 p-2 text-center">
                <a 
                  href={videoUrl} 
                  download="speedpainting.mp4"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-white hover:text-blue-300 text-sm flex items-center justify-center gap-1"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                  </svg>
                  Direct Download Link
                </a>
              </div>
            </>
          )}
        </div>
      )}
    </div>
  );
};

export default VideoPlayer;
