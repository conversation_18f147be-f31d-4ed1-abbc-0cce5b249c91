import React, { useRef, type ChangeEvent } from "react";
import { Plus } from "lucide-react";

interface ClothingItem {
  id: string;
  image: string;
  type: string;
}

interface ModelSelectionProps {
  models: ClothingItem[];
  selectedModel: string | null;
  uploadedModelImage: string | null;
  onModelSelect: (modelId: string) => void;
  onFileSelect: (file: File) => void;
  onClearUploadedModel: () => void;
}

const ModelSelection: React.FC<ModelSelectionProps> = ({
  models,
  selectedModel,
  uploadedModelImage,
  onModelSelect,
  onFileSelect,
  onClearUploadedModel,
}) => {
  const modelInputRef = useRef<HTMLInputElement>(null);

  const handleModelClick = (): void => {
    modelInputRef.current?.click();
  };

  const handleFileChange = (e: ChangeEvent<HTMLInputElement>): void => {
    const files: FileList | null = e.target.files;
    if (files?.length) {
      onFileSelect(files[0]);
    }
  };

  const handleModelItemClick = (modelId: string): void => {
    onModelSelect(modelId);
  };

  return (
    <div className="mb-6">
      <h4 className="text-white font-medium mb-2">Select a Model</h4>
      <p className="text-gray-400 text-sm mb-4">Select our model to try on</p>

      {/* Scrollable container with proper grid */}
      <div className="max-h-[200px] overflow-y-auto overflow-x-hidden">
        <div className="grid grid-cols-4 gap-3 pr-2">
          {/* Upload Model Button */}
          <div
            className="w-full h-20 bg-gray-700 rounded-lg border-2 border-dashed border-gray-600 flex items-center justify-center cursor-pointer hover:border-gray-500 transition-colors flex-shrink-0"
            onClick={handleModelClick}
          >
            {uploadedModelImage ? (
              <div className="relative w-full h-full">
                <img
                  loading="lazy"
                  src={uploadedModelImage}
                  alt="Uploaded model"
                  className="w-full h-full object-cover object-top rounded-lg"
                />
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    onClearUploadedModel();
                  }}
                  className="absolute top-1 right-1 bg-red-500 hover:bg-red-600 text-white rounded-full w-4 h-4 flex items-center justify-center text-xs"
                >
                  ×
                </button>
              </div>
            ) : (
              <Plus className="w-6 h-6 text-gray-400" />
            )}
          </div>

          {/* Predefined Models */}
          {models.map((model) => (
            <div
              key={model.id}
              onClick={() => handleModelItemClick(model.id)}
              className={`w-full h-20 rounded-lg overflow-hidden cursor-pointer border-2 transition-colors flex-shrink-0 ${
                selectedModel === model.id && !uploadedModelImage
                  ? "border-purple-400"
                  : "border-transparent hover:border-gray-500"
              }`}
            >
              <img
                loading="lazy"
                src={model.image}
                alt={`Model ${model.id}`}
                className="w-full h-full object-cover object-top"
              />
            </div>
          ))}
        </div>
      </div>

      <input
        ref={modelInputRef}
        type="file"
        className="hidden"
        accept=".png,.jpg,.jpeg,.webp"
        onChange={handleFileChange}
      />
    </div>
  );
};

export default ModelSelection;
