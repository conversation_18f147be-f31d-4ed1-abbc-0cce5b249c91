/* eslint-disable @typescript-eslint/no-explicit-any */
import React, {
  useRef,
  type ChangeEvent,
  type Dispatch,
  type DragEvent,
  type SetStateAction,
} from "react";
import { Upload, Plus, Trash2 } from "lucide-react";

interface ClothingUploadProps {
  selectedClothingType: string;
  // Single clothes props
  isDragOver: boolean;
  setIsDragOver: (value: boolean) => void;
  uploadedImage: string | null;
  setUploadedImage: (value: string | null) => void;
  setSelectedCategory: Dispatch<SetStateAction<string>>;
  selectedRecentItem: any;
  setSelectedRecentItem: (value: any) => void;
  // Top & Bottom props
  isDragOverTop: boolean;
  setIsDragOverTop: (value: boolean) => void;
  isDragOverBottom: boolean;
  setIsDragOverBottom: (value: boolean) => void;
  uploadedTopImage: string | null;
  setUploadedTopImage: (value: string | null) => void;
  uploadedBottomImage: string | null;
  setUploadedBottomImage: (value: string | null) => void;
  selectedTopItem: any;
  setSelectedTopItem: (value: any) => void;
  selectedBottomItem: any;
  setSelectedBottomItem: (value: any) => void;
  // File handling
  onFileSelect: (file: File, type: "single" | "top" | "bottom") => void;
  // New props for dropdown values
  onDropdownChange?: (fit: string, category: string) => void;
  selectedCategory?: string;
}

const ClothingUpload: React.FC<ClothingUploadProps> = ({
  selectedClothingType,
  isDragOver,
  setIsDragOver,
  uploadedImage,
  setUploadedImage,
  selectedRecentItem,
  setSelectedRecentItem,
  isDragOverTop,
  setIsDragOverTop,
  isDragOverBottom,
  setIsDragOverBottom,
  uploadedTopImage,
  setUploadedTopImage,
  uploadedBottomImage,
  setUploadedBottomImage,
  selectedTopItem,
  setSelectedTopItem,
  selectedBottomItem,
  setSelectedBottomItem,
  onFileSelect,
  setSelectedCategory,
  selectedCategory,
  // onDropdownChange,
}) => {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const topInputRef = useRef<HTMLInputElement>(null);
  const bottomInputRef = useRef<HTMLInputElement>(null);
  console.log("categoryType", selectedCategory);
  // Dropdown states for single clothes
  // const [selectedFit, setSelectedFit] = useState<string>("Regular Fit");
  // const [, ] = useState<string>("Top");

  // Dropdown states for top and bottom
  //   const [, setSelectedTopFit] = useState<string>("Regular Fit");
  //   const [, setSelectedBottomFit] =
  //     useState<string>("Regular Fit");

  // Single clothes drag handlers
  const handleDragOver = (e: DragEvent<HTMLDivElement>): void => {
    e.preventDefault();
    setIsDragOver(true);
  };

  const handleDragLeave = (e: DragEvent<HTMLDivElement>): void => {
    e.preventDefault();
    setIsDragOver(false);
  };

  const handleDrop = (e: DragEvent<HTMLDivElement>): void => {
    e.preventDefault();
    setIsDragOver(false);
    const files: FileList = e.dataTransfer.files;
    if (files.length > 0) {
      onFileSelect(files[0], "single");
    }
  };

  // Top drag handlers
  const handleTopDragOver = (e: DragEvent<HTMLDivElement>): void => {
    e.preventDefault();
    setIsDragOverTop(true);
  };

  const handleTopDragLeave = (e: DragEvent<HTMLDivElement>): void => {
    e.preventDefault();
    setIsDragOverTop(false);
  };

  const handleTopDrop = (e: DragEvent<HTMLDivElement>): void => {
    e.preventDefault();
    setIsDragOverTop(false);
    const files: FileList = e.dataTransfer.files;
    if (files.length > 0) {
      onFileSelect(files[0], "top");
    }
  };

  // Bottom drag handlers
  const handleBottomDragOver = (e: DragEvent<HTMLDivElement>): void => {
    e.preventDefault();
    setIsDragOverBottom(true);
  };

  const handleBottomDragLeave = (e: DragEvent<HTMLDivElement>): void => {
    e.preventDefault();
    setIsDragOverBottom(false);
  };

  const handleBottomDrop = (e: DragEvent<HTMLDivElement>): void => {
    e.preventDefault();
    setIsDragOverBottom(false);
    const files: FileList = e.dataTransfer.files;
    if (files.length > 0) {
      onFileSelect(files[0], "bottom");
    }
  };

  const handleFileChange = (
    e: ChangeEvent<HTMLInputElement>,
    type: "single" | "top" | "bottom"
  ): void => {
    const files: FileList | null = e.target.files;
    if (files?.length) {
      onFileSelect(files[0], type);
    }
  };

  const handleUploadClick = (): void => {
    fileInputRef.current?.click();
  };

  const handleTopClick = (): void => {
    topInputRef.current?.click();
  };

  const handleBottomClick = (): void => {
    bottomInputRef.current?.click();
  };

  const clearSingleImage = () => {
    setUploadedImage(null);
    setSelectedRecentItem(null);
    if (fileInputRef.current) fileInputRef.current.value = "";
  };

  const clearTopImage = () => {
    setUploadedTopImage(null);
    setSelectedTopItem(null);
    if (topInputRef.current) topInputRef.current.value = "";
  };

  const clearBottomImage = () => {
    setUploadedBottomImage(null);
    setSelectedBottomItem(null);
    if (bottomInputRef.current) bottomInputRef.current.value = "";
  };

  // const handleFitChange = (e: ChangeEvent<HTMLSelectElement>) => {
  //   const newFit = e.target.value;
  //   setSelectedFit(newFit);
  //   onDropdownChange?.(newFit, selectedCategory);
  // };
  console.log("selectedCategory", selectedCategory);
  const handleCategoryChange = (e: ChangeEvent<HTMLSelectElement>) => {
    const newCategory = e.target.value;
    setSelectedCategory(newCategory);
    // onDropdownChange?.(selectedFit, newCategory);
  };

  //   const handleTopFitChange = (e: ChangeEvent<HTMLSelectElement>) => {
  //     setSelectedTopFit(e.target.value);
  //   };

  //   const handleBottomFitChange = (e: ChangeEvent<HTMLSelectElement>) => {
  //     setSelectedBottomFit(e.target.value);
  //   };

  if (selectedClothingType === "Single clothes") {
    return (
      <div className="mt-8">
        {/* Dropdowns Row */}
        {/* <div className="grid grid-cols-2 gap-3 mb-4">
          <div>
            <select
              value={selectedFit}
              onChange={handleFitChange}
              className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white text-sm focus:outline-none focus:border-purple-400 transition-colors"
            >
              <option value="Regular Fit">Regular Fit</option>
              <option value="Loose Fit">Loose Fit</option>
            </select>
          </div>

          <div>
            
          </div>
        </div> */}

        {/* Upload Area */}
        <div
          className={`border-2 border-dashed rounded-lg p-3 text-center transition-colors relative ${
            isDragOver
              ? "border-purple-400 bg-purple-900/30"
              : "border-gray-600 hover:border-gray-500"
          }`}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
        >
          {uploadedImage || selectedRecentItem ? (
            <div className="flex flex-col h-full">
              <div className="flex-1 flex items-center justify-center mb-3">
                <img
                  loading="lazy"
                  src={uploadedImage || selectedRecentItem?.image || ""}
                  alt="Selected clothing"
                  className="max-w-full max-h-24 object-contain rounded-lg"
                />
              </div>

              {/* Dropdown inside the box */}
              <div className="flex gap-2 items-center justify-end">
                {/* <select
                  value={selectedFit}
                  onChange={handleFitChange}
                  className="w-fit bg-gray-700 border border-gray-600 rounded px-2 py-1 text-white text-xs focus:outline-none focus:border-purple-400"
                >
                  <option value="Regular Fit">Regular Fit</option>
                  <option value="Loose Fit">Loose Fit</option>
                </select> */}
                <select
                  defaultValue={selectedCategory}
                  onChange={handleCategoryChange}
                  // defaultValue={categoryType || selectedCategory}
                  className="w-fit bg-gray-700 border border-gray-600 rounded px-2 py-1 text-white text-xs focus:outline-none focus:border-purple-400"
                >
                  <option value="Top">Top</option>
                  <option value="Bottom">Bottom</option>
                  <option value="Dress_Suit">Dress/Suit</option>
                </select>
                <button
                  onClick={handleUploadClick}
                  className="bg-blue-500 hover:bg-blue-600 text-white rounded-full w-6 h-6 flex items-center justify-center transition-colors"
                  title="Upload new image"
                >
                  <Upload size={12} />
                </button>
                <button
                  onClick={clearSingleImage}
                  className="bg-red-500 hover:bg-red-600 text-white rounded-full w-6 h-6 flex items-center justify-center transition-colors"
                  title="Delete image"
                >
                  <Trash2 size={12} />
                </button>
              </div>

              {/* Action Icons at bottom */}
              {/* <div className="flex gap-2 justify-center">
              </div> */}
            </div>
          ) : (
            <div className="cursor-pointer" onClick={handleUploadClick}>
              <div className="flex gap-2 justify-center items-center mb-2">
                <Plus className="w-5 h-5 text-gray-400" />
                <p className="text-gray-300 font-medium">Add Item</p>
              </div>
              <p className="text-gray-500 text-sm mb-3">Or drag & drop here</p>
            </div>
          )}

          <input
            ref={fileInputRef}
            type="file"
            className="hidden"
            accept=".png,.jpg,.jpeg,.webp"
            onChange={(e) => handleFileChange(e, "single")}
          />

          {isDragOver && (
            <div className="absolute inset-0 bg-purple-600/20 border-2 border-purple-400 rounded-lg flex items-center justify-center z-10">
              <div className="text-center">
                <Upload size={32} className="text-purple-300 mx-auto mb-2" />
                <p className="text-purple-200 font-medium">
                  Drop your files here
                </p>
              </div>
            </div>
          )}
        </div>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-2 gap-4 mt-8">
      {/* Top Upload */}
      <div
        className={`border-2 border-dashed rounded-lg p-2 text-center transition-colors cursor-pointer relative h-32 ${
          isDragOverTop
            ? "border-purple-400 bg-purple-900/30"
            : "border-gray-600 hover:border-gray-500"
        }`}
        onDragOver={handleTopDragOver}
        onDragLeave={handleTopDragLeave}
        onDrop={handleTopDrop}
        onClick={
          uploadedTopImage || selectedTopItem ? undefined : handleTopClick
        }
        role="button"
        tabIndex={0}
        aria-label="Add top"
      >
        {uploadedTopImage || selectedTopItem ? (
          <div className="flex flex-col h-full">
            <div className="flex-1 flex items-center justify-center mb-2">
              <img
                loading="lazy"
                src={uploadedTopImage || selectedTopItem?.image || ""}
                alt="Uploaded top"
                className="max-w-full max-h-16 object-contain rounded"
              />
            </div>

            {/* Dropdown inside the box */}
            {/* <select
              value={selectedTopFit}
              onChange={handleTopFitChange}
              className="w-full bg-gray-700 border border-gray-600 rounded px-1 py-1 text-white text-xs mb-2 focus:outline-none focus:border-purple-400"
              onClick={(e) => e.stopPropagation()}
            >
              <option value="Regular Fit">Regular Fit</option>
              <option value="Loose Fit">Loose Fit</option>
            </select> */}

            {/* Action Icons at bottom */}
            <div className="flex gap-1 justify-center">
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  handleTopClick();
                }}
                className="bg-blue-500 hover:bg-blue-600 text-white rounded-full w-5 h-5 flex items-center justify-center transition-colors"
                title="Upload new image"
              >
                <Upload size={10} />
              </button>
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  clearTopImage();
                }}
                className="bg-red-500 hover:bg-red-600 text-white rounded-full w-5 h-5 flex items-center justify-center transition-colors"
                title="Delete image"
              >
                <Trash2 size={10} />
              </button>
            </div>
          </div>
        ) : (
          <>
            <Plus className="w-4 h-4 text-gray-400 mx-auto mb-2" />
            <p className="text-gray-300 font-medium text-sm">Add Top</p>
            <p className="text-gray-500 text-xs">Or drag & drop here</p>
          </>
        )}
        <input
          ref={topInputRef}
          type="file"
          className="hidden"
          accept=".png,.jpg,.jpeg,.webp"
          onChange={(e) => handleFileChange(e, "top")}
        />
        {isDragOverTop && (
          <div className="absolute inset-0 bg-purple-600/20 border-2 border-purple-400 rounded-lg flex items-center justify-center z-10">
            <div className="text-center">
              <Upload size={20} className="text-purple-300 mx-auto mb-1" />
              <p className="text-purple-200 font-medium text-xs">
                Drop top here
              </p>
            </div>
          </div>
        )}
      </div>

      {/* Bottom Upload */}
      <div
        className={`border-2 border-dashed rounded-lg p-2 text-center transition-colors cursor-pointer relative h-32 ${
          isDragOverBottom
            ? "border-purple-400 bg-purple-900/30"
            : "border-gray-600 hover:border-gray-500"
        }`}
        onDragOver={handleBottomDragOver}
        onDragLeave={handleBottomDragLeave}
        onDrop={handleBottomDrop}
        onClick={
          uploadedBottomImage || selectedBottomItem
            ? undefined
            : handleBottomClick
        }
        role="button"
        tabIndex={0}
        aria-label="Add bottom"
      >
        {uploadedBottomImage || selectedBottomItem ? (
          <div className="flex flex-col h-full">
            <div className="flex-1 flex items-center justify-center mb-2">
              <img
                loading="lazy"
                src={uploadedBottomImage || selectedBottomItem?.image || ""}
                alt="Uploaded bottom"
                className="max-w-full max-h-16 object-contain rounded"
              />
            </div>

            {/* Dropdown inside the box */}
            {/* <select
              value={selectedBottomFit}
              onChange={handleBottomFitChange}
              className="w-full bg-gray-700 border border-gray-600 rounded px-1 py-1 text-white text-xs mb-2 focus:outline-none focus:border-purple-400"
              onClick={(e) => e.stopPropagation()}
            >
              <option value="Regular Fit">Regular Fit</option>
              <option value="Loose Fit">Loose Fit</option>
            </select> */}

            {/* Action Icons at bottom */}
            <div className="flex gap-1 justify-center">
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  handleBottomClick();
                }}
                className="bg-blue-500 hover:bg-blue-600 text-white rounded-full w-5 h-5 flex items-center justify-center transition-colors"
                title="Upload new image"
              >
                <Upload size={10} />
              </button>
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  clearBottomImage();
                }}
                className="bg-red-500 hover:bg-red-600 text-white rounded-full w-5 h-5 flex items-center justify-center transition-colors"
                title="Delete image"
              >
                <Trash2 size={10} />
              </button>
            </div>
          </div>
        ) : (
          <>
            <Plus className="w-4 h-4 text-gray-400 mx-auto mb-2" />
            <p className="text-gray-300 font-medium text-sm">Add Bottom</p>
            <p className="text-gray-500 text-xs">Or drag & drop here</p>
          </>
        )}
        <input
          ref={bottomInputRef}
          type="file"
          className="hidden"
          accept=".png,.jpg,.jpeg,.webp"
          onChange={(e) => handleFileChange(e, "bottom")}
        />
        {isDragOverBottom && (
          <div className="absolute inset-0 bg-purple-600/20 border-2 border-purple-400 rounded-lg flex items-center justify-center z-10">
            <div className="text-center">
              <Upload size={20} className="text-purple-300 mx-auto mb-1" />
              <p className="text-purple-200 font-medium text-xs">
                Drop bottom here
              </p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ClothingUpload;
