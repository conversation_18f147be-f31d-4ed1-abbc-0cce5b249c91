import { useState } from "react";
import { X, CreditCard, Calendar, Lock, User } from "lucide-react";
import ShadowButton from "@/components/ui/shadowButton";

const DepositModal = ({ isOpen = false, onClose = () => {} }) => {
  const [currentStep, setCurrentStep] = useState(0);
  const [amount, setAmount] = useState("1.00");
  const [cardNumber, setCardNumber] = useState("");
  const [expiryDate, setExpiryDate] = useState("");
  const [cvc, setCvc] = useState("");
  const [cardHolderName, setCardHolderName] = useState("");
  const [isSuccess, setIsSuccess] = useState(false);
  const [errors, setErrors] = useState({
    amount: "",
    cardNumber: "",
    expiryDate: "",
    cvc: "",
    cardHolderName: "",
  });

  const steps = ["Amount", "Payment", "Validation"];

  const validateCurrentStep = () => {
    if (currentStep === 0) {
      const amountNum = parseFloat(amount);
      if (isNaN(amountNum)) {
        setErrors((prev) => ({
          ...prev,
          amount: "Please enter a valid amount",
        }));
        return false;
      }
      if (amountNum < 1) {
        setErrors((prev) => ({ ...prev, amount: "Minimum amount is 1 USD" }));
        return false;
      }
    } else if (currentStep === 1) {
      const newErrors = {
        amount: "",
        cardNumber: !cardNumber ? "Card number is required" : "",
        expiryDate: !expiryDate ? "Expiry date is required" : "",
        cvc: !cvc ? "CVC is required" : "",
        cardHolderName: !cardHolderName ? "Card holder name is required" : "",
      };
      setErrors(newErrors);

      if (Object.values(newErrors).some((error) => error)) {
        return false;
      }
    }
    return true;
  };

  const handleNext = () => {
    if (!validateCurrentStep()) return;

    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    } else {
      setIsSuccess(true);
    }
  };

  const handleBack = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleAmountChange = (value: string) => {
    if (value === "" || /^\d*\.?\d*$/.test(value)) {
      setAmount(value);
      if (parseFloat(value) >= 1 || value === "") {
        setErrors((prev) => ({ ...prev, amount: "" }));
      }
    }
  };

  const formatCardNumber = (value: string) => {
    const v = value.replace(/\s+/g, "").replace(/[^0-9]/gi, "");
    const matches = v.match(/\d{4,16}/g);
    const match = (matches && matches[0]) || "";
    const parts = [];
    for (let i = 0, len = match.length; i < len; i += 4) {
      parts.push(match.substring(i, i + 4));
    }
    if (parts.length) {
      return parts.join("-");
    } else {
      return v;
    }
  };

  const renderStepContent = () => {
    if (isSuccess) {
      return (
        <div className="flex flex-col items-center py-8 text-center">
          <h3 className="text-2xl font-semibold text-white mb-4">
            Your deposit was a success! 🎉
          </h3>
          <p className="text-gray-400 mb-8">
            Congratulations, your deposit went on swiftly!
          </p>
        </div>
      );
    }

    switch (currentStep) {
      case 0:
        return (
          <div className="flex flex-col items-center py-3">
            <div className="relative w-full">
              <h3 className="text-lg text-gray-300 ml-3 mb-2">Amount</h3>
              <div className="flex items-center justify-center relative">
                <input
                  type="text"
                  value={amount}
                  onChange={(e) => handleAmountChange(e.target.value)}
                  className="bg-transparent text-5xl font-semibold text-white text-center w-32 outline-none"
                  placeholder="0.00"
                />
                <span className="text-5xl font-bold text-white ml-2">USD</span>
              </div>
              {errors.amount && (
                <p className="absolute left-0 right-0 text-center text-red-400 text-sm mt-1">
                  {errors.amount}
                </p>
              )}
              <p className="text-gray-500 text-sm text-center mt-8">
                Minimum deposit: 1 USD
              </p>
            </div>
          </div>
        );

      case 1:
        return (
          <div className="py-8 space-y-6">
            {/* Card Holder Name */}
            <div className="relative">
              <div className="relative">
                <User className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                <input
                  type="text"
                  value={cardHolderName}
                  onChange={(e) => setCardHolderName(e.target.value)}
                  placeholder="Card Holder Name"
                  className="w-full bg-white/10 border border-gray-700 rounded-lg px-10 py-3 text-white placeholder-gray-400 focus:border-purple-500 focus:outline-none"
                />
              </div>
              {errors.cardHolderName && (
                <p className="absolute text-red-400 text-sm mt-1">
                  {errors.cardHolderName}
                </p>
              )}
            </div>

            {/* Card Number */}
            <div className="relative">
              <div className="relative">
                <CreditCard className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                <input
                  type="text"
                  value={cardNumber}
                  onChange={(e) =>
                    setCardNumber(formatCardNumber(e.target.value))
                  }
                  placeholder="Card Number"
                  className="w-full bg-white/10 border border-gray-700 rounded-lg px-10 py-3 text-white placeholder-gray-400 focus:border-purple-500 focus:outline-none"
                />
              </div>
              {errors.cardNumber && (
                <p className="absolute text-red-400 text-sm mt-1">
                  {errors.cardNumber}
                </p>
              )}
            </div>

            {/* Expiry Date and CVC */}
            <div className="grid grid-cols-2 gap-4">
              {/* Expiry Date */}
              <div className="relative">
                <div className="relative">
                  <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                  <input
                    type="text"
                    value={expiryDate}
                    onChange={(e) => setExpiryDate(e.target.value)}
                    placeholder="MM/YY"
                    className="w-full bg-white/10 border border-gray-700 rounded-lg px-10 py-3 text-white placeholder-gray-400 focus:border-purple-500 focus:outline-none"
                  />
                </div>
                {errors.expiryDate && (
                  <p className="absolute text-red-400 text-sm mt-1">
                    {errors.expiryDate}
                  </p>
                )}
              </div>

              {/* CVC */}
              <div className="relative">
                <div className="relative">
                  <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                  <input
                    type="text"
                    value={cvc}
                    onChange={(e) => setCvc(e.target.value)}
                    placeholder="CVC"
                    className="w-full bg-white/10 border border-gray-700 rounded-lg px-10 py-3 text-white placeholder-gray-400 focus:border-purple-500 focus:outline-none"
                  />
                </div>
                {errors.cvc && (
                  <p className="absolute text-red-400 text-sm mt-1">
                    {errors.cvc}
                  </p>
                )}
              </div>
            </div>
          </div>
        );

      case 2:
        return (
          <div className="flex flex-col items-center py-8">
            <div className="">
              <h3 className="text-lg font-medium text-white mb-2">Amount</h3>
              <div className="text-4xl font-bold text-gray-400 mb-8">
                {amount} USD
              </div>
            </div>
            <div className="text-left w-full space-y-4">
              <div>
                <div className="flex items-center mb-1">
                  <span className="text-white font-medium">Card Number</span>
                  <div className="w-2 h-2 bg-green-500 rounded-full ml-2"></div>
                </div>
                <div className="text-gray-400">
                  {cardNumber
                    ? `XXXX-XXXX-XXXX-${cardNumber.slice(-4)}`
                    : "XXXX-XXXX-XXXX-XXXX"}
                </div>
              </div>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  const getButtonText = () => {
    if (isSuccess) return "Close";
    switch (currentStep) {
      case 0:
        return "Next";
      case 1:
        return "Next";
      case 2:
        return "Finalize";
      default:
        return "Next";
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-primary/80 bg-opacity-50 flex items-center justify-center p-4 z-50 overflow-auto py-10">
      <div className="bg-gray-800/70 rounded-2xl p-8 w-full max-w-[600px] relative">
        <button
          onClick={onClose}
          className="absolute top-6 right-6 text-gray-400 hover:text-white transition-colors"
        >
          <X className="w-6 h-6" />
        </button>

        {!isSuccess && (
          <>
            <h2 className="text-2xl font-inter text-white mb-4">
              Let's make a deposit!
            </h2>
            <p className="text-gray-400 mb-8">
              Start by providing data relevant to your deposit.
            </p>
          </>
        )}

        {/* Progress Steps */}
        {!isSuccess && (
          <div className="mb-8">
            <div className="flex items-center justify-between mb-4">
              {steps.map((step, index) => (
                <div
                  key={step}
                  className={`text-sm font-medium ${
                    index === currentStep
                      ? "text-purple-400"
                      : index < currentStep
                      ? "text-purple-400"
                      : "text-gray-500"
                  }`}
                >
                  {step}
                </div>
              ))}
            </div>

            {/* Progress Line */}
            <div className="relative">
              <div className="w-full h-1 bg-gray-700 rounded-full"></div>
              <div
                className="absolute top-0 left-0 h-1 bg-purple-500 rounded-full transition-all duration-300"
                style={{
                  width: `${((currentStep + 1) / steps.length) * 100}%`,
                }}
              ></div>
              {/* Progress Dot */}
              <div
                className="absolute top-1/2 transform -translate-y-1/2 w-3 h-3 bg-purple-500 rounded-full transition-all duration-300"
                style={{
                  left: `${((currentStep + 1) / steps.length) * 100 - 1.5}%`,
                }}
              ></div>
            </div>
          </div>
        )}

        {/* Step Content */}
        <div className="">{renderStepContent()}</div>

        {/* Action Buttons */}
        <div className="flex gap-4 justify-center">
          {!isSuccess && currentStep > 0 && (
            <button
              onClick={handleBack}
              className="min-w-[150px] px-6 py-3 bg-transparent border border-gray-600 text-white rounded-full font-medium hover:bg-gray-700 transition-colors"
            >
              Back
            </button>
          )}
          {!isSuccess && currentStep === 0 && (
            <button
              onClick={onClose}
              className="min-w-[150px] px-6 py-3 bg-transparent border border-gray-600 text-white rounded-full font-medium hover:bg-gray-700 transition-colors"
            >
              Cancel
            </button>
          )}
          <ShadowButton
            onClick={isSuccess ? onClose : handleNext}
            className="min-w-[150px] !px-6 !py-3 !rounded-full font-medium"
          >
            {getButtonText()}
          </ShadowButton>
        </div>
      </div>
    </div>
  );
};

export default DepositModal;
