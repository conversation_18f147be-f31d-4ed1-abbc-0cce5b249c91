import React from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  Di<PERSON>Title,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Download } from "lucide-react";
import { Link } from "react-router-dom";

interface TransactionDetailsModalProps {
  isOpen: boolean;
  onClose: () => void;
  transaction: {
    id: string;
    name: string;
    date: string;
    time: string;
    amount: string;
    status: string;
    paymentType: string;
    transactionId: string;
    createdAt?: string;
    metadata?: {
      planName?: string;
      planInterval?: string;
      creditAmount?: number;
      packageName?: string;
      refundReason?: string;
    };
    invoicePdf?: string;
    hostedInvoiceUrl?: string;
  } | null;
}

const formatStatus = (status: string): string => {
  const statusMap: Record<string, string> = {
    succeeded: "Completed",
    processing: "Processing",
    pending: "Pending",
    failed: "Failed",
    refunded: "Refunded",
    canceled: "Canceled",
  };
  return statusMap[status.toLowerCase()] || status;
};

const getStatusColor = (status: string): string => {
  const statusColorMap: Record<string, string> = {
    succeeded: "bg-green-500/20 text-green-400",
    processing: "bg-blue-500/20 text-blue-400",
    pending: "bg-yellow-500/20 text-yellow-400",
    failed: "bg-red-500/20 text-red-400",
    refunded: "bg-purple-500/20 text-purple-400",
    canceled: "bg-gray-500/20 text-gray-400",
  };
  return statusColorMap[status.toLowerCase()] || "bg-gray-500/20 text-gray-400";
};

const TransactionDetailsModal: React.FC<TransactionDetailsModalProps> = ({
  isOpen,
  onClose,
  transaction,
}) => {
  if (!transaction) return null;

  const paymentTypeMap: Record<string, string> = {
    subscription: "Subscription",
    credit: "Credit Purchase",
    topup: "Top-up",
    refund: "Refund",
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="bg-gray-900 text-white border-gray-800">
        <DialogHeader>
          <DialogTitle className="text-xl font-semibold">
            Transaction Details
          </DialogTitle>
          <DialogDescription className="text-gray-400">
            Details for transaction on {transaction.date}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4 py-4">
          <div className="flex justify-between items-center">
            <span className="text-gray-400">Transaction ID</span>
            <span className="font-medium">{transaction.transactionId}</span>
          </div>

          <div className="flex justify-between items-center">
            <span className="text-gray-400">Date & Time</span>
            <span className="font-medium">
              {transaction.date} at {transaction.time}
            </span>
          </div>

          <div className="flex justify-between items-center">
            <span className="text-gray-400">Amount</span>
            <span className="font-medium text-lg">{transaction.amount}</span>
          </div>

          <div className="flex justify-between items-center">
            <span className="text-gray-400">Status</span>
            <Badge
              className={`${getStatusColor(transaction.status)} border-none`}
            >
              {formatStatus(transaction.status)}
            </Badge>
          </div>

          <div className="flex justify-between items-center">
            <span className="text-gray-400">Payment Type</span>
            <span className="font-medium">
              {paymentTypeMap[transaction.paymentType.toLowerCase()] ||
                transaction.paymentType}
            </span>
          </div>

          {/* Subscription details */}
          {transaction.paymentType.toLowerCase() === "subscription" &&
            transaction.metadata?.planName && (
              <div className="mt-6 pt-4 border-t border-gray-800">
                <h3 className="text-lg font-medium mb-3">
                  Subscription Details
                </h3>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-gray-400">Plan</span>
                    <span className="font-medium">
                      {transaction.metadata.planName}
                    </span>
                  </div>
                  {transaction.metadata.planInterval && (
                    <div className="flex justify-between">
                      <span className="text-gray-400">Billing Cycle</span>
                      <span className="font-medium capitalize">
                        {transaction.metadata.planInterval}
                      </span>
                    </div>
                  )}
                </div>
              </div>
            )}

          {/* Credit purchase details */}
          {transaction.paymentType.toLowerCase() === "credit" &&
            transaction.metadata?.creditAmount && (
              <div className="mt-6 pt-4 border-t border-gray-800">
                <h3 className="text-lg font-medium mb-3">
                  Credit Purchase Details
                </h3>
                <div className="space-y-2">
                  {transaction.metadata.packageName && (
                    <div className="flex justify-between">
                      <span className="text-gray-400">Package</span>
                      <span className="font-medium">
                        {transaction.metadata.packageName}
                      </span>
                    </div>
                  )}
                  <div className="flex justify-between">
                    <span className="text-gray-400">Credits Added</span>
                    <span className="font-medium">
                      {transaction.metadata.creditAmount}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-400">Expiry</span>
                    <span className="font-medium">2 years from purchase</span>
                  </div>
                </div>
              </div>
            )}

          {/* Refund details */}
          {transaction.paymentType.toLowerCase() === "refund" && (
            <div className="mt-6 pt-4 border-t border-gray-800">
              <h3 className="text-lg font-medium mb-3">Refund Details</h3>
              <div className="space-y-2">
                {transaction.metadata?.refundReason && (
                  <div className="flex justify-between">
                    <span className="text-gray-400">Reason</span>
                    <span className="font-medium">
                      {transaction.metadata.refundReason}
                    </span>
                  </div>
                )}
                <div className="text-gray-400 text-sm mt-2">
                  This amount has been refunded to your original payment method.
                </div>
              </div>
            </div>
          )}

          {transaction.status.toLowerCase() === "succeeded" && (
            <div className="pt-4">
              <p className="text-gray-400 text-sm mb-2">
                Your payment was successful. You can download your invoice for
                your records.
              </p>
            </div>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            Close
          </Button>
          {transaction.status.toLowerCase() === "completed" && (
            <Link to={`${transaction.invoicePdf}`} target="_blank">
              <Button className="bg-purple-600 hover:bg-purple-700">
                <Download className="w-4 h-4 mr-2" />
                Download Invoice
              </Button>
            </Link>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default TransactionDetailsModal;
