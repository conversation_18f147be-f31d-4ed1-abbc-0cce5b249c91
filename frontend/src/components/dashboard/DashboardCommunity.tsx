import { VTO_COMMUNITY_IMAGES } from "@/utils/DUMMY_DATA";
import { useState } from "react";

// Define a type for media items (images or videos)
interface MediaItem {
  id: string;
  src: string;
  height?: string; // Optional height for Tailwind CSS classes
}

// Define types for specific tab content
type BackgroundImage = MediaItem;
type VtoImage = MediaItem;
type SpeedpaintingVideo = MediaItem;

// Define the shape of the DATA_TO_DISPLAY object
interface DataToDisplay {
  All: MediaItem[];
  "Background Removal": BackgroundImage[];
  "Virtual Try On": VtoImage[];
  SpeedPainting: SpeedpaintingVideo[];
}

const DashboardCommunity: React.FC = () => {
  const [activeTab, setActiveTab] = useState<
    "All" | "Background Removal" | "Virtual Try On" | "SpeedPainting"
  >("All");

  const tabs: string[] = [
    "All",
    "Background Removal",
    "Virtual Try On",
    "SpeedPainting",
  ];

  // Sample images for Background Removal
  const backgroundImages: BackgroundImage[] = [
    {
      id: "bg_1",
      src: "https://picsum.photos/300/400?random=1",
      height: "h-64",
    },
    {
      id: "bg_2",
      src: "https://picsum.photos/300/300?random=2",
      height: "h-48",
    },
    {
      id: "bg_3",
      src: "https://picsum.photos/300/500?random=3",
      height: "h-80",
    },
    {
      id: "bg_6",
      src: "https://picsum.photos/300/320?random=6",
      height: "h-52",
    },
    {
      id: "bg_7",
      src: "https://picsum.photos/300/480?random=7",
      height: "h-76",
    },
    {
      id: "bg_8",
      src: "https://picsum.photos/300/360?random=8",
      height: "h-60",
    },
    {
      id: "bg_9",
      src: "https://picsum.photos/300/420?random=9",
      height: "h-68",
    },
    {
      id: "bg_10",
      src: "https://picsum.photos/300/380?random=10",
      height: "h-64",
    },
    {
      id: "bg_11",
      src: "https://picsum.photos/300/340?random=11",
      height: "h-56",
    },
    {
      id: "bg_12",
      src: "https://picsum.photos/300/460?random=12",
      height: "h-74",
    },
    {
      id: "bg_13",
      src: "https://picsum.photos/300/480?random=13",
      height: "h-76",
    },
  ];

  // Sample images for Virtual Try On
  const vtoImages: VtoImage[] = VTO_COMMUNITY_IMAGES;

  // Sample videos for SpeedPainting
  const speedpaintingVideos: SpeedpaintingVideo[] = [
    { id: "sp_1", src: "/SpeedpaintingVideo/1.mp4", height: "h-64" },
    { id: "sp_2", src: "/SpeedpaintingVideo/2.mp4", height: "h-48" },
    { id: "sp_3", src: "/SpeedpaintingVideo/3.mp4", height: "h-80" },
    { id: "sp_4", src: "/SpeedpaintingVideo/4.mp4", height: "h-52" },
    { id: "sp_5", src: "/SpeedpaintingVideo/5.mp4", height: "h-76" },
    { id: "sp_6", src: "/SpeedpaintingVideo/6.mp4", height: "h-60" },
    { id: "sp_7", src: "/SpeedpaintingVideo/7.mp4", height: "h-68" },
  ];

  // Combine all content for the "All" tab
  const allContent: MediaItem[] = [
    ...backgroundImages,
    ...vtoImages,
    ...speedpaintingVideos,
  ];

  const DATA_TO_DISPLAY: DataToDisplay = {
    All: allContent,
    "Background Removal": backgroundImages,
    "Virtual Try On": vtoImages,
    SpeedPainting: speedpaintingVideos,
  };

  // Force re-render by using a key on the grid container
  return (
    <div className="text-white bg-[#1A1A1A]">
      <div className="mx-auto max-w-7xl p-4">
        {/* Tabs */}
        <div className="flex space-x-8 mb-8 border-b border-gray-700">
          {tabs.map((tab) => (
            <button
              key={tab}
              onClick={() => setActiveTab(tab as keyof DataToDisplay)}
              className={`pb-4 px-1 font-medium transition-colors duration-200 cursor-pointer ${
                activeTab === tab
                  ? "text-white border-b-2 border-blue-500"
                  : "text-gray-400 hover:text-gray-300"
              }`}
            >
              {tab}
            </button>
          ))}
        </div>

        {/* Content Grid - Masonry Layout with Vertical Scrolling */}
        <div
          className="relative max-h-[calc(90vh-12rem)] overflow-y-auto overflow-x-hidden"
          key={activeTab} // Force re-render when activeTab changes
        >
          <div className="columns-2 md:columns-3 lg:columns-4 xl:columns-5 gap-4 space-y-4">
            {DATA_TO_DISPLAY[activeTab].map((item, i) => (
              <div
                key={`${item.id} + ${i}`}
                className="break-inside-avoid mb-4 group cursor-pointer"
              >
                <div className="relative overflow-hidden rounded-lg bg-gray-800 hover:scale-105 transition-transform duration-200">
                  {item.src.endsWith(".mp4") ? (
                    <video
                      src={item.src}
                      className={`w-full ${item.height || "h-64"} object-cover`}
                      controls
                      muted
                      loop
                      autoPlay
                    />
                  ) : (
                    <img
                      loading="lazy"
                      src={item.src}
                      alt={`Community item ${item.id}`}
                      className={`w-full ${
                        item.height || "h-64"
                      } object-cover object-top`}
                    />
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default DashboardCommunity;
