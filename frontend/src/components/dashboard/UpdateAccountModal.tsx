import { X } from "lucide-react";
// import UpgradeCreditModal from "../subscription/UpgradeCreditModal";
import { PricingCard } from "../common/PricingCard";

const UpdateAccountModal = ({ isOpen = true, onClose = () => {} }) => {
  // const [billingCycle, setBillingCycle] = useState("monthly");
  // const [isOpenUpgradeModal, setIsOpenUpgradeModal] = useState(false);

  if (!isOpen) return null;

  // const plans = [
  //   {
  //     name: "Basic",
  //     price: "Free!",
  //     features: [
  //       "25 Images or 1.5 mins of video",
  //       "1 Customized Instant Avatar",
  //       "Upload file size limited to 150M and 5mins",
  //       "60+ free Public Studio Avatars",
  //     ],
  //     buttonText: "",
  //     buttonDisabled: true,
  //     isPopular: false,
  //   },
  //   {
  //     name: "Pro",
  //     price: "$21",
  //     originalPrice: "$21",
  //     billingInfo: "Billed yearly as $240",
  //     features: [
  //       "Everything in basic",
  //       "Watermark removal",
  //       "Upload file size limited to 150M and 5mins",
  //       "60+ free Public Studio Avatars",
  //     ],
  //     buttonText: "Start 3 days Free Trial 🔥",
  //     upgradeText: "Upgrade",
  //     buttonDisabled: false,
  //     isPopular: false,
  //   },
  //   {
  //     name: "Pro",
  //     price: "$21",
  //     originalPrice: "$21",
  //     billingInfo: "Billed yearly as $240",
  //     features: [
  //       "Everything in Pro",
  //       "5 Customized Instant Avatars",
  //       "Upload file size limited to 150M and 5mins",
  //       "60+ free Public Studio Avatars",
  //     ],
  //     buttonText: "Start 3 days Free Trial 🔥",
  //     upgradeText: "Upgrade",
  //     buttonDisabled: false,
  //     isPopular: true,
  //   },
  //   {
  //     name: "Pro",
  //     price: "$21",
  //     originalPrice: "$21",
  //     billingInfo: "Billed yearly as $240",
  //     features: [
  //       "Everything in Pro max",
  //       "10 Customized Instant Avatars",
  //       "Upload file size up to 1G",
  //       "40+ free Public Studio Avatars",
  //     ],
  //     buttonText: "Start 3 days Free Trial 🔥",
  //     upgradeText: "Upgrade",
  //     buttonDisabled: false,
  //     isPopular: false,
  //   },
  //   {
  //     name: "Basic",
  //     price: "Free!",
  //     features: [
  //       "Everything in Studio",
  //       "Enterprise Level Security",
  //       "Dedicated Customer Success Manager",
  //       "VIP Processing",
  //     ],
  //     buttonText: "",
  //     buttonDisabled: true,
  //     isPopular: false,
  //   },
  // ];

  return (
    <div className="fixed inset-0 bg-black/80 bg-opacity-50 flex items-center justify-center z-50 ">
      <div className="bg-[#1A1D21F5] py-5 relative  rounded-2xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <button
          onClick={onClose}
          className="text-gray-400 absolute right-5 top-5 hover:text-white transition-colors"
          aria-label="Close modal"
        >
          <X size={24} />
        </button>

        {/* Billing Toggle */}
        {/* <div className="flex justify-center mb-8">
          <div className="bg-gray-700 rounded-lg p-1 flex">
            <button
              onClick={() => setBillingCycle("monthly")}
              className={`px-6 py-2 rounded-md transition-colors ${
                billingCycle === "monthly"
                  ? "bg-purple-600 text-white"
                  : "text-gray-300 hover:text-white"
              }`}
            >
              Monthly
            </button>
            <button
              onClick={() => setBillingCycle("yearly")}
              className={`px-6 py-2 rounded-md transition-colors flex items-center gap-2 ${
                billingCycle === "yearly"
                  ? "bg-purple-600 text-white"
                  : "text-gray-300 hover:text-white"
              }`}
            >
              Yearly &nbsp; |
              <span
                className={`text-xs  ${
                  billingCycle === "yearly" ? "text-white" : "text-purple-500"
                }`}
              >
                30% Off
              </span>
            </button>
          </div>
        </div> */}

        {/* Plans Grid */}
        <PricingCard />
      </div>
    </div>
  );
};

export default UpdateAccountModal;
