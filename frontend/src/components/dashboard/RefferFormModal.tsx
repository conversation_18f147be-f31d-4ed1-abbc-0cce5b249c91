import React, { useState } from "react";
import { X } from "lucide-react";
import { InstagramIcon, LinkedInIcon, XTwitterIcon } from "@/lib/icons";

// Define the props interface
interface RefferFormModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const RefferFormModal: React.FC<RefferFormModalProps> = ({
  isOpen,
  onClose,
}) => {
  const [email, setEmail] = useState<string>("");
  const [selectedPlatform, setSelectedPlatform] = useState<string | null>(null);

  if (!isOpen) return null;

  const handleShare = (platform: string): void => {
    setSelectedPlatform(platform);
    console.log(`Sharing on ${platform}`);
  };

  const handleEmailSubmit = (): void => {
    if (email.trim()) {
      console.log("Sending email to:", email);
    }
  };

  const handleCancel = (): void => {
    setEmail("");
    setSelectedPlatform(null);
    onClose();
  };

  return (
    <div className="fixed inset-0 bg-primary/90 bg-opacity-75 flex items-center justify-center z-50 p-4">
      <div className="bg-[#1A1D21F5] rounded-xl max-w-xl w-full mx-auto relative px-4">
        {/* Close button */}
        <button
          onClick={handleCancel}
          className="absolute top-4 right-4 text-gray-400 hover:text-white transition-colors"
        >
          <X size={20} />
        </button>

        <div className="p-6">
          {/* Header */}
          <div className="mb-6">
            <h2 className="text-xl font-semibold text-white mb-3 font-inter">
              Refer and Earn Exciting Rewards with MiragicAI Referral Program!
            </h2>
            <p className="text-gray-300 text-sm leading-relaxed">
              For each friend you refer who registers and uses at least 4
              credits,you'll earn a $10 discount reward.If they subscribe,
              you'll earn a discount reward worth 30% of their subscription fee.
              Your friend gets 100 free Credits and a 30% discount.
            </p>
          </div>

          {/* Share with */}
          <div className="mb-6">
            <h3 className="text-white font-medium mb-4">Share with</h3>
            <div className="flex gap-3">
              <button
                onClick={() => handleShare("X")}
                className={`flex items-center gap-5 px-6 py-2 rounded-lg border transition-colors ${
                  selectedPlatform === "X"
                    ? "border-blue-500 bg-blue-500 bg-opacity-20"
                    : "border-gray-600 hover:border-gray-500"
                }`}
              >
                <div className="p-2 rounded-full border border-gray-500">
                  <XTwitterIcon size={"20"} />
                </div>
                <span className="text-white text-sm">X</span>
              </button>

              <button
                onClick={() => handleShare("LinkedIn")}
                className={`flex items-center gap-3 px-6 py-2 rounded-lg border transition-colors ${
                  selectedPlatform === "LinkedIn"
                    ? "border-blue-500 bg-blue-500 bg-opacity-20"
                    : "border-gray-600 hover:border-gray-500"
                }`}
              >
                <div className="p-2 rounded-full border border-gray-500">
                  <LinkedInIcon size={"20"} />
                </div>
                <span className="text-white text-sm">LinkedIn</span>
              </button>

              <button
                onClick={() => handleShare("Instagram")}
                className={`flex items-center gap-3 px-6 py-2 rounded-lg border transition-colors ${
                  selectedPlatform === "Instagram"
                    ? "border-blue-500 bg-blue-500 bg-opacity-20"
                    : "border-gray-600 hover:border-gray-500"
                }`}
              >
                <div className="p-2 rounded-full border border-gray-500">
                  <InstagramIcon size={"20"} />
                </div>
                <span className="text-white text-sm">Instagram</span>
              </button>
            </div>
          </div>

          {/* Email section */}
          <div className="mb-8 mt-8">
            <div className="relative">
              {/* <Mail
                className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"
                size={16}
              /> */}
              <textarea
                placeholder="Enter email address"
                value={email}
                onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) =>
                  setEmail(e.target.value)
                }
                className="w-full bg-white/5 border border-gray-600 rounded-lg px-4 py-3 text-white placeholder-gray-400 focus:outline-none focus:border-blue-500 focus:ring-1 focus:ring-blue-500 resize-none"
                rows={5} // Set to 5 rows (can adjust to 6 if preferred)
              />
            </div>
          </div>

          {/* Action buttons */}
          <div className="flex gap-3">
            <button
              onClick={handleCancel}
              className="flex-1 px-4 py-3 bg-gray-700 hover:bg-gray-600 text-white rounded-full font-medium transition-colors"
            >
              Cancel
            </button>
            <button
              onClick={handleEmailSubmit}
              className="flex-1 px-6 text-white text-base rounded-full cursor-pointer bg-gradient-to-br from-[#4e1e95] to-[#3d0d6a] shadow-[0_0_10px_rgba(129,90,219,0.6)] border border-purple-500 hover:shadow-[0_0_15px_rgba(129,90,219,0.9)] transition"
            >
              Go earn!
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RefferFormModal;
