import { Text } from "../ui/text";
import { Button } from "../ui/button";
import { Loader2 } from "lucide-react";
import { cn } from "@/lib/utils";
import RadioButton from "@/components/ui/radioBtn";
import GradientText from "../common/GradientText";
// import type { User } from "@/services/auth.service";

interface CreditPackage {
  id: string;
  currency: string;
  price: number;
  creditsAmount: number;
}

interface TopUpCreditProps {
  creditPackages: CreditPackage[];
  selectedPackageId: string;
  packagesLoading: boolean;
  packagesError: string | null;
  purchaseLoading: boolean;
  handleCreditPackageChange: (value: string) => void;
  handlePurchaseCredits: () => void;
  handleZIndex: (
    cardType: "free" | "subscription" | "topup",
    id: string
  ) => void;
}

export const TopUpCredit = ({
  creditPackages,
  selectedPackageId,
  packagesLoading,
  packagesError,
  purchaseLoading,
  handleCreditPackageChange,
  handlePurchaseCredits,
  handleZIndex,
}: TopUpCreditProps) => {
  return (
    <>
      <div className="p-4 pt-10 flex flex-col gap-8 ">
        <div className="flex flex-col gap-2">
          <Text variant={"card_title"} className="text-[#F5F5F7] text-center">
            Top Up Credits
          </Text>
          {packagesLoading ? (
            <div className="flex justify-center items-center py-8">
              <Loader2 className="w-8 h-8 text-white animate-spin" />
            </div>
          ) : packagesError ? (
            <div className="text-red-500 py-4 text-center">{packagesError}</div>
          ) : (
            <>
              <Text variant={"section_title"} className="text-center">
                <GradientText className="text-5xl font-semibold">
                  {selectedPackageId && creditPackages.length > 0 ? (
                    <>
                      {creditPackages.find(
                        (pkg) => pkg.id === selectedPackageId
                      )?.currency === "USD" && "$"}
                      {creditPackages.find(
                        (pkg) => pkg.id === selectedPackageId
                      )?.price || 0}
                    </>
                  ) : (
                    "$0"
                  )}
                </GradientText>
                <GradientText className="text-[16px]">
                  {" "}
                  /{" "}
                  {selectedPackageId && creditPackages.length > 0 ? (
                    <>
                      {creditPackages.find(
                        (pkg) => pkg.id === selectedPackageId
                      )?.creditsAmount || 0}{" "}
                      Credits
                    </>
                  ) : (
                    "0 Credits"
                  )}
                </GradientText>
              </Text>
            </>
          )}
        </div>
        <div className="flex flex-col gap-2">
          {!packagesLoading &&
            !packagesError &&
            creditPackages.map((pkg) => {
              // Calculate price per credit
              const pricePerCredit = (pkg.price / pkg.creditsAmount).toFixed(3);

              return (
                <div
                  key={pkg.id}
                  onClick={() => handleZIndex("topup", pkg.id)}
                  className={cn(
                    "w-full grid cursor-pointer p-2.5 grid-cols-12 gap-4",
                    selectedPackageId === pkg.id ? "bg-[#192f50]" : ""
                  )}
                >
                  <div className="col-span-6">
                    <RadioButton
                      name="topup"
                      value={pkg.id}
                      checked={selectedPackageId === pkg.id}
                      onChange={(value) => handleCreditPackageChange(value)}
                      label={`${pkg.creditsAmount} credits`}
                      labelClass="text-[#D9D9D9] text-base"
                    />
                  </div>
                  <Text
                    variant={"card_body"}
                    className="text-[#D9D9D9] text-base col-span-2 font-light"
                  >
                    {pkg.currency === "USD" && "$ "}
                    {pkg.price}
                  </Text>
                  <Text
                    variant={"card_body"}
                    className="text-[#D9D9D9] text-base col-span-4 text-right font-light"
                  >
                    {pkg.currency === "USD" && "$"}
                    {pricePerCredit}/credit
                  </Text>
                </div>
              );
            })}
        </div>
        <div className="flex flex-col gap-2.5 justify-center items-center">
          <Button
            variant={"animeShine"}
            outline={false}
            className="w-full bg-[#192f50] border-white/15 rounded-full h-[50px]"
            onClick={handlePurchaseCredits}
            disabled={purchaseLoading || !selectedPackageId}
            maxShineDelayMs={1500}
          >
            {purchaseLoading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Processing...
              </>
            ) : (
              "Buy now"
            )}
          </Button>
          <Text variant={"section_title"} className=" font-light text-[12px]">
            *Price including VAT, if applicable
          </Text>
        </div>
      </div>
      <div className="flex p-5 px-16 bg-[#192f50] flex-col gap-2 items-center justify-center">
        <Text
          variant={"section_title"}
          className="text-white text-center font-light text-[12px]"
        >
          Credits available for use anytime within two years from the date of
          purchase.
        </Text>
      </div>
    </>
  );
};
