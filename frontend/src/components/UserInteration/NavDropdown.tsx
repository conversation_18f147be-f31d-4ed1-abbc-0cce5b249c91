import * as DropdownMenu from "@radix-ui/react-dropdown-menu";
import { ChevronDown } from "lucide-react";
import { useState } from "react";
import { Link } from "react-router-dom";

type DropdownItem = {
  label: string;
  to: string;
};

const NavDropdown = ({
  title,
  items,
}: {
  title: string;
  items: DropdownItem[];
}) => {
  const [open, setOpen] = useState(false);

  return (
    <DropdownMenu.Root open={open} onOpenChange={setOpen}>
      <DropdownMenu.Trigger asChild>
        <button className="flex items-center gap-1 text-sm text-gray-400 hover:text-white transition outline-none focus:outline-none">
          {title}
          <ChevronDown
            size={16}
            className={`transition-transform duration-200 ${
              open ? "rotate-180" : "rotate-0"
            }`}
          />
        </button>
      </DropdownMenu.Trigger>

      <DropdownMenu.Content
        className="bg-zinc-900 text-white rounded-md shadow-lg mt-2 p-2 w-56"
        sideOffset={5}
      >
        {items.map((item) => (
          <DropdownMenu.Item key={item.to} asChild>
            <Link
              to={item.to}
              className="flex items-center gap-3 px-3 py-2 text-sm hover:bg-zinc-800 rounded transition"
            >
              {item.label}
            </Link>
          </DropdownMenu.Item>
        ))}
      </DropdownMenu.Content>
    </DropdownMenu.Root>
  );
};

export default NavDropdown;
