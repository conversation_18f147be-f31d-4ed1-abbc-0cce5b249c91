import React, { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { toast } from "sonner";
import { useApp } from "@/contexts/useApp";
import AuthService from "@/services/auth.service";
import Cookies from "js-cookie";
import {
  handleOAuthRedirect,
  getOAuthRedirectPath,
  getOAuthErrorMessage,
  validateOAuthTokens,
} from "@/utils/oauth.utils";

const OAuthCallback: React.FC = () => {
  const navigate = useNavigate();
  const { setUser } = useApp();
  // const [isProcessing, setIsProcessing] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const processOAuthCallback = async () => {
      try {
        const {
          accessToken,
          refreshToken,
          provider,
          error: oauthError,
        } = handleOAuthRedirect();
        console.log("accessToken", accessToken);
        console.log("refreshToken", refreshToken);
        console.log("provider", provider);
        if (oauthError) {
          const errorMessage = getOAuthErrorMessage(oauthError);
          setError(errorMessage);
          toast.error(errorMessage);

          // Redirect to login page after error
          setTimeout(() => {
            navigate("/");
          }, 3000);
          return;
        }

        if (!validateOAuthTokens(accessToken, refreshToken)) {
          setError("Invalid authentication tokens received.");
          toast.error("Authentication failed. Please try again.");

          setTimeout(() => {
            navigate("/");
          }, 3000);
          return;
        }

        // Store tokens
        if (accessToken && refreshToken) {
          localStorage.setItem("token", accessToken);
          Cookies.set("refreshToken", refreshToken, {
            expires: 7,
            path: "/",
            secure: true,
            sameSite: "strict",
            // httpOnly: true,
          });
          // setTokens({
          //   accessToken,
          //   refreshToken,
          // });

          // Fetch user data
          try {
            const userData = await AuthService.getCurrentUser();
            setUser(userData);

            toast.success(`Successfully signed in with ${provider}!`);

            // Get the stored redirect path or default to home
            const redirectPath = getOAuthRedirectPath();
            navigate(redirectPath);
          } catch (userError) {
            console.error("Error fetching user data:", userError);
            toast.error(
              "Authentication successful, but failed to load user data."
            );
            navigate("/");
          }
        }
      } catch (error) {
        console.error("OAuth callback error:", error);
        setError("An unexpected error occurred during authentication.");
        toast.error("Authentication failed. Please try again.");

        setTimeout(() => {
          navigate("/");
        }, 3000);
      }
      // finally {
      //   setIsProcessing(false);
      // }
    };

    processOAuthCallback();
  }, [navigate, setUser]);

  if (error) {
    return (
      <div className="min-h-screen bg-primary flex items-center justify-center p-4">
        <div className="bg-[#1A1D21F5] rounded-2xl p-8 max-w-md w-full text-center">
          <div className="text-red-500 text-6xl mb-4">⚠️</div>
          <h2 className="text-white text-xl font-semibold mb-4">
            Authentication Error
          </h2>
          <p className="text-gray-400 mb-6">{error}</p>
          <p className="text-gray-500 text-sm">Redirecting to login page...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-primary flex items-center justify-center p-4">
      <div className="bg-[#1A1D21F5] rounded-2xl p-8 max-w-md w-full text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-500 mx-auto mb-4"></div>
        <h2 className="text-white text-xl font-semibold mb-4">
          Completing Sign In
        </h2>
        <p className="text-gray-400">
          Please wait while we complete your authentication...
        </p>
      </div>
    </div>
  );
};

export default OAuthCallback;
