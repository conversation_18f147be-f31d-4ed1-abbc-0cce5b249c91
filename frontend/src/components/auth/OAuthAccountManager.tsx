import React, { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { GoogleIcon } from "@/lib/icons";
import { toast } from "sonner";
import { useApp } from "@/contexts/useApp";
import AuthService from "@/services/auth.service";
import { Spinner } from "@/components/ui/spinner";
import { isOAuthConfigured } from "@/utils/oauth.utils";

interface OAuthAccountManagerProps {
  className?: string;
}

const OAuthAccountManager: React.FC<OAuthAccountManagerProps> = ({
  className = "",
}) => {
  const { user, refreshUserData } = useApp();
  const [isLoading, setIsLoading] = useState<{ [key: string]: boolean }>({});

  if (!user) return null;

  const handleUnlinkAccount = async (provider: "google" | "apple") => {
    try {
      setIsLoading((prev) => ({ ...prev, [provider]: true }));

      await AuthService.unlinkOAuthAccount(provider);

      toast.success(
        `${
          provider.charAt(0).toUpperCase() + provider.slice(1)
        } account unlinked successfully`
      );

      // Refresh user data to update the UI
      await refreshUserData();
    } catch (error: unknown) {
      console.error(`Error unlinking ${provider} account:`, error);

      if (
        error instanceof Error &&
        (error as { response?: { data?: { error?: { code?: string } } } })
          ?.response?.data?.error?.code === "LAST_AUTH_METHOD"
      ) {
        toast.error(
          "Cannot unlink the last authentication method. Please set a password first."
        );
      } else {
        toast.error(`Failed to unlink ${provider} account. Please try again.`);
      }
    } finally {
      setIsLoading((prev) => ({ ...prev, [provider]: false }));
    }
  };

  const handleLinkAccount = (provider: "google" | "apple") => {
    // For linking, we need to redirect to OAuth flow
    // This would typically involve a different flow where the user
    // is already authenticated and wants to link an additional account
    toast.info(
      `Linking ${provider} accounts is not yet implemented. Please contact support.`
    );
  };

  const googleConfigured = isOAuthConfigured("google");
  const appleConfigured = isOAuthConfigured("apple");
  console.log("googleConfigured", googleConfigured);
  // Don't render if no OAuth providers are configured
  if (!googleConfigured && !appleConfigured) {
    return null;
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle>Connected Accounts</CardTitle>
        <CardDescription>Manage your social login connections</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {googleConfigured && (
          <div className="flex items-center justify-between p-4 border rounded-lg">
            <div className="flex items-center gap-3">
              <GoogleIcon />
              <div>
                <h4 className="font-medium">Google</h4>
                <p className="text-sm text-muted-foreground">
                  {user.googleId ? "Connected" : "Not connected"}
                </p>
              </div>
            </div>
            <div className="flex items-center gap-2">
              {user.googleId ? (
                <>
                  <Badge
                    variant="secondary"
                    className="bg-green-100 text-green-800"
                  >
                    Connected
                  </Badge>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleUnlinkAccount("google")}
                    disabled={isLoading.google}
                  >
                    {isLoading.google ? (
                      <>
                        <Spinner size="sm" className="mr-2" />
                        Unlinking...
                      </>
                    ) : (
                      "Unlink"
                    )}
                  </Button>
                </>
              ) : (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleLinkAccount("google")}
                  disabled={isLoading.google}
                >
                  Link Account
                </Button>
              )}
            </div>
          </div>
        )}

        {/*appleConfigured && (
          <div className="flex items-center justify-between p-4 border rounded-lg">
            <div className="flex items-center gap-3">
              <AppleIcon />
              <div>
                <h4 className="font-medium">Apple</h4>
                <p className="text-sm text-muted-foreground">
                  {user.appleId ? 'Connected' : 'Not connected'}
                </p>
              </div>
            </div>
            <div className="flex items-center gap-2">
              {user.appleId ? (
                <>
                  <Badge variant="secondary" className="bg-green-100 text-green-800">
                    Connected
                  </Badge>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleUnlinkAccount('apple')}
                    disabled={isLoading.apple}
                  >
                    {isLoading.apple ? (
                      <>
                        <Spinner size="sm" className="mr-2" />
                        Unlinking...
                      </>
                    ) : (
                      'Unlink'
                    )}
                  </Button>
                </>
              ) : (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleLinkAccount('apple')}
                  disabled={isLoading.apple}
                >
                  Link Account
                </Button>
              )}
            </div>
          </div>
        )*/}

        <div className="text-xs text-muted-foreground mt-4 p-3 bg-muted/50 rounded-lg">
          <p className="font-medium mb-1">Security Note:</p>
          <p>
            You must have at least one authentication method (password or social
            login) connected to your account. If you unlink all social accounts,
            make sure you have a password set for your account.
          </p>
        </div>
      </CardContent>
    </Card>
  );
};

export default OAuthAccountManager;
