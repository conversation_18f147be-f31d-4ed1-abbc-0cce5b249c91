import React, { useState } from "react";
import { X, Mail, Lock, Eye, EyeOff } from "lucide-react";
import ShadowButton from "@/components//ui/shadowButton";
import { useLocation, useNavigate } from "react-router-dom";
import { useApp } from "@/contexts/useApp";
import { toast } from "sonner";
import OAuthButtons from "./OAuthButtons";
import { AxiosError } from "axios";

interface LoginModalProps {
  isOpen: boolean;
  onClose: () => void;
  onClickForgotPassword?: () => void;
  onClickSignUpModal?: () => void;
}

const LoginModal: React.FC<LoginModalProps> = ({
  isOpen,
  onClose,
  onClickForgotPassword,
  onClickSignUpModal,
}) => {
  // const [email, setEmail] = useState("");
  // const [password, setPassword] = useState("");
  const [rememberMe, setRememberMe] = useState(false);
  const [showPassword, setShowPassword] = useState(false);

  // const togglePasswordVisibility = () => {
  //   setShowPassword(!showPassword);
  // };

  const navigate = useNavigate();
  const location = useLocation();
  const { login, isLoading } = useApp();
  const [formData, setFormData] = useState({
    email: "",
    password: "",
  });
  const [error, setError] = useState("");

  // Get the redirect path from location state or default to dashboard
  const from = location.state?.from?.pathname || "/dashboard";

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError("");

    try {
      // Use the login function from AppContext
      const response = await login(formData.email, formData.password);

      console.log(response);
      if (response?.user?.emailVerified) {
        navigate(from, { replace: true });
        onClose();
        toast.success("Welcome back!");
      } else {
        setError("Please verify your email address before logging in.");
        toast.error("Please verify your email address before logging in.");
      }
    } catch (error) {
      if (error instanceof AxiosError) {
        setError(
          error.response?.data.error.message ||
            "Invalid email or password. Please try again."
        );
      } else {
        console.error("Login error:", error);
      }
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-primary/90 bg-opacity-50 flex items-center justify-center z-50 p-4 overflow-y-auto">
      <div className="bg-[#1A1D21F5] rounded-2xl w-full max-w-md relative max-h-[90vh] flex flex-col">
        <button
          onClick={onClose}
          className="absolute top-4 right-4 text-gray-400 hover:text-white transition-colors z-10"
        >
          <X size={20} />
        </button>

        <div className="overflow-y-auto p-8">
          {/* Logo and title */}
          <div className="">
            <img
              loading="lazy"
              src="/png/new_miragic_logo.png"
              className="w-[110px]"
            />
            <h2 className="text-white text-xl font-semibold mt-8 mb-5">
              Welcome to MiragicAI
            </h2>
          </div>

          {/* Social login buttons */}
          <OAuthButtons
            isLoading={isLoading}
            onOAuthStart={(provider) => {
              console.log(`Starting ${provider} OAuth flow`);
            }}
            className="mb-6"
          />

          <div className="relative flex items-center my-8">
            <div className="flex-grow border-t border-gray-600"></div>
            <span className="flex-shrink mx-4 text-gray-400 text-sm">
              or log in with email
            </span>
            <div className="flex-grow border-t border-gray-600"></div>
          </div>
          {error && (
            <div className="bg-destructive/10 text-destructive text-sm p-3 rounded-md mb-4">
              {error}
            </div>
          )}
          {/* Login form */}
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="relative">
              <Mail
                className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"
                size={18}
              />
              <input
                type="email"
                placeholder="Email"
                value={formData.email}
                onChange={handleChange}
                name="email"
                disabled={isLoading}
                className="w-full bg-white/10 border border-gray-700 rounded-lg py-3 pl-10 pr-4 text-white placeholder-gray-400 focus:outline-none focus:border-purple-500 transition-colors"
                required
              />
            </div>

            <div className="relative">
              <Lock
                className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"
                size={18}
              />
              <input
                type={showPassword ? "text" : "password"}
                placeholder="Password"
                value={formData.password}
                onChange={handleChange}
                name="password"
                disabled={isLoading}
                className="w-full bg-white/10 border border-gray-700 rounded-lg py-3 pl-10 pr-12 text-white placeholder-gray-400 focus:outline-none focus:border-purple-500 transition-colors"
                required
              />
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white transition-colors"
              >
                {showPassword ? <EyeOff size={18} /> : <Eye size={18} />}
              </button>
            </div>

            <div className="flex items-center justify-between my-9">
              <label className="flex items-center text-sm text-gray-300">
                <input
                  type="checkbox"
                  checked={rememberMe}
                  onChange={(e) => setRememberMe(e.target.checked)}
                  className="mr-2 rounded bg-gray-800 border-gray-600 text-purple-500 focus:ring-purple-500"
                />
                Remember me
              </label>
              <button
                type="button"
                onClick={onClickForgotPassword}
                className="text-sm text-purple-400 hover:text-purple-300 transition-colors cursor-pointer"
              >
                Forgot Password?
              </button>
            </div>

            <ShadowButton className="w-full py-3 px-4">Log in</ShadowButton>
          </form>

          <div className="mt-10 text-sm text-gray-400">
            Don't have an account?{" "}
            <button
              onClick={onClickSignUpModal}
              className="text-purple-400 hover:text-purple-300 transition-colors font-medium cursor-pointer"
            >
              Sign Up
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LoginModal;
