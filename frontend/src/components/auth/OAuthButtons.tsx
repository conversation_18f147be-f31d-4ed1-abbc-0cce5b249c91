import React from "react";
import { GoogleIcon } from "@/lib/icons";
import { initiateOAuthLogin, isOAuthConfigured } from "@/utils/oauth.utils";

interface OAuthButtonsProps {
  isLoading?: boolean;
  onOAuthStart?: (provider: "google" | "apple") => void;
  className?: string;
}

const OAuthButtons: React.FC<OAuthButtonsProps> = ({
  isLoading = false,
  onOAuthStart,
  className = "",
}) => {
  const handleOAuthClick = (provider: "google" | "apple") => {
    if (isLoading) return;

    // Call optional callback
    onOAuthStart?.(provider);

    // Initiate OAuth flow
    initiateOAuthLogin(provider);
  };

  const googleConfigured = isOAuthConfigured("google");
  const appleConfigured = isOAuthConfigured("apple");

  // Don't render if no OAuth providers are configured
  if (!googleConfigured && !appleConfigured) {
    return null;
  }

  return (
    <div className={`space-y-3 ${className}`}>
      {googleConfigured && (
        <button
          onClick={() => handleOAuthClick("google")}
          disabled={isLoading}
          className="w-full bg-white/10 cursor-pointer hover:bg-gray-700 text-white py-3 px-4 rounded-lg flex items-center justify-center gap-4 transition-colors border border-gray-700 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <GoogleIcon />
          {isLoading ? "Connecting..." : "Google Account"}
        </button>
      )}

      {/*appleConfigured && (
        <button
          onClick={() => handleOAuthClick('apple')}
          disabled={isLoading}
          className="w-full bg-white/10 cursor-pointer hover:bg-gray-700 text-white py-3 px-4 rounded-lg flex items-center justify-center gap-4 transition-colors border border-gray-700 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <AppleIcon />
          {isLoading ? 'Connecting...' : 'Apple Account'}
        </button>
      )*/}
    </div>
  );
};

export default OAuthButtons;
