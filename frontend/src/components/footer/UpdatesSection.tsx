import { useState, type FormEvent } from "react";
import { FaFacebook<PERSON>, FaXTwitter } from "react-icons/fa6";
import { FaInstagram, FaLinkedinIn, FaTik<PERSON> } from "react-icons/fa";
import { Button } from "../ui/button";
import { Text } from "../ui/text";

const socialLinks = [
  {
    name: "Facebook",
    url: "https://www.facebook.com/profile.php?id=61577373416753",
    Icon: FaFacebookF,
    hoverColor: "hover:text-[#1877F2]",
  },
  {
    name: "Instagram",
    url: "https://www.instagram.com/miragic.ai",
    Icon: FaInstagram,
    hoverColor: "hover:text-[#C13584]",
  },
  {
    name: "TikTok",
    url: "https://www.tiktok.com/@miragic.ai",
    Icon: FaTiktok,
    hoverColor: "hover:text-[#69C9D0]",
  },
  {
    name: "X",
    url: "https://x.com/miragic2025",
    Icon: FaXTwitter,
    hoverColor: "hover:text-[#000000]" },
  {
    name: "LinkedIn",
    url: "https://www.linkedin.com/company/miragic-ai",
    Icon: FaLinkedinIn,
    hoverColor: "hover:text-[#0A66C2]",
  },
];

const UpdatesSection = () => {
  const [email, setEmail] = useState("");

  const handleSubmit = (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault(); // Prevent the page from reloading
    console.log("Subscribing with email:", email);
    setEmail("");
  };

  return (
    <div className="text-white w-full max-w-[370px] space-y-5 mt-5">
      <Text variant={"card_list_title"} className="text-white font-semibold">
        Get Updates
      </Text>
      <Text variant={"small_title"} className="text-[#888] max-w-[340px]">
        Sign up for our mailing list to receive news and updates about
        miragic.ai products and services. You can unsubscribe at any time.
      </Text>

      <form
        className="flex gap-5 mb-8 justify-between max-w-[370px] w-full"
        onSubmit={handleSubmit}
      >
        <input
          type="email"
          placeholder="<EMAIL>"
          value={email}
          onChange={(e) => setEmail(e.target.value)}
          required
          className="flex-grow py-[6px] px-4 rounded-lg border border-[#3a3a4e] bg-[#25253a] text-base
                     placeholder:text-[#6a6a7b] focus:outline-none focus:border-[#8a74ff] 
                     transition-colors duration-300 w-full h-[50px]"
        />
        <Button
          type="submit"
          outline={false}
          variant={"animeGradient"}
          className="bg-radial-[at_5%_5%] from-[#2C9BF7] to-[#8054F3] rounded-full border border-white/15 py-[6px] px-4 h-[50px]"
        >
          Subscribe
        </Button>
      </form>

      <div className="flex gap-4">
        {socialLinks.map(({ name, url, Icon, hoverColor }) => (
          <a
            key={name}
            href={url}
            aria-label={name}
            target="_blank"
            rel="noopener noreferrer"
            className={`w-12 h-12 flex justify-center items-center rounded-full bg-[#F5F5F7] text-[#888]
                       hover:bg-white hover: ${hoverColor} transition-all duration-300 hover:-translate-y-1`}
          >
            <Icon size="20" strokeWidth={2} />
          </a>
        ))}
      </div>
    </div>
  );
};

export default UpdatesSection;
