import React, { useState, useEffect, useCallback } from "react";
import {
  <PERSON><PERSON>,
  Dialog<PERSON>ontent,
  Di<PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog";
import { Loader2 } from "lucide-react";
import StripePaymentWrapper from "./StripePaymentWrapper";
import CreditService from "@/services/credit.service";
import { toast } from "sonner";

interface CreditPurchaseModalProps {
  isOpen: boolean;
  onClose: () => void;
  packageId: string;
  packageName: string;
  onSuccess: () => void;
}

const CreditPurchaseModal: React.FC<CreditPurchaseModalProps> = ({
  isOpen,
  onClose,
  packageId,
  packageName,
  onSuccess,
}) => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [paymentIntent, setPaymentIntent] = useState<{
    clientSecret: string;
    amount: number;
    currency: string;
    orderId: string;
  } | null>(null);

  const createPaymentIntent = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      const response = await CreditService.createStripePaymentIntent(packageId);

      if (response && response.data) {
        setPaymentIntent({
          clientSecret: response.data.clientSecret,
          amount: response.data.amount,
          currency: response.data.currency,
          orderId: response.data.orderId,
        });
      } else {
        setError("Failed to create payment intent");
      }
    } catch (err) {
      console.error("Error creating payment intent:", err);
      setError("Failed to initialize payment. Please try again.");
    } finally {
      setLoading(false);
    }
  }, [packageId]);

  useEffect(() => {
    if (isOpen && packageId) {
      createPaymentIntent();
    }
  }, [isOpen, packageId, createPaymentIntent]);

  const handlePaymentSuccess = () => {
    toast("Payment Successful", {
      description: `Your credit purchase for ${packageName} was successful.`,
      // variant: 'default',
    });
    onSuccess();
    onClose();
  };

  const handleCancel = () => {
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Purchase Credits - {packageName}</DialogTitle>
        </DialogHeader>

        <div className="mt-4">
          {loading ? (
            <div className="flex justify-center items-center py-12">
              <Loader2 className="h-8 w-8 animate-spin text-blue-500" />
              <span className="ml-2">Initializing payment...</span>
            </div>
          ) : error ? (
            <div className="text-red-500 py-8 text-center">
              <p>{error}</p>
              <button
                onClick={() => createPaymentIntent()}
                className="mt-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
              >
                Try Again
              </button>
            </div>
          ) : paymentIntent ? (
            <StripePaymentWrapper
              clientSecret={paymentIntent.clientSecret}
              amount={paymentIntent.amount}
              currency={paymentIntent.currency}
              onSuccess={handlePaymentSuccess}
              onCancel={handleCancel}
            />
          ) : null}
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default CreditPurchaseModal;
