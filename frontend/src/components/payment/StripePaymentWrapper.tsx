import React from 'react';
import { Elements } from '@stripe/react-stripe-js';
import { loadStripe } from '@stripe/stripe-js';
import StripePaymentForm from './StripePaymentForm';
import { STRIPE_PUBLISHABLE_KEY } from '@/utils/stripe-config';

// Initialize Stripe with the key from our config utility
const stripePromise = loadStripe(STRIPE_PUBLISHABLE_KEY);

// Log initialization for debugging
console.log('Initializing Stripe with key prefix:', 
  STRIPE_PUBLISHABLE_KEY ? STRIPE_PUBLISHABLE_KEY.substring(0, 8) + '...' : 'undefined');

interface StripePaymentWrapperProps {
  clientSecret: string;
  amount: number;
  currency: string;
  onSuccess: () => void;
  onCancel: () => void;
}

const StripePaymentWrapper: React.FC<StripePaymentWrapperProps> = ({
  clientSecret,
  amount,
  currency,
  onSuccess,
  onCancel,
}) => {
  const options = {
    clientSecret,
    appearance: {
      theme: 'stripe' as const,
      variables: {
        colorPrimary: '#3b82f6', // blue-500
        colorBackground: '#ffffff',
        colorText: '#1e293b', // slate-800
        colorDanger: '#ef4444', // red-500
        fontFamily: 'Inter, system-ui, sans-serif',
        borderRadius: '0.5rem',
      },
    },
  };

  return (
    <Elements stripe={stripePromise} options={options}>
      <StripePaymentForm
        amount={amount}
        currency={currency}
        onSuccess={onSuccess}
        onCancel={onCancel}
      />
    </Elements>
  );
};

export default StripePaymentWrapper;
