import { useState, useEffect, useRef } from "react";

interface UseSlideUpAnimationOptions {
  duration?: number;
  delay?: number;
  distance?: number;
}

export function useSlideUpAnimation({ 
  duration = 800, 
  delay = 0,
  distance = 50
}: UseSlideUpAnimationOptions = {}) {
  const [isVisible, setIsVisible] = useState(false);
  const [isAnimating, setIsAnimating] = useState(false);
  const elementRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const element = elementRef.current;
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting && !isVisible) {
          setIsVisible(true);
          setTimeout(() => setIsAnimating(true), delay);
        }
      },
      { threshold: 0.1 }
    );

    if (element) {
      observer.observe(element);
    }

    return () => {
      if (element) {
        observer.unobserve(element);
      }
    };
  }, [isVisible, delay]);

  const animationStyle = {
    transform: isAnimating ? 'translateY(0)' : `translateY(${distance}px)`,
    opacity: isAnimating ? 1 : 0,
    transition: `all ${duration}ms cubic-bezier(0.4, 0, 0.2, 1)`,
  };

  return { elementRef, animationStyle };
} 