import { useState, useEffect, useCallback } from "react";
import CreditService from "@/services/credit.service";
import type { CreditPackage } from "@/services/credit.service";

export const useCreditPackages = () => {
  const [creditPackages, setCreditPackages] = useState<CreditPackage[]>([]);
  const [selectedPackageId, setSelectedPackageId] = useState("");
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");

  const fetchPackages = useCallback(async () => {
    try {
      setLoading(true);
      const response = await CreditService.getCreditPackages();
      if (response.data) {
        const activePackages = response.data.filter((pkg) => pkg.isActive);
        setCreditPackages(activePackages);
        if (activePackages.length > 0) {
          setSelectedPackageId(activePackages[0].id);
        }
      }
      setError("");
    } catch (err) {
      console.error("Error fetching credit packages:", err);
      setError("Failed to load credit packages");
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchPackages();
  }, [fetchPackages]);

  return {
    creditPackages,
    selectedPackageId,
    setSelectedPackageId,
    loading,
    error,
  };
};
