import { useState, useEffect } from "react";
import BlogService, {
  type ApiBlogPost,
  type BlogPost,
} from "@/services/blog.service";

interface PaginatedApiBlogPosts {
  posts: ApiBlogPost[];
  totalCount?: number;
  totalPages?: number;
  currentPage?: number;
}

interface UseBlogPostsOptions {
  limit?: number;
}

export function useBlogPosts(options: UseBlogPostsOptions = {}) {
  const [blogData, setBlogData] = useState<BlogPost[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchBlogPosts = async () => {
      try {
        setLoading(true);
        setError(null);
        
        const response = (await BlogService.getPosts({
          limit: options.limit || 10,
        })) as unknown as PaginatedApiBlogPosts;

        const transformedData: BlogPost[] = response.posts.map(
          (post: ApiBlogPost) => ({
            id: post.id,
            title: post.title,
            slug: post.slug,
            content: post.content || "",
            excerpt: post.excerpt || "",
            status: post.status || "PUBLISHED",
            authorId: post.authorId || post.author.id,
            categoryIds: post.categories.map((cat) => cat.id),
            tagIds: post.tags.map((tag) => tag.id),
            publishedAt: post.publishedAt || undefined,
            createdAt: post.createdAt,
            updatedAt: post.updatedAt,
            featuredImageUrl: post.featuredImageUrl || undefined,
            categories: post.categories,
            tags: post.tags,
            author: post.author,
          })
        );

        setBlogData(transformedData);
      } catch (error) {
        console.error("Error fetching blog posts:", error);
        setError("Failed to fetch blog posts");
      } finally {
        setLoading(false);
      }
    };

    fetchBlogPosts();
  }, [options.limit]);

  return { blogData, loading, error };
} 