// import { useState, useEffect, useCallback, useRef } from "react";
// import { toast } from "sonner";
// import VirtualTryOnService, {
//   type VirtualTryOnJob,
//   type ModelImage,
//   type RecentTryOn,
//   type ClothingItem,
// } from "@/services/virtualTryOn.service";

// // Placeholder for credit hook - will be implemented or imported
// const useCredits = () => {
//   return {
//     refreshCredits: () => {
//       // This would be implemented to refresh credits after a job completes
//       console.log("Refreshing credits");
//     },
//   };
// };

// interface UseVirtualTryOnOptions {
//   pollingInterval?: number;
//   autoLoadModels?: boolean;
//   autoLoadRecent?: boolean;
// }

// export function useVirtualTryOn(options: UseVirtualTryOnOptions = {}) {
//   const {
//     pollingInterval = 2000,
//     autoLoadModels = true,
//     autoLoadRecent = true,
//   } = options;

//   const { refreshCredits } = useCredits();

//   // State for file uploads
//   const [humanImage, setHumanImage] = useState<File | null>(null);
//   const [humanImagePreview, setHumanImagePreview] = useState<string | null>(
//     null
//   );
//   const [clothImage, setClothImage] = useState<File | null>(null);
//   const [clothImagePreview, setClothImagePreview] = useState<string | null>(
//     null
//   );
//   const [bottomClothImage, setBottomClothImage] = useState<File | null>(null);
//   const [bottomClothImagePreview, setBottomClothImagePreview] = useState<
//     string | null
//   >(null);

//   // State for model selection
//   const [selectedModelId, setSelectedModelId] = useState<string | null>(null);
//   const [saveAsModel, setSaveAsModel] = useState<boolean>(false);
//   const [modelName, setModelName] = useState<string>("");
//   const [modelGender, setModelGender] = useState<"MALE" | "FEMALE" | "UNISEX">(
//     "UNISEX"
//   );

//   // State for try-on mode
//   const [mode, setMode] = useState<"SINGLE" | "TOP_BOTTOM">("SINGLE");

//   // State for processing
//   const [isProcessing, setIsProcessing] = useState<boolean>(false);
//   const [currentJob, setCurrentJob] = useState<VirtualTryOnJob | null>(null);

//   // State for data fetching
//   const [models, setModels] = useState<ModelImage[]>([]);
//   const [adminModels, setAdminModels] = useState<ModelImage[]>([]);
//   const [userModels, setUserModels] = useState<ModelImage[]>([]);
//   const [recentTryOns, setRecentTryOns] = useState<RecentTryOn[]>([]);
//   const [clothingItems, setClothingItems] = useState<ClothingItem[]>([]);
//   const [isLoadingModels, setIsLoadingModels] = useState<boolean>(false);
//   const [isLoadingAdminModels, setIsLoadingAdminModels] =
//     useState<boolean>(false);
//   const [isLoadingUserModels, setIsLoadingUserModels] =
//     useState<boolean>(false);
//   const [isLoadingRecent, setIsLoadingRecent] = useState<boolean>(false);

//   // Refs for polling and file inputs
//   const statusCheckIntervalRef = useRef<NodeJS.Timeout | null>(null);
//   const humanFileInputRef = useRef<HTMLInputElement>(null);
//   const clothFileInputRef = useRef<HTMLInputElement>(null);
//   const bottomClothFileInputRef = useRef<HTMLInputElement>(null);

//   // Fetch model images
//   const fetchModels = useCallback(async () => {
//     setIsLoadingModels(true);
//     try {
//       const response = await VirtualTryOnService.getModelImages();
//       if (response.success) {
//         setModels(response.data);
//       }
//     } catch (error) {
//       console.error("Error fetching models:", error);
//       toast.error("Failed to load model images");
//     } finally {
//       setIsLoadingModels(false);
//     }
//   }, []);

//   // Fetch admin model images (our models)
//   const fetchAdminModels = useCallback(async () => {
//     setIsLoadingAdminModels(true);
//     try {
//       const response = await VirtualTryOnService.getAdminModelImages();
//       if (response.success) {
//         setAdminModels(response.data);
//       }
//     } catch (error) {
//       console.error("Error fetching admin models:", error);
//       toast.error("Failed to load admin model images");
//     } finally {
//       setIsLoadingAdminModels(false);
//     }
//   }, []);

//   // Fetch user model images (your models)
//   const fetchUserModels = useCallback(async () => {
//     setIsLoadingUserModels(true);
//     try {
//       const response = await VirtualTryOnService.getUserModelImages();
//       if (response.success) {
//         setUserModels(response.data);
//       }
//     } catch (error) {
//       console.error("Error fetching user models:", error);
//       toast.error("Failed to load your model images");
//     } finally {
//       setIsLoadingUserModels(false);
//     }
//   }, []);

//   // Fetch recent try-ons
//   const fetchRecentTryOns = useCallback(async () => {
//     setIsLoadingRecent(true);
//     try {
//       const response = await VirtualTryOnService.getRecentTryOns();
//       if (response.success) {
//         setRecentTryOns(response.data);

//         // Extract clothing items from recent try-ons
//         const extractedItems: ClothingItem[] = [];

//         response.data.forEach((tryOn) => {
//           // Add top/single clothing item
//           if (tryOn.clothImagePath) {
//             extractedItems.push({
//               id: `${tryOn.id}-top`,
//               userId: "",
//               imagePath: tryOn.clothImagePath,
//               clothingType: tryOn.mode === "SINGLE" ? "FULL_OUTFIT" : "TOP",
//               createdAt: tryOn.createdAt,
//               updatedAt: tryOn.accessedAt || tryOn.createdAt,
//               name: "", // Placeholder
//               category: "", // Placeholder
//               color: "", // Placeholder
//               style: "", // Placeholder
//             });
//           }

//           // Add bottom clothing item if it exists
//           if (tryOn.bottomClothImagePath) {
//             extractedItems.push({
//               id: `${tryOn.id}-bottom`,
//               userId: "",
//               imagePath: tryOn.bottomClothImagePath,
//               clothingType: "BOTTOM",
//               createdAt: tryOn.createdAt,
//               updatedAt: tryOn.accessedAt || tryOn.createdAt,
//               name: "", // Placeholder
//               category: "", // Placeholder
//               color: "", // Placeholder
//               style: "", // Placeholder
//             });
//           }
//         });

//         // Update clothing items state
//         setClothingItems(extractedItems);
//       }
//     } catch (error) {
//       console.error("Error fetching recent try-ons:", error);
//       toast.error("Failed to load recent try-ons");
//     } finally {
//       setIsLoadingRecent(false);
//     }
//   }, []);

//   // Load initial data
//   useEffect(() => {
//     if (autoLoadModels) {
//       fetchModels();
//       fetchAdminModels();
//       fetchUserModels();
//     }

//     if (autoLoadRecent) {
//       fetchRecentTryOns();
//     }

//     // Clean up interval on unmount
//     return () => {
//       if (statusCheckIntervalRef.current) {
//         clearInterval(statusCheckIntervalRef.current);
//       }
//     };
//   }, [
//     autoLoadModels,
//     autoLoadRecent,
//     fetchModels,
//     fetchAdminModels,
//     fetchUserModels,
//     fetchRecentTryOns,
//   ]);

//   // Validate image file
//   const validateImageFile = useCallback(
//     (file: File, onSuccess: (file: File) => void): void => {
//       const maxSize = 10 * 1024 * 1024; // 10MB
//       if (file.size > maxSize) {
//         toast.error("File size exceeds 10MB limit.");
//         return;
//       }
//       if (
//         !["image/png", "image/jpeg", "image/jpg", "image/webp"].includes(
//           file.type
//         )
//       ) {
//         toast.error("Invalid file format. Use PNG, JPG, JPEG, or WEBP.");
//         return;
//       }
//       onSuccess(file);
//     },
//     []
//   );

//   // Process human image file
//   const processHumanFile = useCallback(
//     (file: File | null): void => {
//       if (!file) {
//         // Reset human image state
//         setHumanImage(null);
//         setHumanImagePreview(null);
//         return;
//       }

//       validateImageFile(file, (validFile) => {
//         setHumanImage(validFile);
//         const reader = new FileReader();
//         reader.onload = (e: ProgressEvent<FileReader>): void => {
//           if (e.target?.result) {
//             setHumanImagePreview(e.target.result as string);
//             // Reset model selection when uploading a new human image
//             setSelectedModelId(null);
//           }
//         };
//         reader.readAsDataURL(validFile);
//       });
//     },
//     [validateImageFile]
//   );

//   // Process clothing image file
//   const processClothFile = useCallback(
//     (file: File | null): void => {
//       if (!file) {
//         // Reset clothing image state
//         setClothImage(null);
//         setClothImagePreview(null);
//         return;
//       }

//       validateImageFile(file, (validFile) => {
//         setClothImage(validFile);
//         const reader = new FileReader();
//         reader.onload = (e: ProgressEvent<FileReader>): void => {
//           if (e.target?.result) {
//             setClothImagePreview(e.target.result as string);
//           }
//         };
//         reader.readAsDataURL(validFile);
//       });
//     },
//     [validateImageFile]
//   );

//   // Process bottom clothing image file
//   const processBottomClothFile = useCallback(
//     (file: File | null): void => {
//       if (!file) {
//         // Reset bottom clothing image state
//         setBottomClothImage(null);
//         setBottomClothImagePreview(null);
//         return;
//       }

//       validateImageFile(file, (validFile) => {
//         setBottomClothImage(validFile);
//         const reader = new FileReader();
//         reader.onload = (e: ProgressEvent<FileReader>): void => {
//           if (e.target?.result) {
//             setBottomClothImagePreview(e.target.result as string);
//           }
//         };
//         reader.readAsDataURL(validFile);
//       });
//     },
//     [validateImageFile]
//   );

//   // Handle model selection
//   const handleModelSelect = useCallback(
//     (modelId: string): void => {
//       setSelectedModelId(modelId === selectedModelId ? null : modelId);
//       // Clear human image when selecting a model
//       if (modelId !== selectedModelId) {
//         setHumanImage(null);
//         setHumanImagePreview(null);
//         if (humanFileInputRef.current) humanFileInputRef.current.value = "";
//       }
//     },
//     [selectedModelId]
//   );

//   // Handle mode selection
//   const handleModeSelect = useCallback(
//     (newMode: "SINGLE" | "TOP_BOTTOM"): void => {
//       // If we're switching from TOP_BOTTOM to SINGLE and we have a bottom cloth image
//       // but no top cloth image, move the bottom cloth to the top position
//       if (
//         newMode === "SINGLE" &&
//         mode === "TOP_BOTTOM" &&
//         !clothImage &&
//         bottomClothImage
//       ) {
//         // Move bottom cloth to top position
//         setClothImage(bottomClothImage);

//         // If we have a preview image for the bottom cloth, use it for the top cloth
//         if (bottomClothImagePreview) {
//           setClothImagePreview(bottomClothImagePreview);
//         }

//         // Clear bottom cloth data
//         setBottomClothImage(null);
//         setBottomClothImagePreview(null);

//         // Reset file input references
//         if (bottomClothFileInputRef.current) {
//           bottomClothFileInputRef.current.value = "";
//         }
//       } else if (newMode === "SINGLE") {
//         // Just reset bottom cloth when switching to single mode
//         setBottomClothImage(null);
//         setBottomClothImagePreview(null);
//         if (bottomClothFileInputRef.current) {
//           bottomClothFileInputRef.current.value = "";
//         }
//       }

//       setMode(newMode);
//     },
//     [mode, clothImage, bottomClothImage, bottomClothImagePreview]
//   );

//   // Reset all state
//   const handleReset = useCallback((): void => {
//     setHumanImage(null);
//     setHumanImagePreview(null);
//     setClothImage(null);
//     setClothImagePreview(null);
//     setBottomClothImage(null);
//     setBottomClothImagePreview(null);
//     setSelectedModelId(null);
//     setCurrentJob(null);
//     setIsProcessing(false);
//   }, []);

//   // Handle download of result image
//   const handleDownload = useCallback((): void => {
//     if (currentJob?.resultImagePath) {
//       VirtualTryOnService.downloadImage(
//         currentJob.resultImagePath,
//         `virtual-tryon-${currentJob.id}.jpg`
//       );
//     }
//   }, [currentJob]);

//   // Poll for job status
//   const startPollingJobStatus = useCallback(
//     (jobId: string): void => {
//       // Clear any existing interval
//       if (statusCheckIntervalRef.current) {
//         clearInterval(statusCheckIntervalRef.current);
//       }

//       // Set up new interval
//       statusCheckIntervalRef.current = setInterval(async () => {
//         try {
//           const response = await VirtualTryOnService.getJob(jobId);
//           if (response.success) {
//             const job = response.data;
//             setCurrentJob(job);

//             // If job is completed or failed, stop polling
//             if (job.status === "COMPLETED" || job.status === "FAILED") {
//               if (statusCheckIntervalRef.current) {
//                 clearInterval(statusCheckIntervalRef.current);
//                 statusCheckIntervalRef.current = null;
//               }
//               setIsProcessing(false);

//               // Refresh credit balance on completion
//               refreshCredits();

//               // Refresh recent try-ons
//               fetchRecentTryOns();

//               if (job.status === "COMPLETED") {
//                 toast.success("Virtual try-on completed successfully");
//               } else {
//                 toast.error(
//                   `Virtual try-on failed: ${
//                     job.errorMessage || "Unknown error"
//                   }`
//                 );
//               }
//             }
//           }
//         } catch (error) {
//           console.error("Error checking job status:", error);
//           // Stop polling on error
//           if (statusCheckIntervalRef.current) {
//             clearInterval(statusCheckIntervalRef.current);
//             statusCheckIntervalRef.current = null;
//           }
//           setIsProcessing(false);
//           toast.error("Failed to check processing status");
//         }
//       }, pollingInterval);
//     },
//     [fetchRecentTryOns, pollingInterval, refreshCredits]
//   );

//   // Start processing the virtual try-on
//   const handleGenerate = useCallback(async (): Promise<void> => {
//     // Validate inputs
//     if (!humanImage && !selectedModelId) {
//       toast.error("Please upload a human image or select a model");
//       return;
//     }

//     if (!clothImage) {
//       toast.error("Please upload a clothing image");
//       return;
//     }

//     if (mode === "TOP_BOTTOM" && !bottomClothImage) {
//       toast.error(
//         "Please upload a bottom clothing image for Top & Bottom mode"
//       );
//       return;
//     }

//     setIsProcessing(true);

//     try {
//       const params: Parameters<
//         typeof VirtualTryOnService.processVirtualTryOn
//       >[0] = {
//         mode: mode === "SINGLE" ? "single" : "top_bottom",
//         cloth_image: clothImage,
//       };

//       // Add human image or model ID
//       if (humanImage) {
//         params.human_image = humanImage;
//         params.saveAsModel = saveAsModel;
//         if (saveAsModel) {
//           params.modelName =
//             modelName || `Model ${new Date().toLocaleDateString()}`;
//           params.gender = modelGender;
//         }
//       } else if (selectedModelId) {
//         params.modelImageId = selectedModelId;
//       }

//       // Add bottom clothing image for TOP_BOTTOM mode
//       if (mode === "TOP_BOTTOM" && bottomClothImage) {
//         params.low_cloth_image = bottomClothImage;
//       }

//       // Make API request
//       const response = await VirtualTryOnService.processVirtualTryOn(params);

//       if (response.success) {
//         // Start polling for job status
//         startPollingJobStatus(response.data.jobId);
//         toast.success("Virtual try-on processing started");
//       } else {
//         toast.error("Failed to start virtual try-on processing");
//         setIsProcessing(false);
//       }
//     } catch (error) {
//       console.error("Error starting virtual try-on:", error);
//       toast.error("An error occurred while processing your request");
//       setIsProcessing(false);
//     }
//   }, [
//     bottomClothImage,
//     clothImage,
//     humanImage,
//     mode,
//     modelGender,
//     modelName,
//     saveAsModel,
//     selectedModelId,
//     startPollingJobStatus,
//   ]);

//   // Load a recent try-on
//   const loadRecentTryOn = useCallback((job: VirtualTryOnJob): void => {
//     setCurrentJob(job);
//     setIsProcessing(false);
//   }, []);

//   // Upload a new model image
//   const uploadModelImage = useCallback(
//     async (
//       file: File
//       // params: {
//       //   modelName?: string;
//       //   gender?: "MALE" | "FEMALE" | "UNISEX";
//       //   bodyType?: string;
//       //   poseType?: string;
//       //   ethnicity?: string;
//       // }
//     ): Promise<void> => {
//       try {
//         const response = await VirtualTryOnService.uploadModelImage(
//           file
//           // params
//         );
//         if (response.success) {
//           toast.success("Model image uploaded successfully");
//           fetchModels();
//         } else {
//           toast.error("Failed to upload model image");
//         }
//       } catch (error) {
//         console.error("Error uploading model image:", error);
//         toast.error("An error occurred while uploading model image");
//       }
//     },
//     [fetchModels]
//   );

//   // Initialize clothing items if empty
//   // useEffect(() => {
//   //   if (clothingItems.length === 0) {
//   //     // This would be replaced with an API call in production
//   //     setClothingItems([
//   //       {
//   //         id: "1",
//   //         userId: "",
//   //         imagePath: "/placeholder/cloth1.jpg",
//   //         clothingType: "TOP",
//   //         createdAt: new Date().toISOString(),
//   //         updatedAt: new Date().toISOString()
//   //       },
//   //       {
//   //         id: "2",
//   //         userId: "",
//   //         imagePath: "/placeholder/cloth2.jpg",
//   //         clothingType: "BOTTOM",
//   //         createdAt: new Date().toISOString(),
//   //         updatedAt: new Date().toISOString()
//   //       },
//   //       {
//   //         id: "3",
//   //         userId: "",
//   //         imagePath: "/placeholder/cloth3.jpg",
//   //         clothingType: "DRESS",
//   //         createdAt: new Date().toISOString(),
//   //         updatedAt: new Date().toISOString()
//   //       },
//   //       {
//   //         id: "4",
//   //         userId: "",
//   //         imagePath: "/placeholder/cloth4.jpg",
//   //         clothingType: "FULL_OUTFIT",
//   //         createdAt: new Date().toISOString(),
//   //         updatedAt: new Date().toISOString()
//   //       },
//   //     ]);
//   //   }
//   // }, [clothingItems.length]);

//   return {
//     // State
//     humanImage,
//     humanImagePreview,
//     clothImage,
//     clothImagePreview,
//     bottomClothImage,
//     bottomClothImagePreview,
//     selectedModelId,
//     saveAsModel,
//     modelName,
//     modelGender,
//     mode,
//     isProcessing,
//     currentJob,
//     models,
//     adminModels,
//     userModels,
//     recentTryOns,
//     clothingItems,
//     isLoadingModels,
//     isLoadingAdminModels,
//     isLoadingUserModels,
//     isLoadingRecent,

//     // Refs
//     humanFileInputRef,
//     clothFileInputRef,
//     bottomClothFileInputRef,

//     // Actions
//     setSaveAsModel,
//     setModelName,
//     setModelGender,
//     setMode,

//     // Methods
//     processHumanFile,
//     processClothFile,
//     processBottomClothFile,
//     handleModelSelect,
//     handleModeSelect,
//     handleReset,
//     handleDownload,
//     handleGenerate,
//     loadRecentTryOn,
//     fetchAdminModels,
//     fetchUserModels,
//     uploadModelImage,
//     fetchModels,
//     fetchRecentTryOns,
//     validateImageFile,
//   };
// }

// export default useVirtualTryOn;
