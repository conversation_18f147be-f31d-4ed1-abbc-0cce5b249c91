import { useState, useCallback } from "react";
import BgRemovalService from "@/services/bgRemoval.service";

export interface FileHandlerState {
  previewUrl: string | null;
  uploadedFile: File | null;
  processedImage: string | null;
  error: string | null;
  selectedModel: string | null;
  isProcessing: boolean;
  backgroundColor: string | null;
  originalProcessedImage: string | null;
}

const initialState: FileHandlerState = {
  previewUrl: null,
  uploadedFile: null,
  processedImage: null,
  error: null,
  selectedModel: null,
  isProcessing: false,
  backgroundColor: null,
  originalProcessedImage: null,
};

export const useFileHandler = () => {
  const [state, setState] = useState<FileHandlerState>(initialState);

  const resetState = useCallback(() => {
    setState(initialState);
  }, []);

  const handleFile = useCallback((file: File) => {
    const reader = new FileReader();
    reader.onload = (e: ProgressEvent<FileReader>) => {
      const target = e.target as FileReader;
      if (target?.result) {
        setState((prev) => ({
          ...prev,
          uploadedFile: file,
          previewUrl: target.result as string,
          selectedModel: null,
          processedImage: null,
          error: null,
        }));
      }
    };
    reader.readAsDataURL(file);
  }, []);

  const handleModelSelect = useCallback((modelPath: string) => {
    setState((prev) => ({
      ...prev,
      selectedModel: modelPath,
      uploadedFile: null,
      previewUrl: modelPath,
      processedImage: null,
      error: null,
    }));
  }, []);

  const handleDownload = useCallback(async () => {
    if (!state.processedImage) return;

    try {
      // Convert data URL to blob
      const response = await fetch(state.processedImage);
      const blob = await response.blob();
      const url = URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;
      link.download = `bg-removed-${Date.now()}.png`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
    } catch (error) {
      console.error("Error downloading image:", error);
      setState((prev) => ({ ...prev, error: "Failed to download the image" }));
    }
  }, [state.processedImage]);

  const handleProcessImage = useCallback(async () => {
    let fileToProcess: File | null = null;

    try {
      if (state.uploadedFile) {
        fileToProcess = state.uploadedFile;
      } else if (state.selectedModel) {
        const response = await fetch(state.selectedModel);
        const blob = await response.blob();
        fileToProcess = new File([blob], "selected-model.jpg", {
          type: "image/jpeg",
        });
      }

      if (!fileToProcess) return;

      setState((prev) => ({ ...prev, isProcessing: true, error: null }));
      const response = await BgRemovalService.directRemoveBackgroundHomePage(
        fileToProcess
      );

      if (response.url) {
        setState((prev) => ({
          ...prev,
          processedImage: response.url,
          originalProcessedImage: response.url,
          isProcessing: false,
        }));
      } else {
        throw new Error(response.message || "Failed to remove background");
      }
    } catch (error: unknown) {
      const err = error as {
        response?: { data?: { error?: { message?: string } } };
        message?: string;
      };
      console.error("Error processing image:", err);
      const errorMessage =
        err.response?.data?.error?.message || err.message || "Unknown error";
      setState((prev) => ({
        ...prev,
        error: `Failed to process image: ${errorMessage}`,
        isProcessing: false,
      }));
    }
  }, [state.uploadedFile, state.selectedModel]);

  const handleBackgroundColor = useCallback(
    async (background: string) => {
      if (!state.processedImage) return;

      try {
        setState((prev) => ({ ...prev, isProcessing: true }));

        // Create a canvas to draw the image with background
        const img = new Image();
        img.crossOrigin = "anonymous";
        img.src = state.originalProcessedImage || state.processedImage;

        await new Promise((resolve) => {
          img.onload = resolve;
        });

        const canvas = document.createElement("canvas");
        canvas.width = img.width;
        canvas.height = img.height;
        const ctx = canvas.getContext("2d");

        if (!ctx) throw new Error("Could not get canvas context");

        // Handle background (color or image)
        if (background.startsWith("#")) {
          // If it's a color
          ctx.fillStyle = background;
          ctx.fillRect(0, 0, canvas.width, canvas.height);
        } else {
          // If it's an image
          try {
            const bgImg = new Image();
            bgImg.crossOrigin = "anonymous";

            // Wait for background image to load
            await new Promise((resolve, reject) => {
              bgImg.onload = resolve;
              bgImg.onerror = reject;
              bgImg.src = background;
            });

            // Clear the canvas
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // Calculate scaling to cover the canvas while maintaining aspect ratio
            const scale = Math.max(
              canvas.width / bgImg.width,
              canvas.height / bgImg.height
            );

            // Calculate position to center the image
            const x = (canvas.width - bgImg.width * scale) / 2;
            const y = (canvas.height - bgImg.height * scale) / 2;

            // Draw background image
            ctx.drawImage(
              bgImg,
              x,
              y,
              bgImg.width * scale,
              bgImg.height * scale
            );

            console.log("Background image drawn successfully");
          } catch (error) {
            console.error("Error loading background image:", error);
            throw new Error("Failed to load background image");
          }
        }

        // Draw the processed image on top
        ctx.drawImage(img, 0, 0);

        // Create object URL
        const url = canvas.toDataURL("image/png");

        setState((prev) => ({
          ...prev,
          processedImage: url,
          backgroundColor: background,
          isProcessing: false,
        }));
      } catch (error) {
        console.error("Error applying background:", error);
        setState((prev) => ({
          ...prev,
          error: "Failed to apply background",
          isProcessing: false,
        }));
      }
    },
    [state.processedImage, state.originalProcessedImage]
  );

  return {
    state,
    resetState,
    handleFile,
    handleModelSelect,
    handleDownload,
    handleProcessImage,
    handleBackgroundColor,
  };
};
