import { useState } from "react";
import { toast } from "sonner";
import type { User } from "@/services/auth.service";

export interface CreditPackage {
  id: string;
  name?: string;
  price?: number;
  currency?: string;
  creditsAmount?: number;
  isActive?: boolean;
  description?: string | null;
}

interface PurchasingState {
  loading: boolean;
  setLoading: (value: boolean) => void;
  handlePurchaseCredits: (params: {
    user: User | null;
    selectedPackageId: string;
    creditPackages: CreditPackage[];
    onSuccess: () => void;
  }) => void;
}

export const usePurchasing = (): PurchasingState => {
  const [loading, setLoading] = useState(false);

  const handlePurchaseCredits = ({
    user,
    selectedPackageId,
    creditPackages,
    onSuccess,
  }: {
    user: User | null;
    selectedPackageId: string;
    creditPackages: CreditPackage[];
    onSuccess: () => void;
  }) => {
    if (!user) {
      toast("Authentication required", {
        description: "Please sign in to purchase credits",
      });
      return;
    }

    if (!selectedPackageId) {
      toast("Selection required", {
        description: "Please select a credit package",
      });
      return;
    }

    const selectedPackage = creditPackages.find(
      (pkg) => pkg.id === selectedPackageId
    );
    if (!selectedPackage) {
      toast("Error", {
        description: "Selected package not found",
      });
      return;
    }

    setLoading(true);
    onSuccess();
  };

  return {
    loading,
    setLoading,
    handlePurchaseCredits,
  };
};
