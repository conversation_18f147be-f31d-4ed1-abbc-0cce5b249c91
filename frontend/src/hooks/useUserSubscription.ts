import { useState, useEffect, useCallback } from "react";
import SubscriptionService from "@/services/subscription.service";
import type { UserSubscription } from "@/services/subscription.service";

export const useUserSubscription = (user: any) => {
  const [userSubscription, setUserSubscription] =
    useState<UserSubscription | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");

  const fetchUserSubscription = useCallback(async () => {
    if (!user) return;

    try {
      setLoading(true);
      const subscription = await SubscriptionService.getUserSubscription();
      setUserSubscription(subscription);
      setError("");
    } catch (err) {
      console.error("Error fetching user subscription:", err);
      setError("Failed to load user subscription");
    } finally {
      setLoading(false);
    }
  }, [user]);

  useEffect(() => {
    fetchUserSubscription();
  }, [fetchUserSubscription]);

  return {
    userSubscription,
    loading,
    error,
    refreshUserSubscription: fetchUserSubscription,
  };
};
