import { useState, useEffect, useCallback } from "react";
import SubscriptionService from "@/services/subscription.service";
import type {
  SubscriptionPlan,
  UserSubscription,
} from "@/services/subscription.service";

export const usePricingPlans = (userSubscription: UserSubscription | null) => {
  const [subscriptionPlans, setSubscriptionPlans] = useState<
    SubscriptionPlan[]
  >([]);
  const [selectedSubscriptionId, setSelectedSubscriptionId] = useState("");
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");

  const fetchPlans = useCallback(async () => {
    try {
      setLoading(true);
      const response = await SubscriptionService.getSubscriptionPlans();
      const activePlans = response.filter((plan) => plan.isActive);
      setSubscriptionPlans(activePlans);

      if (activePlans.length > 0) {
        setSelectedSubscriptionId(
          userSubscription ? userSubscription.plan.id : activePlans[0].id
        );
      }
      setError("");
    } catch (err) {
      console.error("Error fetching subscription plans:", err);
      setError("Failed to load subscription plans");
    } finally {
      setLoading(false);
    }
  }, [userSubscription]);

  useEffect(() => {
    fetchPlans();
  }, [fetchPlans]);

  return {
    subscriptionPlans,
    selectedSubscriptionId,
    setSelectedSubscriptionId,
    loading,
    error,
  };
};
