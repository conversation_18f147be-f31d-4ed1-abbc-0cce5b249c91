import { useState, useEffect, useRef } from "react";

interface UseCountUpOptions {
  end: number;
  duration?: number;
  delay?: number;
  formatNumber?: (num: number) => string;
}

export function useCountUp({ 
  end, 
  duration = 2000, 
  delay = 0,
  formatNumber = (num: number) => num.toLocaleString()
}: UseCountUpOptions) {
  const [count, setCount] = useState(0);
  const [isVisible, setIsVisible] = useState(false);
  const elementRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const element = elementRef.current;
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting && !isVisible) {
          setIsVisible(true);
        }
      },
      { threshold: 0.1 }
    );

    if (element) {
      observer.observe(element);
    }

    return () => {
      if (element) {
        observer.unobserve(element);
      }
    };
  }, [isVisible]);

  useEffect(() => {
    if (!isVisible) return;

    const startTime = Date.now();
    const startValue = 0;
    let animationFrameId: number;

    const animate = () => {
      const currentTime = Date.now();
      const elapsed = currentTime - startTime - delay;

      if (elapsed < 0) {
        animationFrameId = requestAnimationFrame(animate);
        return;
      }

      const progress = Math.min(elapsed / duration, 1);
      
      // Smooth easing function - combination of ease-out and ease-in-out
      const easeOutCubic = 1 - Math.pow(1 - progress, 3);
      const easeInOutQuart = progress < 0.5 
        ? 8 * progress * progress * progress * progress 
        : 1 - Math.pow(-2 * progress + 2, 4) / 2;
      
      // Blend the easing functions for ultra-smooth animation
      const smoothProgress = easeOutCubic * 0.7 + easeInOutQuart * 0.3;
      
      const currentValue = Math.floor(startValue + (end - startValue) * smoothProgress);

      setCount(currentValue);

      if (progress < 1) {
        animationFrameId = requestAnimationFrame(animate);
      }
    };

    animationFrameId = requestAnimationFrame(animate);

    return () => {
      if (animationFrameId) {
        cancelAnimationFrame(animationFrameId);
      }
    };
  }, [isVisible, end, duration, delay]);

  return { count, elementRef, formattedCount: formatNumber(count) };
} 