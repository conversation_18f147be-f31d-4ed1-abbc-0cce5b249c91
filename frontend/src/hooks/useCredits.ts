import { useState, useEffect, useCallback } from "react";
import CreditService, {
  type CreditBalance,
  type ServiceCost,
} from "@/services/credit.service";
import { useApp } from "@/contexts/useApp";

export function useCredits() {
  const [balance, setBalance] = useState<CreditBalance | null>(null);
  const [costs, setCosts] = useState<ServiceCost | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isRefreshing, setIsRefreshing] = useState<boolean>(false);
  const { setUserCredits } = useApp();

  // Fetch credit balance
  const fetchBalance = useCallback(async () => {
    setIsLoading(true);
    try {
      console.log("Fetching credit balance...");
      const response = await CreditService.getUserCreditBalance();
      console.log("balance", response);
      if (response.success) {
        setBalance(response.data);
      } else {
        console.error("Error fetching credit balance:", response.message);
      }
    } catch (error) {
      console.error("Error fetching credit balance:", error);
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Fetch service costs
  const fetchCosts = useCallback(async () => {
    try {
      const response = await CreditService.getServiceCosts();
      if (response.success) {
        setCosts(response.data);
      } else {
        console.error("Error fetching service costs:", response.message);
      }
    } catch (error) {
      console.error("Error fetching service costs:", error);
    }
  }, []);

  // Refresh credit balance
  const refreshCredits = useCallback(async () => {
    setIsRefreshing(true);
    console.log("Refreshing credit balance...");
    try {
      const response = await CreditService.getUserCreditBalance();
      console.log("refreshed balance", response);
      if (response.success) {
        console.log("Refreshed credit balance:", response.data.balance);
        setBalance(response.data);
        setUserCredits(response.data);
        return response.data;
      } else {
        console.error("Error refreshing credit balance:", response.message);
        return null;
      }
    } catch (error) {
      console.error("Error refreshing credit balance:", error);
      return null;
    } finally {
      setIsRefreshing(false);
    }
  }, []);

  // Check if user has enough credits for a service
  const hasEnoughCredits = useCallback(
    (service: keyof ServiceCost) => {
      if (!balance || !costs) return false;
      return balance.balance >= (costs[service] || 0);
    },
    [balance, costs]
  );

  // Get cost for a specific service
  const getCost = useCallback(
    (service: keyof ServiceCost) => {
      if (!costs) return 0;
      return costs[service] || 0;
    },
    [costs]
  );

  // Load initial data
  useEffect(() => {
    fetchBalance();
    fetchCosts();
  }, [fetchBalance, fetchCosts]);

  return {
    balance,
    costs,
    isLoading,
    isRefreshing,
    refreshCredits,
    hasEnoughCredits,
    getCost,
  };
}

export default useCredits;
