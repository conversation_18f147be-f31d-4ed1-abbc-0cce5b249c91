{"info": {"name": "Miragic-AI API - Part 1", "description": "API collection for Miragic-AI SaaS platform - Authentication, User, and Admin endpoints", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Authentication", "description": "Endpoints for user authentication and account management", "item": [{"name": "Register", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/api/v1/auth/register", "host": ["{{baseUrl}}"], "path": ["api", "v1", "auth", "register"]}, "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"securePassword123\",\n  \"firstName\": \"<PERSON>\",\n  \"lastName\": \"<PERSON><PERSON>\"\n}"}, "description": "Register a new user account"}}, {"name": "<PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/api/v1/auth/login", "host": ["{{baseUrl}}"], "path": ["api", "v1", "auth", "login"]}, "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"securePassword123\"\n}"}, "description": "Authenticate a user and receive access token"}}, {"name": "Refresh <PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/api/v1/auth/refresh-token", "host": ["{{baseUrl}}"], "path": ["api", "v1", "auth", "refresh-token"]}, "body": {"mode": "raw", "raw": "{\n  \"refreshToken\": \"your-refresh-token\"\n}"}, "description": "Get a new access token using refresh token"}}, {"name": "Logout", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/v1/auth/logout", "host": ["{{baseUrl}}"], "path": ["api", "v1", "auth", "logout"]}, "body": {"mode": "raw", "raw": "{\n  \"refreshToken\": \"your-refresh-token\"\n}"}, "description": "Invalidate the current refresh token"}}, {"name": "Request Password Reset", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/api/v1/auth/request-password-reset", "host": ["{{baseUrl}}"], "path": ["api", "v1", "auth", "request-password-reset"]}, "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\"\n}"}, "description": "Request a password reset email"}}, {"name": "Reset Password", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/api/v1/auth/reset-password", "host": ["{{baseUrl}}"], "path": ["api", "v1", "auth", "reset-password"]}, "body": {"mode": "raw", "raw": "{\n  \"token\": \"reset-token-from-email\",\n  \"password\": \"newSecurePassword123\"\n}"}, "description": "Reset password using token received via email"}}, {"name": "<PERSON><PERSON><PERSON>", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/api/v1/auth/verify-email/:token", "host": ["{{baseUrl}}"], "path": ["api", "v1", "auth", "verify-email", ":token"], "variable": [{"key": "token", "value": "verification-token-from-email"}]}, "description": "Verify user email address using token received via email"}}]}, {"name": "User", "description": "Endpoints for user profile management", "item": [{"name": "Get Current User", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/v1/user/me", "host": ["{{baseUrl}}"], "path": ["api", "v1", "user", "me"]}, "description": "Get current user profile information"}}, {"name": "Update Profile", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/v1/user/me", "host": ["{{baseUrl}}"], "path": ["api", "v1", "user", "me"]}, "body": {"mode": "raw", "raw": "{\n  \"firstName\": \"Updated\",\n  \"lastName\": \"Name\",\n  \"email\": \"<EMAIL>\"\n}"}, "description": "Update user profile information"}}, {"name": "Update Password", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/v1/user/me/password", "host": ["{{baseUrl}}"], "path": ["api", "v1", "user", "me", "password"]}, "body": {"mode": "raw", "raw": "{\n  \"currentPassword\": \"currentPassword123\",\n  \"newPassword\": \"newPassword123\"\n}"}, "description": "Update user password"}}, {"name": "Get Credit Transactions", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/v1/user/credits/transactions", "host": ["{{baseUrl}}"], "path": ["api", "v1", "user", "credits", "transactions"]}, "description": "Get user credit transaction history"}}, {"name": "Upload Profile Image", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/v1/user/me/avatar", "host": ["{{baseUrl}}"], "path": ["api", "v1", "user", "me", "avatar"]}, "body": {"mode": "formdata", "formdata": [{"key": "profileImage", "type": "file", "src": "/path/to/profile-image.jpg"}]}, "description": "Upload a profile image"}}]}, {"name": "Admin", "description": "Admin endpoints for platform management", "item": [{"name": "User Management", "item": [{"name": "Get All Users", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/v1/admin/users", "host": ["{{baseUrl}}"], "path": ["api", "v1", "admin", "users"]}, "description": "Get all users (admin only)"}}, {"name": "Get User by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/v1/admin/users/:userId", "host": ["{{baseUrl}}"], "path": ["api", "v1", "admin", "users", ":userId"], "variable": [{"key": "userId", "value": "user-id"}]}, "description": "Get user details by ID (admin only)"}}, {"name": "Update User", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/v1/admin/users/:userId", "host": ["{{baseUrl}}"], "path": ["api", "v1", "admin", "users", ":userId"], "variable": [{"key": "userId", "value": "user-id"}]}, "body": {"mode": "raw", "raw": "{\n  \"firstName\": \"Updated\",\n  \"lastName\": \"Name\",\n  \"email\": \"<EMAIL>\",\n  \"role\": \"ADMIN\",\n  \"isActive\": true\n}"}, "description": "Update user details (admin only)"}}, {"name": "Delete User", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/v1/admin/users/:userId", "host": ["{{baseUrl}}"], "path": ["api", "v1", "admin", "users", ":userId"], "variable": [{"key": "userId", "value": "user-id"}]}, "description": "Delete user (admin only)"}}]}, {"name": "Blog Management", "item": [{"name": "Create Blog Post", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/v1/admin/blog/posts", "host": ["{{baseUrl}}"], "path": ["api", "v1", "admin", "blog", "posts"]}, "body": {"mode": "raw", "raw": "{\n  \"title\": \"New Blog Post\",\n  \"content\": \"Blog post content goes here...\",\n  \"excerpt\": \"Short excerpt of the blog post\",\n  \"categoryId\": \"category-id\",\n  \"tagIds\": [\"tag-id-1\", \"tag-id-2\"],\n  \"featuredImage\": \"image-url.jpg\",\n  \"status\": \"PUBLISHED\"\n}"}, "description": "Create a new blog post (admin only)"}}, {"name": "Update Blog Post", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/v1/admin/blog/posts/:postId", "host": ["{{baseUrl}}"], "path": ["api", "v1", "admin", "blog", "posts", ":postId"], "variable": [{"key": "postId", "value": "post-id"}]}, "body": {"mode": "raw", "raw": "{\n  \"title\": \"Updated Blog Post\",\n  \"content\": \"Updated blog post content...\",\n  \"excerpt\": \"Updated short excerpt\",\n  \"categoryId\": \"category-id\",\n  \"tagIds\": [\"tag-id-1\", \"tag-id-3\"],\n  \"featuredImage\": \"updated-image-url.jpg\",\n  \"status\": \"PUBLISHED\"\n}"}, "description": "Update an existing blog post (admin only)"}}, {"name": "Delete Blog Post", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/v1/admin/blog/posts/:postId", "host": ["{{baseUrl}}"], "path": ["api", "v1", "admin", "blog", "posts", ":postId"], "variable": [{"key": "postId", "value": "post-id"}]}, "description": "Delete a blog post (admin only)"}}, {"name": "Get All Blog Posts", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/v1/admin/blog/posts/all", "host": ["{{baseUrl}}"], "path": ["api", "v1", "admin", "blog", "posts", "all"]}, "description": "Get all blog posts (admin only)"}}, {"name": "Create Category", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/v1/admin/blog/categories", "host": ["{{baseUrl}}"], "path": ["api", "v1", "admin", "blog", "categories"]}, "body": {"mode": "raw", "raw": "{\n  \"name\": \"New Category\",\n  \"description\": \"Category description\"\n}"}, "description": "Create a new blog category (admin only)"}}, {"name": "Create Tag", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/v1/admin/blog/tags", "host": ["{{baseUrl}}"], "path": ["api", "v1", "admin", "blog", "tags"]}, "body": {"mode": "raw", "raw": "{\n  \"name\": \"New Tag\"\n}"}, "description": "Create a new blog tag (admin only)"}}]}]}], "variable": [{"key": "baseUrl", "value": "http://localhost:3000", "type": "string"}]}