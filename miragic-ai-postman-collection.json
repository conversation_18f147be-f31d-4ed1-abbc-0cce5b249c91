{"info": {"name": "Miragic-AI API", "description": "API collection for Miragic-AI SaaS platform offering video generation, image generation, background removal, and more.", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Authentication", "description": "Endpoints for user authentication and account management", "item": [{"name": "Register", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/api/v1/auth/register", "host": ["{{baseUrl}}"], "path": ["api", "v1", "auth", "register"]}, "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"securePassword123\",\n  \"firstName\": \"<PERSON>\",\n  \"lastName\": \"<PERSON><PERSON>\"\n}"}, "description": "Register a new user account"}}, {"name": "<PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/api/v1/auth/login", "host": ["{{baseUrl}}"], "path": ["api", "v1", "auth", "login"]}, "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"securePassword123\"\n}"}, "description": "Authenticate a user and receive access token"}}, {"name": "Refresh <PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/api/v1/auth/refresh-token", "host": ["{{baseUrl}}"], "path": ["api", "v1", "auth", "refresh-token"]}, "body": {"mode": "raw", "raw": "{\n  \"refreshToken\": \"your-refresh-token\"\n}"}, "description": "Get a new access token using refresh token"}}, {"name": "Logout", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/v1/auth/logout", "host": ["{{baseUrl}}"], "path": ["api", "v1", "auth", "logout"]}, "body": {"mode": "raw", "raw": "{\n  \"refreshToken\": \"your-refresh-token\"\n}"}, "description": "Invalidate the current refresh token"}}, {"name": "Request Password Reset", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/api/v1/auth/request-password-reset", "host": ["{{baseUrl}}"], "path": ["api", "v1", "auth", "request-password-reset"]}, "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\"\n}"}, "description": "Request a password reset email"}}, {"name": "Reset Password", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/api/v1/auth/reset-password", "host": ["{{baseUrl}}"], "path": ["api", "v1", "auth", "reset-password"]}, "body": {"mode": "raw", "raw": "{\n  \"token\": \"reset-token-from-email\",\n  \"password\": \"newSecurePassword123\"\n}"}, "description": "Reset password using token received via email"}}, {"name": "<PERSON><PERSON><PERSON>", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/api/v1/auth/verify-email/:token", "host": ["{{baseUrl}}"], "path": ["api", "v1", "auth", "verify-email", ":token"], "variable": [{"key": "token", "value": "verification-token-from-email"}]}, "description": "Verify user email address using token received via email"}}]}], "variable": [{"key": "baseUrl", "value": "http://localhost:3000", "type": "string"}]}