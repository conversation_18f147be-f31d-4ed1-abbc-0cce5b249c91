{"info": {"name": "Miragic-AI API - Part 4", "description": "API collection for Miragic-AI SaaS platform - Background Removal, Virtual Try-On, Speed Painting, and Other Services", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Background Removal", "description": "Endpoints for image background removal using Removal.ai", "item": [{"name": "Remove Background (File Upload)", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/v1/background-removal/remove", "host": ["{{baseUrl}}"], "path": ["api", "v1", "background-removal", "remove"]}, "body": {"mode": "formdata", "formdata": [{"key": "image", "type": "file", "src": "/path/to/image.jpg"}]}, "description": "Remove background from an uploaded image (costs 1 credit)"}}, {"name": "Process Image URL", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/v1/background-removal/process-url", "host": ["{{baseUrl}}"], "path": ["api", "v1", "background-removal", "process-url"]}, "body": {"mode": "raw", "raw": "{\n  \"imageUrl\": \"https://example.com/image.jpg\"\n}"}, "description": "Remove background from an image URL (costs 1 credit)"}}, {"name": "Direct Background Removal", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/v1/background-removal/direct-remove", "host": ["{{baseUrl}}"], "path": ["api", "v1", "background-removal", "direct-remove"]}, "body": {"mode": "formdata", "formdata": [{"key": "image", "type": "file", "src": "/path/to/image.jpg"}]}, "description": "Direct background removal with Miragic AI API (costs 1 credit)"}}, {"name": "Get User Background Removal Jobs", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/v1/background-removal/jobs", "host": ["{{baseUrl}}"], "path": ["api", "v1", "background-removal", "jobs"]}, "description": "Get all background removal jobs for the current user"}}, {"name": "Get Background Removal Job", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/v1/background-removal/jobs/:jobId", "host": ["{{baseUrl}}"], "path": ["api", "v1", "background-removal", "jobs", ":jobId"], "variable": [{"key": "jobId", "value": "job-id"}]}, "description": "Get details of a specific background removal job"}}]}, {"name": "Virtual Try-On", "description": "Endpoints for virtual try-on service", "item": [{"name": "Virtual Try-On (File Upload)", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/v1/virtual-try-on/process", "host": ["{{baseUrl}}"], "path": ["api", "v1", "virtual-try-on", "process"]}, "body": {"mode": "formdata", "formdata": [{"key": "personImage", "type": "file", "src": "/path/to/person.jpg"}, {"key": "clothingImage", "type": "file", "src": "/path/to/clothing.jpg"}]}, "description": "Process virtual try-on with uploaded images (costs 3 credits)"}}, {"name": "Get User Virtual Try-On Jobs", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/v1/virtual-try-on/jobs", "host": ["{{baseUrl}}"], "path": ["api", "v1", "virtual-try-on", "jobs"]}, "description": "Get all virtual try-on jobs for the current user"}}, {"name": "Get Virtual Try-On Job", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/v1/virtual-try-on/jobs/:jobId", "host": ["{{baseUrl}}"], "path": ["api", "v1", "virtual-try-on", "jobs", ":jobId"], "variable": [{"key": "jobId", "value": "job-id"}]}, "description": "Get details of a specific virtual try-on job"}}]}, {"name": "Speed Painting", "description": "Endpoints for speed painting service", "item": [{"name": "Generate Speed Painting", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/v1/speed-painting/generate", "host": ["{{baseUrl}}"], "path": ["api", "v1", "speed-painting", "generate"]}, "body": {"mode": "raw", "raw": "{\n  \"prompt\": \"A beautiful landscape with mountains and a lake\",\n  \"style\": \"oil painting\",\n  \"duration\": 30\n}"}, "description": "Generate a speed painting video (costs 5 credits)"}}, {"name": "Get User Speed Painting Jobs", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/v1/speed-painting/jobs", "host": ["{{baseUrl}}"], "path": ["api", "v1", "speed-painting", "jobs"]}, "description": "Get all speed painting jobs for the current user"}}, {"name": "Get Speed Painting Job", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/v1/speed-painting/jobs/:jobId", "host": ["{{baseUrl}}"], "path": ["api", "v1", "speed-painting", "jobs", ":jobId"], "variable": [{"key": "jobId", "value": "job-id"}]}, "description": "Get details of a specific speed painting job"}}]}, {"name": "Blog", "description": "Endpoints for blog functionality", "item": [{"name": "Get Blog Posts", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/api/v1/blog/posts?page=1&limit=10", "host": ["{{baseUrl}}"], "path": ["api", "v1", "blog", "posts"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}]}, "description": "Get published blog posts with pagination"}}, {"name": "Get Blog Post by ID", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/api/v1/blog/posts/:postId", "host": ["{{baseUrl}}"], "path": ["api", "v1", "blog", "posts", ":postId"], "variable": [{"key": "postId", "value": "post-id"}]}, "description": "Get a specific blog post by ID"}}, {"name": "Get Blog Categories", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/api/v1/blog/categories", "host": ["{{baseUrl}}"], "path": ["api", "v1", "blog", "categories"]}, "description": "Get all blog categories"}}, {"name": "Get Blog Tags", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/api/v1/blog/tags", "host": ["{{baseUrl}}"], "path": ["api", "v1", "blog", "tags"]}, "description": "Get all blog tags"}}]}, {"name": "Subscriptions", "description": "Endpoints for subscription management", "item": [{"name": "Get All Subscription Plans", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/api/v1/subscriptions/plans", "host": ["{{baseUrl}}"], "path": ["api", "v1", "subscriptions", "plans"]}, "description": "Get all available subscription plans"}}, {"name": "Compare Subscription Plans", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/api/v1/subscriptions/plans/compare", "host": ["{{baseUrl}}"], "path": ["api", "v1", "subscriptions", "plans", "compare"]}, "description": "Compare features of different subscription plans"}}, {"name": "Get Subscription Plan by ID", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/api/v1/subscriptions/plans/:id", "host": ["{{baseUrl}}"], "path": ["api", "v1", "subscriptions", "plans", ":id"], "variable": [{"key": "id", "value": "plan-id"}]}, "description": "Get details of a specific subscription plan"}}, {"name": "Get User Subscription", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/v1/subscriptions/user", "host": ["{{baseUrl}}"], "path": ["api", "v1", "subscriptions", "user"]}, "description": "Get current user's subscription details"}}]}, {"name": "Payment", "description": "Endpoints for payment processing", "item": [{"name": "Get Subscription Plans", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/api/v1/payment/plans", "host": ["{{baseUrl}}"], "path": ["api", "v1", "payment", "plans"]}, "description": "Get available subscription plans for purchase"}}, {"name": "Get Credit Packs", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/api/v1/payment/credit-packs", "host": ["{{baseUrl}}"], "path": ["api", "v1", "payment", "credit-packs"]}, "description": "Get available credit packs for purchase"}}, {"name": "Create Stripe Checkout Session", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/v1/payment/stripe/create-checkout-session", "host": ["{{baseUrl}}"], "path": ["api", "v1", "payment", "stripe", "create-checkout-session"]}, "body": {"mode": "raw", "raw": "{\n  \"planId\": \"plan-id\",\n  \"successUrl\": \"https://yourdomain.com/success\",\n  \"cancelUrl\": \"https://yourdomain.com/cancel\"\n}"}, "description": "Create a Stripe checkout session for subscription purchase"}}, {"name": "Create Stripe Credit Checkout Session", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/v1/payment/stripe/create-credit-checkout-session", "host": ["{{baseUrl}}"], "path": ["api", "v1", "payment", "stripe", "create-credit-checkout-session"]}, "body": {"mode": "raw", "raw": "{\n  \"creditPackId\": \"credit-pack-id\",\n  \"successUrl\": \"https://yourdomain.com/success\",\n  \"cancelUrl\": \"https://yourdomain.com/cancel\"\n}"}, "description": "Create a Stripe checkout session for credit pack purchase"}}, {"name": "Cancel Subscription", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/v1/payment/subscriptions/cancel", "host": ["{{baseUrl}}"], "path": ["api", "v1", "payment", "subscriptions", "cancel"]}, "description": "Cancel the current user's subscription"}}, {"name": "Get User Subscription", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/v1/payment/subscriptions/me", "host": ["{{baseUrl}}"], "path": ["api", "v1", "payment", "subscriptions", "me"]}, "description": "Get the current user's subscription details"}}, {"name": "Get User Invoices", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/v1/payment/invoices", "host": ["{{baseUrl}}"], "path": ["api", "v1", "payment", "invoices"]}, "description": "Get the current user's payment invoices"}}]}, {"name": "Health Check", "description": "Health check endpoint for monitoring", "item": [{"name": "Health Check", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/health", "host": ["{{baseUrl}}"], "path": ["health"]}, "description": "Check if the server is running"}}]}], "variable": [{"key": "baseUrl", "value": "http://localhost:3000", "type": "string"}]}