import React, {
  useRef,
  useState,
  type ChangeEvent,
  type DragEvent,
} from "react";
import { Upload, Plus, RefreshCw, Download } from "lucide-react";
import ShadowButton from "@/components/ui/shadowButton";

// Define interfaces for clothing items and models
interface ClothingItem {
  id: number;
  type: string;
  image: string;
}

interface Model {
  id: number;
  image: string;
}

const VirtualTryOnPage: React.FC = () => {
  const [isDragOver, setIsDragOver] = useState<boolean>(false);
  const [uploadedImage, setUploadedImage] = useState<string | null>(null);
  const [selectedClothingType, setSelectedClothingType] =
    useState<string>("Single clothes");
  const [selectedModel, setSelectedModel] = useState<number | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Sample clothing items and models data
  const clothingItems: ClothingItem[] = [
    { id: 1, type: "suit", image: "/png/streaming_avatar_1.png" },
    { id: 2, type: "suit", image: "/png/streaming_avatar_1.png" },
    { id: 3, type: "suit", image: "/png/streaming_avatar_1.png" },
    { id: 4, type: "suit", image: "/png/streaming_avatar_1.png" },
  ];

  const models: Model[] = [
    { id: 1, image: "/png/streaming_avatar_1.png" },
    { id: 2, image: "/png/streaming_avatar_1.png" },
    { id: 3, image: "/png/streaming_avatar_1.png" },
    { id: 4, image: "/png/streaming_avatar_1.png" },
    { id: 5, image: "/png/streaming_avatar_1.png" },
    { id: 6, image: "/png/streaming_avatar_1.png" },
    { id: 7, image: "/png/streaming_avatar_1.png" },
    { id: 8, image: "/png/streaming_avatar_1.png" },
  ];

  const handleDragOver = (e: DragEvent<HTMLDivElement>): void => {
    e.preventDefault();
    setIsDragOver(true);
  };

  const handleDragLeave = (e: DragEvent<HTMLDivElement>): void => {
    e.preventDefault();
    setIsDragOver(false);
  };

  const handleDrop = (e: DragEvent<HTMLDivElement>): void => {
    e.preventDefault();
    setIsDragOver(false);
    const files: FileList = e.dataTransfer.files;
    if (files.length > 0) {
      const file: File = files[0];
      processFile(file);
    }
  };

  const processFile = (file: File): void => {
    const maxSize = 30 * 1024 * 1024; // 30MB
    if (file.size > maxSize) {
      alert("File size exceeds 30MB limit.");
      return;
    }
    if (
      !["image/png", "image/jpeg", "image/jpg", "image/webp"].includes(
        file.type
      )
    ) {
      alert("Invalid file format. Use PNG, JPG, JPEG, or WEBP.");
      return;
    }
    const reader: FileReader = new FileReader();
    reader.onload = (e: ProgressEvent<FileReader>): void => {
      if (e.target?.result) {
        setUploadedImage(e.target.result as string);
      }
    };
    reader.readAsDataURL(file);
  };

  const handleFileSelect = (e: ChangeEvent<HTMLInputElement>): void => {
    const files: FileList | null = e.target.files;
    if (files?.length) {
      processFile(files[0]);
    }
  };

  const handleReset = (): void => {
    setUploadedImage(null);
    setSelectedModel(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = ""; // Clear file input
    }
  };

  const handleDownload = (): void => {
    console.log("Download processed image");
  };

  const handleGenerate = (): void => {
    console.log("Generate virtual try-on", {
      selectedClothingType,
      selectedModel,
      uploadedImage,
    });
  };

  const handleAddClick = (): void => {
    fileInputRef.current?.click();
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLDivElement>): void => {
    if (e.key === "Enter" || e.key === " ") {
      e.preventDefault();
      handleAddClick();
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center relative">
      <img
        src="/png/image_gene_shadow_1.png"
        className="absolute bottom-[-24px] right-[-24px]"
        alt=""
        aria-hidden="true"
      />
      {/* Background Grid Pattern with Fade Effect */}
      <div className="absolute inset-0" aria-hidden="true">
        <div
          className="grid grid-cols-12 h-full opacity-10"
          style={{
            maskImage:
              "radial-gradient(ellipse at top left, transparent 20%, black 50%)",
            WebkitMaskImage:
              "radial-gradient(ellipse at top left, transparent 20%, black 50%)",
          }}
        >
          {Array.from({ length: 144 }).map((_, i: number) => (
            <div key={i} className="border border-white/20"></div>
          ))}
        </div>
      </div>

      {/* Floating Dots */}
      <div
        className="absolute top-20 right-20 w-2 h-2 bg-white/30 rounded-full animate-pulse"
        aria-hidden="true"
      ></div>
      <div
        className="absolute top-40 right-40 w-1 h-1 bg-white/20 rounded-full animate-pulse delay-1000"
        aria-hidden="true"
      ></div>
      <div
        className="absolute bottom-32 left-32 w-1.5 h-1.5 bg-white/25 rounded-full animate-pulse delay-500"
        aria-hidden="true"
      ></div>
      <div
        className="absolute bottom-20 right-60 w-1 h-1 bg-white/20 rounded-full animate-pulse delay-700"
        aria-hidden="true"
      ></div>

      <div className="relative z-10 w-full max-w-7xl px-6">
        {/* Header */}
        <div className="text-left mb-8">
          <h1 className="text-4xl font-inter font-semibold text-white mb-4">
            Virtual Try-On
          </h1>
        </div>

        <div className="flex gap-8 items-center">
          {/* Left Panel - Controls */}
          <div className="w-96">
            <div className="bg-white/10 backdrop-blur-sm border border-gray-600 rounded-2xl p-6">
              {/* Clothing Selection */}
              <div className="mb-6">
                <h3 className="text-white text-lg font-medium mb-4">
                  Select Clothes
                </h3>
                {/* Clothing Type Buttons */}
                <div className="flex gap-8 mb-4 justify-between">
                  <ShadowButton
                    onClick={() => setSelectedClothingType("Single clothes")}
                    className={`flex-1 px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                      selectedClothingType === "Single clothes"
                        ? "!bg-purple-600 !text-white"
                        : "!bg-gray-700 !text-gray-300 hover:!bg-gray-600"
                    }`}
                  >
                    Single Clothes
                  </ShadowButton>
                  <ShadowButton
                    onClick={() => setSelectedClothingType("Top & Bottom")}
                    className={`flex-1 px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                      selectedClothingType === "Top & Bottom"
                        ? "!bg-purple-600 !text-white"
                        : "!bg-gray-700 !text-gray-300 hover:!bg-gray-600"
                    }`}
                  >
                    Top & Bottom
                  </ShadowButton>
                </div>

                {/* Add Item Area */}
                <div
                  className={`border-2 border-dashed rounded-lg p-8 mt-8 text-center transition-colors cursor-pointer relative ${
                    isDragOver
                      ? "border-purple-400 bg-purple-900/30"
                      : "border-gray-600 hover:border-gray-500"
                  }`}
                  onDragOver={handleDragOver}
                  onDragLeave={handleDragLeave}
                  onDrop={handleDrop}
                  onClick={handleAddClick}
                  onKeyDown={handleKeyDown}
                  role="button"
                  tabIndex={0}
                  aria-label="Add an item"
                >
                  <div className="flex gap-2 justify-center items-center">
                    <Plus className="w-5 h-5 text-gray-400" />
                    <p className="text-gray-300 font-medium">Add Item</p>
                  </div>
                  <p className="text-gray-500 text-sm mb-3">
                    Or drag & drop here
                  </p>
                  <input
                    ref={fileInputRef}
                    type="file"
                    className="hidden"
                    accept=".png,.jpg,.jpeg,.webp"
                    onChange={handleFileSelect}
                  />
                  {isDragOver && (
                    <div className="absolute inset-0 bg-purple-600/20 border-2 border-purple-400 rounded-lg flex items-center justify-center z-10">
                      <div className="text-center">
                        <Upload
                          size={32}
                          className="text-purple-300 mx-auto mb-2"
                        />
                        <p className="text-purple-200 font-medium">
                          Drop your files here
                        </p>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* Recent Items */}
              <div className="mb-6">
                <div className="flex justify-between items-center mb-3">
                  <h4 className="text-white font-medium">Recent Items</h4>
                  <button className="text-gray-400 hover:text-gray-300 cursor-pointer">
                    See all
                  </button>
                </div>
                <div className="grid grid-cols-4 gap-2">
                  {clothingItems.map((item) => (
                    <div
                      key={item.id}
                      className="aspect-square bg-gray-700 rounded-lg overflow-hidden"
                    >
                      <img
                        src={item.image}
                        alt={`Clothing item ${item.type} ${item.id}`}
                        className="w-full h-full object-cover object-top"
                      />
                    </div>
                  ))}
                </div>
              </div>

              {/* Model Selection */}
              <div className="mb-6">
                <h4 className="text-white font-medium mb-2">Select a Model</h4>
                <p className="text-gray-400 text-sm mb-4">
                  Select our model to try on
                </p>
                <div className="grid grid-cols-4 gap-2 h-[150px] overflow-auto">
                  <div className="aspect-square bg-gray-700 rounded-lg border-2 border-dashed border-gray-600 flex items-center justify-center">
                    <Plus className="w-6 h-6 text-gray-400" />
                  </div>
                  {models.map((model) => (
                    <div
                      key={model.id}
                      onClick={() => setSelectedModel(model.id)}
                      className={`aspect-square rounded-lg overflow-hidden cursor-pointer border-2 transition-colors ${
                        selectedModel === model.id
                          ? "border-purple-400"
                          : "border-transparent hover:border-gray-500"
                      }`}
                    >
                      <img
                        src={model.image}
                        alt={`Model ${model.id}`}
                        className="w-full h-full object-cover object-top"
                      />
                    </div>
                  ))}
                </div>
              </div>

              {/* Generate Button */}
              <ShadowButton onClick={handleGenerate} className="!w-full !py-3">
                Generate
              </ShadowButton>
            </div>
          </div>

          {/* Right Panel - Instructions or Result */}
          <div className="flex-1">
            {!uploadedImage && !selectedModel ? (
              // Instructions Panel
              <div className="opacity-75 rounded-2xl p-8">
                <div className="space-y-6">
                  <div className="flex items-start gap-4">
                    <div className="w-8 h-8 bg-gray-700 rounded-full flex items-center justify-center text-white font-medium text-sm">
                      1
                    </div>
                    <div>
                      <h3 className="text-white font-medium mb-2">
                        Select Clothes
                      </h3>
                      <p className="text-gray-400">
                        Choose which clothes you’d like to try on, please follow
                        the guidelines
                      </p>
                    </div>
                  </div>
                  <div className="flex items-start gap-4">
                    <div className="w-8 h-8 bg-gray-700 rounded-full flex items-center justify-center text-white font-medium text-sm">
                      2
                    </div>
                    <div>
                      <h3 className="text-white font-medium mb-2">
                        Pick a Model
                      </h3>
                      <p className="text-gray-400">
                        Choose a model of your own to try on
                      </p>
                    </div>
                  </div>
                  <div className="flex items-start gap-4">
                    <div className="w-8 h-8 bg-gray-700 rounded-full flex items-center justify-center text-white font-medium text-sm">
                      3
                    </div>
                    <div>
                      <h3 className="text-white font-medium mb-2">
                        Try it On!
                      </h3>
                      <p className="text-gray-400">
                        Click “Generate” to see the outfit come to life on the
                        model
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            ) : (
              // Result Panel
              <div className="bg-gray-800/50 backdrop-blur-sm border border-gray-600 rounded-2xl overflow-hidden">
                <div className="relative">
                  <img
                    src={
                      uploadedImage ||
                      models.find((m) => m.id === selectedModel)?.image ||
                      ""
                    }
                    alt="Virtual try-on result"
                    className="w-full h-auto object-contain"
                    style={{
                      background:
                        "url(\"data:image/svg+xml,%3csvg width='20' height='20' xmlns='http://www.w3.org/2000/svg'%3e%3cdefs%3e%3cpattern id='a' width='20' height='20' patternUnits='userSpaceOnUse'%3e%3crect width='10' height='10' fill='%23f3f4f6'/%3e%3crect x='10' y='10' width='10' height='10' fill='%23f3f4f6'/%3e%3c/pattern%3e%3c/defs%3e%3crect width='100%25' height='100%25' fill='url(%23a)'/%3e%3c/svg%3e\")",
                    }}
                  />
                  {/* Control Buttons Overlay */}
                  <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 bg-gray-900/90 backdrop-blur-sm rounded-lg p-3 flex items-center gap-3">
                    <button
                      onClick={handleReset}
                      className="flex items-center justify-center w-10 h-10 bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition-colors"
                      title="Reset"
                    >
                      <RefreshCw size={18} />
                    </button>
                    <button
                      onClick={handleDownload}
                      className="flex items-center gap-2 px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors"
                    >
                      <Download size={18} />
                      Download
                    </button>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default VirtualTryOnPage;
